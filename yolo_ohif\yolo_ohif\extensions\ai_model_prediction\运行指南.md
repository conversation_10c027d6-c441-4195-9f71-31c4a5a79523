# 重构后AI预测扩展运行指南

## 🚀 快速开始

### 方法1: 直接运行快速开始脚本
```bash
# 进入扩展目录
cd e:\Trae\yolo_ohif\extensions\ai_model_prediction

# 运行快速开始演示
python quick_start.py
```

### 方法2: 运行完整测试套件
```bash
# 进入扩展目录
cd e:\Trae\yolo_ohif\extensions\ai_model_prediction

# 运行所有测试
python run_tests.py
```

### 方法3: 运行使用示例
```bash
# 进入扩展目录
cd e:\Trae\yolo_ohif\extensions\ai_model_prediction

# 运行使用示例
python example_usage.py
```

## 📁 文件说明

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `quick_start.py` | 快速演示 | 展示主要功能，适合初次了解 |
| `run_tests.py` | 完整测试 | 运行所有测试用例，验证功能正确性 |
| `example_usage.py` | 使用示例 | 详细的使用示例，适合学习具体用法 |
| `test_refactored_extension.py` | 测试套件 | unittest测试用例（通过run_tests.py运行） |
| `README.md` | 详细文档 | 完整的项目文档和API说明 |

## 🔧 解决导入问题

如果遇到类似这样的导入错误：
```
ImportError: attempted relative import with no known parent package
```

**解决方案：**

1. **使用提供的运行脚本**（推荐）：
   ```bash
   python quick_start.py
   python run_tests.py
   python example_usage.py
   ```

2. **设置Python路径**：
   ```bash
   # Windows
   set PYTHONPATH=e:\Trae\yolo_ohif\extensions\ai_model_prediction
   python test_refactored_extension.py
   
   # 或者使用-m参数
   cd e:\Trae\yolo_ohif\extensions
   python -m ai_model_prediction.test_refactored_extension
   ```

3. **在Python代码中添加路径**：
   ```python
   import sys
   import os
   sys.path.insert(0, r'e:\Trae\yolo_ohif\extensions\ai_model_prediction')
   ```

## 📊 运行结果说明

### 快速开始脚本 (quick_start.py)
- 演示基本功能创建和使用
- 展示构建器模式
- 演示事件系统
- 演示缓存系统
- 演示服务容器
- 演示错误处理

### 测试脚本 (run_tests.py)
- 检查依赖和模块导入
- 运行基本功能测试
- 运行高级功能测试
- 运行异步功能测试
- 运行完整unittest套件

### 使用示例 (example_usage.py)
- 基本使用方法
- 配置文件使用
- 构建器模式详解
- 异步预测示例
- 事件处理示例
- 缓存操作示例
- 错误处理示例
- 性能监控示例

## ⚠️ 注意事项

1. **模型服务**：示例中的预测功能需要真实的模型服务，如果没有运行模型服务，预测会返回None或抛出异常，这是正常行为。

2. **依赖检查**：运行前会自动检查必要的Python模块，如果缺少依赖会给出提示。

3. **日志文件**：测试运行时会生成`test_results.log`日志文件，包含详细的运行信息。

4. **清理资源**：所有示例都会自动清理资源，避免内存泄漏。

## 🐛 常见问题

### Q: 运行时提示模块导入失败
A: 确保所有必要的文件都已创建，特别是core和services目录下的文件。

### Q: 异步测试失败
A: 这通常是正常的，因为没有真实的模型服务。异步接口会正确处理这种情况。

### Q: 缓存或事件测试失败
A: 检查是否有权限问题或端口冲突。

### Q: 想要集成到现有项目
A: 参考`example_usage.py`中的示例，使用工厂模式或构建器模式创建扩展实例。

## 📞 获取帮助

如果遇到问题：
1. 查看`README.md`获取详细文档
2. 检查`test_results.log`日志文件
3. 运行`python quick_start.py`验证基本功能
4. 确保所有依赖都已安装

## 🎯 下一步

成功运行演示后，你可以：
1. 根据需要修改配置
2. 添加自定义模型
3. 集成到OHIF查看器
4. 部署到生产环境

---

**祝你使用愉快！** 🎉