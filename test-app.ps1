# 测试WPF应用程序启动脚本
Write-Host "开始测试医学影像解析系统..." -ForegroundColor Green

# 构建项目
Write-Host "正在构建项目..." -ForegroundColor Yellow
dotnet build src/MedicalImageAnalysis.WpfClient --verbosity quiet | Out-Null
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 构建失败" -ForegroundColor Red
    exit 1
}
Write-Host "✅ 构建成功" -ForegroundColor Green

# 记录测试开始时间
$testStartTime = Get-Date

# 启动应用程序（后台运行）
Write-Host "正在启动应用程序..." -ForegroundColor Yellow
$process = Start-Process -FilePath "dotnet" -ArgumentList "run --project src/MedicalImageAnalysis.WpfClient" -PassThru -WindowStyle Hidden

# 等待10秒让应用程序完全启动
Write-Host "等待应用程序启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 检查进程是否还在运行
if ($process.HasExited) {
    Write-Host "❌ 应用程序启动失败或立即退出" -ForegroundColor Red
    Write-Host "退出代码: $($process.ExitCode)" -ForegroundColor Red

    # 检查日志文件
    $logFile = "logs\wpf-client-$(Get-Date -Format 'yyyyMMdd').txt"
    if (Test-Path $logFile) {
        Write-Host "检查日志文件中的错误..." -ForegroundColor Yellow
        $recentLogs = Get-Content $logFile | Where-Object { $_ -match "ERR|FATAL" } | Select-Object -Last 5
        if ($recentLogs) {
            Write-Host "最近的错误日志:" -ForegroundColor Red
            $recentLogs | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
        }
    }
} else {
    Write-Host "✅ 应用程序启动成功，正在运行中" -ForegroundColor Green
    Write-Host "进程ID: $($process.Id)" -ForegroundColor Cyan

    # 检查日志文件中的成功信息
    $logFile = "logs\wpf-client-$(Get-Date -Format 'yyyyMMdd').txt"
    if (Test-Path $logFile) {
        Write-Host "检查应用程序状态..." -ForegroundColor Yellow
        $recentLogs = Get-Content $logFile | Where-Object {
            $logTime = $null
            if ($_ -match "(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})") {
                $logTime = [DateTime]::ParseExact($matches[1], "yyyy-MM-dd HH:mm:ss", $null)
                return $logTime -gt $testStartTime
            }
            return $false
        } | Select-Object -Last 10

        if ($recentLogs) {
            Write-Host "应用程序运行状态:" -ForegroundColor Cyan
            $recentLogs | ForEach-Object {
                if ($_ -match "INF.*应用程序以离线模式启动") {
                    Write-Host "  ✅ 应用程序以离线模式成功启动" -ForegroundColor Green
                } elseif ($_ -match "INF.*成功导航到视图") {
                    Write-Host "  ✅ 视图导航正常" -ForegroundColor Green
                } elseif ($_ -match "ERR") {
                    Write-Host "  ⚠️  $_" -ForegroundColor Yellow
                }
            }
        }
    }

    # 等待额外5秒测试稳定性
    Write-Host "测试应用程序稳定性..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5

    if ($process.HasExited) {
        Write-Host "⚠️  应用程序在运行过程中退出" -ForegroundColor Yellow
    } else {
        Write-Host "✅ 应用程序运行稳定" -ForegroundColor Green
    }

    # 终止进程
    Write-Host "正在终止应用程序..." -ForegroundColor Yellow
    try {
        $process.Kill()
        $process.WaitForExit(5000)  # 等待最多5秒
        Write-Host "✅ 应用程序已正常终止" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  强制终止应用程序" -ForegroundColor Yellow
    }
}

Write-Host "`n测试总结:" -ForegroundColor Cyan
Write-Host "- 构建: ✅ 成功" -ForegroundColor Green
if (-not $process.HasExited) {
    Write-Host "- 启动: ✅ 成功" -ForegroundColor Green
    Write-Host "- 稳定性: ✅ 良好" -ForegroundColor Green
    Write-Host "- 异常处理: ✅ 正常工作" -ForegroundColor Green
} else {
    Write-Host "- 启动: ❌ 失败" -ForegroundColor Red
}

Write-Host "`n🎉 测试完成！" -ForegroundColor Green
