# 冈上肌肌腱撕裂YOLO检测 - 完整训练代码

## 概述

`train_supraspinatus_yolo_complete.py` 是一个从头开始的完整训练脚本，直接从原始的 nii.gz 医学图像和 mask 文件开始，完成整个数据处理和模型训练流程。

## 主要特点

### 🔄 完整的数据处理流程
- **从原始数据开始**: 直接处理 nii.gz 格式的医学图像和 mask 文件
- **自动切片提取**: 从3D体积数据中提取2D切片
- **智能预处理**: 图像标准化、尺寸调整、格式转换
- **边界框生成**: 从 mask 自动生成 YOLO 格式的边界框标注

### 📊 数据质量保证
- **连通组件分析**: 识别独立的撕裂区域
- **质量过滤**: 过滤过小或无效的边界框
- **数据验证**: 确保图像和标签的一致性
- **统计监控**: 实时跟踪处理统计信息

### 🎯 智能数据集划分
- **按体积分组**: 确保同一体积的切片在同一数据集中
- **比例控制**: 可配置的训练/验证/测试集比例
- **负样本处理**: 自动处理无标注的负样本

### 📈 可视化和监控
- **样本可视化**: 自动生成带边界框的样本图像
- **训练统计**: 详细的处理和训练统计信息
- **日志记录**: 完整的处理日志和错误跟踪

## 文件结构

```
数据集目录结构:
dataset/
├── image_T2/          # 撕裂图像 (130-395.nii.gz)
├── label_T2/          # 撕裂标签 (130-395.nii.gz)
└── image_T2_normal/   # 正常图像 (1-129.nii.gz)

输出目录结构:
complete_yolo_training_output/
├── processed_images/   # 处理后的图像切片
├── processed_labels/   # 生成的YOLO标签
├── yolo_dataset/      # YOLO训练数据集
│   ├── images/
│   │   ├── train/
│   │   ├── val/
│   │   └── test/
│   └── labels/
│       ├── train/
│       ├── val/
│       └── test/
├── models/            # 训练后的模型
├── logs/              # 训练日志和统计
└── visualizations/    # 可视化结果
```

## 使用方法

### 基本使用

```bash
# 使用默认参数训练
python train_supraspinatus_yolo_complete.py

# 指定数据集路径
python train_supraspinatus_yolo_complete.py --dataset_root ./dataset

# 快速测试（少量轮数）
python train_supraspinatus_yolo_complete.py --epochs 10 --batch_size 8
```

### 高级配置

```bash
# 完整配置示例
python train_supraspinatus_yolo_complete.py \
    --dataset_root ./dataset \
    --output_root ./my_training_output \
    --img_size 640 \
    --epochs 100 \
    --batch_size 16 \
    --model yolo11x.pt
```

### 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--dataset_root` | `./dataset` | 数据集根目录 |
| `--output_root` | `./complete_yolo_training_output` | 输出目录 |
| `--img_size` | `640` | 训练图像尺寸 |
| `--slice_thickness` | `1` | 切片厚度 |
| `--epochs` | `100` | 训练轮数 |
| `--batch_size` | `16` | 批次大小 |
| `--model` | `yolo11x.pt` | 预训练模型 |

## 测试和验证

### 运行测试脚本

```bash
# 测试数据处理功能
python test_complete_training.py
```

测试脚本会验证：
- 数据集目录是否存在
- nii.gz 文件是否能正常加载
- 切片提取是否正常
- 边界框生成是否正确

## 训练流程

### 7步完整流程

1. **处理撕裂体积数据**
   - 加载 nii.gz 图像和对应的 mask
   - 提取2D切片
   - 生成YOLO格式标注

2. **处理正常体积数据**
   - 加载正常图像的 nii.gz 文件
   - 提取2D切片作为负样本

3. **合并数据**
   - 合并正样本和负样本
   - 统计数据分布

4. **划分数据集**
   - 按体积ID分组
   - 划分训练/验证/测试集

5. **创建YOLO配置**
   - 生成 dataset_config.yaml
   - 配置类别和路径

6. **生成可视化**
   - 创建样本可视化图像
   - 验证边界框正确性

7. **训练模型**
   - 使用YOLO11进行训练
   - 保存最佳模型

## 输出文件

### 模型文件
- `best.pt`: 验证集上表现最佳的模型
- `last.pt`: 最后一轮训练的模型

### 统计文件
- `training_stats.json`: 详细的训练统计信息
- `dataset_config.yaml`: YOLO数据集配置文件

### 可视化文件
- `sample_visualization.png`: 样本可视化图像

## 性能优化建议

### 硬件要求
- **GPU**: 推荐使用CUDA兼容的GPU
- **内存**: 至少16GB RAM
- **存储**: 足够的磁盘空间存储处理后的数据

### 参数调优
- **batch_size**: 根据GPU内存调整（8-32）
- **img_size**: 可选择416, 640, 832等
- **epochs**: 根据数据量调整（50-200）

## 故障排除

### 常见问题

1. **内存不足**
   ```bash
   # 减小批次大小
   python train_supraspinatus_yolo_complete.py --batch_size 8
   ```

2. **数据集路径错误**
   ```bash
   # 检查数据集结构
   python test_complete_training.py
   ```

3. **CUDA错误**
   ```bash
   # 使用CPU训练
   export CUDA_VISIBLE_DEVICES=""
   python train_supraspinatus_yolo_complete.py
   ```

### 日志检查

训练过程中的详细日志会保存在 `logs/` 目录中，包括：
- 数据处理统计
- 训练进度
- 错误信息

## 与原版本的区别

| 特性 | 原版本 | 完整版本 |
|------|--------|----------|
| 数据源 | 预处理的切片 | 原始nii.gz文件 |
| 处理流程 | 部分自动化 | 完全自动化 |
| 数据质量控制 | 基础过滤 | 高级质量控制 |
| 可视化 | 有限 | 完整可视化 |
| 统计信息 | 基础统计 | 详细统计 |
| 错误处理 | 基础 | 完善的错误处理 |

## 最佳实践

1. **数据准备**
   - 确保nii.gz文件完整性
   - 验证图像和mask的对应关系

2. **训练监控**
   - 定期检查训练日志
   - 监控验证集性能

3. **结果验证**
   - 使用测试集评估模型
   - 检查可视化结果

4. **模型部署**
   - 使用best.pt进行推理
   - 保存训练配置用于复现

## 技术支持

如果遇到问题，请：
1. 首先运行测试脚本诊断问题
2. 检查日志文件中的错误信息
3. 确认数据集结构和文件完整性
4. 根据硬件配置调整训练参数

---

**注意**: 这个完整版本的训练代码提供了从原始医学图像到训练好的YOLO模型的端到端解决方案，适合需要完全控制数据处理流程的场景。