"""AI预测扩展核心模块

提供核心业务逻辑和基础设施组件
"""

from .interfaces import (
    ModelManagerInterface,
    PredictionServiceInterface,
    UIComponentsInterface,
    ConfigValidatorInterface
)

from .exceptions import (
    ExtensionError,
    ModelNotFoundError,
    PredictionError,
    ConfigurationError,
    ValidationError
)

from .types import (
    ModelConfig,
    PredictionResult,
    DetectionResult,
    ExtensionConfig
)

__all__ = [
    'ModelManagerInterface',
    'PredictionServiceInterface', 
    'UIComponentsInterface',
    'ConfigValidatorInterface',
    'ExtensionError',
    'ModelNotFoundError',
    'PredictionError',
    'ConfigurationError',
    'ValidationError',
    'ModelConfig',
    'PredictionResult',
    'DetectionResult',
    'ExtensionConfig'
]