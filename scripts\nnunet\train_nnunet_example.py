#!/usr/bin/env python3
"""
nnUNet训练示例脚本
用于演示如何使用nnUNet进行医学图像分割训练
"""

import os
import sys
import argparse
import subprocess
import json
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NnUNetTrainer:
    """nnUNet训练器"""
    
    def __init__(self, dataset_id, dataset_name, base_dir="./nnunet_data"):
        """
        初始化训练器
        
        Args:
            dataset_id: 数据集ID
            dataset_name: 数据集名称
            base_dir: 基础目录
        """
        self.dataset_id = dataset_id
        self.dataset_name = dataset_name
        self.base_dir = Path(base_dir)
        
        # 设置nnUNet环境路径
        self.nnunet_raw = self.base_dir / "nnUNet_raw"
        self.nnunet_preprocessed = self.base_dir / "nnUNet_preprocessed"
        self.nnunet_results = self.base_dir / "nnUNet_results"
        
        # 创建目录
        self.nnunet_raw.mkdir(parents=True, exist_ok=True)
        self.nnunet_preprocessed.mkdir(parents=True, exist_ok=True)
        self.nnunet_results.mkdir(parents=True, exist_ok=True)
        
        # 设置环境变量
        os.environ['nnUNet_raw'] = str(self.nnunet_raw)
        os.environ['nnUNet_preprocessed'] = str(self.nnunet_preprocessed)
        os.environ['nnUNet_results'] = str(self.nnunet_results)
        
        logger.info(f"nnUNet环境已设置:")
        logger.info(f"  nnUNet_raw: {self.nnunet_raw}")
        logger.info(f"  nnUNet_preprocessed: {self.nnunet_preprocessed}")
        logger.info(f"  nnUNet_results: {self.nnunet_results}")
    
    def check_environment(self):
        """检查nnUNet环境"""
        logger.info("检查nnUNet环境...")
        
        try:
            # 检查Python包
            import torch
            import nnunetv2
            logger.info(f"✓ PyTorch版本: {torch.__version__}")
            logger.info(f"✓ nnUNetv2已安装")
            
            # 检查CUDA
            if torch.cuda.is_available():
                logger.info(f"✓ CUDA可用，设备数量: {torch.cuda.device_count()}")
            else:
                logger.warning("⚠ CUDA不可用，将使用CPU训练")
            
            return True
        except ImportError as e:
            logger.error(f"✗ 缺少必需的包: {e}")
            return False
    
    def create_dataset_json(self, modalities, labels, description="Medical segmentation dataset"):
        """
        创建数据集配置文件
        
        Args:
            modalities: 模态字典，例如 {"0": "CT"}
            labels: 标签字典，例如 {"0": "background", "1": "tumor"}
            description: 数据集描述
        """
        dataset_dir = self.nnunet_raw / f"Dataset{self.dataset_id:03d}_{self.dataset_name}"
        dataset_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        (dataset_dir / "imagesTr").mkdir(exist_ok=True)
        (dataset_dir / "labelsTr").mkdir(exist_ok=True)
        (dataset_dir / "imagesTs").mkdir(exist_ok=True)
        
        # 创建dataset.json
        dataset_config = {
            "channel_names": modalities,
            "labels": labels,
            "numTraining": 0,  # 将在添加数据时更新
            "numTest": 0,
            "file_ending": ".nii.gz",
            "dataset_name": self.dataset_name,
            "description": description,
            "reference": "Medical Image Analysis System",
            "licence": "Research Use Only",
            "release": "1.0",
            "tensorImageSize": "4D",
            "training": [],
            "test": []
        }
        
        config_path = dataset_dir / "dataset.json"
        with open(config_path, 'w') as f:
            json.dump(dataset_config, f, indent=2)
        
        logger.info(f"数据集配置文件已创建: {config_path}")
        return dataset_dir
    
    def preprocess_dataset(self, verify_integrity=True, num_threads=8):
        """
        预处理数据集
        
        Args:
            verify_integrity: 是否验证数据集完整性
            num_threads: 线程数
        """
        logger.info("开始预处理数据集...")
        
        cmd = [
            "nnUNetv2_plan_and_preprocess",
            "-d", str(self.dataset_id),
            "-np", str(num_threads)
        ]
        
        if verify_integrity:
            cmd.append("--verify_dataset_integrity")
        else:
            cmd.append("--no_verify_dataset_integrity")
        
        try:
            logger.info(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            logger.info("预处理完成")
            logger.info(result.stdout)
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"预处理失败: {e}")
            logger.error(e.stderr)
            return False
    
    def train_model(self, architecture="3d_fullres", fold=0, trainer="nnUNetTrainer", 
                   continue_training=False, epochs=None):
        """
        训练模型
        
        Args:
            architecture: 网络架构 (2d, 3d_lowres, 3d_fullres, 3d_cascade_fullres)
            fold: 交叉验证折数
            trainer: 训练器类型
            continue_training: 是否继续训练
            epochs: 训练轮数（可选）
        """
        logger.info(f"开始训练模型 - 架构: {architecture}, 折数: {fold}")
        
        cmd = [
            "nnUNetv2_train",
            str(self.dataset_id),
            architecture,
            str(fold),
            "--tr", trainer
        ]
        
        if continue_training:
            cmd.append("--c")
        
        if epochs:
            cmd.extend(["--npz", str(epochs)])
        
        try:
            logger.info(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            logger.info("训练完成")
            logger.info(result.stdout)
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"训练失败: {e}")
            logger.error(e.stderr)
            return False
    
    def predict(self, input_folder, output_folder, architecture="3d_fullres", 
               fold=0, save_probabilities=False, disable_tta=False):
        """
        使用训练好的模型进行预测
        
        Args:
            input_folder: 输入文件夹
            output_folder: 输出文件夹
            architecture: 网络架构
            fold: 折数
            save_probabilities: 是否保存概率图
            disable_tta: 是否禁用测试时增强
        """
        logger.info(f"开始预测 - 输入: {input_folder}, 输出: {output_folder}")
        
        cmd = [
            "nnUNetv2_predict",
            "-i", str(input_folder),
            "-o", str(output_folder),
            "-d", str(self.dataset_id),
            "-c", architecture,
            "-f", str(fold)
        ]
        
        if save_probabilities:
            cmd.append("--save_probabilities")
        
        if disable_tta:
            cmd.append("--disable_tta")
        
        try:
            logger.info(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            logger.info("预测完成")
            logger.info(result.stdout)
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"预测失败: {e}")
            logger.error(e.stderr)
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="nnUNet训练示例脚本")
    parser.add_argument("--dataset_id", type=int, default=1, help="数据集ID")
    parser.add_argument("--dataset_name", default="MedicalDemo", help="数据集名称")
    parser.add_argument("--base_dir", default="./nnunet_data", help="基础目录")
    parser.add_argument("--architecture", default="3d_fullres", 
                       choices=["2d", "3d_lowres", "3d_fullres", "3d_cascade_fullres"],
                       help="网络架构")
    parser.add_argument("--fold", type=int, default=0, help="交叉验证折数")
    parser.add_argument("--epochs", type=int, help="训练轮数")
    parser.add_argument("--skip_preprocessing", action="store_true", help="跳过预处理")
    parser.add_argument("--only_preprocess", action="store_true", help="仅执行预处理")
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = NnUNetTrainer(args.dataset_id, args.dataset_name, args.base_dir)
    
    # 检查环境
    if not trainer.check_environment():
        logger.error("环境检查失败，请安装必需的依赖")
        return 1
    
    # 创建示例数据集配置
    modalities = {"0": "CT"}
    labels = {"0": "background", "1": "tumor"}
    dataset_dir = trainer.create_dataset_json(modalities, labels)
    
    logger.info(f"数据集目录: {dataset_dir}")
    logger.info("请将训练图像放入 imagesTr/ 目录")
    logger.info("请将训练标签放入 labelsTr/ 目录")
    logger.info("请将测试图像放入 imagesTs/ 目录")
    
    # 预处理
    if not args.skip_preprocessing:
        if not trainer.preprocess_dataset():
            logger.error("预处理失败")
            return 1
    
    if args.only_preprocess:
        logger.info("仅执行预处理，训练已跳过")
        return 0
    
    # 训练
    if not trainer.train_model(args.architecture, args.fold, epochs=args.epochs):
        logger.error("训练失败")
        return 1
    
    logger.info("nnUNet训练流程完成！")
    return 0

if __name__ == "__main__":
    sys.exit(main())
