import time
import logging
import threading
import psutil
from functools import wraps
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable
import json

logger = logging.getLogger(__name__)

class PerformanceMetrics:
    """性能指标收集器"""
    
    def __init__(self, max_history=1000):
        self.max_history = max_history
        self.metrics = defaultdict(lambda: deque(maxlen=max_history))
        self.counters = defaultdict(int)
        self.lock = threading.Lock()
    
    def record_timing(self, operation: str, duration: float):
        """记录操作耗时"""
        with self.lock:
            self.metrics[f"{operation}_duration"].append({
                'timestamp': datetime.utcnow().isoformat(),
                'duration': duration
            })
            self.counters[f"{operation}_count"] += 1
    
    def record_counter(self, metric: str, value: int = 1):
        """记录计数器"""
        with self.lock:
            self.counters[metric] += value
    
    def record_gauge(self, metric: str, value: float):
        """记录瞬时值"""
        with self.lock:
            self.metrics[metric].append({
                'timestamp': datetime.utcnow().isoformat(),
                'value': value
            })
    
    def get_stats(self, operation: str) -> Dict[str, Any]:
        """获取操作统计信息"""
        with self.lock:
            durations = [m['duration'] for m in self.metrics[f"{operation}_duration"]]
            
            if not durations:
                return {'count': 0}
            
            return {
                'count': len(durations),
                'avg_duration': sum(durations) / len(durations),
                'min_duration': min(durations),
                'max_duration': max(durations),
                'total_count': self.counters[f"{operation}_count"]
            }
    
    def get_all_metrics(self) -> Dict[str, Any]:
        """获取所有指标"""
        with self.lock:
            return {
                'counters': dict(self.counters),
                'metrics': {k: list(v) for k, v in self.metrics.items()}
            }

class SystemMonitor:
    """系统资源监控器"""
    
    def __init__(self, metrics: PerformanceMetrics):
        self.metrics = metrics
        self.monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self, interval: int = 30):
        """开始系统监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info(f"系统监控已启动，间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止系统监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("系统监控已停止")
    
    def _monitor_loop(self, interval: int):
        """监控循环"""
        while self.monitoring:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                self.metrics.record_gauge('system_cpu_percent', cpu_percent)
                
                # 内存使用
                memory = psutil.virtual_memory()
                self.metrics.record_gauge('system_memory_percent', memory.percent)
                self.metrics.record_gauge('system_memory_used_gb', memory.used / (1024**3))
                
                # 磁盘使用
                disk = psutil.disk_usage('/')
                self.metrics.record_gauge('system_disk_percent', disk.percent)
                
                # 网络IO
                net_io = psutil.net_io_counters()
                self.metrics.record_gauge('system_net_bytes_sent', net_io.bytes_sent)
                self.metrics.record_gauge('system_net_bytes_recv', net_io.bytes_recv)
                
                time.sleep(interval)
            except Exception as e:
                logger.error(f"系统监控错误: {str(e)}")
                time.sleep(interval)

class SimpleCache:
    """简单内存缓存"""
    
    def __init__(self, default_ttl: int = 300, max_size: int = 1000):
        self.cache = {}
        self.timestamps = {}
        self.default_ttl = default_ttl
        self.max_size = max_size
        self.lock = threading.Lock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            if key not in self.cache:
                return None
            
            # 检查是否过期
            if self._is_expired(key):
                self._remove(key)
                return None
            
            return self.cache[key]
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        with self.lock:
            # 检查缓存大小限制
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_oldest()
            
            self.cache[key] = value
            self.timestamps[key] = {
                'created': time.time(),
                'ttl': ttl or self.default_ttl
            }
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        with self.lock:
            if key in self.cache:
                self._remove(key)
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.timestamps.clear()
    
    def _is_expired(self, key: str) -> bool:
        """检查是否过期"""
        if key not in self.timestamps:
            return True
        
        timestamp_info = self.timestamps[key]
        return time.time() - timestamp_info['created'] > timestamp_info['ttl']
    
    def _remove(self, key: str) -> None:
        """移除缓存项"""
        self.cache.pop(key, None)
        self.timestamps.pop(key, None)
    
    def _evict_oldest(self) -> None:
        """移除最旧的缓存项"""
        if not self.timestamps:
            return
        
        oldest_key = min(self.timestamps.keys(), 
                        key=lambda k: self.timestamps[k]['created'])
        self._remove(oldest_key)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hit_rate': getattr(self, '_hit_count', 0) / max(getattr(self, '_access_count', 1), 1)
            }

# 全局实例
performance_metrics = PerformanceMetrics()
system_monitor = SystemMonitor(performance_metrics)
simple_cache = SimpleCache()

def timing_decorator(operation_name: str):
    """性能计时装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                performance_metrics.record_counter(f"{operation_name}_success")
                return result
            except Exception as e:
                performance_metrics.record_counter(f"{operation_name}_error")
                raise
            finally:
                duration = time.time() - start_time
                performance_metrics.record_timing(operation_name, duration)
        return wrapper
    return decorator

def cache_decorator(key_func: Callable = None, ttl: int = 300):
    """缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = simple_cache.get(cache_key)
            if cached_result is not None:
                performance_metrics.record_counter('cache_hit')
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            simple_cache.set(cache_key, result, ttl)
            performance_metrics.record_counter('cache_miss')
            
            return result
        return wrapper
    return decorator

def get_performance_report() -> Dict[str, Any]:
    """获取性能报告"""
    return {
        'timestamp': datetime.utcnow().isoformat(),
        'metrics': performance_metrics.get_all_metrics(),
        'cache_stats': simple_cache.get_stats()
    }

def start_monitoring():
    """启动性能监控"""
    system_monitor.start_monitoring()

def stop_monitoring():
    """停止性能监控"""
    system_monitor.stop_monitoring()