namespace MedicalImageAnalysis.Core.Models;

/// <summary>
/// DICOM元数据
/// </summary>
public class DicomMetadata
{
    // 患者信息
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }

    /// <summary>
    /// 患者ID
    /// </summary>
    public string? PatientId { get; set; }

    /// <summary>
    /// 患者性别
    /// </summary>
    public string? PatientSex { get; set; }

    /// <summary>
    /// 患者出生日期
    /// </summary>
    public DateTime? PatientBirthDate { get; set; }

    /// <summary>
    /// 患者年龄
    /// </summary>
    public string? PatientAge { get; set; }

    /// <summary>
    /// 患者体重
    /// </summary>
    public double? PatientWeight { get; set; }

    // 研究信息
    /// <summary>
    /// 研究实例UID
    /// </summary>
    public string? StudyInstanceUid { get; set; }

    /// <summary>
    /// 研究日期
    /// </summary>
    public DateTime? StudyDate { get; set; }

    /// <summary>
    /// 研究时间
    /// </summary>
    public TimeSpan? StudyTime { get; set; }

    /// <summary>
    /// 研究描述
    /// </summary>
    public string? StudyDescription { get; set; }

    /// <summary>
    /// 研究ID
    /// </summary>
    public string? StudyId { get; set; }

    /// <summary>
    /// 入院号
    /// </summary>
    public string? AccessionNumber { get; set; }

    // 序列信息
    /// <summary>
    /// 序列实例UID
    /// </summary>
    public string? SeriesInstanceUid { get; set; }

    /// <summary>
    /// 序列号
    /// </summary>
    public int? SeriesNumber { get; set; }

    /// <summary>
    /// 序列描述
    /// </summary>
    public string? SeriesDescription { get; set; }

    /// <summary>
    /// 模态
    /// </summary>
    public string? Modality { get; set; }

    /// <summary>
    /// 序列日期
    /// </summary>
    public DateTime? SeriesDate { get; set; }

    /// <summary>
    /// 序列时间
    /// </summary>
    public TimeSpan? SeriesTime { get; set; }

    // 实例信息
    /// <summary>
    /// SOP实例UID
    /// </summary>
    public string? SopInstanceUid { get; set; }

    /// <summary>
    /// SOP类UID
    /// </summary>
    public string? SopClassUid { get; set; }

    /// <summary>
    /// 实例号
    /// </summary>
    public int? InstanceNumber { get; set; }

    /// <summary>
    /// 内容日期
    /// </summary>
    public DateTime? ContentDate { get; set; }

    /// <summary>
    /// 内容时间
    /// </summary>
    public TimeSpan? ContentTime { get; set; }

    // 图像信息
    /// <summary>
    /// 行数
    /// </summary>
    public int? Rows { get; set; }

    /// <summary>
    /// 列数
    /// </summary>
    public int? Columns { get; set; }

    /// <summary>
    /// 像素间距 (行间距, 列间距)
    /// </summary>
    public (double Row, double Column)? PixelSpacing { get; set; }

    /// <summary>
    /// 层厚
    /// </summary>
    public double? SliceThickness { get; set; }

    /// <summary>
    /// 层间距
    /// </summary>
    public double? SpacingBetweenSlices { get; set; }

    /// <summary>
    /// 层位置
    /// </summary>
    public double? SliceLocation { get; set; }

    /// <summary>
    /// 图像位置 (x, y, z)
    /// </summary>
    public (double X, double Y, double Z)? ImagePosition { get; set; }

    /// <summary>
    /// 图像方向患者
    /// </summary>
    public double[]? ImageOrientationPatient { get; set; }

    /// <summary>
    /// 窗宽
    /// </summary>
    public double? WindowWidth { get; set; }

    /// <summary>
    /// 窗位
    /// </summary>
    public double? WindowCenter { get; set; }

    /// <summary>
    /// 重缩放斜率
    /// </summary>
    public double? RescaleSlope { get; set; }

    /// <summary>
    /// 重缩放截距
    /// </summary>
    public double? RescaleIntercept { get; set; }

    // 设备信息
    /// <summary>
    /// 制造商
    /// </summary>
    public string? Manufacturer { get; set; }

    /// <summary>
    /// 制造商型号名称
    /// </summary>
    public string? ManufacturerModelName { get; set; }

    /// <summary>
    /// 设备序列号
    /// </summary>
    public string? DeviceSerialNumber { get; set; }

    /// <summary>
    /// 软件版本
    /// </summary>
    public string? SoftwareVersions { get; set; }

    /// <summary>
    /// 机构名称
    /// </summary>
    public string? InstitutionName { get; set; }

    /// <summary>
    /// 机构地址
    /// </summary>
    public string? InstitutionAddress { get; set; }

    // 技术参数
    /// <summary>
    /// KVP
    /// </summary>
    public double? Kvp { get; set; }

    /// <summary>
    /// 管电流
    /// </summary>
    public double? XRayTubeCurrent { get; set; }

    /// <summary>
    /// 曝光时间
    /// </summary>
    public double? ExposureTime { get; set; }

    /// <summary>
    /// 曝光量
    /// </summary>
    public double? Exposure { get; set; }

    /// <summary>
    /// 滤波器类型
    /// </summary>
    public string? FilterType { get; set; }

    /// <summary>
    /// 对比剂/造影剂
    /// </summary>
    public string? ContrastBolusAgent { get; set; }

    /// <summary>
    /// 扫描选项
    /// </summary>
    public string? ScanOptions { get; set; }

    /// <summary>
    /// 数据收集直径
    /// </summary>
    public double? DataCollectionDiameter { get; set; }

    /// <summary>
    /// 重建直径
    /// </summary>
    public double? ReconstructionDiameter { get; set; }

    /// <summary>
    /// 距离源到探测器
    /// </summary>
    public double? DistanceSourceToDetector { get; set; }

    /// <summary>
    /// 距离源到患者
    /// </summary>
    public double? DistanceSourceToPatient { get; set; }

    /// <summary>
    /// 估计放射剂量
    /// </summary>
    public double? EstimatedRadiographicMagnificationFactor { get; set; }

    /// <summary>
    /// 图像类型
    /// </summary>
    public string[]? ImageType { get; set; }

    /// <summary>
    /// 获取患者年龄（计算值）
    /// </summary>
    public int? CalculatedPatientAge
    {
        get
        {
            if (PatientBirthDate.HasValue && StudyDate.HasValue)
            {
                var age = StudyDate.Value.Year - PatientBirthDate.Value.Year;
                if (StudyDate.Value.DayOfYear < PatientBirthDate.Value.DayOfYear)
                    age--;
                return age;
            }
            return null;
        }
    }

    /// <summary>
    /// 获取体素大小
    /// </summary>
    public (double X, double Y, double Z)? VoxelSize
    {
        get
        {
            if (PixelSpacing.HasValue && SliceThickness.HasValue)
            {
                return (PixelSpacing.Value.Column, PixelSpacing.Value.Row, SliceThickness.Value);
            }
            return null;
        }
    }

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
