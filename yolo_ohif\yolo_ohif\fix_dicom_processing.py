#!/usr/bin/env python3
"""
修复DICOM处理中的图像归一化方法
使其与训练数据处理保持一致
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def backup_original_file():
    """
    备份原始的detection_service.py文件
    """
    original_file = "E:/Trae/yolo_ohif/src/services/detection_service.py"
    backup_file = "E:/Trae/yolo_ohif/src/services/detection_service.py.backup"
    
    if os.path.exists(original_file) and not os.path.exists(backup_file):
        import shutil
        shutil.copy2(original_file, backup_file)
        print(f"✅ 已备份原始文件: {backup_file}")
        return True
    elif os.path.exists(backup_file):
        print(f"ℹ️  备份文件已存在: {backup_file}")
        return True
    else:
        print(f"❌ 原始文件不存在: {original_file}")
        return False

def create_fixed_normalize_method():
    """
    创建修复后的归一化方法
    """
    fixed_method = '''
    def _normalize_to_uint8(self, img_array):
        """将图像数组归一化到uint8格式
        使用与训练数据相同的归一化方法
        
        Args:
            img_array: 输入图像数组
            
        Returns:
            归一化后的uint8图像数组
        """
        # 使用与训练时相同的归一化方法（简单的min-max归一化）
        # 移除百分位数窗口化，因为训练数据没有使用这种方法
        
        # 转换为float32进行计算
        img_float = img_array.astype(np.float32)
        
        # 简单的min-max归一化到0-255范围
        if img_float.max() > img_float.min():
            img_normalized = ((img_float - img_float.min()) / 
                            (img_float.max() - img_float.min()) * 255).astype(np.uint8)
        else:
            # 如果图像是常数，返回全零图像
            img_normalized = np.zeros_like(img_float, dtype=np.uint8)
        
        return img_normalized
'''
    return fixed_method

def create_fixed_read_dicom_method():
    """
    创建修复后的DICOM读取方法
    """
    fixed_method = '''
    def _read_dicom(self, dicom_path):
        """读取DICOM文件并转换为图像数组
        使用与训练数据相同的处理方法
        
        Args:
            dicom_path: DICOM文件路径
            
        Returns:
            图像数组
        """
        try:
            # 读取DICOM文件
            ds = pydicom.dcmread(dicom_path)
            
            # 获取像素数据
            pixel_array = ds.pixel_array
            
            # 处理不同的图像格式
            if hasattr(ds, 'PhotometricInterpretation'):
                # 根据光度解释进行处理
                if ds.PhotometricInterpretation == "MONOCHROME1":
                    # 反转灰度
                    pixel_array = np.max(pixel_array) - pixel_array
            
            # 使用与训练数据相同的归一化方法
            if pixel_array.dtype != np.uint8:
                pixel_array = self._normalize_to_uint8(pixel_array)
            
            # 如果是灰度图像，转换为RGB（与训练数据处理保持一致）
            if len(pixel_array.shape) == 2:
                pixel_array = cv2.cvtColor(pixel_array, cv2.COLOR_GRAY2RGB)
            elif len(pixel_array.shape) == 3 and pixel_array.shape[2] == 1:
                pixel_array = cv2.cvtColor(pixel_array, cv2.COLOR_GRAY2RGB)
            
            return pixel_array
        except Exception as e:
            logger.error(f"读取DICOM文件 {dicom_path} 时出错: {str(e)}")
            return None
'''
    return fixed_method

def apply_fix():
    """
    应用修复
    """
    print("开始修复DICOM处理中的图像归一化方法...")
    print("="*80)
    
    # 1. 备份原始文件
    if not backup_original_file():
        return False
    
    # 2. 读取原始文件
    detection_service_file = "E:/Trae/yolo_ohif/src/services/detection_service.py"
    
    try:
        with open(detection_service_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 读取原始文件失败: {e}")
        return False
    
    # 3. 查找需要替换的方法
    normalize_start = content.find('def _normalize_to_uint8(self, img_array):')
    read_dicom_start = content.find('def _read_dicom(self, dicom_path):')
    
    if normalize_start == -1:
        print("❌ 未找到_normalize_to_uint8方法")
        return False
    
    if read_dicom_start == -1:
        print("❌ 未找到_read_dicom方法")
        return False
    
    # 4. 找到方法的结束位置
    def find_method_end(content, start_pos):
        lines = content[start_pos:].split('\n')
        indent_level = None
        method_lines = []
        
        for i, line in enumerate(lines):
            if i == 0:  # 第一行是方法定义
                method_lines.append(line)
                continue
            
            # 确定缩进级别
            if indent_level is None and line.strip():
                indent_level = len(line) - len(line.lstrip())
            
            # 如果遇到同级别或更低级别的缩进，且不是空行，则方法结束
            if line.strip() and indent_level is not None:
                current_indent = len(line) - len(line.lstrip())
                if current_indent <= indent_level - 4:  # 方法结束
                    break
            
            method_lines.append(line)
        
        return '\n'.join(method_lines)
    
    # 5. 提取原始方法
    original_normalize = find_method_end(content, normalize_start)
    original_read_dicom = find_method_end(content, read_dicom_start)
    
    print(f"✅ 找到_normalize_to_uint8方法 (长度: {len(original_normalize)} 字符)")
    print(f"✅ 找到_read_dicom方法 (长度: {len(original_read_dicom)} 字符)")
    
    # 6. 替换方法
    fixed_normalize = create_fixed_normalize_method().strip()
    fixed_read_dicom = create_fixed_read_dicom_method().strip()
    
    # 替换内容
    new_content = content.replace(original_normalize, fixed_normalize)
    new_content = new_content.replace(original_read_dicom, fixed_read_dicom)
    
    # 7. 写入修复后的文件
    try:
        with open(detection_service_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print("✅ 修复完成！")
    except Exception as e:
        print(f"❌ 写入修复文件失败: {e}")
        return False
    
    # 8. 验证修复
    print("\n🔍 验证修复结果...")
    
    try:
        with open(detection_service_file, 'r', encoding='utf-8') as f:
            new_file_content = f.read()
        
        if "简单的min-max归一化" in new_file_content:
            print("✅ _normalize_to_uint8方法已成功修复")
        else:
            print("⚠️  _normalize_to_uint8方法修复可能不完整")
        
        if "使用与训练数据相同的处理方法" in new_file_content:
            print("✅ _read_dicom方法已成功修复")
        else:
            print("⚠️  _read_dicom方法修复可能不完整")
            
    except Exception as e:
        print(f"❌ 验证修复失败: {e}")
        return False
    
    print("\n📋 修复总结:")
    print("1. ✅ 移除了DICOM处理中的百分位数窗口化")
    print("2. ✅ 使用与训练数据相同的min-max归一化方法")
    print("3. ✅ 保持了RGB转换的一致性")
    print("4. ✅ 备份了原始文件以便恢复")
    
    print("\n🔄 下一步操作:")
    print("1. 重启应用: python app.py")
    print("2. 测试DICOM检测功能")
    print("3. 如果问题仍然存在，可以恢复备份文件")
    
    return True

def restore_backup():
    """
    恢复备份文件
    """
    original_file = "E:/Trae/yolo_ohif/src/services/detection_service.py"
    backup_file = "E:/Trae/yolo_ohif/src/services/detection_service.py.backup"
    
    if os.path.exists(backup_file):
        import shutil
        shutil.copy2(backup_file, original_file)
        print(f"✅ 已恢复备份文件: {original_file}")
        return True
    else:
        print(f"❌ 备份文件不存在: {backup_file}")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='修复DICOM处理中的图像归一化方法')
    parser.add_argument('--restore', action='store_true', help='恢复备份文件')
    
    args = parser.parse_args()
    
    if args.restore:
        restore_backup()
    else:
        apply_fix()