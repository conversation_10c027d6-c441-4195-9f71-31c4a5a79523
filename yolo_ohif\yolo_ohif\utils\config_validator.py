import os
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class ConfigValidationResult:
    """配置验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    
class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
    
    def validate_config(self, config) -> ConfigValidationResult:
        """验证完整配置"""
        self.errors = []
        self.warnings = []
        
        # 验证Flask配置
        self._validate_flask_config(config.FLASK)
        
        # 验证Orthanc配置
        self._validate_orthanc_config(config.ORTHANC)
        
        # 验证YOLO配置
        self._validate_yolo_config(config.YOLO)
        
        # 验证日志配置
        self._validate_log_config(config.LOG)
        
        # 验证目录结构
        self._validate_directories(config)
        
        return ConfigValidationResult(
            is_valid=len(self.errors) == 0,
            errors=self.errors,
            warnings=self.warnings
        )
    
    def _validate_flask_config(self, flask_config):
        """验证Flask配置"""
        # 检查密钥安全性
        if hasattr(flask_config, 'SECRET_KEY'):
            if flask_config.SECRET_KEY == 'dev_key_please_change_in_production':
                self.warnings.append("使用默认SECRET_KEY，生产环境中应更改")
            elif len(flask_config.SECRET_KEY) < 32:
                self.warnings.append("SECRET_KEY长度过短，建议至少32个字符")
        else:
            self.errors.append("缺少SECRET_KEY配置")
        
        # 检查上传文件夹
        if hasattr(flask_config, 'UPLOAD_FOLDER'):
            if not os.path.exists(flask_config.UPLOAD_FOLDER):
                self.warnings.append(f"上传文件夹不存在: {flask_config.UPLOAD_FOLDER}")
        
        # 检查文件大小限制
        if hasattr(flask_config, 'MAX_CONTENT_LENGTH'):
            max_size_mb = flask_config.MAX_CONTENT_LENGTH / (1024 * 1024)
            if max_size_mb > 500:
                self.warnings.append(f"文件上传限制过大: {max_size_mb}MB")
    
    def _validate_orthanc_config(self, orthanc_config):
        """验证Orthanc配置"""
        # 检查URL格式
        if hasattr(orthanc_config, 'ORTHANC_URL'):
            url = orthanc_config.ORTHANC_URL
            if not url.startswith(('http://', 'https://')):
                self.errors.append(f"Orthanc URL格式无效: {url}")
        else:
            self.errors.append("缺少ORTHANC_URL配置")
        
        # 检查认证信息
        if hasattr(orthanc_config, 'ORTHANC_USERNAME') and hasattr(orthanc_config, 'ORTHANC_PASSWORD'):
            if orthanc_config.ORTHANC_USERNAME == 'admin' and orthanc_config.ORTHANC_PASSWORD == 'password':
                self.warnings.append("使用默认Orthanc认证信息，生产环境中应更改")
    
    def _validate_yolo_config(self, yolo_config):
        """验证YOLO配置"""
        # 检查模型路径
        if hasattr(yolo_config, 'MODEL_PATH'):
            if not os.path.exists(yolo_config.MODEL_PATH):
                self.warnings.append(f"YOLO模型路径不存在: {yolo_config.MODEL_PATH}")
        
        # 检查默认模型
        if hasattr(yolo_config, 'DEFAULT_MODEL') and hasattr(yolo_config, 'MODEL_PATH'):
            model_file = os.path.join(yolo_config.MODEL_PATH, yolo_config.DEFAULT_MODEL)
            if not os.path.exists(model_file):
                self.warnings.append(f"默认YOLO模型文件不存在: {model_file}")
        
        # 检查阈值范围
        if hasattr(yolo_config, 'CONFIDENCE_THRESHOLD'):
            threshold = yolo_config.CONFIDENCE_THRESHOLD
            if not 0.0 <= threshold <= 1.0:
                self.errors.append(f"置信度阈值超出范围[0,1]: {threshold}")
        
        if hasattr(yolo_config, 'IOU_THRESHOLD'):
            threshold = yolo_config.IOU_THRESHOLD
            if not 0.0 <= threshold <= 1.0:
                self.errors.append(f"IOU阈值超出范围[0,1]: {threshold}")
    
    def _validate_log_config(self, log_config):
        """验证日志配置"""
        # 检查日志级别
        if hasattr(log_config, 'LOG_LEVEL'):
            valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            if log_config.LOG_LEVEL not in valid_levels:
                self.errors.append(f"无效的日志级别: {log_config.LOG_LEVEL}")
        
        # 检查日志目录
        if hasattr(log_config, 'LOG_DIR'):
            if not os.path.exists(log_config.LOG_DIR):
                self.warnings.append(f"日志目录不存在: {log_config.LOG_DIR}")
    
    def _validate_directories(self, config):
        """验证目录结构"""
        required_dirs = [
            ('uploads', getattr(config.FLASK, 'UPLOAD_FOLDER', None)),
            ('models', getattr(config.YOLO, 'MODEL_PATH', None)),
            ('logs', getattr(config.LOG, 'LOG_DIR', None)),
        ]
        
        for dir_name, dir_path in required_dirs:
            if dir_path and not os.path.exists(dir_path):
                self.warnings.append(f"{dir_name}目录不存在: {dir_path}")

class SecurityChecker:
    """安全性检查器"""
    
    @staticmethod
    def check_file_permissions(file_path: str) -> List[str]:
        """检查文件权限"""
        warnings = []
        
        if os.path.exists(file_path):
            # 检查文件是否对所有用户可读
            stat_info = os.stat(file_path)
            if stat_info.st_mode & 0o044:  # 其他用户可读
                warnings.append(f"文件权限过于宽松: {file_path}")
        
        return warnings
    
    @staticmethod
    def check_environment_variables() -> List[str]:
        """检查环境变量安全性"""
        warnings = []
        
        # 检查敏感环境变量
        sensitive_vars = ['SECRET_KEY', 'ORTHANC_PASSWORD', 'DATABASE_URL']
        
        for var in sensitive_vars:
            value = os.getenv(var)
            if value:
                if len(value) < 16:
                    warnings.append(f"环境变量{var}值过短")
                if var in ['SECRET_KEY', 'ORTHANC_PASSWORD'] and value in ['password', 'admin', '123456']:
                    warnings.append(f"环境变量{var}使用弱密码")
        
        return warnings

def validate_and_report_config(config):
    """验证配置并生成报告"""
    validator = ConfigValidator()
    security_checker = SecurityChecker()
    
    # 配置验证
    result = validator.validate_config(config)
    
    # 安全检查
    security_warnings = security_checker.check_environment_variables()
    
    # 生成报告
    logger.info("=== 配置验证报告 ===")
    
    if result.errors:
        logger.error("配置错误:")
        for error in result.errors:
            logger.error(f"  - {error}")
    
    if result.warnings or security_warnings:
        logger.warning("配置警告:")
        for warning in result.warnings + security_warnings:
            logger.warning(f"  - {warning}")
    
    if result.is_valid and not security_warnings:
        logger.info("配置验证通过")
    
    return result