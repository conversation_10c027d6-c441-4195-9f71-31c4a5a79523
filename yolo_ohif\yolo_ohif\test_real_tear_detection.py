#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实撕裂病人DICOM数据检测测试
测试检测系统在真实撕裂病例上的表现
"""

import os
import sys
import logging
import json
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.detection_service import DetectionService
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_real_tear_detection():
    """
    测试真实撕裂病人数据的检测效果
    """
    print("=" * 60)
    print("真实撕裂病人DICOM数据检测测试")
    print("=" * 60)
    
    # 初始化服务
    try:
        # 获取模型路径
        model_path = os.path.join(Config.YOLO.MODEL_PATH, Config.YOLO.DEFAULT_MODEL)
        print(f"使用模型: {model_path}")
        
        detection_service = DetectionService(
            model_path=model_path,
            confidence_threshold=Config.YOLO.CONFIDENCE_THRESHOLD,
            iou_threshold=Config.YOLO.IOU_THRESHOLD,
            device=Config.YOLO.DEVICE
        )
        print("✓ 检测服务初始化成功")
    except Exception as e:
        print(f"✗ 检测服务初始化失败: {e}")
        return
    
    # 检查模型状态
    print(f"\n模型信息:")
    print(f"- 模型路径: {detection_service.model_path}")
    print(f"- 置信度阈值: {detection_service.confidence_threshold}")
    print(f"- IOU阈值: {detection_service.iou_threshold}")
    print(f"- 类别名称: {detection_service.class_names}")
    
    # 测试数据路径
    abnormal_dir = Path("E:/Trae/yolo_ohif/results/abnormal")
    
    if not abnormal_dir.exists():
        print(f"✗ 测试数据目录不存在: {abnormal_dir}")
        return
    
    # 获取病人列表
    patient_dirs = [d for d in abnormal_dir.iterdir() if d.is_dir()]
    print(f"\n找到 {len(patient_dirs)} 个病人数据")
    
    # 测试前3个病人的数据
    test_patients = patient_dirs[:3]
    
    for i, patient_dir in enumerate(test_patients, 1):
        print(f"\n{'='*40}")
        print(f"测试病人 {i}: {patient_dir.name}")
        print(f"{'='*40}")
        
        # 获取序列目录
        series_dirs = [d for d in patient_dir.iterdir() if d.is_dir()]
        print(f"找到 {len(series_dirs)} 个序列:")
        for series_dir in series_dirs:
            print(f"  - {series_dir.name}")
        
        # 测试第一个序列
        if series_dirs:
            test_series = series_dirs[0]
            print(f"\n测试序列: {test_series.name}")
            
            # 获取DICOM文件
            dicom_files = list(test_series.glob("*.dcm"))
            print(f"找到 {len(dicom_files)} 个DICOM文件")
            
            if dicom_files:
                # 测试中间的几个切片
                test_files = dicom_files[len(dicom_files)//4:len(dicom_files)*3//4]
                print(f"测试 {len(test_files)} 个切片")
                
                detection_count = 0
                total_confidence = 0
                
                for j, dicom_file in enumerate(test_files[:5]):  # 最多测试5个切片
                    print(f"\n  切片 {j+1}: {dicom_file.name}")
                    
                    try:
                        # 执行检测 - 传入文件路径列表
                        results = detection_service.detect_dicom([str(dicom_file)])
                        
                        if results and len(results) > 0:
                            result = results[0]  # 获取第一个结果
                            if 'detections' in result:
                                detections = result['detections']
                                print(f"    检测到 {len(detections)} 个目标")
                                
                                for k, detection in enumerate(detections):
                                    detection_count += 1
                                    confidence = detection.get('confidence', 0)
                                    total_confidence += confidence
                                    class_name = detection.get('class', 'unknown')
                                    
                                    print(f"      目标 {k+1}:")
                                    print(f"        类别: {class_name}")
                                    print(f"        置信度: {confidence:.3f}")
                                    print(f"        位置: ({detection.get('x', 0):.1f}, {detection.get('y', 0):.1f})")
                                    print(f"        大小: {detection.get('width', 0):.1f} x {detection.get('height', 0):.1f}")
                            else:
                                print(f"    未检测到目标")
                        else:
                            print(f"    未检测到目标")
                            
                    except Exception as e:
                        print(f"    检测失败: {e}")
                
                # 统计结果
                if detection_count > 0:
                    avg_confidence = total_confidence / detection_count
                    print(f"\n  序列检测统计:")
                    print(f"    总检测数: {detection_count}")
                    print(f"    平均置信度: {avg_confidence:.3f}")
                    print(f"    检测率: {detection_count}/{len(test_files[:5])} = {detection_count/len(test_files[:5])*100:.1f}%")
                else:
                    print(f"\n  序列检测统计: 未检测到任何目标")
    
    print(f"\n{'='*60}")
    print("测试完成")
    print(f"{'='*60}")
    
    # 建议
    print("\n检测建议:")
    print("1. 如果检测率很低，可能需要调整置信度阈值")
    print("2. 如果检测到的类别显示为'item'，说明类别名称映射有问题")
    print("3. 如果检测位置不准确，可能需要重新训练模型")
    print("4. 建议使用专门标注的撕裂数据进行模型验证")

if __name__ == "__main__":
    test_real_tear_detection()