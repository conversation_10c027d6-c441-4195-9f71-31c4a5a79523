namespace MedicalImageAnalysis.Core.Models;

/// <summary>
/// DICOM解析结果
/// </summary>
public class DicomParseResult
{
    /// <summary>
    /// 是否解析成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 解析的DICOM实例
    /// </summary>
    public Entities.DicomInstance? DicomInstance { get; set; }

    /// <summary>
    /// 解析的DICOM实例（别名）
    /// </summary>
    public Entities.DicomInstance? Instance
    {
        get => DicomInstance;
        set => DicomInstance = value;
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public DicomValidationResult? ValidationResult { get; set; }

    /// <summary>
    /// 解析耗时（毫秒）
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 文件哈希值
    /// </summary>
    public string? FileHash { get; set; }

    /// <summary>
    /// 解析的标签数量
    /// </summary>
    public int TagCount { get; set; }

    /// <summary>
    /// 是否包含像素数据
    /// </summary>
    public bool HasPixelData { get; set; }

    /// <summary>
    /// 像素数据大小（字节）
    /// </summary>
    public long PixelDataSize { get; set; }

    /// <summary>
    /// 解析时间戳
    /// </summary>
    public DateTime ParsedAt { get; set; } = DateTime.UtcNow;
}
