import cv2
import numpy as np

# 读取一个label图像
img_path = r"E:\Trae\yolo_ohif\sliced_output\sliced_images\label_T2\130_slice_000.jpg"
img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)

if img is not None:
    print(f"图像尺寸: {img.shape}")
    print(f"像素值范围: {img.min()} - {img.max()}")
    unique_values = np.unique(img)
    print(f"唯一像素值: {unique_values}")
    
    for val in unique_values:
        count = np.sum(img == val)
        print(f"值 {val}: {count} 像素")
    
    # 检查不同阈值
    print(f"\n阈值检查:")
    print(f"== 255: {np.sum(img == 255)} 像素")
    print(f"> 0: {np.sum(img > 0)} 像素")
    print(f"> 128: {np.sum(img > 128)} 像素")
    print(f"> 200: {np.sum(img > 200)} 像素")
else:
    print("无法读取图像")