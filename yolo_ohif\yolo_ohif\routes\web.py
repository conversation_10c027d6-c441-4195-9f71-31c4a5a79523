import os
import json
import shutil
import tempfile
import logging
from flask import Blueprint, render_template, redirect, url_for, request, flash, current_app, session
from werkzeug.utils import secure_filename

# 导入安全工具
try:
    from utils.security import validate_upload_file, InputSanitizer
    from utils.performance_monitor import timing_decorator
except ImportError:
    # 如果导入失败，创建空函数
    def validate_upload_file(file_path, filename):
        return {'is_valid': True, 'errors': [], 'warnings': []}
    
    class InputSanitizer:
        @staticmethod
        def sanitize_string(s, max_length=255):
            return str(s)[:max_length] if s else ''
    
    def timing_decorator(name):
        def decorator(func):
            return func
        return decorator

logger = logging.getLogger(__name__)

# 创建Blueprint
web_bp = Blueprint('web', __name__)

@web_bp.route('/')
def index():
    """首页"""
    return render_template('index.html')

@web_bp.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        try:
            # 用户登录
            token, user = current_app.auth_service.login(username, password)
            
            # 保存令牌到会话
            session['token'] = token
            session['user'] = user
            
            # 重定向到仪表板
            return redirect(url_for('web.dashboard'))
        except ValueError as e:
            flash(str(e), 'danger')
        except Exception as e:
            logger.error(f"登录时出错: {str(e)}")
            flash('登录失败，请稍后重试', 'danger')
    
    return render_template('login.html')

@web_bp.route('/logout')
def logout():
    """退出登录"""
    # 清除会话
    session.pop('token', None)
    session.pop('user', None)
    
    # 重定向到登录页面
    return redirect(url_for('web.login'))

@web_bp.route('/register', methods=['GET', 'POST'])
def register():
    """注册页面"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        email = request.form.get('email')
        
        try:
            # 注册用户
            user_id = current_app.auth_service.register_user(username, password, email)
            
            # 显示成功消息
            flash('注册成功，请登录', 'success')
            
            # 重定向到登录页面
            return redirect(url_for('web.login'))
        except ValueError as e:
            flash(str(e), 'danger')
        except Exception as e:
            logger.error(f"注册时出错: {str(e)}")
            flash('注册失败，请稍后重试', 'danger')
    
    return render_template('register.html')

@web_bp.route('/dashboard')
def dashboard():
    """仪表板页面"""
    # 检查用户是否已登录
    if 'token' not in session:
        return redirect(url_for('web.login'))
    
    studies = []
    model_info = {}
    error_messages = []
    
    # 检查Orthanc服务健康状态
    try:
        health_status = current_app.orthanc_service.check_health()
        if health_status['status'] != 'ok':
            logger.warning(f"Orthanc服务状态异常: {health_status.get('message', '未知错误')}")
            error_messages.append(f"DICOM服务器状态异常: {health_status.get('message', '服务不可用')}")
        else:
            logger.info(f"Orthanc服务正常，版本: {health_status.get('version', '未知')}")
    except Exception as e:
        logger.error(f"检查Orthanc服务状态时出错: {str(e)}")
        error_messages.append(f"无法连接到DICOM服务器: {str(e)}")
    
    # 尝试获取研究列表（仅在服务健康时）
    if not error_messages:
        try:
            studies = current_app.orthanc_service.get_studies()
            logger.info(f"成功获取 {len(studies)} 个研究")
        except Exception as e:
            logger.error(f"获取研究列表时出错: {str(e)}")
            error_messages.append(f"获取研究列表失败: {str(e)}")
    
    # 尝试获取模型信息
    try:
        model_info = current_app.detection_service.get_model_info()
        logger.info("成功获取模型信息")
    except Exception as e:
        logger.error(f"获取模型信息时出错: {str(e)}")
        error_messages.append(f"无法获取模型信息: {str(e)}")
    
    # 显示错误消息（如果有）
    for error_msg in error_messages:
        flash(error_msg, 'warning')
    
    return render_template('dashboard.html', studies=studies, model_info=model_info)

@web_bp.route('/system-status')
def system_status():
    """系统状态页面"""
    # 检查用户是否已登录
    if 'token' not in session:
        return redirect(url_for('web.login'))
    
    from config import Config
    return render_template('system_status.html', config=Config)

@web_bp.route('/upload', methods=['GET', 'POST'])
@timing_decorator('file_upload')
def upload():
    """上传页面"""
    # 检查用户是否已登录
    if 'token' not in session:
        return redirect(url_for('web.login'))
    
    if request.method == 'POST':
        # 检查是否有文件
        if 'file' not in request.files:
            flash('没有选择文件', 'danger')
            return redirect(request.url)
        
        file = request.files['file']
        
        # 检查文件名
        if file.filename == '':
            flash('没有选择文件', 'danger')
            return redirect(request.url)
        
        try:
            # 清理文件名
            original_filename = InputSanitizer.sanitize_string(file.filename)
            secure_name = secure_filename(original_filename)
            
            # 保存文件到临时目录
            temp_dir = os.path.join(os.getcwd(), 'temp')
            os.makedirs(temp_dir, exist_ok=True)
            temp_path = os.path.join(temp_dir, secure_name)
            file.save(temp_path)
            
            # 安全验证
            validation_result = validate_upload_file(temp_path, original_filename)
            
            if not validation_result['is_valid']:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                
                error_msg = '文件验证失败: ' + '; '.join(validation_result['errors'])
                flash(error_msg, 'danger')
                logger.warning(f"文件上传被拒绝: {error_msg}")
                return redirect(request.url)
            
            # 记录警告
            if validation_result['warnings']:
                warning_msg = '; '.join(validation_result['warnings'])
                logger.warning(f"文件上传警告: {warning_msg}")
            
            # 上传到Orthanc
            result = current_app.orthanc_service.upload_dicom(temp_path)
            
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
            
            # 显示成功消息
            flash('文件上传成功', 'success')
            logger.info(f"文件上传成功: {original_filename}")
            
            # 重定向到仪表板
            return redirect(url_for('web.dashboard'))
        except Exception as e:
            # 确保清理临时文件
            if 'temp_path' in locals() and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass
            
            logger.error(f"上传文件时出错: {str(e)}")
            flash('上传文件时出错，请稍后重试', 'danger')
    
    return render_template('upload.html')

@web_bp.route('/viewer/<study_id>')
def viewer(study_id):
    """查看器页面 - 自动进行YOLO检测并显示结果"""
    # 检查用户是否已登录
    if 'token' not in session:
        return redirect(url_for('web.login'))
    
    try:
        # 获取研究信息
        study = current_app.database_service.get_study(study_id)
        
        # 如果数据库中没有研究信息，尝试从Orthanc获取并同步
        if not study:
            try:
                # 从Orthanc获取研究信息
                orthanc_studies = current_app.orthanc_service.get_studies()
                orthanc_study = next((s for s in orthanc_studies if s['id'] == study_id), None)
                
                if orthanc_study:
                    # 同步到数据库
                    current_app.database_service.add_study(
                        orthanc_id=orthanc_study['id'],
                        patient_name=orthanc_study.get('patientName', 'Unknown'),
                        patient_id=orthanc_study.get('patientID', 'Unknown'),
                        study_date=orthanc_study.get('studyDate', ''),
                        study_description=orthanc_study.get('studyDescription', ''),
                        modality=orthanc_study.get('modality', ''),
                        series_count=orthanc_study.get('seriesCount', 0),
                        instance_count=orthanc_study.get('instanceCount', 0)
                    )
                    # 重新获取研究信息
                    study = current_app.database_service.get_study(study_id)
                    logger.info(f"已同步研究信息到数据库: {study_id}")
            except Exception as sync_error:
                logger.warning(f"同步研究信息失败: {str(sync_error)}")
        
        # 检查是否已有检测结果
        existing_results = current_app.database_service.get_detection_results(study_id)
        detection_results = []
        
        if not existing_results:
            # 自动进行YOLO检测
            try:
                logger.info(f"开始自动检测研究: {study_id}")
                
                # 获取用户信息
                user_info = current_app.auth_service.verify_token(session['token'])
                user_id = user_info.get('user_id')
                
                # 从Orthanc下载DICOM文件
                temp_dir = tempfile.mkdtemp()
                dicom_files = current_app.orthanc_service.download_study_dicoms(study_id, temp_dir)
                
                if dicom_files:
                    # 进行YOLO检测
                    detection_results = current_app.detection_service.detect_dicom(
                        dicom_files, 
                        save_images=False
                    )
                    
                    # 保存检测结果到数据库
                    if detection_results:
                        current_app.database_service.add_detection_result(
                            study_id=study_id,
                            user_id=user_id,
                            model_name=current_app.detection_service.model_path,
                            confidence_threshold=current_app.detection_service.confidence_threshold,
                            result_data=detection_results
                        )
                        logger.info(f"检测完成，发现 {len(detection_results)} 个结果")
                    else:
                        logger.info("检测完成，未发现异常")
                
                # 清理临时文件
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    
            except Exception as detection_error:
                logger.error(f"自动检测失败: {str(detection_error)}")
                # 即使检测失败也继续显示查看器
        else:
            # 解析现有检测结果
            for result in existing_results:
                if isinstance(result.get('result_data'), str):
                    result_data = json.loads(result['result_data'])
                else:
                    result_data = result.get('result_data', [])
                detection_results.extend(result_data)
        
        # 生成带检测结果的查看器URL
        viewer_url = f"/dicom-viewer?study_id={study_id}"
        
        # 调试日志
        logger.info(f"检测结果数量: {len(detection_results)}")
        if detection_results:
            logger.info(f"检测结果示例: {detection_results[0] if detection_results else 'None'}")
        
        # 如果有检测结果，生成覆盖层配置
        if detection_results:
            overlay_config = current_app.ohif_service.generate_detection_overlay(detection_results)
            logger.info(f"覆盖层配置: {overlay_config}")
            import urllib.parse
            overlay_param = urllib.parse.quote(json.dumps(overlay_config))
            viewer_url += f"&overlays={overlay_param}"
            logger.info(f"最终查看器URL: {viewer_url}")
        
        return render_template('viewer.html', study_id=study_id, viewer_url=viewer_url, study=study, auto_detect=True)
    except Exception as e:
        logger.error(f"加载查看器时出错: {str(e)}")
        flash('加载查看器时出错，请稍后重试', 'danger')
        return redirect(url_for('web.dashboard'))

@web_bp.route('/detect/<study_id>', methods=['GET', 'POST'])
def detect(study_id):
    """检测页面"""
    # 检查用户是否已登录
    if 'token' not in session:
        return redirect(url_for('web.login'))
    
    if request.method == 'POST':
        # 获取参数
        confidence = float(request.form.get('confidence', current_app.config.get('YOLO_CONFIDENCE_THRESHOLD', 0.25)))
        save_images = request.form.get('save_images', 'true').lower() == 'true'
        
        try:
            # 直接调用检测服务执行检测
            import tempfile
            import shutil
            
            # 创建临时目录
            temp_dir = tempfile.mkdtemp()
            
            try:
                # 获取研究的所有实例
                instances = current_app.orthanc_service.get_study_instances(study_id)
                
                if not instances:
                    flash('未找到DICOM实例', 'warning')
                    return render_template('detect.html', study_id=study_id, study=current_app.database_service.get_study(study_id))
                
                detection_results = []
                
                # 对每个实例进行检测
                for instance in instances:
                    instance_id = instance['ID']
                    
                    # 下载DICOM文件
                    dicom_data = current_app.orthanc_service.get_instance_dicom(instance_id)
                    dicom_path = os.path.join(temp_dir, f"{instance_id}.dcm")
                    
                    with open(dicom_path, 'wb') as f:
                        f.write(dicom_data)
                    
                    # 执行检测（使用单文件检测方法）
                    result = current_app.detection_service.detect_single_dicom(
                        dicom_path,
                        output_dir=temp_dir if save_images else None,
                        save_result_image=save_images,
                        confidence_threshold=confidence
                    )
                    
                    if result and result.get('detections'):
                        detection_results.append({
                            'instance_id': instance_id,
                            'detections': result['detections'],
                            'image_path': result.get('result_image_path')
                        })
                
                # 保存检测结果到数据库
                if detection_results:
                    try:
                        # 获取当前用户ID
                        try:
                            user_info = current_app.auth_service.verify_token(session.get('token'))
                            user_id = user_info.get('id') if user_info else None
                        except Exception as e:
                            logger.warning(f"无法获取用户信息: {str(e)}")
                            user_id = None
                        
                        # 获取模型信息
                        model_info = current_app.detection_service.get_model_info()
                        model_name = model_info.get('model_name', 'yolo')
                        
                        # 保存检测结果
                        result_id = current_app.database_service.add_detection_result(
                            study_id=study_id,
                            user_id=user_id,
                            model_name=model_name,
                            confidence_threshold=confidence,
                            result_data=detection_results
                        )
                        
                        logger.info(f"检测完成，共发现 {len(detection_results)} 个实例有检测结果，结果已保存到数据库 (ID: {result_id})")
                        flash(f'检测完成，共发现 {len(detection_results)} 个实例有检测结果', 'success')
                    except Exception as e:
                        logger.error(f"保存检测结果到数据库时出错: {str(e)}")
                        flash(f'检测完成，共发现 {len(detection_results)} 个实例有检测结果，但保存时出错', 'warning')
                else:
                    flash('检测完成，但未发现任何异常', 'info')
                
                # 重定向到结果页面
                return redirect(url_for('web.results', study_id=study_id))
                
            finally:
                # 清理临时目录
                shutil.rmtree(temp_dir, ignore_errors=True)
                
        except Exception as e:
            logger.error(f"执行检测时出错: {str(e)}")
            flash(f'执行检测时出错: {str(e)}', 'danger')
    
    # 获取研究信息
    study = current_app.database_service.get_study(study_id)
    
    # 获取模型信息
    model_info = current_app.detection_service.get_model_info()
    
    return render_template('detect.html', study_id=study_id, study=study, model_info=model_info)

@web_bp.route('/results/<study_id>')
def results(study_id):
    """结果页面"""
    # 检查用户是否已登录
    if 'token' not in session:
        return redirect(url_for('web.login'))
    
    try:
        # 获取研究信息
        study = current_app.database_service.get_study(study_id)
        
        # 获取检测结果
        results = current_app.database_service.get_detection_results(study['id'])
        
        # 解析和格式化检测结果
        detection_results = []
        all_confidences = []
        detection_summary = {}
        
        for result in results if results else []:
            # 解析result_data
            if isinstance(result.get('result_data'), str):
                result_data = json.loads(result['result_data'])
            else:
                result_data = result.get('result_data', [])
            
            # 处理每个检测结果
            for detection_result in result_data:
                if detection_result.get('detections'):
                    # 格式化为模板期望的结构
                    formatted_result = {
                        'id': result.get('id'),
                        'instance_id': detection_result.get('instance_id'),
                        'instance_number': detection_result.get('instance_id', '未知'),
                        'acquisition_date': result.get('created_at', '未知日期'),
                        'image_size': '未知尺寸',
                        'detections': []
                    }
                    
                    # 处理检测项
                    for detection in detection_result['detections']:
                        confidence = detection.get('confidence', 0) * 100
                        all_confidences.append(confidence)
                        
                        # 统计检测类型
                        label = detection.get('class', detection.get('class_name', '未知'))
                        if label not in detection_summary:
                            detection_summary[label] = {'count': 0, 'confidences': []}
                        detection_summary[label]['count'] += 1
                        detection_summary[label]['confidences'].append(confidence)
                        
                        # 格式化检测项
                        formatted_detection = {
                            'label': label,
                            'confidence': confidence,
                            'x': detection.get('x1', 0),
                            'y': detection.get('y1', 0),
                            'width': detection.get('x2', 0) - detection.get('x1', 0),
                            'height': detection.get('y2', 0) - detection.get('y1', 0)
                        }
                        formatted_result['detections'].append(formatted_detection)
                    
                    detection_results.append(formatted_result)
        
        abnormal_count = len(detection_results)
        
        avg_confidence = sum(all_confidences) / len(all_confidences) if all_confidences else 0
        
        # 格式化检测摘要
        detection_summary_list = []
        colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40']
        for i, (label, data) in enumerate(detection_summary.items()):
            detection_summary_list.append({
                'label': label,
                'count': data['count'],
                'avg_confidence': sum(data['confidences']) / len(data['confidences']),
                'color': colors[i % len(colors)]
            })
        
        # 获取OHIF查看器URL
        viewer_url = current_app.ohif_service.get_viewer_url(study_id)
        
        return render_template('detection_results.html', 
                             study_id=study_id, 
                             study=study, 
                             detection_results=detection_results,
                             abnormal_count=abnormal_count,
                             avg_confidence=avg_confidence,
                             detection_summary=detection_summary_list,
                             viewer_url=viewer_url)
    except Exception as e:
        logger.error(f"加载结果时出错: {str(e)}")
        flash('加载结果时出错，请稍后重试', 'danger')
        return redirect(url_for('web.dashboard'))

@web_bp.route('/profile')
def profile():
    """个人资料页面"""
    # 检查用户是否已登录
    if 'token' not in session:
        return redirect(url_for('web.login'))
    
    return render_template('profile.html', user=session['user'])

@web_bp.route('/change-password', methods=['GET', 'POST'])
def change_password():
    """修改密码页面"""
    # 检查用户是否已登录
    if 'token' not in session:
        return redirect(url_for('web.login'))
    
    if request.method == 'POST':
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        # 验证新密码
        if new_password != confirm_password:
            flash('新密码和确认密码不匹配', 'danger')
            return redirect(request.url)
        
        try:
            # 修改密码
            current_app.auth_service.change_password(
                user_id=session['user']['id'],
                current_password=current_password,
                new_password=new_password
            )
            
            # 显示成功消息
            flash('密码修改成功', 'success')
            
            # 重定向到个人资料页面
            return redirect(url_for('web.profile'))
        except ValueError as e:
            flash(str(e), 'danger')
        except Exception as e:
            logger.error(f"修改密码时出错: {str(e)}")
            flash('修改密码时出错，请稍后重试', 'danger')
    
    return render_template('change_password.html')

@web_bp.route('/reset-password', methods=['GET', 'POST'])
def reset_password():
    """重置密码页面"""
    if request.method == 'POST':
        username = request.form.get('username')
        
        if not username:
            flash('请输入用户名', 'danger')
            return redirect(request.url)
        
        try:
            # 这里可以实现发送重置密码邮件的逻辑
            # 目前只是显示一个提示信息
            flash('密码重置链接已发送到您的邮箱（功能开发中）', 'info')
            return redirect(url_for('web.login'))
        except Exception as e:
            logger.error(f"重置密码时出错: {str(e)}")
            flash('重置密码失败，请稍后重试', 'danger')
    
    return render_template('reset_password.html')

@web_bp.route('/about')
def about():
    """关于页面"""
    return render_template('about.html')

@web_bp.route('/help')
def help():
    """帮助页面"""
    return render_template('help.html')

@web_bp.route('/smart-detect')
def smart_detect():
    """智能检测页面 - 简化的上传和检测流程"""
    return render_template('simple_detect.html')

@web_bp.route('/viewer-test')
def viewer_test():
    """查看器测试页面"""
    return render_template('viewer_test.html')

@web_bp.route('/view-image/<study_id>/<instance_id>')
def view_image(study_id, instance_id):
    """在OHIF查看器中查看特定实例的图像，并显示检测结果"""
    # 检查用户是否已登录
    if 'token' not in session:
        return redirect(url_for('web.login'))
    
    try:
        # 获取检测结果
        results = current_app.database_service.get_detection_results(study_id)
        logger.info(f"获取到 {len(results)} 个检测结果记录")
        
        # 处理检测结果数据
        all_detections = []
        for result in results:
            if isinstance(result.get('result_data'), str):
                try:
                    result_data = json.loads(result['result_data'])
                except json.JSONDecodeError:
                    logger.error(f"解析检测结果JSON失败: {result.get('result_data')}")
                    continue
            else:
                result_data = result.get('result_data', [])
            
            # 检查result_data的结构
            if isinstance(result_data, list):
                # 如果是检测结果列表，直接添加
                for detection in result_data:
                    if isinstance(detection, dict) and 'detections' in detection:
                        # 如果检测结果包含detections字段
                        all_detections.extend(detection['detections'])
                    elif isinstance(detection, dict) and all(k in detection for k in ['x1', 'y1', 'x2', 'y2']):
                        # 如果是直接的检测框数据
                        all_detections.append(detection)
            elif isinstance(result_data, dict) and 'detections' in result_data:
                # 如果是包含detections字段的字典
                all_detections.extend(result_data['detections'])
        
        logger.info(f"处理后得到 {len(all_detections)} 个检测框")
        
        # 生成OHIF查看器URL
        viewer_url = current_app.ohif_service.get_viewer_url(study_id)
        
        # 如果有检测结果，生成覆盖层配置
        if all_detections:
            # 将检测结果包装成generate_detection_overlay期望的格式
            formatted_results = [{'detections': all_detections}]
            overlay_config = current_app.ohif_service.generate_detection_overlay(formatted_results)
            # 将覆盖层配置作为URL参数传递
            import urllib.parse
            overlay_param = urllib.parse.quote(json.dumps(overlay_config))
            viewer_url += f"&overlays={overlay_param}"
            logger.info(f"生成覆盖层配置，包含 {len(overlay_config.get('overlays', []))} 个覆盖层")
        else:
            logger.warning(f"研究 {study_id} 没有找到有效的检测结果")
        
        return redirect(viewer_url)
        
    except Exception as e:
        logger.error(f"打开OHIF查看器时出错: {str(e)}")
        flash('打开查看器时出错，请稍后重试', 'danger')
        return redirect(url_for('web.results', study_id=study_id))

@web_bp.route('/dicom-viewer')
@web_bp.route('/dicom-viewer/<study_id>')
def dicom_viewer(study_id=None):
    """DICOM智能查看器"""
    if not study_id:
        study_id = request.args.get('study_id')
    
    # 检查用户是否已登录
    if 'token' not in session:
        return redirect(url_for('web.login'))
    
    # 如果有study_id，获取研究信息
    study = None
    if study_id:
        try:
            study = current_app.database_service.get_study(study_id)
            if not study:
                flash('未找到指定的研究', 'warning')
                return redirect(url_for('web.dashboard'))
        except Exception as e:
            logger.error(f"获取研究信息时出错: {str(e)}")
            flash('获取研究信息时出错', 'danger')
            return redirect(url_for('web.dashboard'))
    
    return render_template('dicom_viewer.html', study_id=study_id, study=study)

@web_bp.route('/dicom-test')
def dicom_test():
    """DICOM诊断测试页面"""
    return render_template('dicom_test.html')

@web_bp.route('/simple-dicom-test')
def simple_dicom_test():
    """简化DICOM测试页面"""
    return render_template('simple_dicom_test.html')

@web_bp.route('/dicom-debug')
def dicom_debug():
    """DICOM调试页面"""
    return render_template('dicom_debug.html')

@web_bp.route('/professional-viewer')
@web_bp.route('/professional-viewer/<study_id>')
def professional_viewer(study_id=None):
    """专业DICOM阅读器"""
    if not study_id:
        study_id = request.args.get('study_id')
    
    # 检查用户是否已登录
    if 'token' not in session:
        return redirect(url_for('web.login'))
    
    # 如果有study_id，获取研究信息
    study = None
    if study_id:
        try:
            study = current_app.database_service.get_study(study_id)
            if not study:
                flash('未找到指定的研究', 'warning')
                return redirect(url_for('web.dashboard'))
        except Exception as e:
            logger.error(f"获取研究信息时出错: {str(e)}")
            flash('获取研究信息时出错', 'danger')
            return redirect(url_for('web.dashboard'))
    
    return render_template('dicom_viewer_professional.html', study_id=study_id, study=study)

@web_bp.route('/delete-study/<study_id>', methods=['POST'])
def delete_study(study_id):
    """删除研究"""
    # 检查用户是否已登录
    if 'token' not in session:
        return redirect(url_for('web.login'))
    
    try:
        # 调用Orthanc服务删除研究
        current_app.orthanc_service.delete_study(study_id)
        
        # 从数据库中删除相关记录
        current_app.database_service.delete_study(study_id)
        
        flash('研究已成功删除', 'success')
        logger.info(f"用户 {session.get('user', {}).get('username', 'unknown')} 删除了研究 {study_id}")
    except Exception as e:
        logger.error(f"删除研究时出错: {str(e)}")
        flash('删除研究时出错，请稍后重试', 'danger')
    
    return redirect(url_for('web.dashboard'))