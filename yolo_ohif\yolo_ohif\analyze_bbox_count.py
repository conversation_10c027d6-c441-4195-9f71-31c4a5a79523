import os
from collections import Counter
from pathlib import Path

def analyze_bbox_count_per_file():
    """分析每个txt文件中的边界框个数分布"""
    
    bbox_dir = r"e:\Trae\yolo_ohif\sliced_output\bbox_labels"
    
    # 统计信息
    bbox_counts = []
    file_details = []
    
    # 获取所有标注文件
    txt_files = sorted([f for f in os.listdir(bbox_dir) if f.endswith('.txt')])
    
    print(f"正在分析 {len(txt_files)} 个标注文件...\n")
    
    # 分析每个文件
    for txt_file in txt_files:
        txt_path = os.path.join(bbox_dir, txt_file)
        
        with open(txt_path, 'r') as f:
            lines = [line.strip() for line in f.readlines() if line.strip()]
            bbox_count = len(lines)
            
        bbox_counts.append(bbox_count)
        file_details.append((txt_file, bbox_count))
    
    # 统计边界框个数分布
    count_distribution = Counter(bbox_counts)
    
    print("=== 边界框个数分布统计 ===")
    print(f"总文件数: {len(txt_files)}")
    print(f"总边界框数: {sum(bbox_counts)}")
    print(f"平均每文件边界框数: {sum(bbox_counts) / len(txt_files):.2f}")
    print(f"最少边界框数: {min(bbox_counts)}")
    print(f"最多边界框数: {max(bbox_counts)}")
    
    print("\n=== 边界框个数分布详情 ===")
    for bbox_count in sorted(count_distribution.keys()):
        file_count = count_distribution[bbox_count]
        percentage = (file_count / len(txt_files)) * 100
        print(f"{bbox_count} 个边界框: {file_count} 个文件 ({percentage:.1f}%)")
    
    # 显示一些具体例子
    print("\n=== 文件示例 ===")
    
    # 显示边界框数量最多的文件
    max_bbox_count = max(bbox_counts)
    max_files = [f for f, c in file_details if c == max_bbox_count]
    print(f"\n边界框最多的文件 ({max_bbox_count} 个):")
    for i, file_name in enumerate(max_files[:5]):  # 只显示前5个
        print(f"  {file_name}")
    
    # 显示边界框数量最少的文件
    min_bbox_count = min(bbox_counts)
    min_files = [f for f, c in file_details if c == min_bbox_count]
    print(f"\n边界框最少的文件 ({min_bbox_count} 个):")
    for i, file_name in enumerate(min_files[:5]):  # 只显示前5个
        print(f"  {file_name}")
    
    # 显示一些中等数量的文件
    if len(count_distribution) > 2:
        middle_counts = sorted(count_distribution.keys())[1:-1]
        if middle_counts:
            middle_count = middle_counts[len(middle_counts)//2]
            middle_files = [f for f, c in file_details if c == middle_count]
            print(f"\n中等边界框数量的文件 ({middle_count} 个):")
            for i, file_name in enumerate(middle_files[:5]):  # 只显示前5个
                print(f"  {file_name}")
    
    return {
        'total_files': len(txt_files),
        'total_bboxes': sum(bbox_counts),
        'distribution': count_distribution,
        'file_details': file_details
    }

if __name__ == "__main__":
    analyze_bbox_count_per_file()