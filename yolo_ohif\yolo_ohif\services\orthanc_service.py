import os
import requests
import json
import tempfile
import logging
import pydicom
from pydicom.dataset import Dataset, FileDataset
from pydicom.uid import generate_uid
import datetime
import shutil
import zipfile

logger = logging.getLogger(__name__)

class OrthancService:
    """Orthanc DICOM服务器接口类"""
    
    def __init__(self, orthanc_url, username=None, password=None):
        """初始化Orthanc服务接口
        
        Args:
            orthanc_url: Orthanc服务器URL
            username: Orthanc认证用户名
            password: Orthanc认证密码
        """
        self.orthanc_url = orthanc_url.rstrip('/')
        self.auth = None
        if username and password:
            self.auth = (username, password)
        self.session = requests.Session()
        if self.auth:
            self.session.auth = self.auth
    
    def check_health(self):
        """检查Orthanc服务器健康状态"""
        try:
            response = self.session.get(f"{self.orthanc_url}/system", timeout=5)
            if response.status_code == 200:
                return {'status': 'ok', 'version': response.json().get('Version', 'unknown')}
            else:
                return {'status': 'error', 'message': f"HTTP错误: {response.status_code}"}
        except Exception as e:
            logger.error(f"检查Orthanc健康状态时出错: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    def get_studies(self):
        """获取所有研究列表"""
        try:
            response = self.session.get(f"{self.orthanc_url}/studies")
            response.raise_for_status()
            study_ids = response.json()
            
            studies = []
            for study_id in study_ids:
                study_info = self.session.get(f"{self.orthanc_url}/studies/{study_id}")
                study_info.raise_for_status()
                study_data = study_info.json()
                
                # 提取主要信息
                main_dicom_tags = study_data.get('MainDicomTags', {})
                patient_main_dicom_tags = study_data.get('PatientMainDicomTags', {})
                
                # 获取序列信息来确定模态和计算图像数量
                series_list = study_data.get('Series', [])
                modality = 'Unknown'
                total_instances = 0
                
                # 遍历所有序列获取模态和实例数量
                for series_id in series_list:
                    try:
                        series_response = self.session.get(f"{self.orthanc_url}/series/{series_id}")
                        series_response.raise_for_status()
                        series_data = series_response.json()
                        
                        # 获取模态（使用第一个序列的模态）
                        if modality == 'Unknown':
                            modality = series_data.get('MainDicomTags', {}).get('Modality', 'Unknown')
                        
                        # 累加实例数量
                        instances = series_data.get('Instances', [])
                        total_instances += len(instances)
                        
                    except Exception as e:
                        logger.warning(f"获取序列 {series_id} 信息失败: {str(e)}")
                        continue
                
                studies.append({
                    'id': study_id,
                    'patient_name': patient_main_dicom_tags.get('PatientName', 'Unknown'),
                    'patient_id': patient_main_dicom_tags.get('PatientID', 'Unknown'),
                    'study_date': main_dicom_tags.get('StudyDate', ''),
                    'study_time': main_dicom_tags.get('StudyTime', ''),
                    'study_description': main_dicom_tags.get('StudyDescription', 'No description'),
                    'accession_number': main_dicom_tags.get('AccessionNumber', ''),
                    'modality': modality,
                    'series_count': len(series_list),
                    'instance_count': total_instances,
                    'images_count': total_instances,
                    # 为向后兼容保留驼峰命名
                    'patientName': patient_main_dicom_tags.get('PatientName', 'Unknown'),
                    'patientID': patient_main_dicom_tags.get('PatientID', 'Unknown'),
                    'studyDate': main_dicom_tags.get('StudyDate', ''),
                    'studyTime': main_dicom_tags.get('StudyTime', ''),
                    'studyDescription': main_dicom_tags.get('StudyDescription', 'No description'),
                    'accessionNumber': main_dicom_tags.get('AccessionNumber', ''),
                    'seriesCount': len(series_list),
                    'instanceCount': study_data.get('CountInstances', 0)
                })
            
            return studies
        except Exception as e:
            logger.error(f"获取研究列表时出错: {str(e)}")
            raise
    
    def upload_dicom(self, file_path):
        """上传DICOM文件到Orthanc服务器
        
        Args:
            file_path: DICOM文件路径
            
        Returns:
            包含上传结果的字典
        """
        try:
            # 检查文件是否为ZIP压缩包
            if file_path.lower().endswith('.zip'):
                return self._upload_zip(file_path)
            
            # 上传单个DICOM文件
            with open(file_path, 'rb') as f:
                response = self.session.post(
                    f"{self.orthanc_url}/instances",
                    data=f.read(),
                    headers={'Content-Type': 'application/dicom'}
                )
                response.raise_for_status()
                result = response.json()
                
                # 获取研究信息
                study_id = result.get('ParentStudy', '')
                if study_id:
                    study_info = self.session.get(f"{self.orthanc_url}/studies/{study_id}")
                    study_info.raise_for_status()
                    study_data = study_info.json()
                    
                    main_dicom_tags = study_data.get('MainDicomTags', {})
                    return {
                        'status': 'success',
                        'studyID': study_id,
                        'patientName': main_dicom_tags.get('PatientName', 'Unknown'),
                        'patientID': main_dicom_tags.get('PatientID', 'Unknown'),
                        'studyDate': main_dicom_tags.get('StudyDate', ''),
                        'studyDescription': main_dicom_tags.get('StudyDescription', 'No description'),
                        'instanceID': result.get('ID', '')
                    }
                
                return {'status': 'success', 'instanceID': result.get('ID', '')}
        except Exception as e:
            logger.error(f"上传DICOM文件时出错: {str(e)}")
            raise
    
    def _upload_zip(self, zip_path):
        """上传ZIP压缩包中的DICOM文件
        
        Args:
            zip_path: ZIP文件路径
            
        Returns:
            包含上传结果的字典
        """
        temp_dir = tempfile.mkdtemp()
        try:
            # 解压ZIP文件
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            # 上传所有DICOM文件
            study_ids = set()
            instance_ids = []
            
            for root, _, files in os.walk(temp_dir):
                for file in files:
                    if file.lower().endswith(('.dcm', '.dicom')) or not os.path.splitext(file)[1]:
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'rb') as f:
                                response = self.session.post(
                                    f"{self.orthanc_url}/instances",
                                    data=f.read(),
                                    headers={'Content-Type': 'application/dicom'}
                                )
                                response.raise_for_status()
                                result = response.json()
                                instance_ids.append(result.get('ID', ''))
                                
                                if 'ParentStudy' in result:
                                    study_ids.add(result['ParentStudy'])
                        except Exception as e:
                            logger.warning(f"上传文件 {file_path} 时出错: {str(e)}")
            
            # 如果只有一个研究，返回详细信息
            if len(study_ids) == 1:
                study_id = list(study_ids)[0]
                study_info = self.session.get(f"{self.orthanc_url}/studies/{study_id}")
                study_info.raise_for_status()
                study_data = study_info.json()
                
                main_dicom_tags = study_data.get('MainDicomTags', {})
                return {
                    'status': 'success',
                    'studyID': study_id,
                    'patientName': main_dicom_tags.get('PatientName', 'Unknown'),
                    'patientID': main_dicom_tags.get('PatientID', 'Unknown'),
                    'studyDate': main_dicom_tags.get('StudyDate', ''),
                    'studyDescription': main_dicom_tags.get('StudyDescription', 'No description'),
                    'instanceCount': len(instance_ids)
                }
            
            # 多个研究或没有研究
            return {
                'status': 'success',
                'studyIDs': list(study_ids),
                'instanceCount': len(instance_ids)
            }
        except Exception as e:
            logger.error(f"上传ZIP文件时出错: {str(e)}")
            raise
        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    def get_study_info(self, study_id):
        """获取研究详细信息
        
        Args:
            study_id: 研究ID
            
        Returns:
            研究信息字典
        """
        try:
            response = self.session.get(f"{self.orthanc_url}/studies/{study_id}")
            response.raise_for_status()
            study_data = response.json()
            
            main_dicom_tags = study_data.get('MainDicomTags', {})
            patient_main_dicom_tags = study_data.get('PatientMainDicomTags', {})
            
            return {
                'id': study_id,
                'patient_name': patient_main_dicom_tags.get('PatientName', 'Unknown'),
                'patient_id': patient_main_dicom_tags.get('PatientID', 'Unknown'),
                'study_date': main_dicom_tags.get('StudyDate', ''),
                'study_time': main_dicom_tags.get('StudyTime', ''),
                'study_description': main_dicom_tags.get('StudyDescription', 'No description'),
                'accession_number': main_dicom_tags.get('AccessionNumber', ''),
                'series_count': len(study_data.get('Series', [])),
                'instances_count': study_data.get('CountInstances', 0)
            }
        except Exception as e:
            logger.error(f"获取研究信息失败: {str(e)}")
            return None
    
    def get_study_series(self, study_id):
        """获取研究的序列列表
        
        Args:
            study_id: 研究ID
            
        Returns:
            序列信息列表
        """
        try:
            response = self.session.get(f"{self.orthanc_url}/studies/{study_id}")
            response.raise_for_status()
            study_data = response.json()
            
            series_list = []
            for series_id in study_data.get('Series', []):
                series_response = self.session.get(f"{self.orthanc_url}/series/{series_id}")
                series_response.raise_for_status()
                series_data = series_response.json()
                
                main_dicom_tags = series_data.get('MainDicomTags', {})
                series_list.append({
                    'id': series_id,
                    'description': main_dicom_tags.get('SeriesDescription', 'Unknown Series'),
                    'modality': main_dicom_tags.get('Modality', 'Unknown'),
                    'series_number': main_dicom_tags.get('SeriesNumber', ''),
                    'image_count': len(series_data.get('Instances', [])),
                    'instances': series_data.get('Instances', [])
                })
            
            return series_list
        except Exception as e:
            logger.error(f"获取研究序列失败: {str(e)}")
            return []
    
    def get_series_images(self, series_id):
        """获取序列的图像列表
        
        Args:
            series_id: 序列ID
            
        Returns:
            图像信息列表
        """
        try:
            response = self.session.get(f"{self.orthanc_url}/series/{series_id}")
            response.raise_for_status()
            series_data = response.json()
            
            images = []
            for instance_id in series_data.get('Instances', []):
                instance_response = self.session.get(f"{self.orthanc_url}/instances/{instance_id}")
                instance_response.raise_for_status()
                instance_data = instance_response.json()
                
                main_dicom_tags = instance_data.get('MainDicomTags', {})
                images.append({
                    'id': instance_id,
                    'instance_number': main_dicom_tags.get('InstanceNumber', ''),
                    'sop_instance_uid': main_dicom_tags.get('SOPInstanceUID', ''),
                    'image_position_patient': main_dicom_tags.get('ImagePositionPatient', ''),
                    'slice_location': main_dicom_tags.get('SliceLocation', '')
                })
            
            # 按实例号排序
            images.sort(key=lambda x: int(x['instance_number']) if x['instance_number'].isdigit() else 0)
            return images
        except Exception as e:
            logger.error(f"获取序列图像失败: {str(e)}")
            return []
    
    def get_study_instances(self, study_id):
        """获取研究的所有实例
        
        Args:
            study_id: 研究ID
            
        Returns:
            实例信息列表
        """
        try:
            response = self.session.get(f"{self.orthanc_url}/studies/{study_id}")
            response.raise_for_status()
            study_data = response.json()
            
            instances = []
            for series_id in study_data.get('Series', []):
                series_response = self.session.get(f"{self.orthanc_url}/series/{series_id}")
                series_response.raise_for_status()
                series_data = series_response.json()
                
                for instance_id in series_data.get('Instances', []):
                    instances.append({'ID': instance_id})
            
            return instances
        except Exception as e:
            logger.error(f"获取研究实例失败: {str(e)}")
            return []
    
    def get_instance_dicom(self, instance_id):
        """获取实例的DICOM文件数据
        
        Args:
            instance_id: 实例ID
            
        Returns:
            DICOM文件的二进制数据
        """
        return self.get_dicom_file(instance_id)
    
    def get_dicom_file(self, instance_id):
        """获取DICOM文件数据
        
        Args:
            instance_id: 实例ID
            
        Returns:
            DICOM文件的二进制数据
        """
        try:
            response = self.session.get(f"{self.orthanc_url}/instances/{instance_id}/file")
            response.raise_for_status()
            return response.content
        except Exception as e:
            logger.error(f"获取DICOM文件失败: {str(e)}")
            return None

    def download_study(self, study_id, output_dir):
        """下载研究的所有DICOM文件
        
        Args:
            study_id: 研究ID
            output_dir: 输出目录
            
        Returns:
            下载的DICOM文件路径列表
        """
        try:
            # 获取研究中的所有实例
            response = self.session.get(f"{self.orthanc_url}/studies/{study_id}/instances")
            response.raise_for_status()
            instances = response.json()
            
            # 下载每个实例
            dicom_files = []
            for instance in instances:
                instance_id = instance.get('ID', '')
                if not instance_id:
                    continue
                
                # 下载DICOM文件
                dicom_response = self.session.get(
                    f"{self.orthanc_url}/instances/{instance_id}/file",
                    headers={'Accept': 'application/dicom'}
                )
                dicom_response.raise_for_status()
                
                # 保存文件
                output_file = os.path.join(output_dir, f"{instance_id}.dcm")
                with open(output_file, 'wb') as f:
                    f.write(dicom_response.content)
                
                dicom_files.append(output_file)
            
            return dicom_files
        except Exception as e:
            logger.error(f"下载研究 {study_id} 时出错: {str(e)}")
            raise
    
    def save_annotations(self, study_id, detection_results):
        """将检测结果保存为DICOM标注
        
        Args:
            study_id: 研究ID
            detection_results: 检测结果列表
            
        Returns:
            标注ID列表
        """
        try:
            # 获取研究信息
            study_response = self.session.get(f"{self.orthanc_url}/studies/{study_id}")
            study_response.raise_for_status()
            study_info = study_response.json()
            
            # 创建标注
            annotation_ids = []
            for result in detection_results:
                # 获取原始DICOM文件
                instance_id = os.path.splitext(os.path.basename(result['file']))[0]
                instance_response = self.session.get(
                    f"{self.orthanc_url}/instances/{instance_id}"
                )
                instance_response.raise_for_status()
                instance_info = instance_response.json()
                
                # 获取DICOM数据
                dicom_response = self.session.get(
                    f"{self.orthanc_url}/instances/{instance_id}/file",
                    headers={'Accept': 'application/dicom'}
                )
                dicom_response.raise_for_status()
                
                # 创建临时文件
                with tempfile.NamedTemporaryFile(delete=False, suffix='.dcm') as temp_file:
                    temp_file.write(dicom_response.content)
                    temp_path = temp_file.name
                
                try:
                    # 读取原始DICOM
                    ds = pydicom.dcmread(temp_path)
                    
                    # 创建标注DICOM
                    annotation_ds = self._create_annotation_dicom(ds, result['detections'])
                    
                    # 保存标注DICOM
                    annotation_path = temp_path + '.ann.dcm'
                    annotation_ds.save_as(annotation_path)
                    
                    # 上传标注到Orthanc
                    with open(annotation_path, 'rb') as f:
                        ann_response = self.session.post(
                            f"{self.orthanc_url}/instances",
                            data=f.read(),
                            headers={'Content-Type': 'application/dicom'}
                        )
                        ann_response.raise_for_status()
                        annotation_ids.append(ann_response.json().get('ID', ''))
                finally:
                    # 清理临时文件
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                    if os.path.exists(annotation_path):
                        os.remove(annotation_path)
            
            return annotation_ids
        except Exception as e:
            logger.error(f"保存标注时出错: {str(e)}")
            raise
    
    def _create_annotation_dicom(self, source_ds, detections):
        """创建包含检测结果的DICOM标注
        
        Args:
            source_ds: 源DICOM数据集
            detections: 检测结果列表
            
        Returns:
            标注DICOM数据集
        """
        # 创建新的DICOM数据集
        file_meta = Dataset()
        file_meta.MediaStorageSOPClassUID = '1.2.840.10008.5.1.4.1.1.7'  # Secondary Capture Image Storage
        file_meta.MediaStorageSOPInstanceUID = generate_uid()
        file_meta.TransferSyntaxUID = pydicom.uid.ExplicitVRLittleEndian
        
        # 创建文件数据集
        ds = FileDataset(None, {}, file_meta=file_meta, preamble=b"\0" * 128)
        
        # 复制必要的属性
        for attr in ['PatientName', 'PatientID', 'PatientBirthDate', 'PatientSex',
                     'StudyInstanceUID', 'StudyID', 'StudyDate', 'StudyTime',
                     'AccessionNumber', 'ReferringPhysicianName', 'Modality']:
            if hasattr(source_ds, attr):
                setattr(ds, attr, getattr(source_ds, attr))
        
        # 设置新的属性
        ds.SOPClassUID = file_meta.MediaStorageSOPClassUID
        ds.SOPInstanceUID = file_meta.MediaStorageSOPInstanceUID
        ds.SeriesInstanceUID = generate_uid()
        
        # 设置当前日期和时间
        dt = datetime.datetime.now()
        ds.ContentDate = dt.strftime('%Y%m%d')
        ds.ContentTime = dt.strftime('%H%M%S')
        
        # 设置序列信息
        ds.SeriesNumber = 1000  # 使用高序列号以区分原始图像
        ds.SeriesDescription = 'AI Detection Results'
        ds.Manufacturer = 'YOLO-OHIF Detection System'
        
        # 添加检测结果
        detection_text = "AI检测结果:\n"
        for i, detection in enumerate(detections):
            detection_text += f"[{i+1}] 类别: {detection['class']}, 置信度: {detection['confidence']:.2f}, "
            detection_text += f"位置: ({detection['x']:.1f}, {detection['y']:.1f}, {detection['width']:.1f}, {detection['height']:.1f})\n"
        
        # 设置为结构化报告
        ds.Modality = 'SR'  # 结构化报告
        ds.ConversionType = 'WSD'  # 工作站处理
        
        # 添加文本内容
        ds.ContentSequence = [Dataset()]
        ds.ContentSequence[0].RelationshipType = 'CONTAINS'
        ds.ContentSequence[0].ValueType = 'TEXT'
        ds.ContentSequence[0].TextValue = detection_text
        
        return ds
    
    def get_annotations(self, study_id):
        """获取研究的标注
        
        Args:
            study_id: 研究ID
            
        Returns:
            标注信息列表
        """
        try:
            # 获取研究中的所有序列
            response = self.session.get(f"{self.orthanc_url}/studies/{study_id}/series")
            response.raise_for_status()
            series_list = response.json()
            
            annotations = []
            for series in series_list:
                # 获取序列信息
                series_response = self.session.get(f"{self.orthanc_url}/series/{series}")
                series_response.raise_for_status()
                series_info = series_response.json()
                
                # 检查是否为标注序列
                main_dicom_tags = series_info.get('MainDicomTags', {})
                if main_dicom_tags.get('SeriesDescription', '') == 'AI Detection Results':
                    # 获取实例
                    instances = series_info.get('Instances', [])
                    for instance_id in instances:
                        instance_response = self.session.get(
                            f"{self.orthanc_url}/instances/{instance_id}"
                        )
                        instance_response.raise_for_status()
                        instance_info = instance_response.json()
                        
                        annotations.append({
                            'id': instance_id,
                            'seriesID': series,
                            'seriesNumber': main_dicom_tags.get('SeriesNumber', ''),
                            'seriesDescription': main_dicom_tags.get('SeriesDescription', ''),
                            'instanceNumber': instance_info.get('MainDicomTags', {}).get('InstanceNumber', ''),
                            'sopInstanceUID': instance_info.get('MainDicomTags', {}).get('SOPInstanceUID', '')
                        })
            
            return annotations
        except Exception as e:
            logger.error(f"获取标注时出错: {str(e)}")
            raise
    
    def delete_study(self, study_id):
        """删除研究
        
        Args:
            study_id: 研究ID
        """
        try:
            response = self.session.delete(f"{self.orthanc_url}/studies/{study_id}")
            response.raise_for_status()
            logger.info(f"成功删除研究: {study_id}")
        except Exception as e:
            logger.error(f"删除研究时出错: {str(e)}")
            raise