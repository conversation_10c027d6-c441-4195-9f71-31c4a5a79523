# 医学影像智能标注与模型训练系统 - 部署说明

## 📦 发布包创建

### 自动化发布
运行发布脚本创建完整的部署包：

```powershell
# 创建完整发布包（包含Python环境和依赖）
.\创建发布包.ps1

# 自定义发布选项
.\创建发布包.ps1 -OutputPath ".\MyRelease" -IncludePython $true -IncludeModels $true
```

### 手动发布
如果需要手动创建发布包：

```bash
# 1. 发布.NET应用程序
dotnet publish src\MedicalImageAnalysis.Wpf\MedicalImageAnalysis.Wpf.csproj ^
    --configuration Release ^
    --runtime win-x64 ^
    --self-contained true ^
    --output Release\App

# 2. 复制Python环境
python -m venv Release\Python\venv
Release\Python\venv\Scripts\activate
pip install -r yolo_ohif\yolo_ohif\requirements_yolo_training.txt

# 3. 复制数据文件
xcopy Brain Release\Data\DICOM\ /E /I
xcopy yolo_ohif\yolo_ohif\models Release\Data\Models\ /E /I
```

## 📁 发布包目录结构

```
Release/
├── 启动应用程序.bat          # 主应用程序启动脚本
├── 启动训练环境.bat          # Python训练环境启动脚本
├── 用户指南.md              # 用户使用指南
├── App/                     # .NET应用程序
│   ├── MedicalImageAnalysis.Wpf.exe
│   ├── appsettings.json
│   └── [其他应用文件]
├── Data/                    # 数据目录
│   ├── DICOM/              # DICOM影像文件
│   │   ├── DJ01.dcm        # 示例脑部CT文件
│   │   ├── DJ02.dcm
│   │   └── [用户DICOM文件]
│   ├── Models/             # YOLO模型文件
│   │   ├── yolo11n.pt      # 轻量级模型
│   │   ├── yolo11x.pt      # 高精度模型
│   │   └── [自定义模型]
│   ├── Datasets/           # 训练数据集
│   │   ├── Train/
│   │   │   ├── images/     # 训练图像
│   │   │   └── labels/     # 训练标签(YOLO格式)
│   │   ├── Val/
│   │   │   ├── images/     # 验证图像
│   │   │   └── labels/     # 验证标签
│   │   └── Test/
│   │       ├── images/     # 测试图像
│   │       └── labels/     # 测试标签
│   ├── Output/             # 处理结果输出
│   ├── Temp/               # 临时文件
│   └── Logs/               # 日志文件
├── Python/                 # Python训练环境
│   ├── venv/               # Python虚拟环境
│   ├── start_yolo11x_training.py
│   ├── train_yolo11x_from_scratch.py
│   └── [其他训练脚本]
├── Scripts/                # 辅助脚本
│   └── 验证训练环境.py
├── Docs/                   # 文档
│   ├── 智能打标教程.md
│   ├── 模型训练教程.md
│   ├── 快速入门指南.md
│   └── 教程使用说明.md
└── Examples/               # 示例文件
    └── [示例数据]
```

## 🎯 文件放置位置说明

### 📄 DICOM影像文件
**位置**: `Data\DICOM\`
**格式**: .dcm, .dicom
**说明**: 
- 将您的医学影像DICOM文件放置在此目录
- 系统已包含示例脑部CT文件（DJ01.dcm - DJ10.dcm）
- 支持单个文件或批量文件
- 文件名可以是任意格式

**示例**:
```
Data\DICOM\
├── DJ01.dcm                 # 示例文件
├── patient001_ct_001.dcm    # 患者CT文件
├── brain_mri_t1.dcm         # 脑部MRI文件
└── chest_xray.dcm           # 胸部X光文件
```

### 🤖 YOLO模型文件
**位置**: `Data\Models\`
**格式**: .pt, .onnx, .engine
**说明**:
- 将训练好的YOLO模型文件放置在此目录
- 支持PyTorch (.pt)、ONNX (.onnx)、TensorRT (.engine) 格式
- 推荐使用YOLO11系列模型

**推荐模型**:
```
Data\Models\
├── yolo11n.pt              # 轻量级模型（快速推理）
├── yolo11s.pt              # 小型模型（平衡性能）
├── yolo11m.pt              # 中型模型（较高精度）
├── yolo11l.pt              # 大型模型（高精度）
├── yolo11x.pt              # 超大型模型（最高精度）
├── custom_medical.pt       # 自定义医学模型
└── pretrained/             # 预训练模型子目录
    └── [预训练模型文件]
```

### 📊 训练数据集
**位置**: `Data\Datasets\`
**格式**: YOLO格式（图像 + 标签文件）
**说明**:
- 按照Train/Val/Test结构组织数据
- 图像和标签文件名必须对应
- 标签文件采用YOLO格式（归一化坐标）

**数据集结构**:
```
Data\Datasets\
├── Train\                  # 训练集（70%数据）
│   ├── images\
│   │   ├── image001.jpg
│   │   ├── image002.jpg
│   │   └── ...
│   └── labels\
│       ├── image001.txt    # 对应的标签文件
│       ├── image002.txt
│       └── ...
├── Val\                    # 验证集（20%数据）
│   ├── images\
│   └── labels\
└── Test\                   # 测试集（10%数据）
    ├── images\
    └── labels\
```

**YOLO标签格式**:
```
# 每行一个目标，格式：class_id center_x center_y width height
0 0.5 0.5 0.3 0.4          # 类别0，中心点(0.5,0.5)，宽0.3高0.4
1 0.2 0.3 0.1 0.2          # 类别1，中心点(0.2,0.3)，宽0.1高0.2
```

### 📈 训练结果输出
**位置**: `Data\Output\`
**内容**: 
- 训练完成的模型权重
- 训练过程图表和日志
- 模型评估结果
- 推理结果图像

### 📝 日志文件
**位置**: `Data\Logs\`
**内容**:
- 应用程序运行日志
- 训练过程日志
- 错误和调试信息

## 🚀 部署和分发

### 系统要求
- **操作系统**: Windows 10/11 (x64)
- **内存**: 至少 8GB RAM (推荐 16GB)
- **存储**: 至少 10GB 可用空间
- **显卡**: NVIDIA GPU (推荐，用于训练加速)
- **.NET Runtime**: 自包含，无需额外安装

### 分发方式

#### 方式1：压缩包分发
1. 将整个Release文件夹压缩为ZIP文件
2. 用户下载后解压到任意目录
3. 双击"启动应用程序.bat"即可使用

#### 方式2：安装程序分发
1. 使用NSIS创建安装程序
2. 自动创建桌面快捷方式
3. 注册文件关联和卸载程序

#### 方式3：便携版分发
1. 创建便携版文件夹
2. 包含所有必需文件和依赖
3. 无需安装，直接运行

### 用户安装步骤
1. **下载**: 下载发布包压缩文件
2. **解压**: 解压到目标目录（如：C:\MedicalImageAnalysis）
3. **启动**: 双击"启动应用程序.bat"
4. **验证**: 运行环境验证脚本检查配置

## 🔧 配置和自定义

### 应用程序配置
编辑 `App\appsettings.json` 文件：

```json
{
  "Application": {
    "DataDirectory": "../Data",
    "TempDirectory": "../Data/Temp",
    "LogDirectory": "../Data/Logs"
  },
  "Processing": {
    "DefaultConfidenceThreshold": 0.7,
    "MaxFileSize": 524288000
  },
  "Training": {
    "DefaultEpochs": 100,
    "DefaultBatchSize": 16,
    "ModelOutputDirectory": "../Data/Models"
  }
}
```

### Python环境配置
激活Python环境并安装额外包：

```bash
# 激活虚拟环境
Data\Python\venv\Scripts\activate

# 安装额外依赖
pip install [package_name]

# 验证环境
python Scripts\验证训练环境.py
```

## 📞 技术支持

### 常见问题
1. **应用无法启动**: 检查.NET运行时和文件权限
2. **Python环境问题**: 重新创建虚拟环境
3. **模型加载失败**: 检查模型文件格式和路径
4. **训练速度慢**: 检查GPU驱动和CUDA环境

### 获取帮助
1. 查看 `Docs\` 目录中的详细教程
2. 检查 `Data\Logs\` 中的日志文件
3. 运行环境验证脚本诊断问题
4. 联系技术支持团队

---

**部署成功后，用户即可开始使用医学影像智能标注与模型训练系统！** 🎉
