"""模型管理服务

重构后的模型管理器，采用现代化架构模式
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import replace

from ..core.interfaces import ModelManagerInterface, CacheManagerInterface
from ..core.types import ModelConfig, ModelType, ImageModality
from ..core.exceptions import (
    ModelNotFoundError, 
    ValidationError, 
    ConfigurationError,
    create_model_not_found_error,
    create_validation_error
)

logger = logging.getLogger(__name__)


class ModelManagerService(ModelManagerInterface):
    """模型管理服务
    
    负责管理AI模型的配置、状态和元数据
    支持依赖注入和缓存
    """
    
    def __init__(self, 
                 models_config: List[Dict[str, Any]],
                 cache_manager: Optional[CacheManagerInterface] = None):
        """初始化模型管理服务
        
        Args:
            models_config: 模型配置列表
            cache_manager: 缓存管理器（可选）
        """
        self._models_config_raw = models_config
        self._cache_manager = cache_manager
        self._models: Dict[str, ModelConfig] = {}
        self._active_model_id: Optional[str] = None
        self._is_initialized = False
        
        logger.info(f"模型管理服务创建，配置了 {len(models_config)} 个模型")
    
    def initialize(self) -> bool:
        """初始化模型管理服务"""
        try:
            # 验证和加载模型配置
            self._validate_and_load_models()
            
            # 设置默认活跃模型
            if self._models:
                first_model_id = next(iter(self._models.keys()))
                self.set_active_model(first_model_id)
            
            self._is_initialized = True
            logger.info(f"模型管理服务初始化成功，加载了 {len(self._models)} 个模型")
            return True
            
        except Exception as e:
            logger.error(f"模型管理服务初始化失败: {e}")
            return False
    
    def _validate_and_load_models(self) -> None:
        """验证并加载模型配置"""
        if not self._models_config_raw:
            logger.warning("没有提供模型配置")
            return
        
        # 验证模型ID唯一性
        model_ids = [config.get('id') for config in self._models_config_raw]
        if len(model_ids) != len(set(model_ids)):
            raise ValidationError("模型ID必须唯一", validation_errors=["Duplicate model IDs found"])
        
        # 加载每个模型配置
        for config_dict in self._models_config_raw:
            try:
                model_config = self._create_model_config(config_dict)
                self._models[model_config.id] = model_config
                logger.debug(f"加载模型配置: {model_config.id} - {model_config.name}")
            except Exception as e:
                logger.error(f"加载模型配置失败: {config_dict.get('id', 'unknown')}, 错误: {e}")
                raise ConfigurationError(
                    f"Failed to load model config: {e}",
                    config_field="models_details",
                    cause=e
                )
    
    def _create_model_config(self, config_dict: Dict[str, Any]) -> ModelConfig:
        """从字典创建模型配置"""
        # 验证必需字段
        required_fields = ['id', 'name', 'predictionApi']
        missing_fields = [field for field in required_fields if field not in config_dict]
        if missing_fields:
            raise ValidationError(
                f"Missing required fields: {missing_fields}",
                validation_errors=[f"Missing field: {field}" for field in missing_fields]
            )
        
        # 转换模型类型
        model_type_str = config_dict.get('modelType', 'yolo')
        try:
            model_type = ModelType(model_type_str)
        except ValueError:
            logger.warning(f"Unknown model type '{model_type_str}', using CUSTOM")
            model_type = ModelType.CUSTOM
        
        # 转换支持的模态
        supported_modalities = []
        modalities_raw = config_dict.get('supportedModalities', ['CR', 'DX'])
        for modality_str in modalities_raw:
            try:
                supported_modalities.append(ImageModality(modality_str))
            except ValueError:
                logger.warning(f"Unknown modality '{modality_str}', skipping")
        
        return ModelConfig(
            id=config_dict['id'],
            name=config_dict['name'],
            description=config_dict.get('description', ''),
            model_type=model_type,
            prediction_api=config_dict['predictionApi'],
            info_api=config_dict.get('infoApi'),
            input_format=config_dict.get('inputFormat', 'dicom'),
            output_format=config_dict.get('outputFormat', 'bbox'),
            supported_modalities=supported_modalities,
            class_names=config_dict.get('classNames', []),
            confidence_threshold=config_dict.get('confidenceThreshold', 0.1),
            iou_threshold=config_dict.get('iouThreshold', 0.45),
            max_detections=config_dict.get('maxDetections', 100),
            input_size=tuple(config_dict['inputSize']) if config_dict.get('inputSize') else None,
            preprocessing_config=config_dict.get('preprocessingConfig', {}),
            postprocessing_config=config_dict.get('postprocessingConfig', {}),
            metadata=config_dict.get('metadata', {})
        )
    
    def get_available_models(self) -> List[ModelConfig]:
        """获取可用模型列表"""
        if not self._is_initialized:
            logger.warning("模型管理服务未初始化")
            return []
        
        # 尝试从缓存获取
        cache_key = "available_models"
        if self._cache_manager:
            cached_models = self._cache_manager.get(cache_key)
            if cached_models:
                logger.debug("从缓存获取可用模型列表")
                return cached_models
        
        models = list(self._models.values())
        
        # 缓存结果
        if self._cache_manager:
            self._cache_manager.set(cache_key, models, ttl=300)  # 缓存5分钟
        
        return models
    
    def get_model_config(self, model_id: str) -> Optional[ModelConfig]:
        """获取指定模型配置"""
        if not self._is_initialized:
            logger.warning("模型管理服务未初始化")
            return None
        
        model_config = self._models.get(model_id)
        if model_config:
            # 更新最后使用时间
            updated_config = replace(model_config, last_used=datetime.now())
            self._models[model_id] = updated_config
            return updated_config
        
        logger.warning(f"未找到模型: {model_id}")
        return None
    
    def add_model(self, model_config_dict: Dict[str, Any]) -> bool:
        """添加新模型"""
        try:
            model_config = self._create_model_config(model_config_dict)
            
            if model_config.id in self._models:
                logger.warning(f"模型 {model_config.id} 已存在，将被覆盖")
            
            self._models[model_config.id] = model_config
            
            # 清除缓存
            if self._cache_manager:
                self._cache_manager.delete("available_models")
            
            logger.info(f"成功添加模型: {model_config.id} - {model_config.name}")
            return True
            
        except Exception as e:
            logger.error(f"添加模型失败: {e}")
            return False
    
    def remove_model(self, model_id: str) -> bool:
        """移除模型"""
        if model_id not in self._models:
            logger.warning(f"尝试移除不存在的模型: {model_id}")
            return False
        
        # 如果是活跃模型，需要切换到其他模型
        if self._active_model_id == model_id:
            remaining_models = [mid for mid in self._models.keys() if mid != model_id]
            if remaining_models:
                self.set_active_model(remaining_models[0])
            else:
                self._active_model_id = None
        
        del self._models[model_id]
        
        # 清除缓存
        if self._cache_manager:
            self._cache_manager.delete("available_models")
        
        logger.info(f"成功移除模型: {model_id}")
        return True
    
    def set_active_model(self, model_id: str) -> bool:
        """设置活跃模型"""
        if model_id not in self._models:
            available_ids = list(self._models.keys())
            raise create_model_not_found_error(model_id, available_ids)
        
        # 更新之前的活跃模型状态
        if self._active_model_id and self._active_model_id in self._models:
            old_config = self._models[self._active_model_id]
            self._models[self._active_model_id] = replace(old_config, is_active=False)
        
        # 设置新的活跃模型
        new_config = self._models[model_id]
        self._models[model_id] = replace(new_config, is_active=True)
        self._active_model_id = model_id
        
        logger.info(f"设置活跃模型: {model_id} - {new_config.name}")
        return True
    
    def get_active_model(self) -> Optional[ModelConfig]:
        """获取当前活跃模型"""
        if self._active_model_id:
            return self._models.get(self._active_model_id)
        return None
    
    def get_models_by_modality(self, modality: str) -> List[ModelConfig]:
        """根据影像模态获取支持的模型"""
        try:
            target_modality = ImageModality(modality)
        except ValueError:
            logger.warning(f"未知的影像模态: {modality}")
            return []
        
        matching_models = [
            model for model in self._models.values()
            if target_modality in model.supported_modalities
        ]
        
        logger.debug(f"找到 {len(matching_models)} 个支持 {modality} 模态的模型")
        return matching_models
    
    def update_model_usage(self, model_id: str) -> None:
        """更新模型使用统计"""
        if model_id in self._models:
            config = self._models[model_id]
            updated_config = replace(
                config,
                usage_count=config.usage_count + 1,
                last_used=datetime.now()
            )
            self._models[model_id] = updated_config
    
    def get_model_statistics(self) -> Dict[str, Any]:
        """获取模型统计信息"""
        if not self._models:
            return {'total_models': 0}
        
        total_usage = sum(model.usage_count for model in self._models.values())
        most_used_model = max(self._models.values(), key=lambda m: m.usage_count)
        
        modality_support = {}
        for modality in ImageModality:
            supporting_models = len(self.get_models_by_modality(modality.value))
            if supporting_models > 0:
                modality_support[modality.value] = supporting_models
        
        return {
            'total_models': len(self._models),
            'active_model': self._active_model_id,
            'total_usage': total_usage,
            'most_used_model': {
                'id': most_used_model.id,
                'name': most_used_model.name,
                'usage_count': most_used_model.usage_count
            },
            'modality_support': modality_support,
            'model_types': list(set(model.model_type.value for model in self._models.values()))
        }
    
    def cleanup(self) -> None:
        """清理资源"""
        self._models.clear()
        self._active_model_id = None
        self._is_initialized = False
        
        if self._cache_manager:
            self._cache_manager.clear()
        
        logger.info("模型管理服务已清理")
    
    @property
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self._is_initialized
    
    @property
    def model_count(self) -> int:
        """获取模型数量"""
        return len(self._models)