import os
import numpy as np
from pathlib import Path

def validate_final_bbox_labels():
    """验证最终生成的bbox标注文件质量"""
    
    bbox_dir = r"e:\Trae\yolo_ohif\sliced_output\bbox_labels"
    
    # 统计信息
    stats = {
        'total_files': 0,
        'total_bboxes': 0,
        'bbox_size_distribution': {
            'small': 0,      # 面积 < 0.1
            'medium': 0,     # 0.1 <= 面积 < 0.3
            'large': 0,      # 0.3 <= 面积 < 0.7
            'very_large': 0  # 面积 >= 0.7
        },
        'dimension_stats': {
            'width': [],
            'height': [],
            'area': []
        }
    }
    
    # 获取所有标注文件
    txt_files = [f for f in os.listdir(bbox_dir) if f.endswith('.txt')]
    stats['total_files'] = len(txt_files)
    
    print(f"总标注文件数量: {stats['total_files']}")
    
    # 分析每个标注文件
    for txt_file in txt_files:
        txt_path = os.path.join(bbox_dir, txt_file)
        
        with open(txt_path, 'r') as f:
            lines = f.readlines()
            
        for line in lines:
            if line.strip():
                stats['total_bboxes'] += 1
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id, x_center, y_center, width, height = map(float, parts[:5])
                    
                    # 计算面积
                    area = width * height
                    
                    # 记录统计信息
                    stats['dimension_stats']['width'].append(width)
                    stats['dimension_stats']['height'].append(height)
                    stats['dimension_stats']['area'].append(area)
                    
                    # 分类边界框大小
                    if area < 0.1:
                        stats['bbox_size_distribution']['small'] += 1
                    elif area < 0.3:
                        stats['bbox_size_distribution']['medium'] += 1
                    elif area < 0.7:
                        stats['bbox_size_distribution']['large'] += 1
                    else:
                        stats['bbox_size_distribution']['very_large'] += 1
    
    # 计算统计数据
    widths = np.array(stats['dimension_stats']['width'])
    heights = np.array(stats['dimension_stats']['height'])
    areas = np.array(stats['dimension_stats']['area'])
    
    print(f"\n边界框统计:")
    print(f"总边界框数量: {stats['total_bboxes']}")
    print(f"平均每个文件的边界框数量: {stats['total_bboxes'] / stats['total_files']:.2f}")
    
    print(f"\n边界框尺寸分布:")
    for size_type, count in stats['bbox_size_distribution'].items():
        percentage = (count / stats['total_bboxes']) * 100 if stats['total_bboxes'] > 0 else 0
        print(f"  {size_type}: {count} ({percentage:.1f}%)")
    
    print(f"\n边界框尺寸统计:")
    print(f"宽度 - 最小: {widths.min():.3f}, 最大: {widths.max():.3f}, 平均: {widths.mean():.3f}")
    print(f"高度 - 最小: {heights.min():.3f}, 最大: {heights.max():.3f}, 平均: {heights.mean():.3f}")
    print(f"面积 - 最小: {areas.min():.3f}, 最大: {areas.max():.3f}, 平均: {areas.mean():.3f}")
    
    # 检查是否还有覆盖整张图像的边界框
    large_bboxes = [(w, h, a) for w, h, a in zip(widths, heights, areas) if w > 0.95 or h > 0.95 or a > 0.9]
    if large_bboxes:
        print(f"\n警告: 仍有 {len(large_bboxes)} 个过大的边界框:")
        for i, (w, h, a) in enumerate(large_bboxes[:5]):  # 只显示前5个
            print(f"  边界框 {i+1}: 宽度={w:.3f}, 高度={h:.3f}, 面积={a:.3f}")
    else:
        print(f"\n✓ 成功过滤掉所有覆盖整张图像的边界框")
    
    return stats

if __name__ == "__main__":
    validate_final_bbox_labels()