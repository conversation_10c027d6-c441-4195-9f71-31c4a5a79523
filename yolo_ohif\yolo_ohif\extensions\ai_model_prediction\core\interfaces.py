"""核心接口定义

定义扩展中各组件的抽象接口，支持依赖注入和测试
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from .types import ModelConfig, PredictionResult, DetectionResult, ExtensionConfig


class ModelManagerInterface(ABC):
    """模型管理器接口"""
    
    @abstractmethod
    def initialize(self) -> bool:
        """初始化模型管理器"""
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[ModelConfig]:
        """获取可用模型列表"""
        pass
    
    @abstractmethod
    def get_model_config(self, model_id: str) -> Optional[ModelConfig]:
        """获取指定模型配置"""
        pass
    
    @abstractmethod
    def add_model(self, model_config: Dict) -> bool:
        """添加新模型"""
        pass
    
    @abstractmethod
    def remove_model(self, model_id: str) -> bool:
        """移除模型"""
        pass
    
    @abstractmethod
    def set_active_model(self, model_id: str) -> bool:
        """设置活跃模型"""
        pass
    
    @abstractmethod
    def get_active_model(self) -> Optional[ModelConfig]:
        """获取当前活跃模型"""
        pass
    
    @abstractmethod
    def get_models_by_modality(self, modality: str) -> List[ModelConfig]:
        """根据影像模态获取支持的模型"""
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """清理资源"""
        pass


class PredictionServiceInterface(ABC):
    """预测服务接口"""
    
    @abstractmethod
    async def predict_async(self, 
                           model_config: ModelConfig, 
                           image_data: Any, 
                           options: Optional[Dict] = None) -> PredictionResult:
        """异步预测"""
        pass
    
    @abstractmethod
    def predict(self, 
               model_config: ModelConfig, 
               image_data: Any, 
               options: Optional[Dict] = None) -> PredictionResult:
        """同步预测"""
        pass
    
    @abstractmethod
    def validate_input(self, image_data: Any, model_config: ModelConfig) -> bool:
        """验证输入数据"""
        pass
    
    @abstractmethod
    def preprocess_image(self, image_data: Any, model_config: ModelConfig) -> Any:
        """预处理图像"""
        pass
    
    @abstractmethod
    def postprocess_result(self, raw_result: Any, model_config: ModelConfig) -> PredictionResult:
        """后处理结果"""
        pass


class UIComponentsInterface(ABC):
    """UI组件接口"""
    
    @abstractmethod
    def initialize(self, viewer_instance: Any) -> bool:
        """初始化UI组件"""
        pass
    
    @abstractmethod
    def update_bounding_boxes(self, detections: List[DetectionResult]) -> None:
        """更新边界框显示"""
        pass
    
    @abstractmethod
    def show_prediction_results(self, result: PredictionResult) -> None:
        """显示预测结果"""
        pass
    
    @abstractmethod
    def show_model_selector(self, models: List[ModelConfig]) -> None:
        """显示模型选择器"""
        pass
    
    @abstractmethod
    def show_loading_indicator(self, show: bool = True) -> None:
        """显示加载指示器"""
        pass
    
    @abstractmethod
    def show_error_message(self, message: str, error_type: str = 'error') -> None:
        """显示错误消息"""
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """清理UI资源"""
        pass


class ConfigValidatorInterface(ABC):
    """配置验证器接口"""
    
    @abstractmethod
    def validate_extension_config(self, config: Dict) -> tuple[bool, List[str]]:
        """验证扩展配置"""
        pass
    
    @abstractmethod
    def validate_model_config(self, model_config: Dict) -> tuple[bool, List[str]]:
        """验证模型配置"""
        pass
    
    @abstractmethod
    def validate_prediction_options(self, options: Dict) -> tuple[bool, List[str]]:
        """验证预测选项"""
        pass


class EventManagerInterface(ABC):
    """事件管理器接口"""
    
    @abstractmethod
    def register_event_listener(self, event_type: str, callback: callable) -> None:
        """注册事件监听器"""
        pass
    
    @abstractmethod
    def unregister_event_listener(self, event_type: str, callback: callable) -> None:
        """注销事件监听器"""
        pass
    
    @abstractmethod
    def emit_event(self, event_type: str, data: Any = None) -> None:
        """触发事件"""
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """清理事件管理器"""
        pass


class CacheManagerInterface(ABC):
    """缓存管理器接口"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """清空缓存"""
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """清理缓存管理器"""
        pass