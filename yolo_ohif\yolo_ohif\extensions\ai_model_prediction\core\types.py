"""类型定义

定义扩展中使用的数据类型和结构
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union, Literal
from datetime import datetime
from enum import Enum


class ModelType(Enum):
    """模型类型枚举"""
    YOLO = "yolo"
    DETECTRON = "detectron"
    TENSORFLOW = "tensorflow"
    PYTORCH = "pytorch"
    ONNX = "onnx"
    CUSTOM = "custom"


class ImageModality(Enum):
    """影像模态枚举"""
    CR = "CR"  # Computed Radiography
    DX = "DX"  # Digital Radiography
    CT = "CT"  # Computed Tomography
    MR = "MR"  # Magnetic Resonance
    US = "US"  # Ultrasound
    XA = "XA"  # X-Ray Angiography
    MG = "MG"  # Mammography
    PT = "PT"  # Positron Emission Tomography
    NM = "NM"  # Nuclear Medicine


class PredictionStatus(Enum):
    """预测状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class SeverityLevel(Enum):
    """严重程度枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class BoundingBox:
    """边界框数据类"""
    x: float
    y: float
    width: float
    height: float
    
    def to_dict(self) -> Dict[str, float]:
        return {
            'x': self.x,
            'y': self.y,
            'width': self.width,
            'height': self.height
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, float]) -> 'BoundingBox':
        return cls(
            x=data['x'],
            y=data['y'],
            width=data['width'],
            height=data['height']
        )


@dataclass
class DetectionResult:
    """检测结果数据类"""
    class_name: str
    confidence: float
    bbox: BoundingBox
    class_id: Optional[int] = None
    attributes: Dict[str, Any] = field(default_factory=dict)
    severity: SeverityLevel = SeverityLevel.LOW
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'class_name': self.class_name,
            'confidence': self.confidence,
            'bbox': self.bbox.to_dict(),
            'class_id': self.class_id,
            'attributes': self.attributes,
            'severity': self.severity.value
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DetectionResult':
        return cls(
            class_name=data['class_name'],
            confidence=data['confidence'],
            bbox=BoundingBox.from_dict(data['bbox']),
            class_id=data.get('class_id'),
            attributes=data.get('attributes', {}),
            severity=SeverityLevel(data.get('severity', 'low'))
        )


@dataclass
class PredictionMetrics:
    """预测指标数据类"""
    inference_time: float  # 推理时间（秒）
    preprocessing_time: float  # 预处理时间（秒）
    postprocessing_time: float  # 后处理时间（秒）
    total_time: float  # 总时间（秒）
    memory_usage: Optional[float] = None  # 内存使用（MB）
    gpu_usage: Optional[float] = None  # GPU使用率（%）
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'inference_time': self.inference_time,
            'preprocessing_time': self.preprocessing_time,
            'postprocessing_time': self.postprocessing_time,
            'total_time': self.total_time,
            'memory_usage': self.memory_usage,
            'gpu_usage': self.gpu_usage
        }


@dataclass
class PredictionResult:
    """预测结果数据类"""
    success: bool
    detections: List[DetectionResult]
    model_id: str
    model_name: str
    timestamp: datetime
    metrics: Optional[PredictionMetrics] = None
    error_message: Optional[str] = None
    status: PredictionStatus = PredictionStatus.COMPLETED
    attributes: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'success': self.success,
            'detections': [d.to_dict() for d in self.detections],
            'model_id': self.model_id,
            'model_name': self.model_name,
            'timestamp': self.timestamp.isoformat(),
            'metrics': self.metrics.to_dict() if self.metrics else None,
            'error_message': self.error_message,
            'status': self.status.value,
            'attributes': self.attributes
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PredictionResult':
        return cls(
            success=data['success'],
            detections=[DetectionResult.from_dict(d) for d in data['detections']],
            model_id=data['model_id'],
            model_name=data['model_name'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            metrics=PredictionMetrics(**data['metrics']) if data.get('metrics') else None,
            error_message=data.get('error_message'),
            status=PredictionStatus(data.get('status', 'completed')),
            attributes=data.get('attributes', {})
        )


@dataclass
class ModelConfig:
    """模型配置数据类"""
    id: str
    name: str
    description: str
    model_type: ModelType
    prediction_api: str
    info_api: Optional[str] = None
    input_format: str = "dicom"
    output_format: str = "bbox"
    supported_modalities: List[ImageModality] = field(default_factory=lambda: [ImageModality.CR, ImageModality.DX])
    class_names: List[str] = field(default_factory=list)
    confidence_threshold: float = 0.1
    iou_threshold: float = 0.45
    max_detections: int = 100
    input_size: Optional[tuple[int, int]] = None
    preprocessing_config: Dict[str, Any] = field(default_factory=dict)
    postprocessing_config: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    is_active: bool = False
    is_loaded: bool = False
    load_time: Optional[datetime] = None
    last_used: Optional[datetime] = None
    usage_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'model_type': self.model_type.value,
            'prediction_api': self.prediction_api,
            'info_api': self.info_api,
            'input_format': self.input_format,
            'output_format': self.output_format,
            'supported_modalities': [m.value for m in self.supported_modalities],
            'class_names': self.class_names,
            'confidence_threshold': self.confidence_threshold,
            'iou_threshold': self.iou_threshold,
            'max_detections': self.max_detections,
            'input_size': self.input_size,
            'preprocessing_config': self.preprocessing_config,
            'postprocessing_config': self.postprocessing_config,
            'metadata': self.metadata,
            'is_active': self.is_active,
            'is_loaded': self.is_loaded,
            'load_time': self.load_time.isoformat() if self.load_time else None,
            'last_used': self.last_used.isoformat() if self.last_used else None,
            'usage_count': self.usage_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelConfig':
        return cls(
            id=data['id'],
            name=data['name'],
            description=data['description'],
            model_type=ModelType(data['model_type']),
            prediction_api=data['prediction_api'],
            info_api=data.get('info_api'),
            input_format=data.get('input_format', 'dicom'),
            output_format=data.get('output_format', 'bbox'),
            supported_modalities=[ImageModality(m) for m in data.get('supported_modalities', ['CR', 'DX'])],
            class_names=data.get('class_names', []),
            confidence_threshold=data.get('confidence_threshold', 0.1),
            iou_threshold=data.get('iou_threshold', 0.45),
            max_detections=data.get('max_detections', 100),
            input_size=tuple(data['input_size']) if data.get('input_size') else None,
            preprocessing_config=data.get('preprocessing_config', {}),
            postprocessing_config=data.get('postprocessing_config', {}),
            metadata=data.get('metadata', {}),
            is_active=data.get('is_active', False),
            is_loaded=data.get('is_loaded', False),
            load_time=datetime.fromisoformat(data['load_time']) if data.get('load_time') else None,
            last_used=datetime.fromisoformat(data['last_used']) if data.get('last_used') else None,
            usage_count=data.get('usage_count', 0)
        )


@dataclass
class ExtensionOptions:
    """扩展选项数据类"""
    mail_to: str = "<EMAIL>"
    enable_bounding_boxes: bool = True
    enable_reports: bool = True
    auto_detection: bool = False
    show_confidence_threshold: bool = True
    default_confidence_threshold: float = 0.1
    max_detections_per_image: int = 50
    enable_model_switching: bool = True
    enable_batch_processing: bool = False
    cache_predictions: bool = True
    cache_ttl: int = 3600  # 缓存TTL（秒）
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'mailTo': self.mail_to,
            'enableBoundingBoxes': self.enable_bounding_boxes,
            'enableReports': self.enable_reports,
            'autoDetection': self.auto_detection,
            'showConfidenceThreshold': self.show_confidence_threshold,
            'defaultConfidenceThreshold': self.default_confidence_threshold,
            'maxDetectionsPerImage': self.max_detections_per_image,
            'enableModelSwitching': self.enable_model_switching,
            'enableBatchProcessing': self.enable_batch_processing,
            'cachePredictions': self.cache_predictions,
            'cacheTTL': self.cache_ttl
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExtensionOptions':
        return cls(
            mail_to=data.get('mailTo', '<EMAIL>'),
            enable_bounding_boxes=data.get('enableBoundingBoxes', True),
            enable_reports=data.get('enableReports', True),
            auto_detection=data.get('autoDetection', False),
            show_confidence_threshold=data.get('showConfidenceThreshold', True),
            default_confidence_threshold=data.get('defaultConfidenceThreshold', 0.1),
            max_detections_per_image=data.get('maxDetectionsPerImage', 50),
            enable_model_switching=data.get('enableModelSwitching', True),
            enable_batch_processing=data.get('enableBatchProcessing', False),
            cache_predictions=data.get('cachePredictions', True),
            cache_ttl=data.get('cacheTTL', 3600)
        )


@dataclass
class ExtensionConfig:
    """扩展配置数据类"""
    options: ExtensionOptions
    models_details: List[Dict[str, Any]]
    api_endpoints: Dict[str, str] = field(default_factory=dict)
    version: str = "1.0.0"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'options': self.options.to_dict(),
            'models_details': self.models_details,
            'api_endpoints': self.api_endpoints,
            'version': self.version
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExtensionConfig':
        return cls(
            options=ExtensionOptions.from_dict(data.get('options', {})),
            models_details=data.get('models_details', []),
            api_endpoints=data.get('api_endpoints', {}),
            version=data.get('version', '1.0.0')
        )


# 类型别名
ImageData = Union[bytes, str, Any]  # 图像数据类型
ViewerInstance = Any  # 查看器实例类型
APIResponse = Dict[str, Any]  # API响应类型
EventCallback = callable  # 事件回调类型