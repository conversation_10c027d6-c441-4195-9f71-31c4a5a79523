#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证DICOM处理修复是否有效
"""

import os
import sys
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def verify_normalization_fix():
    """
    验证归一化方法是否已修复
    """
    print("=== 验证归一化方法修复 ===")
    
    try:
        from services.detection_service import DetectionService
        
        # 创建检测服务实例
        detection_service = DetectionService(
            model_path="E:/Trae/yolo_ohif/yolo11x_training_output/training_results/weights/best.pt",
            confidence_threshold=0.25
        )
        
        # 创建测试图像（模拟16位医学图像）
        test_img = np.random.randint(0, 4096, (512, 512), dtype=np.uint16)
        
        # 测试新的归一化方法
        normalized_img = detection_service._normalize_to_uint8(test_img)
        
        print(f"✓ 归一化方法测试成功")
        print(f"  原始图像: {test_img.dtype}, 范围 {test_img.min()}-{test_img.max()}")
        print(f"  归一化后: {normalized_img.dtype}, 范围 {normalized_img.min()}-{normalized_img.max()}")
        
        # 验证归一化结果
        if normalized_img.dtype == np.uint8 and normalized_img.min() >= 0 and normalized_img.max() <= 255:
            print("✓ 归一化结果正确")
            return True
        else:
            print("✗ 归一化结果异常")
            return False
            
    except Exception as e:
        print(f"✗ 归一化方法测试失败: {e}")
        return False

def check_training_vs_inference_consistency():
    """
    检查训练和推理的数据处理一致性
    """
    print("\n=== 检查训练vs推理数据处理一致性 ===")
    
    # 模拟训练时的归一化（来自slice_nii_to_jpg.py）
    def training_normalize(img_array):
        img_float = img_array.astype(np.float32)
        if img_float.max() > img_float.min():
            return ((img_float - img_float.min()) / 
                   (img_float.max() - img_float.min()) * 255).astype(np.uint8)
        else:
            return np.zeros_like(img_float, dtype=np.uint8)
    
    # 模拟推理时的归一化（修复后的detection_service）
    def inference_normalize(img_array):
        try:
            from services.detection_service import DetectionService
            ds = DetectionService(
                model_path="E:/Trae/yolo_ohif/yolo11x_training_output/training_results/weights/best.pt",
                confidence_threshold=0.25
            )
            return ds._normalize_to_uint8(img_array)
        except:
            # 如果无法加载服务，使用相同的逻辑
            img_float = img_array.astype(np.float32)
            if img_float.max() > img_float.min():
                return ((img_float - img_float.min()) / 
                       (img_float.max() - img_float.min()) * 255).astype(np.uint8)
            else:
                return np.zeros_like(img_float, dtype=np.uint8)
    
    # 创建测试图像
    test_img = np.random.randint(0, 4096, (100, 100), dtype=np.uint16)
    
    # 应用两种归一化方法
    training_result = training_normalize(test_img)
    inference_result = inference_normalize(test_img)
    
    # 比较结果
    if np.array_equal(training_result, inference_result):
        print("✓ 训练和推理的归一化方法完全一致")
        return True
    else:
        diff = np.abs(training_result.astype(np.float32) - inference_result.astype(np.float32))
        max_diff = diff.max()
        mean_diff = diff.mean()
        
        print(f"⚠️  训练和推理的归一化方法存在差异")
        print(f"   最大差异: {max_diff}")
        print(f"   平均差异: {mean_diff}")
        
        if max_diff < 1.0:  # 允许很小的数值误差
            print("✓ 差异在可接受范围内")
            return True
        else:
            print("✗ 差异过大，可能影响检测效果")
            return False

def test_model_loading():
    """
    测试模型是否能正常加载
    """
    print("\n=== 测试模型加载 ===")
    
    model_path = "E:/Trae/yolo_ohif/yolo11x_training_output/training_results/weights/best.pt"
    
    if not os.path.exists(model_path):
        print(f"✗ 模型文件不存在: {model_path}")
        return False
    
    try:
        from services.detection_service import DetectionService
        
        detection_service = DetectionService(
            model_path=model_path,
            confidence_threshold=0.25
        )
        
        print(f"✓ 模型加载成功: {model_path}")
        return True
        
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return False

def main():
    """
    主验证函数
    """
    print("DICOM处理修复验证")
    print("=" * 50)
    
    results = []
    
    # 1. 验证归一化方法
    results.append(verify_normalization_fix())
    
    # 2. 检查一致性
    results.append(check_training_vs_inference_consistency())
    
    # 3. 测试模型加载
    results.append(test_model_loading())
    
    # 总结
    print("\n" + "=" * 50)
    print("验证结果总结:")
    
    if all(results):
        print("✅ 所有验证通过！DICOM处理修复成功")
        print("\n建议下一步:")
        print("1. 使用实际的DICOM文件测试检测效果")
        print("2. 如果仍检测不到目标，尝试降低置信度阈值")
        print("3. 检查测试的DICOM图像是否包含训练时见过的病灶类型")
    else:
        print("❌ 部分验证失败，需要进一步检查")
        print("\n可能的问题:")
        print("1. 模型文件路径不正确")
        print("2. 依赖包缺失")
        print("3. 代码修改不完整")
    
    return all(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)