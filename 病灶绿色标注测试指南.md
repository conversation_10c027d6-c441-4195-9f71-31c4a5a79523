# 病灶绿色标注测试指南

## 测试目标
验证智能标注系统中病灶区域使用绿色矩形框标记的功能是否正常工作。

## 测试环境
- ✅ 应用程序已启动：`dotnet run --project src/MedicalImageAnalysis.Wpf`
- ✅ 编译成功，无错误
- ✅ 新功能已集成到智能标注界面

## 快速测试步骤

### 1. 打开智能标注界面
1. 启动应用程序
2. 点击左侧导航栏的"智能标注"选项卡
3. 验证界面正常加载，不再出现之前的错误

### 2. 加载测试图像
1. 点击"打开图像"按钮
2. 选择一个DICOM文件或标准图像文件
3. 验证图像正常显示
4. 如果是DICOM文件，验证窗宽窗位控制面板是否显示

### 3. 测试病灶绿色标注

#### 3.1 设置标注类别
1. 在右侧控制面板找到"标注类别"区域
2. 在下拉框中选择"病灶区域"
3. 验证选择成功

#### 3.2 查看颜色说明
1. 在右侧控制面板找到"标注颜色说明"区域
2. 展开该区域（如果是折叠状态）
3. 验证显示的颜色对应关系：
   - ✅ 病灶区域 → 绿色
   - ✅ 肿瘤 → 红色
   - ✅ 骨折 → 橙色
   - ✅ 血管 → 蓝色
   - ✅ 正常组织 → 浅蓝色
   - ✅ 其他 → 黄色

#### 3.3 绘制绿色矩形标注
1. 确保选择了"病灶区域"类别
2. 点击矩形工具按钮（确保已选中）
3. 在图像上拖拽绘制矩形框
4. **验证关键点**：
   - ✅ 矩形框边框是绿色
   - ✅ 矩形框内部是透明的
   - ✅ 边框粗细为2像素

#### 3.4 验证状态提示
1. 完成矩形绘制后
2. 查看底部状态栏
3. 验证显示类似："已添加 绿色矩形 标注 - 病灶区域"

### 4. 测试其他标注工具

#### 4.1 绿色圆形标注
1. 保持"病灶区域"类别选择
2. 点击圆形工具按钮
3. 在图像上拖拽绘制圆形
4. 验证圆形边框是绿色

#### 4.2 绿色点标注
1. 保持"病灶区域"类别选择
2. 点击点工具按钮
3. 在图像上点击一个位置
4. 验证点标注是绿色填充，深绿色边框

### 5. 测试其他类别颜色

#### 5.1 肿瘤红色标注
1. 在标注类别中选择"肿瘤"
2. 使用矩形工具绘制标注
3. 验证矩形框是红色

#### 5.2 骨折橙色标注
1. 在标注类别中选择"骨折"
2. 使用矩形工具绘制标注
3. 验证矩形框是橙色

#### 5.3 血管蓝色标注
1. 在标注类别中选择"血管"
2. 使用矩形工具绘制标注
3. 验证矩形框是蓝色

### 6. 测试自定义类别

#### 6.1 添加自定义病灶类别
1. 在"自定义类别"输入框中输入"病灶"
2. 点击"添加类别"按钮
3. 选择新添加的"病灶"类别
4. 绘制标注，验证仍然是绿色

#### 6.2 测试英文类别
1. 在"自定义类别"输入框中输入"lesion"
2. 点击"添加类别"按钮
3. 选择"lesion"类别
4. 绘制标注，验证是绿色

### 7. 测试窗宽窗位配合使用（仅DICOM文件）

如果加载的是DICOM文件：

#### 7.1 软组织窗 + 病灶标注
1. 点击"软组织窗"预设按钮
2. 选择"病灶区域"类别
3. 绘制绿色矩形标注
4. 验证绿色标注在软组织窗下的可见性

#### 7.2 其他预设窗口测试
1. 依次测试肺窗、骨窗、脑窗
2. 在每种窗口设置下绘制绿色病灶标注
3. 验证绿色标注的对比度和可见性

### 8. 测试标注列表功能

1. 绘制多个不同颜色的标注
2. 查看右侧的标注列表
3. 验证列表中显示的标注信息
4. 点击列表中的标注项，验证是否高亮显示

## 预期结果

### ✅ 成功标准

1. **颜色正确性**：
   - 病灶区域标注显示为绿色
   - 其他类别显示对应的正确颜色
   - 所有标注工具（矩形、圆形、点）颜色一致

2. **功能完整性**：
   - 颜色说明面板正确显示
   - 状态提示包含颜色信息
   - 自定义类别支持中英文

3. **用户体验**：
   - 颜色对比度良好，易于识别
   - 界面响应流畅
   - 操作直观简单

4. **专业性**：
   - 绿色病灶标注在医学影像上清晰可见
   - 与窗宽窗位调整功能配合良好
   - 符合医学影像标注的专业要求

### ❌ 问题排查

如果遇到问题：

1. **颜色不正确**：
   - 检查标注类别是否正确选择
   - 验证类别名称是否匹配（支持中英文）

2. **标注不显示**：
   - 检查图像是否正确加载
   - 验证标注工具是否正确选择

3. **界面异常**：
   - 重启应用程序
   - 检查控制台是否有错误信息

## 测试报告模板

```
测试日期：[日期]
测试人员：[姓名]
测试图像：[DICOM/标准图像]

病灶绿色标注测试：
□ 矩形绿色标注：通过/失败
□ 圆形绿色标注：通过/失败
□ 点绿色标注：通过/失败
□ 状态提示正确：通过/失败

其他颜色测试：
□ 肿瘤红色标注：通过/失败
□ 骨折橙色标注：通过/失败
□ 血管蓝色标注：通过/失败

界面功能测试：
□ 颜色说明面板：通过/失败
□ 自定义类别：通过/失败
□ 窗宽窗位配合：通过/失败

总体评价：
□ 功能完全正常
□ 存在小问题但不影响使用
□ 存在严重问题需要修复

备注：[详细说明]
```

## 医学应用场景测试

### 1. 肺部CT病灶标注
1. 加载肺部CT DICOM文件
2. 使用肺窗预设
3. 选择"病灶区域"类别
4. 用绿色矩形框标注肺结节
5. 验证标注在肺窗下的可见性

### 2. 腹部CT病灶标注
1. 加载腹部CT DICOM文件
2. 使用软组织窗预设
3. 选择"病灶区域"类别
4. 用绿色矩形框标注肝脏病灶
5. 验证标注效果

### 3. 骨骼X光骨折标注
1. 加载骨骼X光图像
2. 选择"骨折"类别
3. 用橙色矩形框标注骨折线
4. 验证颜色对比度

这个测试指南将帮助您全面验证病灶绿色标注功能的正确性和实用性。
