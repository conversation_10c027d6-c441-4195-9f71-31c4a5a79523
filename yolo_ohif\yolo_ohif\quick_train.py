#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
冈上肌肌腱撕裂YOLO模型快速训练脚本

使用方法:
1. 确保已安装依赖: pip install -r requirements_yolo_training.txt
2. 运行训练: python quick_train.py
"""

import os
import sys
from pathlib import Path
import logging

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from train_supraspinatus_yolo import SupraspinatusYOLOTrainer

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('yolo_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def main():
    """
    快速训练主函数
    """
    logger.info("开始冈上肌肌腱撕裂YOLO11x模型训练（高精度版本）")
    logger.info("处理逻辑：")
    logger.info("  - 精确匹配image_T2和label_T2的层面")
    logger.info("  - 所有层面的图像都会被保存")
    logger.info("  - 只有当label_T2某层面存在mask=1时，才生成对应的YOLO标签文件")
    logger.info("  - 无撕裂区域的层面只保存图像，不生成标签文件")
    logger.info("  - 图像保持原样，无任何变换（旋转、镜像等）")
    logger.info("  - image_T2_normal文件夹中的所有层面 → 正常样本")
    logger.info("  - 图像保持原样，无任何变换")
    
    # 配置参数
    config = {
        'dataset_root': './dataset',  # 数据集目录
        'output_root': './yolo_training_output',  # 输出目录
        'img_size': 640,  # 图像尺寸
        'epochs': 50,  # 训练轮数（快速训练用较少轮数）
        'batch_size': 8   # 批次大小（根据显存调整）
    }
    
    # 检查数据集是否存在
    dataset_path = Path(config['dataset_root'])
    if not dataset_path.exists():
        logger.error(f"数据集目录不存在: {dataset_path}")
        return
    
    # 检查必要的子目录
    required_dirs = ['image_T2', 'label_T2', 'image_T2_normal']
    for dir_name in required_dirs:
        dir_path = dataset_path / dir_name
        if not dir_path.exists():
            logger.error(f"必要的数据目录不存在: {dir_path}")
            return
        logger.info(f"找到数据目录: {dir_path} (文件数: {len(list(dir_path.glob('*.nii.gz')))}个)")
    
    try:
        # 创建训练器
        trainer = SupraspinatusYOLOTrainer(
            dataset_root=config['dataset_root'],
            output_root=config['output_root'],
            img_size=config['img_size']
        )
        
        # 运行训练流程
        logger.info("开始训练流程...")
        results = trainer.run_full_pipeline(
            epochs=config['epochs'],
            batch_size=config['batch_size']
        )
        
        logger.info("训练完成!")
        logger.info(f"模型保存在: {config['output_root']}/supraspinatus_detection")
        
        # 显示训练结果摘要
        if results:
            logger.info("训练结果摘要:")
            logger.info(f"最佳mAP50: {results.results_dict.get('metrics/mAP50(B)', 'N/A')}")
            logger.info(f"最佳mAP50-95: {results.results_dict.get('metrics/mAP50-95(B)', 'N/A')}")
        
    except Exception as e:
        logger.error(f"训练过程中出现错误: {e}")
        logger.error("请检查:", exc_info=True)
        logger.error("1. 是否正确安装了所有依赖包")
        logger.error("2. 数据集格式是否正确")
        logger.error("3. 是否有足够的磁盘空间")
        logger.error("4. GPU内存是否足够（如果使用GPU）")
        return
    
    logger.info("训练流程全部完成!")

if __name__ == "__main__":
    main()