# YOLO数据集创建工具

## 概述

`create_yolo_dataset.py` 是一个完整的YOLO数据集生成工具，合并了 `slice_nii_to_jpg.py` 和 `convert_label_to_bbox.py` 的功能，提供从原始nii.gz文件到完整YOLO训练数据集的端到端解决方案。

## 主要功能

### 🔄 数据处理流程
1. **NII.gz切片转换** - 将3D医学图像转换为2D切片
2. **图像格式转换** - 将切片转换为JPG格式
3. **标注生成** - 从mask生成YOLO格式的边界框标注
4. **数据集划分** - 按volume ID智能划分训练/验证/测试集
5. **配置文件生成** - 自动生成YOLO训练配置文件

### 📁 输入数据结构
```
dataset/
├── image_T2/           # 撕裂图像 (130-395.nii.gz)
├── label_T2/           # 撕裂标签 (130-395.nii.gz)
└── image_T2_normal/    # 正常图像 (1-129.nii.gz)
```

### 📁 输出数据结构
```
yolo_dataset_output/
└── yolo_dataset/
    ├── images/
    │   ├── train/      # 训练图像
    │   ├── val/        # 验证图像
    │   └── test/       # 测试图像
    ├── labels/
    │   ├── train/      # 训练标注
    │   ├── val/        # 验证标注
    │   └── test/       # 测试标注
    └── dataset.yaml    # YOLO配置文件
```

## 使用方法

### 基本使用
```bash
# 使用默认参数
python create_yolo_dataset.py

# 自定义参数
python create_yolo_dataset.py --dataset_root ./dataset --output_root ./my_output --img_size 640
```

### 参数说明
- `--dataset_root`: 原始数据集根目录 (默认: `./dataset`)
- `--output_root`: 输出目录 (默认: `./yolo_dataset_output`)
- `--img_size`: 图像尺寸 (默认: `640`)

### 测试验证
```bash
# 运行测试脚本
python test_create_yolo_dataset.py
```

## 核心特性

### 🎯 智能标注生成
- **连通组件分析**: 自动检测mask中的独立区域
- **边界框合并**: 多个区域自动合并为单个边界框
- **无过滤策略**: 保留所有有效的标注数据
- **归一化坐标**: 生成YOLO标准格式的归一化坐标

### 📊 数据集划分策略
- **按Volume划分**: 确保同一volume的所有切片在同一数据集中
- **比例控制**: 默认 70% 训练 / 20% 验证 / 10% 测试
- **随机打乱**: 避免数据分布偏差

### 🔧 图像处理
- **尺寸标准化**: 统一调整到指定尺寸
- **强度归一化**: 自动调整到0-255范围
- **格式转换**: 灰度图转RGB三通道
- **质量保证**: 保持医学图像的诊断价值

## 代码结构

### 主要类: `YOLODatasetCreator`

#### 核心方法
- `load_nii_image()` - 加载NII.gz文件
- `mask_to_bbox()` - mask转边界框
- `process_tear_images()` - 处理撕裂图像
- `process_normal_images()` - 处理正常图像
- `split_dataset()` - 数据集划分
- `create_yaml_config()` - 生成配置文件
- `create_dataset()` - 完整流程执行

#### 关键特性
```python
# 边界框生成 - 无过滤策略
def mask_to_bbox(self, mask):
    # 使用连通组件分析
    labeled_mask, num_features = ndimage.label(binary_mask)
    # 生成所有边界框
    # 合并多个边界框为一个
    # 返回归一化坐标

# 数据集划分 - 按Volume分组
def split_dataset(self, all_data):
    # 按volume_id分组
    # 确保同一volume的切片在同一数据集
    # 随机划分volume到不同数据集
```

## 输出统计

脚本运行后会显示详细的处理统计信息：

```
撕裂图像处理完成，共处理 1500 个层面
  - 生成标注的层面: 800 个
  - 只保存图像的层面: 700 个

正常图像处理完成，共处理 1200 个层面

数据集划分: 训练集 15 个volume, 验证集 4 个volume, 测试集 3 个volume

train集: 1890 张图像, 560 个标注文件
val集: 540 张图像, 160 个标注文件
test集: 270 张图像, 80 个标注文件

总共处理了 2700 个样本
```

## 训练模型

生成数据集后，可以直接用于YOLO模型训练：

```bash
# 使用YOLOv11训练
yolo train data=./yolo_dataset_output/yolo_dataset/dataset.yaml model=yolo11x.pt epochs=100 imgsz=640

# 自定义训练参数
yolo train data=./yolo_dataset_output/yolo_dataset/dataset.yaml model=yolo11x.pt epochs=200 imgsz=640 batch=16 lr0=0.01
```

## 故障排除

### 常见问题

1. **内存不足**
   - 减小 `img_size` 参数
   - 分批处理大型数据集

2. **标注数量少**
   - 检查mask文件质量
   - 验证图像和标签的对应关系

3. **数据集不平衡**
   - 调整数据集划分比例
   - 检查volume分布

### 调试模式
```python
# 启用详细日志
logging.basicConfig(level=logging.DEBUG)
```

## 性能优化

### 建议配置
- **内存**: 建议16GB以上
- **存储**: SSD硬盘提升I/O性能
- **CPU**: 多核处理器加速图像处理

### 批处理优化
```python
# 可以修改批处理大小
for img_file in tqdm(tear_files, desc="处理撕裂图像"):
    # 处理逻辑
```

## 与原脚本的区别

| 特性 | 原脚本 | 新脚本 |
|------|--------|--------|
| 功能整合 | 分离的脚本 | 一体化解决方案 |
| 数据集生成 | 手动步骤 | 自动化流程 |
| 配置管理 | 手动创建 | 自动生成 |
| 错误处理 | 基础 | 完善的异常处理 |
| 进度显示 | 有限 | 详细的进度条 |
| 测试验证 | 无 | 完整的测试套件 |

## 扩展功能

### 自定义处理
```python
# 可以继承YOLODatasetCreator类进行自定义
class CustomYOLOCreator(YOLODatasetCreator):
    def custom_preprocessing(self, image):
        # 自定义图像预处理
        return processed_image
```

### 数据增强
```python
# 可以在图像处理阶段添加数据增强
def apply_augmentation(self, image):
    # 旋转、翻转、亮度调整等
    return augmented_image
```

## 许可证

本工具遵循项目的开源许可证，可自由使用和修改。

## 贡献

欢迎提交问题报告和功能请求。在提交代码前，请确保：
1. 运行测试脚本验证功能
2. 更新相关文档
3. 遵循代码风格规范

---

**注意**: 本工具专为医学图像处理设计，特别适用于冈上肌肌腱撕裂检测任务。在其他医学图像任务中使用时，可能需要调整参数和处理逻辑。