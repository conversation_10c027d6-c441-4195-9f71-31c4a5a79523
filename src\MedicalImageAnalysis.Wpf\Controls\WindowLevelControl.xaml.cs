using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.Logging;

namespace MedicalImageAnalysis.Wpf.Controls
{
    /// <summary>
    /// 窗宽窗位控制用户控件
    /// </summary>
    public partial class WindowLevelControl : UserControl
    {
        private readonly ILogger<WindowLevelControl> _logger;
        private double _originalWindowWidth = 400;
        private double _originalWindowCenter = 40;
        private readonly ObservableCollection<CustomWindowLevelPreset> _customPresets;
        private readonly string _presetsFilePath;

        /// <summary>
        /// 窗宽窗位变化事件
        /// </summary>
        public event EventHandler<WindowLevelChangedEventArgs>? WindowLevelChanged;

        public WindowLevelControl()
        {
            InitializeComponent();
            _logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<WindowLevelControl>.Instance;

            // 初始化自定义预设
            _customPresets = new ObservableCollection<CustomWindowLevelPreset>();
            _presetsFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                                          "MedicalImageAnalysis", "WindowLevelPresets.json");

            // 设置默认值
            WindowWidthTextBox.Text = _originalWindowWidth.ToString("F0");
            WindowCenterTextBox.Text = _originalWindowCenter.ToString("F0");

            // 初始化自定义预设UI
            InitializeCustomPresets();

            UpdateCurrentSettings();
        }

        /// <summary>
        /// 设置原始窗宽窗位值
        /// </summary>
        public void SetOriginalValues(double windowWidth, double windowCenter)
        {
            _originalWindowWidth = windowWidth;
            _originalWindowCenter = windowCenter;
            
            WindowWidthTextBox.Text = windowWidth.ToString("F0");
            WindowCenterTextBox.Text = windowCenter.ToString("F0");
            
            UpdateCurrentSettings();
        }

        /// <summary>
        /// 获取当前窗宽值
        /// </summary>
        public double WindowWidth
        {
            get
            {
                if (double.TryParse(WindowWidthTextBox.Text, out double value))
                    return value;
                return _originalWindowWidth;
            }
        }

        /// <summary>
        /// 获取当前窗位值
        /// </summary>
        public double WindowCenter
        {
            get
            {
                if (double.TryParse(WindowCenterTextBox.Text, out double value))
                    return value;
                return _originalWindowCenter;
            }
        }

        /// <summary>
        /// 窗宽窗位文本框变化事件
        /// </summary>
        private void WindowLevel_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                if (WindowWidthTextBox != null && WindowCenterTextBox != null)
                {
                    if (double.TryParse(WindowWidthTextBox.Text ?? "400", out double windowWidth) &&
                        double.TryParse(WindowCenterTextBox.Text ?? "40", out double windowCenter))
                    {
                        UpdateCurrentSettings();
                        OnWindowLevelChanged(windowWidth, windowCenter);
                    }
                    else
                    {
                        UpdateCurrentSettings();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "窗宽窗位值解析失败");
            }
        }

        /// <summary>
        /// 重置窗宽窗位按钮点击事件
        /// </summary>
        private void ResetWindowButton_Click(object sender, RoutedEventArgs e)
        {
            WindowWidthTextBox.Text = _originalWindowWidth.ToString("F0");
            WindowCenterTextBox.Text = _originalWindowCenter.ToString("F0");
            UpdateCurrentSettings();
            OnWindowLevelChanged(_originalWindowWidth, _originalWindowCenter);
        }

        /// <summary>
        /// 肺窗预设按钮点击事件
        /// </summary>
        private void LungWindowButton_Click(object sender, RoutedEventArgs e)
        {
            // 肺窗：窗宽1500，窗位-600
            SetWindowLevel(1500, -600);
        }

        /// <summary>
        /// 软组织窗预设按钮点击事件
        /// </summary>
        private void SoftTissueWindowButton_Click(object sender, RoutedEventArgs e)
        {
            // 软组织窗：窗宽400，窗位40
            SetWindowLevel(400, 40);
        }

        /// <summary>
        /// 骨窗预设按钮点击事件
        /// </summary>
        private void BoneWindowButton_Click(object sender, RoutedEventArgs e)
        {
            // 骨窗：窗宽2000，窗位400
            SetWindowLevel(2000, 400);
        }

        /// <summary>
        /// 脑窗预设按钮点击事件
        /// </summary>
        private void BrainWindowButton_Click(object sender, RoutedEventArgs e)
        {
            // 脑窗：窗宽80，窗位40
            SetWindowLevel(80, 40);
        }

        /// <summary>
        /// 设置窗宽窗位值
        /// </summary>
        private void SetWindowLevel(double windowWidth, double windowCenter)
        {
            WindowWidthTextBox.Text = windowWidth.ToString("F0");
            WindowCenterTextBox.Text = windowCenter.ToString("F0");
            UpdateCurrentSettings();
            OnWindowLevelChanged(windowWidth, windowCenter);
        }

        /// <summary>
        /// 更新当前设置显示
        /// </summary>
        private void UpdateCurrentSettings()
        {
            try
            {
                if (WindowWidthTextBox != null && WindowCenterTextBox != null && CurrentSettingsText != null)
                {
                    if (double.TryParse(WindowWidthTextBox.Text ?? "400", out double ww) &&
                        double.TryParse(WindowCenterTextBox.Text ?? "40", out double wc))
                    {
                        CurrentSettingsText.Text = $"当前设置: 窗宽 {ww:F0}, 窗位 {wc:F0}";
                    }
                    else
                    {
                        CurrentSettingsText.Text = "当前设置: 窗宽 400, 窗位 40";
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "更新当前设置显示失败");
            }
        }

        /// <summary>
        /// 触发窗宽窗位变化事件
        /// </summary>
        private void OnWindowLevelChanged(double windowWidth, double windowCenter)
        {
            WindowLevelChanged?.Invoke(this, new WindowLevelChangedEventArgs(windowWidth, windowCenter));
        }

        /// <summary>
        /// 初始化自定义预设
        /// </summary>
        private void InitializeCustomPresets()
        {
            try
            {
                LoadCustomPresets();
                CustomPresetsComboBox.ItemsSource = _customPresets;
                CustomPresetsComboBox.DisplayMemberPath = "Name";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化自定义预设失败");
            }
        }

        /// <summary>
        /// 加载自定义预设
        /// </summary>
        private void LoadCustomPresets()
        {
            try
            {
                if (File.Exists(_presetsFilePath))
                {
                    var json = File.ReadAllText(_presetsFilePath);
                    var presets = JsonSerializer.Deserialize<List<CustomWindowLevelPreset>>(json);

                    _customPresets.Clear();
                    if (presets != null)
                    {
                        foreach (var preset in presets)
                        {
                            _customPresets.Add(preset);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载自定义预设失败");
            }
        }

        /// <summary>
        /// 保存自定义预设
        /// </summary>
        private void SaveCustomPresets()
        {
            try
            {
                var directory = Path.GetDirectoryName(_presetsFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = JsonSerializer.Serialize(_customPresets.ToList(), new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                File.WriteAllText(_presetsFilePath, json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存自定义预设失败");
            }
        }

        /// <summary>
        /// 自定义预设选择变化事件
        /// </summary>
        private void CustomPresetsComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CustomPresetsComboBox.SelectedItem is CustomWindowLevelPreset preset)
            {
                SetWindowLevel(preset.WindowWidth, preset.WindowCenter);
            }
        }

        /// <summary>
        /// 保存预设按钮点击事件
        /// </summary>
        private void SavePresetButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (double.TryParse(WindowWidthTextBox.Text, out double windowWidth) &&
                    double.TryParse(WindowCenterTextBox.Text, out double windowCenter))
                {
                    var presetName = Microsoft.VisualBasic.Interaction.InputBox(
                        "请输入预设名称:",
                        "保存自定义预设",
                        $"自定义预设_{DateTime.Now:yyyyMMdd_HHmmss}");

                    if (!string.IsNullOrWhiteSpace(presetName))
                    {
                        // 检查是否已存在同名预设
                        var existingPreset = _customPresets.FirstOrDefault(p => p.Name == presetName);
                        if (existingPreset != null)
                        {
                            var result = MessageBox.Show($"预设 '{presetName}' 已存在，是否覆盖？", "确认",
                                                       MessageBoxButton.YesNo, MessageBoxImage.Question);
                            if (result == MessageBoxResult.Yes)
                            {
                                existingPreset.WindowWidth = windowWidth;
                                existingPreset.WindowCenter = windowCenter;
                                existingPreset.CreatedDate = DateTime.Now;
                            }
                            else
                            {
                                return;
                            }
                        }
                        else
                        {
                            var newPreset = new CustomWindowLevelPreset
                            {
                                Name = presetName,
                                WindowWidth = windowWidth,
                                WindowCenter = windowCenter,
                                CreatedDate = DateTime.Now
                            };
                            _customPresets.Add(newPreset);
                        }

                        SaveCustomPresets();
                        MessageBox.Show("自定义预设保存成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show("请确保窗宽窗位值有效", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存自定义预设失败");
                MessageBox.Show($"保存自定义预设失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除预设按钮点击事件
        /// </summary>
        private void DeletePresetButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (CustomPresetsComboBox.SelectedItem is CustomWindowLevelPreset preset)
                {
                    var result = MessageBox.Show($"确定要删除预设 '{preset.Name}' 吗？", "确认删除",
                                               MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (result == MessageBoxResult.Yes)
                    {
                        _customPresets.Remove(preset);
                        SaveCustomPresets();
                        CustomPresetsComboBox.SelectedItem = null;
                        MessageBox.Show("自定义预设删除成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show("请先选择要删除的预设", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除自定义预设失败");
                MessageBox.Show($"删除自定义预设失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    /// <summary>
    /// 窗宽窗位变化事件参数
    /// </summary>
    public class WindowLevelChangedEventArgs : EventArgs
    {
        public double WindowWidth { get; }
        public double WindowCenter { get; }

        public WindowLevelChangedEventArgs(double windowWidth, double windowCenter)
        {
            WindowWidth = windowWidth;
            WindowCenter = windowCenter;
        }
    }

    /// <summary>
    /// 自定义窗宽窗位预设
    /// </summary>
    public class CustomWindowLevelPreset
    {
        public string Name { get; set; } = string.Empty;
        public double WindowWidth { get; set; }
        public double WindowCenter { get; set; }
        public DateTime CreatedDate { get; set; }
        public string Description { get; set; } = string.Empty;

        public override string ToString()
        {
            return $"{Name} (WW:{WindowWidth:F0}, WC:{WindowCenter:F0})";
        }
    }
}
