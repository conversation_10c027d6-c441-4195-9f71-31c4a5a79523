using System;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.Logging;

namespace MedicalImageAnalysis.Wpf.Controls
{
    /// <summary>
    /// 窗宽窗位控制用户控件
    /// </summary>
    public partial class WindowLevelControl : UserControl
    {
        private readonly ILogger<WindowLevelControl> _logger;
        private double _originalWindowWidth = 400;
        private double _originalWindowCenter = 40;

        /// <summary>
        /// 窗宽窗位变化事件
        /// </summary>
        public event EventHandler<WindowLevelChangedEventArgs>? WindowLevelChanged;

        public WindowLevelControl()
        {
            InitializeComponent();
            _logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<WindowLevelControl>.Instance;

            // 设置默认值
            WindowWidthTextBox.Text = _originalWindowWidth.ToString("F0");
            WindowCenterTextBox.Text = _originalWindowCenter.ToString("F0");

            UpdateCurrentSettings();
        }

        /// <summary>
        /// 设置原始窗宽窗位值
        /// </summary>
        public void SetOriginalValues(double windowWidth, double windowCenter)
        {
            _originalWindowWidth = windowWidth;
            _originalWindowCenter = windowCenter;
            
            WindowWidthTextBox.Text = windowWidth.ToString("F0");
            WindowCenterTextBox.Text = windowCenter.ToString("F0");
            
            UpdateCurrentSettings();
        }

        /// <summary>
        /// 获取当前窗宽值
        /// </summary>
        public double WindowWidth
        {
            get
            {
                if (double.TryParse(WindowWidthTextBox.Text, out double value))
                    return value;
                return _originalWindowWidth;
            }
        }

        /// <summary>
        /// 获取当前窗位值
        /// </summary>
        public double WindowCenter
        {
            get
            {
                if (double.TryParse(WindowCenterTextBox.Text, out double value))
                    return value;
                return _originalWindowCenter;
            }
        }

        /// <summary>
        /// 窗宽窗位文本框变化事件
        /// </summary>
        private void WindowLevel_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                if (WindowWidthTextBox != null && WindowCenterTextBox != null)
                {
                    if (double.TryParse(WindowWidthTextBox.Text ?? "400", out double windowWidth) &&
                        double.TryParse(WindowCenterTextBox.Text ?? "40", out double windowCenter))
                    {
                        UpdateCurrentSettings();
                        OnWindowLevelChanged(windowWidth, windowCenter);
                    }
                    else
                    {
                        UpdateCurrentSettings();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "窗宽窗位值解析失败");
            }
        }

        /// <summary>
        /// 重置窗宽窗位按钮点击事件
        /// </summary>
        private void ResetWindowButton_Click(object sender, RoutedEventArgs e)
        {
            WindowWidthTextBox.Text = _originalWindowWidth.ToString("F0");
            WindowCenterTextBox.Text = _originalWindowCenter.ToString("F0");
            UpdateCurrentSettings();
            OnWindowLevelChanged(_originalWindowWidth, _originalWindowCenter);
        }

        /// <summary>
        /// 肺窗预设按钮点击事件
        /// </summary>
        private void LungWindowButton_Click(object sender, RoutedEventArgs e)
        {
            // 肺窗：窗宽1500，窗位-600
            SetWindowLevel(1500, -600);
        }

        /// <summary>
        /// 软组织窗预设按钮点击事件
        /// </summary>
        private void SoftTissueWindowButton_Click(object sender, RoutedEventArgs e)
        {
            // 软组织窗：窗宽400，窗位40
            SetWindowLevel(400, 40);
        }

        /// <summary>
        /// 骨窗预设按钮点击事件
        /// </summary>
        private void BoneWindowButton_Click(object sender, RoutedEventArgs e)
        {
            // 骨窗：窗宽2000，窗位400
            SetWindowLevel(2000, 400);
        }

        /// <summary>
        /// 脑窗预设按钮点击事件
        /// </summary>
        private void BrainWindowButton_Click(object sender, RoutedEventArgs e)
        {
            // 脑窗：窗宽80，窗位40
            SetWindowLevel(80, 40);
        }

        /// <summary>
        /// 设置窗宽窗位值
        /// </summary>
        private void SetWindowLevel(double windowWidth, double windowCenter)
        {
            WindowWidthTextBox.Text = windowWidth.ToString("F0");
            WindowCenterTextBox.Text = windowCenter.ToString("F0");
            UpdateCurrentSettings();
            OnWindowLevelChanged(windowWidth, windowCenter);
        }

        /// <summary>
        /// 更新当前设置显示
        /// </summary>
        private void UpdateCurrentSettings()
        {
            try
            {
                if (WindowWidthTextBox != null && WindowCenterTextBox != null && CurrentSettingsText != null)
                {
                    if (double.TryParse(WindowWidthTextBox.Text ?? "400", out double ww) &&
                        double.TryParse(WindowCenterTextBox.Text ?? "40", out double wc))
                    {
                        CurrentSettingsText.Text = $"当前设置: 窗宽 {ww:F0}, 窗位 {wc:F0}";
                    }
                    else
                    {
                        CurrentSettingsText.Text = "当前设置: 窗宽 400, 窗位 40";
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "更新当前设置显示失败");
            }
        }

        /// <summary>
        /// 触发窗宽窗位变化事件
        /// </summary>
        private void OnWindowLevelChanged(double windowWidth, double windowCenter)
        {
            WindowLevelChanged?.Invoke(this, new WindowLevelChangedEventArgs(windowWidth, windowCenter));
        }
    }

    /// <summary>
    /// 窗宽窗位变化事件参数
    /// </summary>
    public class WindowLevelChangedEventArgs : EventArgs
    {
        public double WindowWidth { get; }
        public double WindowCenter { get; }

        public WindowLevelChangedEventArgs(double windowWidth, double windowCenter)
        {
            WindowWidth = windowWidth;
            WindowCenter = windowCenter;
        }
    }
}
