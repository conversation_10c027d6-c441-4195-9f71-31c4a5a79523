{% extends "base.html" %}

{% block title %}关于 - YOLO-OHIF医学图像疾病检测系统{% endblock %}

{% block content %}
<div class="row mb-5">
    <div class="col-md-12 text-center">
        <h1 class="display-4 mb-3">关于YOLO-OHIF医学图像疾病检测系统</h1>
        <p class="lead">基于人工智能的医学图像疾病检测与诊断辅助平台</p>
    </div>
</div>

<div class="row mb-5">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <h2 class="mb-4">项目概述</h2>
                <p>YOLO-OHIF医学图像疾病检测系统是一个集成了YOLO深度学习模型、Flask Web框架、Orthanc DICOM服务器和OHIF医学影像查看器的综合平台，旨在为医疗专业人员提供高效、准确的医学图像疾病检测和诊断辅助工具。</p>
                <p>本系统利用最新的计算机视觉技术，对医学图像进行自动分析和疾病检测，帮助医生快速识别潜在的病灶，提高诊断效率和准确性。同时，系统集成了专业的医学影像查看和管理功能，为医疗工作流程提供全面支持。</p>
            </div>
        </div>
    </div>
</div>

<div class="row mb-5">
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-body">
                <h3 class="mb-4">核心技术</h3>
                <div class="accordion" id="accordionTech">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseYOLO">
                                <i class="fas fa-brain me-2"></i>YOLO深度学习模型
                            </button>
                        </h2>
                        <div id="collapseYOLO" class="accordion-collapse collapse show" data-bs-parent="#accordionTech">
                            <div class="accordion-body">
                                <p>YOLO (You Only Look Once) 是一种先进的实时目标检测算法，本系统采用最新的YOLOv8模型，针对医学图像特点进行了优化，能够快速、准确地检测医学图像中的异常区域和病灶。</p>
                                <p>主要特点：</p>
                                <ul>
                                    <li>高精度的病灶定位和分类</li>
                                    <li>实时检测，快速处理大量医学图像</li>
                                    <li>支持多种医学影像模态，如CT、MRI、X光等</li>
                                    <li>可扩展的模型架构，支持不同疾病类型的检测</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFlask">
                                <i class="fas fa-server me-2"></i>Flask Web框架
                            </button>
                        </h2>
                        <div id="collapseFlask" class="accordion-collapse collapse" data-bs-parent="#accordionTech">
                            <div class="accordion-body">
                                <p>Flask是一个轻量级的Python Web框架，本系统基于Flask构建了灵活、高效的Web应用，提供了用户友好的界面和API接口。</p>
                                <p>主要特点：</p>
                                <ul>
                                    <li>模块化设计，易于扩展和维护</li>
                                    <li>RESTful API支持，便于与其他系统集成</li>
                                    <li>安全的用户认证和权限管理</li>
                                    <li>高性能的异步任务处理</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOrthanc">
                                <i class="fas fa-database me-2"></i>Orthanc DICOM服务器
                            </button>
                        </h2>
                        <div id="collapseOrthanc" class="accordion-collapse collapse" data-bs-parent="#accordionTech">
                            <div class="accordion-body">
                                <p>Orthanc是一个开源的DICOM服务器，本系统集成了Orthanc，提供了完整的医学影像存储、检索和共享功能。</p>
                                <p>主要特点：</p>
                                <ul>
                                    <li>完全兼容DICOM标准，确保医疗数据互操作性</li>
                                    <li>高效的DICOM文件存储和管理</li>
                                    <li>强大的REST API，便于与Web应用集成</li>
                                    <li>支持DICOM查询/检索（Query/Retrieve）</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOHIF">
                                <i class="fas fa-eye me-2"></i>OHIF医学影像查看器
                            </button>
                        </h2>
                        <div id="collapseOHIF" class="accordion-collapse collapse" data-bs-parent="#accordionTech">
                            <div class="accordion-body">
                                <p>OHIF (Open Health Imaging Foundation) Viewer是一个开源的Web医学影像查看器，本系统集成了OHIF，提供了专业的医学影像浏览和分析工具。</p>
                                <p>主要特点：</p>
                                <ul>
                                    <li>支持多种医学影像格式，包括DICOM</li>
                                    <li>丰富的影像操作工具，如窗宽窗位调整、测量、标注等</li>
                                    <li>可扩展的插件系统，支持自定义功能</li>
                                    <li>与YOLO检测结果无缝集成，直观展示病灶位置</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-body">
                <h3 class="mb-4">系统架构</h3>
                <div class="text-center mb-4">
                    <img src="{{ url_for('static', filename='images/architecture.svg') }}" class="img-fluid" alt="系统架构图" style="max-width: 100%;">
                </div>
                <p>YOLO-OHIF医学图像疾病检测系统采用模块化、微服务架构设计，主要包括以下几个核心组件：</p>
                <ol>
                    <li><strong>前端界面</strong>：基于Bootstrap和jQuery构建的响应式Web界面，提供用户友好的操作体验。</li>
                    <li><strong>Flask应用服务</strong>：处理用户请求，协调各组件之间的通信，提供RESTful API接口。</li>
                    <li><strong>YOLO检测服务</strong>：加载预训练的YOLO模型，对医学图像进行疾病检测，生成检测结果。</li>
                    <li><strong>Orthanc DICOM服务</strong>：管理DICOM文件的存储、检索和查询，提供标准的DICOM服务。</li>
                    <li><strong>OHIF查看服务</strong>：集成OHIF医学影像查看器，提供专业的医学影像浏览和分析功能。</li>
                    <li><strong>数据库服务</strong>：存储用户信息、研究记录、检测结果等数据，支持系统的持久化需求。</li>
                </ol>
                <p>各组件之间通过标准化的接口进行通信，确保系统的可扩展性和可维护性。同时，系统采用了安全的认证和授权机制，保护医疗数据的隐私和安全。</p>
            </div>
        </div>
    </div>
</div>

<div class="row mb-5">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <h3 class="mb-4">应用场景</h3>
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h4><i class="fas fa-hospital me-2 text-primary"></i>临床辅助诊断</h4>
                                <p>为临床医生提供AI辅助诊断工具，帮助快速识别潜在病灶，提高诊断效率和准确性，减轻医生工作负担。系统可以自动检测多种疾病，如肺结节、脑肿瘤、骨折等，并提供详细的检测报告。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h4><i class="fas fa-graduation-cap me-2 text-primary"></i>医学教育培训</h4>
                                <p>为医学院校和培训机构提供教学工具，帮助学生学习医学影像诊断知识，提供实践训练平台。学生可以通过系统学习不同疾病的影像特征，提高影像诊断能力。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h4><i class="fas fa-flask me-2 text-primary"></i>医学研究与临床试验</h4>
                                <p>为医学研究人员提供数据分析工具，支持大规模医学图像数据的处理和分析，加速医学研究进展。研究人员可以利用系统进行疾病特征分析、治疗效果评估等研究工作。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h4><i class="fas fa-clinic-medical me-2 text-primary"></i>基层医疗机构</h4>
                                <p>为基层医疗机构提供智能诊断支持，弥补专业医师不足的问题，提高基层医疗服务质量。基层医生可以利用系统进行初步筛查，及时发现潜在疾病，提高诊断准确性。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-5">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <h3 class="mb-4">团队与联系方式</h3>
                <p>YOLO-OHIF医学图像疾病检测系统由一支专注于医学影像AI的团队开发，团队成员包括医学专家、AI研究人员和软件工程师，致力于将先进的人工智能技术应用于医学领域，提高医疗服务质量。</p>
                <div class="row mt-4">
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-envelope fa-2x text-primary me-3"></i>
                            <div>
                                <h5 class="mb-1">电子邮件</h5>
                                <p class="mb-0"><EMAIL></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-globe fa-2x text-primary me-3"></i>
                            <div>
                                <h5 class="mb-1">网站</h5>
                                <p class="mb-0">www.yolo-ohif.example.com</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-phone fa-2x text-primary me-3"></i>
                            <div>
                                <h5 class="mb-1">电话</h5>
                                <p class="mb-0">+86 123 4567 8910</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <h3 class="mb-4">开源与许可</h3>
                <p>YOLO-OHIF医学图像疾病检测系统是一个开源项目，采用MIT许可证发布，欢迎社区贡献和使用。</p>
                <p>本系统集成了多个开源组件，包括：</p>
                <ul>
                    <li>YOLO：<a href="https://github.com/ultralytics/ultralytics" target="_blank">https://github.com/ultralytics/ultralytics</a></li>
                    <li>Flask：<a href="https://flask.palletsprojects.com/" target="_blank">https://flask.palletsprojects.com/</a></li>
                    <li>Orthanc：<a href="https://www.orthanc-server.com/" target="_blank">https://www.orthanc-server.com/</a></li>
                    <li>OHIF Viewer：<a href="https://ohif.org/" target="_blank">https://ohif.org/</a></li>
                </ul>
                <p>我们感谢这些开源项目的贡献者，他们的工作使本系统成为可能。</p>
                <div class="text-center mt-4">
                    <a href="https://github.com/example/yolo-ohif" target="_blank" class="btn btn-outline-primary">
                        <i class="fab fa-github me-1"></i>GitHub仓库
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}