# 预览数据集功能说明

## 功能概述

新增的"预览数据集"功能允许用户快速查看和访问所选数据集的文件夹，提供便捷的数据集管理和预览能力。

## 功能特性

### 🔍 主要功能

1. **一键打开数据集文件夹**
   - 自动在Windows资源管理器中打开所选数据集的文件夹
   - 支持相对路径和绝对路径的自动转换

2. **智能路径解析**
   - 内置数据集名称到路径的映射
   - 支持多个常见数据集存储位置的自动搜索
   - 路径不存在时提供友好的错误处理

3. **数据集结构分析**
   - 自动统计图像文件数量
   - 统计标注文件数量
   - 计算数据集总大小
   - 分析子文件夹结构

4. **示例数据集创建**
   - 当数据集路径不存在时，可自动创建标准的数据集目录结构
   - 生成详细的README.md说明文件
   - 支持YOLO和nnUNet等多种数据集格式

## 使用方法

### 1. 基本使用流程

1. **选择数据集**：在训练配置页面的数据集下拉框中选择一个数据集
2. **点击预览按钮**：点击"预览数据集"按钮（眼睛图标）
3. **查看结果**：系统会自动打开文件夹并显示数据集信息

### 2. 支持的数据集

系统预配置了以下数据集路径映射：

```csharp
// YOLO数据集
"医学影像数据集 v1.0" → "./data/medical_dataset_v1"
"DICOM标注数据集" → "./data/dicom_annotated_dataset"  
"自定义数据集" → "./data/custom_dataset"

// nnUNet数据集
"nnUNet医学分割数据集" → "./data/nnUNet_raw/Dataset001_Medical"
"脑部MRI数据集" → "./data/nnUNet_raw/Dataset002_BrainMRI"
"肺部CT数据集" → "./data/nnUNet_raw/Dataset003_LungCT"
```

### 3. 路径搜索策略

如果预配置的路径不存在，系统会自动搜索以下位置：

1. `./data/[数据集名称]`
2. `./datasets/[数据集名称]`
3. `C:\Data\[数据集名称]`
4. `D:\Data\[数据集名称]`

## 功能详解

### 📁 文件夹打开功能

```csharp
private void OpenFolderInExplorer(string folderPath)
{
    Process.Start(new ProcessStartInfo
    {
        FileName = "explorer.exe",
        Arguments = $"\"{folderPath}\"",
        UseShellExecute = true
    });
}
```

- 使用Windows资源管理器打开指定文件夹
- 支持包含空格和特殊字符的路径
- 提供异常处理和错误日志记录

### 📊 数据集分析功能

系统会自动分析数据集结构并提供以下信息：

- **图像文件统计**：支持 .jpg, .jpeg, .png, .bmp, .tiff, .dcm, .nii, .nii.gz
- **标注文件统计**：支持 .txt, .xml, .json, .csv
- **文件夹结构**：递归统计所有子文件夹
- **存储空间**：计算数据集总大小并格式化显示

### 🏗️ 示例数据集创建

当数据集路径不存在时，系统提供创建示例结构的功能：

```
数据集名称/
├── images/
│   ├── train/          # 训练图像
│   ├── val/            # 验证图像
│   └── test/           # 测试图像
├── labels/
│   ├── train/          # 训练标注
│   └── val/            # 验证标注
└── README.md           # 说明文件
```

## 界面集成

### 按钮设计

```xml
<Button Style="{StaticResource MaterialDesignOutlinedButton}"
        Margin="0,4"
        Click="PreviewDataset_Click">
    <Button.Content>
        <StackPanel Orientation="Horizontal">
            <materialDesign:PackIcon Kind="Eye"
                                   Width="16" Height="16"
                                   Margin="0,0,8,0"/>
            <TextBlock Text="预览数据集"/>
        </StackPanel>
    </Button.Content>
</Button>
```

- 使用Material Design风格的眼睛图标
- 与其他数据集操作按钮保持一致的设计风格
- 位置：在"浏览数据集"和"验证数据集"之间

### 信息显示

点击预览后会显示包含以下信息的对话框：

```
数据集: 医学影像数据集 v1.0
路径: C:\Projects\medical-imaging\data\medical_dataset_v1

结构分析:
• 图像文件: 1,250 个
• 标注文件: 1,250 个  
• 子文件夹: 6 个
• 总大小: 2.34 GB

文件夹已在资源管理器中打开。
```

## 错误处理

### 1. 数据集未选择
- **提示**："请先选择一个数据集。"
- **操作**：引导用户先选择数据集

### 2. 路径不存在
- **提示**：显示具体的缺失路径
- **选项**：提供创建示例数据集结构的选项

### 3. 权限问题
- **处理**：捕获文件系统访问异常
- **日志**：记录详细的错误信息
- **用户反馈**：显示友好的错误消息

## 技术实现

### 核心类和方法

1. **PreviewDataset_Click**：主要事件处理程序
2. **OpenFolderInExplorer**：打开文件夹功能
3. **ShowDatasetInfo**：显示数据集信息
4. **AnalyzeDatasetStructure**：分析数据集结构
5. **CreateSampleDatasetStructure**：创建示例结构

### 数据结构

```csharp
public class DatasetInfo
{
    public int ImageCount { get; set; }
    public int LabelCount { get; set; }
    public int SubfolderCount { get; set; }
    public long TotalSize { get; set; }
}
```

### 依赖项

- `System.Diagnostics`：用于启动外部进程
- `System.IO`：文件系统操作
- `System.Windows`：WPF界面交互

## 使用场景

### 1. 数据集验证
- 快速检查数据集是否存在
- 验证文件夹结构是否正确
- 确认图像和标注文件的对应关系

### 2. 数据准备
- 查看现有数据集内容
- 添加新的训练数据
- 整理和组织数据文件

### 3. 调试训练问题
- 检查数据路径配置
- 验证数据格式和结构
- 排查训练失败的数据相关问题

## 扩展建议

### 未来可能的增强功能

1. **数据集预览窗口**：在应用内直接显示图像缩略图
2. **批量操作**：支持多个数据集的批量预览和管理
3. **云存储支持**：支持云端数据集的预览和下载
4. **数据集统计图表**：提供更详细的数据分布统计
5. **自动数据验证**：集成数据质量检查功能

## 总结

"预览数据集"功能为用户提供了便捷的数据集管理能力，通过一键操作即可快速访问和了解数据集的基本信息。该功能与现有的训练流程无缝集成，提升了用户体验和工作效率。
