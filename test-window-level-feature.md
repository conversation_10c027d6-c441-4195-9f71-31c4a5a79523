# 窗宽窗位功能测试指南

## 测试目标
验证影像处理界面和智能标注界面的窗宽窗位调整功能是否正常工作。

## 测试前准备
1. ✅ 应用程序已启动：`dotnet run --project src/MedicalImageAnalysis.Wpf`
2. ✅ 准备DICOM测试文件（.dcm格式）
3. ✅ 准备标准图像文件（.png, .jpg等）用于对比测试

## 测试步骤

### 1. 影像处理界面测试

#### 1.1 DICOM文件测试
1. **打开影像处理界面**
   - 点击左侧导航栏的"影像处理"选项卡
   
2. **加载DICOM文件**
   - 点击"打开图像"按钮
   - 选择一个.dcm文件
   - 验证：图像正常显示，不再出现"DICOM文件处理功能正在开发中"的提示

3. **验证窗宽窗位控制显示**
   - ✅ 检查右侧控制面板是否显示"窗宽窗位调整"区域
   - ✅ 验证窗宽和窗位输入框是否显示DICOM文件的原始值
   - ✅ 确认四个预设按钮是否可见：肺窗、软组织窗、骨窗、脑窗

4. **测试自定义窗宽窗位**
   - 在窗宽输入框中输入新值（如：800）
   - 在窗位输入框中输入新值（如：100）
   - 验证：图像显示立即更新，当前设置显示正确的数值

5. **测试预设窗口**
   - **肺窗测试**：点击"肺窗"按钮
     - 验证：窗宽显示1500，窗位显示-600
     - 验证：图像对比度适合观察肺部组织
   
   - **软组织窗测试**：点击"软组织窗"按钮
     - 验证：窗宽显示400，窗位显示40
     - 验证：图像对比度适合观察软组织
   
   - **骨窗测试**：点击"骨窗"按钮
     - 验证：窗宽显示2000，窗位显示400
     - 验证：图像对比度适合观察骨骼结构
   
   - **脑窗测试**：点击"脑窗"按钮
     - 验证：窗宽显示80，窗位显示40
     - 验证：图像对比度适合观察脑组织

6. **测试重置功能**
   - 点击"重置到原始值"按钮
   - 验证：窗宽窗位恢复到DICOM文件的原始值
   - 验证：图像显示恢复到原始状态

#### 1.2 标准图像文件测试
1. **加载标准图像文件**
   - 点击"打开图像"按钮
   - 选择一个.png或.jpg文件
   - 验证：图像正常显示

2. **验证窗宽窗位控制隐藏**
   - ✅ 确认右侧控制面板不显示"窗宽窗位调整"区域
   - ✅ 验证只有DICOM文件才显示窗宽窗位控制

### 2. 智能标注界面测试

#### 2.1 DICOM文件测试
1. **打开智能标注界面**
   - 点击左侧导航栏的"智能标注"选项卡

2. **加载DICOM文件**
   - 点击"打开图像"按钮
   - 选择一个.dcm文件
   - 验证：图像正常显示，不再出现"DICOM文件处理功能正在开发中"的提示

3. **验证窗宽窗位控制显示**
   - ✅ 检查右侧控制面板是否显示"窗宽窗位调整"区域
   - ✅ 验证窗宽和窗位输入框是否显示DICOM文件的原始值
   - ✅ 确认四个预设按钮是否可见

4. **测试标注过程中的窗宽窗位调整**
   - 选择一个标注工具（如矩形工具）
   - 在图像上绘制一个标注
   - 调整窗宽窗位（如点击"肺窗"按钮）
   - 验证：图像显示更新，但已绘制的标注保持不变
   - 继续绘制新标注
   - 验证：新标注正常绘制，窗宽窗位调整不影响标注功能

5. **测试预设窗口在标注中的应用**
   - 根据标注的组织类型选择合适的预设窗口
   - 验证：不同预设窗口有助于更好地识别和标注不同类型的组织

#### 2.2 标准图像文件测试
1. **加载标准图像文件**
   - 点击"打开图像"按钮
   - 选择一个.png或.jpg文件
   - 验证：图像正常显示

2. **验证窗宽窗位控制隐藏**
   - ✅ 确认右侧控制面板不显示"窗宽窗位调整"区域
   - ✅ 验证标注功能正常工作

## 预期结果

### ✅ 成功标准
1. **DICOM文件支持**：
   - DICOM文件能正常加载和显示
   - 窗宽窗位控制面板正确显示
   - 原始窗宽窗位值正确读取和显示

2. **窗宽窗位调整**：
   - 自定义窗宽窗位输入立即生效
   - 四种预设窗口按钮功能正常
   - 重置功能能恢复原始值

3. **界面集成**：
   - 窗宽窗位控制仅对DICOM文件显示
   - 标准图像文件不显示窗宽窗位控制
   - 与现有功能无冲突

4. **标注兼容性**：
   - 窗宽窗位调整不影响已有标注
   - 标注过程中可正常调整窗宽窗位
   - AI辅助功能正常工作

### ❌ 失败情况处理
如果遇到以下问题：

1. **编译错误**：
   - 检查依赖项是否正确引用
   - 确认命名空间是否正确导入

2. **运行时错误**：
   - 检查DICOM服务是否正确注入
   - 验证文件路径是否正确

3. **功能异常**：
   - 检查事件订阅是否正确
   - 验证异步方法是否正确实现

## 测试报告模板

```
测试日期：[日期]
测试人员：[姓名]
应用版本：[版本号]

影像处理界面测试：
□ DICOM文件加载：通过/失败
□ 窗宽窗位控制显示：通过/失败
□ 自定义窗宽窗位：通过/失败
□ 预设窗口功能：通过/失败
□ 重置功能：通过/失败
□ 标准图像文件：通过/失败

智能标注界面测试：
□ DICOM文件加载：通过/失败
□ 窗宽窗位控制显示：通过/失败
□ 标注兼容性：通过/失败
□ 预设窗口应用：通过/失败
□ 标准图像文件：通过/失败

总体评价：
□ 功能完全正常
□ 存在小问题但不影响使用
□ 存在严重问题需要修复

备注：[详细说明]
```

## 下一步
测试完成后，如果发现问题，请提供详细的错误信息和重现步骤，以便进行针对性的修复和优化。
