<UserControl x:Class="MedicalImageAnalysis.Wpf.Views.EnhancedImageProcessingView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200">
    
    <Grid Background="#2D2D30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="#3C3C3C" Padding="10">
            <TextBlock Text="增强图像处理与智能分析" FontSize="18" FontWeight="Bold" Foreground="White"/>
        </Border>
        
        <!-- 主内容区域 -->
        <TabControl Grid.Row="1" Background="#2D2D30" BorderBrush="#555">
            
            <!-- 图像处理标签页 -->
            <TabItem Header="图像处理" Foreground="White">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="300"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 控制面板 -->
                    <Border Grid.Column="0" Background="#3C3C3C" Padding="10" Margin="0,0,10,0">
                        <StackPanel>
                            <TextBlock Text="图像处理工具" FontSize="16" FontWeight="Bold" Foreground="White" Margin="0,0,0,15"/>
                            
                            <!-- 边缘检测 -->
                            <GroupBox Header="边缘检测" Foreground="White" Margin="0,0,0,15">
                                <StackPanel>
                                    <TextBlock Text="检测方法:" Foreground="White" Margin="0,5"/>
                                    <ComboBox x:Name="EdgeDetectionMethodComboBox" Margin="0,5"/>
                                    
                                    <TextBlock Text="阈值:" Foreground="White" Margin="0,10,0,5"/>
                                    <Slider x:Name="EdgeDetectionThresholdSlider" Minimum="0.1" Maximum="1.0" Value="0.5" Margin="0,5"/>
                                    <TextBlock Text="{Binding ElementName=EdgeDetectionThresholdSlider, Path=Value, StringFormat=F2}" 
                                               Foreground="White" HorizontalAlignment="Center"/>
                                    
                                    <Button Content="应用边缘检测" Click="ApplyEdgeDetection_Click" 
                                            Background="#007ACC" Foreground="White" Padding="10,5" Margin="0,10"/>
                                </StackPanel>
                            </GroupBox>
                            
                            <!-- 形态学操作 -->
                            <GroupBox Header="形态学操作" Foreground="White" Margin="0,0,0,15">
                                <StackPanel>
                                    <TextBlock Text="操作类型:" Foreground="White" Margin="0,5"/>
                                    <ComboBox x:Name="MorphologyOperationComboBox" Margin="0,5"/>
                                    
                                    <TextBlock Text="核大小:" Foreground="White" Margin="0,10,0,5"/>
                                    <Slider x:Name="MorphologyKernelSizeSlider" Minimum="3" Maximum="15" Value="5" 
                                            IsSnapToTickEnabled="True" TickFrequency="2" Margin="0,5"/>
                                    <TextBlock Text="{Binding ElementName=MorphologyKernelSizeSlider, Path=Value, StringFormat=F0}" 
                                               Foreground="White" HorizontalAlignment="Center"/>
                                    
                                    <Button Content="应用形态学操作" Click="ApplyMorphology_Click" 
                                            Background="#007ACC" Foreground="White" Padding="10,5" Margin="0,10"/>
                                </StackPanel>
                            </GroupBox>
                            
                            <!-- 图像增强 -->
                            <GroupBox Header="图像增强" Foreground="White">
                                <StackPanel>
                                    <TextBlock Text="增强类型:" Foreground="White" Margin="0,5"/>
                                    <ComboBox x:Name="EnhancementTypeComboBox" Margin="0,5"/>
                                    
                                    <TextBlock Text="强度:" Foreground="White" Margin="0,10,0,5"/>
                                    <Slider x:Name="EnhancementStrengthSlider" Minimum="0.1" Maximum="2.0" Value="1.0" Margin="0,5"/>
                                    <TextBlock Text="{Binding ElementName=EnhancementStrengthSlider, Path=Value, StringFormat=F2}" 
                                               Foreground="White" HorizontalAlignment="Center"/>
                                    
                                    <Button Content="应用图像增强" Click="ApplyImageEnhancement_Click" 
                                            Background="#007ACC" Foreground="White" Padding="10,5" Margin="0,10"/>
                                </StackPanel>
                            </GroupBox>
                        </StackPanel>
                    </Border>
                    
                    <!-- 图像显示区域 -->
                    <Border Grid.Column="1" Background="#1E1E1E" BorderBrush="#555" BorderThickness="1">
                        <Grid>
                            <Image x:Name="ProcessedImageDisplay" Stretch="Uniform"/>
                            <TextBlock Text="图像显示区域" Foreground="#666" FontSize="24" 
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Grid>
                    </Border>
                </Grid>
            </TabItem>
            
            <!-- 智能标注标签页 -->
            <TabItem Header="智能标注" Foreground="White">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="300"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 标注控制面板 -->
                    <Border Grid.Column="0" Background="#3C3C3C" Padding="10" Margin="0,0,10,0">
                        <StackPanel>
                            <TextBlock Text="智能标注工具" FontSize="16" FontWeight="Bold" Foreground="White" Margin="0,0,0,15"/>
                            
                            <GroupBox Header="标注生成" Foreground="White" Margin="0,0,0,15">
                                <StackPanel>
                                    <CheckBox Content="启用多模型集成" Foreground="White" IsChecked="True" Margin="0,5"/>
                                    <CheckBox Content="启用上下文分析" Foreground="White" IsChecked="True" Margin="0,5"/>
                                    <CheckBox Content="启用解剖结构检测" Foreground="White" IsChecked="True" Margin="0,5"/>
                                    
                                    <TextBlock Text="置信度阈值:" Foreground="White" Margin="0,10,0,5"/>
                                    <Slider x:Name="AnnotationConfidenceSlider" Minimum="0.1" Maximum="1.0" Value="0.5" Margin="0,5"/>
                                    <TextBlock Text="{Binding ElementName=AnnotationConfidenceSlider, Path=Value, StringFormat=P1}" 
                                               Foreground="White" HorizontalAlignment="Center"/>
                                    
                                    <Button Content="生成智能标注" Click="GenerateSmartAnnotations_Click" 
                                            Background="#28A745" Foreground="White" Padding="10,5" Margin="0,10"/>
                                </StackPanel>
                            </GroupBox>
                            
                            <GroupBox Header="标注推荐" Foreground="White">
                                <ListBox x:Name="********************************" Background="#2D2D30" 
                                         Foreground="White" Height="200" Margin="0,5"/>
                            </GroupBox>
                        </StackPanel>
                    </Border>
                    
                    <!-- 标注显示区域 -->
                    <Border Grid.Column="1" Background="#1E1E1E" BorderBrush="#555" BorderThickness="1">
                        <Grid>
                            <Canvas x:Name="AnnotationCanvas" Background="Transparent"/>
                            <TextBlock Text="标注显示区域" Foreground="#666" FontSize="24" 
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Grid>
                    </Border>
                </Grid>
            </TabItem>
            
            <!-- 模型训练标签页 -->
            <TabItem Header="模型训练" Foreground="White">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="350"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 训练配置面板 -->
                    <Border Grid.Column="0" Background="#3C3C3C" Padding="10" Margin="0,0,10,0">
                        <StackPanel>
                            <TextBlock Text="模型训练配置" FontSize="16" FontWeight="Bold" Foreground="White" Margin="0,0,0,15"/>
                            
                            <GroupBox Header="数据集配置" Foreground="White" Margin="0,0,0,15">
                                <StackPanel>
                                    <TextBlock Text="数据集路径:" Foreground="White" Margin="0,5"/>
                                    <TextBox x:Name="DatasetPathTextBox" Margin="0,5"/>
                                    
                                    <TextBlock Text="输出路径:" Foreground="White" Margin="0,10,0,5"/>
                                    <TextBox x:Name="OutputPathTextBox" Margin="0,5"/>
                                </StackPanel>
                            </GroupBox>
                            
                            <GroupBox Header="训练参数" Foreground="White" Margin="0,0,0,15">
                                <StackPanel>
                                    <TextBlock Text="训练轮数:" Foreground="White" Margin="0,5"/>
                                    <Slider x:Name="EpochsSlider" Minimum="10" Maximum="500" Value="100" 
                                            IsSnapToTickEnabled="True" TickFrequency="10" Margin="0,5"/>
                                    <TextBlock Text="{Binding ElementName=EpochsSlider, Path=Value, StringFormat=F0}" 
                                               Foreground="White" HorizontalAlignment="Center"/>
                                    
                                    <TextBlock Text="批次大小:" Foreground="White" Margin="0,10,0,5"/>
                                    <Slider x:Name="BatchSizeSlider" Minimum="4" Maximum="64" Value="16" 
                                            IsSnapToTickEnabled="True" TickFrequency="4" Margin="0,5"/>
                                    <TextBlock Text="{Binding ElementName=BatchSizeSlider, Path=Value, StringFormat=F0}" 
                                               Foreground="White" HorizontalAlignment="Center"/>
                                    
                                    <TextBlock Text="学习率:" Foreground="White" Margin="0,10,0,5"/>
                                    <Slider x:Name="LearningRateSlider" Minimum="0.0001" Maximum="0.1" Value="0.001" Margin="0,5"/>
                                    <TextBlock Text="{Binding ElementName=LearningRateSlider, Path=Value, StringFormat=F4}" 
                                               Foreground="White" HorizontalAlignment="Center"/>
                                    
                                    <CheckBox x:Name="EarlyStoppingCheckBox" Content="启用早停" Foreground="White" 
                                              IsChecked="True" Margin="0,10"/>
                                </StackPanel>
                            </GroupBox>
                            
                            <Button Content="开始训练" Click="StartModelTraining_Click" 
                                    Background="#DC3545" Foreground="White" Padding="15,8" FontSize="14" FontWeight="Bold"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- 训练监控面板 -->
                    <Border Grid.Column="1" Background="#1E1E1E" BorderBrush="#555" BorderThickness="1" Padding="15">
                        <StackPanel>
                            <TextBlock Text="训练监控" FontSize="18" FontWeight="Bold" Foreground="White" Margin="0,0,0,20"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="训练进度" FontSize="14" FontWeight="Bold" Foreground="White" Margin="0,0,0,10"/>
                                    <ProgressBar x:Name="TrainingProgressBar" Height="20" Margin="0,5"/>
                                    <TextBlock x:Name="TrainingStatusText" Text="等待开始..." Foreground="White" 
                                               HorizontalAlignment="Center" Margin="0,5"/>
                                    
                                    <TextBlock Text="当前损失" FontSize="14" FontWeight="Bold" Foreground="White" Margin="0,20,0,5"/>
                                    <TextBlock x:Name="CurrentLossText" Text="--" Foreground="#FFC107" FontSize="16" 
                                               HorizontalAlignment="Center"/>
                                    
                                    <TextBlock Text="当前准确率" FontSize="14" FontWeight="Bold" Foreground="White" Margin="0,15,0,5"/>
                                    <TextBlock x:Name="CurrentAccuracyText" Text="--" Foreground="#28A745" FontSize="16" 
                                               HorizontalAlignment="Center"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="训练图表" FontSize="14" FontWeight="Bold" Foreground="White" Margin="0,0,0,10"/>
                                    <Border Background="#2D2D30" Height="200" BorderBrush="#555" BorderThickness="1">
                                        <TextBlock Text="训练曲线图表区域" Foreground="#666" 
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </Grid>
            </TabItem>
            
            <!-- 模型评估标签页 -->
            <TabItem Header="模型评估" Foreground="White">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="300"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 评估配置面板 -->
                    <Border Grid.Column="0" Background="#3C3C3C" Padding="10" Margin="0,0,10,0">
                        <StackPanel>
                            <TextBlock Text="模型评估配置" FontSize="16" FontWeight="Bold" Foreground="White" Margin="0,0,0,15"/>
                            
                            <GroupBox Header="模型配置" Foreground="White" Margin="0,0,0,15">
                                <StackPanel>
                                    <TextBlock Text="模型路径:" Foreground="White" Margin="0,5"/>
                                    <TextBox x:Name="ModelPathTextBox" Margin="0,5"/>
                                    
                                    <TextBlock Text="测试数据路径:" Foreground="White" Margin="0,10,0,5"/>
                                    <TextBox x:Name="TestDataPathTextBox" Margin="0,5"/>
                                    
                                    <TextBlock Text="输出路径:" Foreground="White" Margin="0,10,0,5"/>
                                    <TextBox x:Name="EvaluationOutputPathTextBox" Margin="0,5"/>
                                </StackPanel>
                            </GroupBox>
                            
                            <GroupBox Header="评估选项" Foreground="White" Margin="0,0,0,15">
                                <StackPanel>
                                    <CheckBox Content="性能评估" Foreground="White" IsChecked="True" Margin="0,5"/>
                                    <CheckBox Content="速度评估" Foreground="White" IsChecked="True" Margin="0,5"/>
                                    <CheckBox x:Name="RobustnessCheckBox" Content="鲁棒性评估" Foreground="White" Margin="0,5"/>
                                </StackPanel>
                            </GroupBox>
                            
                            <Button Content="开始评估" Click="EvaluateModel_Click" 
                                    Background="#6F42C1" Foreground="White" Padding="15,8" FontSize="14" FontWeight="Bold"/>
                            
                            <StackPanel Margin="0,15,0,0">
                                <ProgressBar x:Name="EvaluationProgressBar" Height="15" Margin="0,5"/>
                                <TextBlock x:Name="EvaluationStatusText" Text="等待开始..." Foreground="White" 
                                           HorizontalAlignment="Center" Margin="0,5"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                    
                    <!-- 评估结果面板 -->
                    <Border Grid.Column="1" Background="#1E1E1E" BorderBrush="#555" BorderThickness="1" Padding="15">
                        <StackPanel>
                            <TextBlock Text="评估结果" FontSize="18" FontWeight="Bold" Foreground="White" Margin="0,0,0,20"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                    <TextBlock Text="性能指标" FontSize="16" FontWeight="Bold" Foreground="White" Margin="0,0,0,15"/>
                                    
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        
                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="mAP@0.5:" Foreground="White" Margin="0,5,10,5"/>
                                        <TextBlock Grid.Row="0" Grid.Column="1" x:Name="Map50Text" Text="--" Foreground="#28A745" Margin="0,5"/>
                                        
                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="mAP@0.5:0.95:" Foreground="White" Margin="0,5,10,5"/>
                                        <TextBlock Grid.Row="1" Grid.Column="1" x:Name="Map5095Text" Text="--" Foreground="#28A745" Margin="0,5"/>
                                        
                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="精确率:" Foreground="White" Margin="0,5,10,5"/>
                                        <TextBlock Grid.Row="2" Grid.Column="1" x:Name="PrecisionText" Text="--" Foreground="#17A2B8" Margin="0,5"/>
                                        
                                        <TextBlock Grid.Row="3" Grid.Column="0" Text="召回率:" Foreground="White" Margin="0,5,10,5"/>
                                        <TextBlock Grid.Row="3" Grid.Column="1" x:Name="RecallText" Text="--" Foreground="#FFC107" Margin="0,5"/>
                                    </Grid>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                    <TextBlock Text="速度指标" FontSize="16" FontWeight="Bold" Foreground="White" Margin="0,0,0,15"/>
                                    
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        
                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="平均推理时间:" Foreground="White" Margin="0,5,10,5"/>
                                        <TextBlock Grid.Row="0" Grid.Column="1" x:Name="AvgInferenceTimeText" Text="--" Foreground="#DC3545" Margin="0,5"/>
                                        
                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="FPS:" Foreground="White" Margin="0,5,10,5"/>
                                        <TextBlock Grid.Row="1" Grid.Column="1" x:Name="FpsText" Text="--" Foreground="#6F42C1" Margin="0,5"/>
                                    </Grid>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </Grid>
            </TabItem>
        </TabControl>
        
        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#007ACC" Padding="10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock x:Name="StatusText" Text="就绪" Foreground="White" VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <ProgressBar x:Name="ProgressBar" Width="150" Height="15" Visibility="Collapsed" Margin="0,0,10,0"/>
                    <TextBlock x:Name="ProgressText" Text="" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
