
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YOLO-OHIF Medical Image Detection API - API文档</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
        }
        h3 {
            color: #7f8c8d;
        }
        .endpoint {
            border: 1px solid #ddd;
            margin: 20px 0;
            border-radius: 5px;
            overflow: hidden;
        }
        .endpoint-header {
            background: #ecf0f1;
            padding: 15px;
            font-weight: bold;
        }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            color: white;
            font-size: 12px;
            margin-right: 10px;
        }
        .method.get { background: #27ae60; }
        .method.post { background: #e74c3c; }
        .method.put { background: #f39c12; }
        .method.delete { background: #e67e22; }
        .endpoint-body {
            padding: 15px;
        }
        .parameters, .responses {
            margin: 15px 0;
        }
        .param {
            background: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #3498db;
        }
        .required {
            color: #e74c3c;
            font-weight: bold;
        }
        .optional {
            color: #95a5a6;
        }
        .response {
            background: #f1f2f6;
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #2ecc71;
        }
        .code {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        .tag {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            margin: 2px;
        }
        .auth-required {
            background: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }
        .deprecated {
            background: #e74c3c;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>YOLO-OHIF Medical Image Detection API</h1>
        <p><strong>版本:</strong> 1.0.0</p>
        <p><strong>描述:</strong> 医学图像检测系统API文档</p>
        
        <h2>API端点</h2>
        
        
        <h2>数据模式</h2>
        
            <h3>User</h3>
            <div class="code">
                <pre>{
  "type": "object",
  "properties": {
    "id": {
      "type": "integer",
      "description": "用户ID"
    },
    "username": {
      "type": "string",
      "description": "用户名"
    },
    "email": {
      "type": "string",
      "format": "email",
      "description": "邮箱地址"
    },
    "role": {
      "type": "string",
      "enum": [
        "admin",
        "doctor",
        "technician"
      ],
      "description": "用户角色"
    },
    "created_at": {
      "type": "string",
      "format": "date-time",
      "description": "创建时间"
    }
  },
  "required": [
    "username",
    "email",
    "role"
  ]
}</pre>
            </div>
            
            <h3>Study</h3>
            <div class="code">
                <pre>{
  "type": "object",
  "properties": {
    "id": {
      "type": "integer",
      "description": "研究ID"
    },
    "orthanc_id": {
      "type": "string",
      "description": "Orthanc系统ID"
    },
    "patient_id": {
      "type": "string",
      "description": "患者ID"
    },
    "patient_name": {
      "type": "string",
      "description": "患者姓名"
    },
    "study_date": {
      "type": "string",
      "format": "date",
      "description": "检查日期"
    },
    "modality": {
      "type": "string",
      "description": "检查方式"
    },
    "description": {
      "type": "string",
      "description": "研究描述"
    }
  },
  "required": [
    "orthanc_id",
    "patient_id"
  ]
}</pre>
            </div>
            
            <h3>DetectionResult</h3>
            <div class="code">
                <pre>{
  "type": "object",
  "properties": {
    "id": {
      "type": "integer",
      "description": "检测结果ID"
    },
    "study_id": {
      "type": "integer",
      "description": "研究ID"
    },
    "user_id": {
      "type": "integer",
      "description": "用户ID"
    },
    "detection_time": {
      "type": "string",
      "format": "date-time",
      "description": "检测时间"
    },
    "results": {
      "type": "object",
      "description": "检测结果JSON"
    },
    "confidence_threshold": {
      "type": "number",
      "description": "置信度阈值"
    },
    "model_version": {
      "type": "string",
      "description": "模型版本"
    }
  },
  "required": [
    "study_id",
    "user_id",
    "results"
  ]
}</pre>
            </div>
            
            <h3>HealthStatus</h3>
            <div class="code">
                <pre>{
  "type": "object",
  "properties": {
    "status": {
      "type": "string",
      "enum": [
        "healthy",
        "unhealthy"
      ],
      "description": "健康状态"
    },
    "orthanc": {
      "type": "object",
      "description": "Orthanc服务状态"
    },
    "detection_service": {
      "type": "object",
      "description": "检测服务状态"
    },
    "database": {
      "type": "object",
      "description": "数据库状态"
    },
    "timestamp": {
      "type": "string",
      "format": "date-time",
      "description": "检查时间"
    }
  }
}</pre>
            </div>
            
            <h3>ErrorResponse</h3>
            <div class="code">
                <pre>{
  "type": "object",
  "properties": {
    "error": {
      "type": "string",
      "description": "错误信息"
    },
    "code": {
      "type": "integer",
      "description": "错误代码"
    },
    "details": {
      "type": "object",
      "description": "错误详情"
    }
  },
  "required": [
    "error"
  ]
}</pre>
            </div>
            
    </div>
</body>
</html>
        