using MedicalImageAnalysis.Core.Models;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Text;

namespace MedicalImageAnalysis.Infrastructure.Services;

/// <summary>
/// NnUNetService 辅助方法
/// </summary>
public partial class NnUNetService
{
    /// <summary>
    /// 执行Python脚本并监控进度
    /// </summary>
    private async Task<ProcessResult> ExecutePythonScriptWithMonitoringAsync(
        string scriptPath,
        NnUNetTrainingConfig config,
        Dictionary<string, string> environmentVariables,
        IProgress<NnUNetTrainingProgress>? progressCallback,
        CancellationToken cancellationToken)
    {
        var result = new ProcessResult();

        try
        {
            var processStartInfo = new ProcessStartInfo
            {
                FileName = _pythonExecutable,
                Arguments = $"\"{scriptPath}\"",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true,
                WorkingDirectory = _nnunetScriptsPath
            };

            // 设置环境变量
            foreach (var kvp in environmentVariables)
            {
                processStartInfo.EnvironmentVariables[kvp.Key] = kvp.Value;
            }

            using var process = new Process { StartInfo = processStartInfo };
            
            var outputBuilder = new StringBuilder();
            var errorBuilder = new StringBuilder();

            process.OutputDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    outputBuilder.AppendLine(e.Data);
                    ParseTrainingProgress(e.Data, progressCallback);
                }
            };

            process.ErrorDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    errorBuilder.AppendLine(e.Data);
                    _logger.LogWarning("nnUNet 训练错误输出: {Error}", e.Data);
                }
            };

            process.Start();
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            await process.WaitForExitAsync(cancellationToken);

            result.Success = process.ExitCode == 0;
            result.Output = outputBuilder.ToString();
            result.ErrorMessage = errorBuilder.ToString();

            if (!result.Success)
            {
                _logger.LogError("Python脚本执行失败，退出代码: {ExitCode}", process.ExitCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行Python脚本时发生异常");
            result.Success = false;
            result.ErrorMessage = ex.Message;
        }

        return result;
    }

    /// <summary>
    /// 执行Python脚本
    /// </summary>
    private async Task<ProcessResult> ExecutePythonScriptAsync(
        string scriptPath,
        Dictionary<string, string> environmentVariables,
        IProgress<string>? progressCallback,
        CancellationToken cancellationToken)
    {
        var result = new ProcessResult();

        try
        {
            var processStartInfo = new ProcessStartInfo
            {
                FileName = _pythonExecutable,
                Arguments = $"\"{scriptPath}\"",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true,
                WorkingDirectory = _nnunetScriptsPath
            };

            // 设置环境变量
            foreach (var kvp in environmentVariables)
            {
                processStartInfo.EnvironmentVariables[kvp.Key] = kvp.Value;
            }

            using var process = new Process { StartInfo = processStartInfo };
            
            var outputBuilder = new StringBuilder();
            var errorBuilder = new StringBuilder();

            process.OutputDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    outputBuilder.AppendLine(e.Data);
                    progressCallback?.Report(e.Data);
                }
            };

            process.ErrorDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    errorBuilder.AppendLine(e.Data);
                    progressCallback?.Report($"错误: {e.Data}");
                }
            };

            process.Start();
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            await process.WaitForExitAsync(cancellationToken);

            result.Success = process.ExitCode == 0;
            result.Output = outputBuilder.ToString();
            result.ErrorMessage = errorBuilder.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行Python脚本时发生异常");
            result.Success = false;
            result.ErrorMessage = ex.Message;
        }

        return result;
    }

    /// <summary>
    /// 解析训练进度
    /// </summary>
    private void ParseTrainingProgress(string output, IProgress<NnUNetTrainingProgress>? progressCallback)
    {
        if (progressCallback == null || string.IsNullOrEmpty(output))
            return;

        try
        {
            // 解析epoch信息
            var epochMatch = Regex.Match(output, @"Epoch (\d+)/(\d+)");
            if (epochMatch.Success)
            {
                var currentEpoch = int.Parse(epochMatch.Groups[1].Value);
                var totalEpochs = int.Parse(epochMatch.Groups[2].Value);

                var progress = new NnUNetTrainingProgress
                {
                    CurrentEpoch = currentEpoch,
                    TotalEpochs = totalEpochs,
                    StatusMessage = output.Trim()
                };

                // 解析损失值
                var lossMatch = Regex.Match(output, @"loss: ([\d.]+)");
                if (lossMatch.Success)
                {
                    progress.TrainingLoss = double.Parse(lossMatch.Groups[1].Value);
                }

                // 解析Dice系数
                var diceMatch = Regex.Match(output, @"dice: ([\d.]+)");
                if (diceMatch.Success)
                {
                    progress.DiceScore = double.Parse(diceMatch.Groups[1].Value);
                }

                // 解析学习率
                var lrMatch = Regex.Match(output, @"lr: ([\d.e-]+)");
                if (lrMatch.Success)
                {
                    progress.LearningRate = double.Parse(lrMatch.Groups[1].Value);
                }

                progressCallback.Report(progress);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "解析训练进度时发生异常: {Output}", output);
        }
    }

    /// <summary>
    /// 查找最佳模型路径
    /// </summary>
    private string FindBestModelPath(string outputDir, NnUNetTrainingConfig config)
    {
        try
        {
            var modelDir = Path.Combine(outputDir, "nnUNetTrainer__nnUNetPlans__3d_fullres");
            var bestModelPath = Path.Combine(modelDir, "fold_0", "checkpoint_best.pth");
            
            if (File.Exists(bestModelPath))
            {
                return bestModelPath;
            }

            // 如果找不到，尝试查找其他可能的路径
            var modelFiles = Directory.GetFiles(outputDir, "*best*.pth", SearchOption.AllDirectories);
            return modelFiles.FirstOrDefault() ?? "";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "查找最佳模型路径时发生异常");
            return "";
        }
    }

    /// <summary>
    /// 查找最终模型路径
    /// </summary>
    private string FindFinalModelPath(string outputDir, NnUNetTrainingConfig config)
    {
        try
        {
            var modelDir = Path.Combine(outputDir, "nnUNetTrainer__nnUNetPlans__3d_fullres");
            var finalModelPath = Path.Combine(modelDir, "fold_0", "checkpoint_final.pth");
            
            if (File.Exists(finalModelPath))
            {
                return finalModelPath;
            }

            // 如果找不到，尝试查找其他可能的路径
            var modelFiles = Directory.GetFiles(outputDir, "*final*.pth", SearchOption.AllDirectories);
            return modelFiles.FirstOrDefault() ?? "";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "查找最终模型路径时发生异常");
            return "";
        }
    }

    /// <summary>
    /// 查找日志文件路径
    /// </summary>
    private string FindLogFilePath(string outputDir)
    {
        try
        {
            var logFiles = Directory.GetFiles(outputDir, "*.log", SearchOption.AllDirectories);
            return logFiles.OrderByDescending(f => File.GetLastWriteTime(f)).FirstOrDefault() ?? "";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "查找日志文件时发生异常");
            return "";
        }
    }

    /// <summary>
    /// 解析训练指标
    /// </summary>
    private async Task<NnUNetTrainingMetrics> ParseTrainingMetricsAsync(string outputDir)
    {
        var metrics = new NnUNetTrainingMetrics();

        try
        {
            // 查找训练日志文件
            var logFile = FindLogFilePath(outputDir);
            if (!string.IsNullOrEmpty(logFile) && File.Exists(logFile))
            {
                var logContent = await File.ReadAllTextAsync(logFile);
                
                // 解析最佳Dice系数
                var diceMatches = Regex.Matches(logContent, @"best dice: ([\d.]+)");
                if (diceMatches.Count > 0)
                {
                    metrics.BestDiceScore = double.Parse(diceMatches.Last().Groups[1].Value);
                }

                // 解析最终损失
                var lossMatches = Regex.Matches(logContent, @"final loss: ([\d.]+)");
                if (lossMatches.Count > 0)
                {
                    metrics.FinalTrainingLoss = double.Parse(lossMatches.Last().Groups[1].Value);
                }
            }

            // 查找验证结果文件
            var validationFiles = Directory.GetFiles(outputDir, "*validation*.json", SearchOption.AllDirectories);
            if (validationFiles.Length > 0)
            {
                var validationFile = validationFiles.First();
                var validationContent = await File.ReadAllTextAsync(validationFile);
                var validationData = JsonSerializer.Deserialize<Dictionary<string, object>>(validationContent);
                
                if (validationData != null && validationData.ContainsKey("mean_dice"))
                {
                    metrics.BestDiceScore = Convert.ToDouble(validationData["mean_dice"]);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "解析训练指标时发生异常");
        }

        return metrics;
    }

    /// <summary>
    /// 创建环境检查脚本
    /// </summary>
    private async Task CreateEnvironmentCheckScriptAsync(string scriptPath)
    {
        var scriptContent = @"#!/usr/bin/env python3
import sys
import subprocess
import importlib

def check_package(package_name):
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False

def main():
    print('检查 nnUNet 环境...')

    # 检查Python版本
    if sys.version_info < (3, 8):
        print(f'错误: Python版本过低 ({sys.version}), 需要Python 3.8+')
        return 1

    print(f'✓ Python版本: {sys.version}')

    # 检查必需的包
    required_packages = [
        'torch',
        'torchvision',
        'numpy',
        'scipy',
        'nibabel',
        'SimpleITK',
        'batchgenerators',
        'nnunetv2'
    ]

    missing_packages = []
    for package in required_packages:
        if check_package(package):
            print(f'✓ {package}')
        else:
            print(f'✗ {package} (缺失)')
            missing_packages.append(package)

    if missing_packages:
        print(f'缺失的包: {missing_packages}')
        return 1

    print('✓ nnUNet 环境检查通过')
    return 0

if __name__ == '__main__':
    sys.exit(main())
";

        await File.WriteAllTextAsync(scriptPath, scriptContent);
    }

    /// <summary>
    /// 创建依赖安装脚本
    /// </summary>
    private async Task CreateInstallDependenciesScriptAsync(string scriptPath)
    {
        var scriptContent = @"#!/usr/bin/env python3
import subprocess
import sys

def install_package(package):
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    print('安装 nnUNet 依赖...')

    packages = [
        'torch>=1.9.0',
        'torchvision',
        'numpy',
        'scipy',
        'nibabel',
        'SimpleITK',
        'batchgenerators>=0.25',
        'nnunetv2'
    ]

    failed_packages = []
    for package in packages:
        print(f'安装 {package}...')
        if install_package(package):
            print(f'✓ {package} 安装成功')
        else:
            print(f'✗ {package} 安装失败')
            failed_packages.append(package)

    if failed_packages:
        print(f'安装失败的包: {failed_packages}')
        return 1

    print('✓ 所有依赖安装完成')
    return 0

if __name__ == '__main__':
    sys.exit(main())
";

        await File.WriteAllTextAsync(scriptPath, scriptContent);
    }

    /// <summary>
    /// 创建下载预训练模型脚本
    /// </summary>
    private async Task CreateDownloadPretrainedScriptAsync(string scriptPath)
    {
        var scriptContent = @"#!/usr/bin/env python3
import os
import sys
import subprocess

def main():
    model_name = os.environ.get('MODEL_NAME', '')
    output_path = os.environ.get('OUTPUT_PATH', '')

    if not model_name or not output_path:
        print('错误: 缺少必需的环境变量 MODEL_NAME 或 OUTPUT_PATH')
        return 1

    print(f'下载预训练模型: {model_name}')

    try:
        # 使用nnUNet的下载命令
        cmd = ['nnUNetv2_download_pretrained_weights', model_name]
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print('模型下载完成')
        print(result.stdout)
        return 0
    except subprocess.CalledProcessError as e:
        print(f'模型下载失败: {e}')
        print(e.stderr)
        return 1

if __name__ == '__main__':
    sys.exit(main())
";

        await File.WriteAllTextAsync(scriptPath, scriptContent);
    }

    /// <summary>
    /// 创建集成预测脚本
    /// </summary>
    private async Task CreateEnsembleScriptAsync(string scriptPath)
    {
        var scriptContent = @"#!/usr/bin/env python3
import os
import sys
import subprocess

def main():
    model_paths = os.environ.get('MODEL_PATHS', '').split(';')
    input_path = os.environ.get('INPUT_PATH', '')
    output_path = os.environ.get('OUTPUT_PATH', '')

    if not model_paths or not input_path or not output_path:
        print('错误: 缺少必需的环境变量')
        return 1

    print(f'集成 {len(model_paths)} 个模型的预测结果')

    try:
        # 使用nnUNet的集成命令
        cmd = [
            'nnUNetv2_ensemble',
            '-i', input_path,
            '-o', output_path
        ]

        # 添加模型路径
        for model_path in model_paths:
            cmd.extend(['-m', model_path])

        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print('模型集成完成')
        print(result.stdout)
        return 0
    except subprocess.CalledProcessError as e:
        print(f'模型集成失败: {e}')
        print(e.stderr)
        return 1

if __name__ == '__main__':
    sys.exit(main())
";

        await File.WriteAllTextAsync(scriptPath, scriptContent);
    }

    /// <summary>
    /// 创建模型导出脚本
    /// </summary>
    private async Task CreateExportModelScriptAsync(string scriptPath)
    {
        var scriptContent = @"#!/usr/bin/env python3
import os
import sys
import torch

def main():
    model_path = os.environ.get('MODEL_PATH', '')
    export_format = os.environ.get('EXPORT_FORMAT', '')
    output_path = os.environ.get('OUTPUT_PATH', '')

    if not model_path or not export_format or not output_path:
        print('错误: 缺少必需的环境变量')
        return 1

    print(f'导出模型为 {export_format} 格式')

    try:
        if export_format.lower() == 'onnx':
            # 导出为ONNX格式
            model = torch.load(model_path, map_location='cpu')
            dummy_input = torch.randn(1, 1, 128, 128, 128)  # 示例输入尺寸
            torch.onnx.export(model, dummy_input, os.path.join(output_path, 'model.onnx'))
            print('ONNX导出完成')
        else:
            print(f'不支持的导出格式: {export_format}')
            return 1

        return 0
    except Exception as e:
        print(f'模型导出失败: {e}')
        return 1

if __name__ == '__main__':
    sys.exit(main())
";

        await File.WriteAllTextAsync(scriptPath, scriptContent);
    }

    /// <summary>
    /// 进程执行结果
    /// </summary>
    private class ProcessResult
    {
        public bool Success { get; set; }
        public string Output { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
