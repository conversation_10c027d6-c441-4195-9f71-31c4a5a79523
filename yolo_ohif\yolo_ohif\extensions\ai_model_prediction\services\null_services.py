"""空实现服务类

提供接口的空实现，用于最小化配置和测试场景
"""

import logging
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime

from ..core.interfaces import (
    CacheManagerInterface,
    UIComponentsInterface,
    EventManagerInterface
)
from ..core.types import BoundingBox, DetectionResult

logger = logging.getLogger(__name__)


class NullCacheService(CacheManagerInterface):
    """空缓存服务
    
    不执行任何缓存操作的空实现
    """
    
    def __init__(self):
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'size': 0,
            'max_size': 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值（总是返回None）"""
        self._stats['misses'] += 1
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值（总是返回True但不实际缓存）"""
        self._stats['sets'] += 1
        return True
    
    def delete(self, key: str) -> bool:
        """删除缓存值（总是返回True）"""
        self._stats['deletes'] += 1
        return True
    
    def clear(self) -> None:
        """清空缓存（无操作）"""
        pass
    
    def exists(self, key: str) -> bool:
        """检查键是否存在（总是返回False）"""
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return self._stats.copy()
    
    def cleanup(self) -> None:
        """清理缓存（无操作）"""
        pass


class NullUIService(UIComponentsInterface):
    """空UI服务
    
    不执行任何UI操作的空实现
    """
    
    def __init__(self):
        self._stats = {
            'buttons_created': 0,
            'panels_created': 0,
            'notifications_shown': 0,
            'overlays_created': 0,
            'components_count': 0
        }
        self._components = {}
    
    def create_toolbar_button(self, 
                              button_id: str,
                              label: str,
                              icon: str,
                              tooltip: Optional[str] = None,
                              callback: Optional[Callable] = None) -> bool:
        """创建工具栏按钮（记录但不实际创建）"""
        self._stats['buttons_created'] += 1
        self._components[button_id] = {
            'type': 'button',
            'label': label,
            'icon': icon,
            'tooltip': tooltip,
            'created_at': datetime.now().isoformat()
        }
        logger.debug(f"[NullUI] 创建工具栏按钮: {button_id}")
        return True
    
    def update_toolbar_button(self, 
                              button_id: str,
                              label: Optional[str] = None,
                              icon: Optional[str] = None,
                              enabled: Optional[bool] = None) -> bool:
        """更新工具栏按钮（记录但不实际更新）"""
        if button_id in self._components:
            component = self._components[button_id]
            if label:
                component['label'] = label
            if icon:
                component['icon'] = icon
            if enabled is not None:
                component['enabled'] = enabled
            component['updated_at'] = datetime.now().isoformat()
            logger.debug(f"[NullUI] 更新工具栏按钮: {button_id}")
            return True
        return False
    
    def remove_toolbar_button(self, button_id: str) -> bool:
        """移除工具栏按钮（记录但不实际移除）"""
        if button_id in self._components:
            del self._components[button_id]
            logger.debug(f"[NullUI] 移除工具栏按钮: {button_id}")
            return True
        return False
    
    def create_panel(self, 
                     panel_id: str,
                     title: str,
                     position: str = "right",
                     width: int = 300,
                     height: Optional[int] = None) -> bool:
        """创建面板（记录但不实际创建）"""
        self._stats['panels_created'] += 1
        self._components[panel_id] = {
            'type': 'panel',
            'title': title,
            'position': position,
            'width': width,
            'height': height,
            'created_at': datetime.now().isoformat()
        }
        logger.debug(f"[NullUI] 创建面板: {panel_id}")
        return True
    
    def update_panel(self, 
                     panel_id: str,
                     title: Optional[str] = None,
                     content: Optional[str] = None,
                     visible: Optional[bool] = None) -> bool:
        """更新面板（记录但不实际更新）"""
        if panel_id in self._components:
            component = self._components[panel_id]
            if title:
                component['title'] = title
            if content:
                component['content'] = content
            if visible is not None:
                component['visible'] = visible
            component['updated_at'] = datetime.now().isoformat()
            logger.debug(f"[NullUI] 更新面板: {panel_id}")
            return True
        return False
    
    def remove_panel(self, panel_id: str) -> bool:
        """移除面板（记录但不实际移除）"""
        if panel_id in self._components:
            del self._components[panel_id]
            logger.debug(f"[NullUI] 移除面板: {panel_id}")
            return True
        return False
    
    def create_model_selector(self, 
                              selector_id: str,
                              models: List[Dict[str, Any]],
                              callback: Optional[Callable] = None) -> bool:
        """创建模型选择器（记录但不实际创建）"""
        self._components[selector_id] = {
            'type': 'model_selector',
            'models': models,
            'created_at': datetime.now().isoformat()
        }
        logger.debug(f"[NullUI] 创建模型选择器: {selector_id}")
        return True
    
    def update_model_selector(self, 
                              selector_id: str,
                              models: Optional[List[Dict[str, Any]]] = None,
                              selected_model: Optional[str] = None) -> bool:
        """更新模型选择器（记录但不实际更新）"""
        if selector_id in self._components:
            component = self._components[selector_id]
            if models:
                component['models'] = models
            if selected_model:
                component['selected_model'] = selected_model
            component['updated_at'] = datetime.now().isoformat()
            logger.debug(f"[NullUI] 更新模型选择器: {selector_id}")
            return True
        return False
    
    def show_progress(self, 
                      progress_id: str,
                      message: str,
                      progress: float = 0.0) -> bool:
        """显示进度（记录但不实际显示）"""
        self._components[progress_id] = {
            'type': 'progress',
            'message': message,
            'progress': progress,
            'created_at': datetime.now().isoformat()
        }
        logger.debug(f"[NullUI] 显示进度: {progress_id} - {message} ({progress:.1%})")
        return True
    
    def update_progress(self, 
                        progress_id: str,
                        message: Optional[str] = None,
                        progress: Optional[float] = None) -> bool:
        """更新进度（记录但不实际更新）"""
        if progress_id in self._components:
            component = self._components[progress_id]
            if message:
                component['message'] = message
            if progress is not None:
                component['progress'] = progress
            component['updated_at'] = datetime.now().isoformat()
            logger.debug(f"[NullUI] 更新进度: {progress_id}")
            return True
        return False
    
    def hide_progress(self, progress_id: str) -> bool:
        """隐藏进度（记录但不实际隐藏）"""
        if progress_id in self._components:
            del self._components[progress_id]
            logger.debug(f"[NullUI] 隐藏进度: {progress_id}")
            return True
        return False
    
    def create_results_display(self, 
                               display_id: str,
                               results: List[DetectionResult],
                               show_confidence: bool = True) -> bool:
        """创建结果显示（记录但不实际创建）"""
        self._components[display_id] = {
            'type': 'results_display',
            'results_count': len(results),
            'show_confidence': show_confidence,
            'created_at': datetime.now().isoformat()
        }
        logger.debug(f"[NullUI] 创建结果显示: {display_id} - {len(results)}个结果")
        return True
    
    def update_results_display(self, 
                               display_id: str,
                               results: Optional[List[DetectionResult]] = None) -> bool:
        """更新结果显示（记录但不实际更新）"""
        if display_id in self._components:
            component = self._components[display_id]
            if results:
                component['results_count'] = len(results)
            component['updated_at'] = datetime.now().isoformat()
            logger.debug(f"[NullUI] 更新结果显示: {display_id}")
            return True
        return False
    
    def show_bounding_boxes(self, 
                            overlay_id: str,
                            bounding_boxes: List[BoundingBox],
                            class_names: Optional[List[str]] = None,
                            confidences: Optional[List[float]] = None) -> bool:
        """显示边界框（记录但不实际显示）"""
        self._stats['overlays_created'] += 1
        self._components[overlay_id] = {
            'type': 'bounding_boxes',
            'boxes_count': len(bounding_boxes),
            'has_class_names': class_names is not None,
            'has_confidences': confidences is not None,
            'created_at': datetime.now().isoformat()
        }
        logger.debug(f"[NullUI] 显示边界框: {overlay_id} - {len(bounding_boxes)}个框")
        return True
    
    def hide_bounding_boxes(self, overlay_id: str) -> bool:
        """隐藏边界框（记录但不实际隐藏）"""
        if overlay_id in self._components:
            del self._components[overlay_id]
            logger.debug(f"[NullUI] 隐藏边界框: {overlay_id}")
            return True
        return False
    
    def show_notification(self, 
                          message: str,
                          type: str = "info",
                          duration: int = 3000) -> bool:
        """显示通知（记录但不实际显示）"""
        self._stats['notifications_shown'] += 1
        notification_id = f"notification_{self._stats['notifications_shown']}"
        self._components[notification_id] = {
            'type': 'notification',
            'message': message,
            'notification_type': type,
            'duration': duration,
            'created_at': datetime.now().isoformat()
        }
        logger.debug(f"[NullUI] 显示通知: {type} - {message}")
        return True
    
    def show_modal(self, 
                   modal_id: str,
                   title: str,
                   content: str,
                   buttons: Optional[List[Dict[str, Any]]] = None) -> bool:
        """显示模态对话框（记录但不实际显示）"""
        self._components[modal_id] = {
            'type': 'modal',
            'title': title,
            'content': content,
            'buttons': buttons or [],
            'created_at': datetime.now().isoformat()
        }
        logger.debug(f"[NullUI] 显示模态对话框: {modal_id} - {title}")
        return True
    
    def hide_modal(self, modal_id: str) -> bool:
        """隐藏模态对话框（记录但不实际隐藏）"""
        if modal_id in self._components:
            del self._components[modal_id]
            logger.debug(f"[NullUI] 隐藏模态对话框: {modal_id}")
            return True
        return False
    
    def set_theme(self, theme: str) -> bool:
        """设置主题（记录但不实际设置）"""
        logger.debug(f"[NullUI] 设置主题: {theme}")
        return True
    
    def register_event_handler(self, 
                               component_id: str,
                               event_type: str,
                               handler: Callable) -> bool:
        """注册事件处理器（记录但不实际注册）"""
        if component_id in self._components:
            component = self._components[component_id]
            if 'event_handlers' not in component:
                component['event_handlers'] = {}
            component['event_handlers'][event_type] = handler.__name__
            logger.debug(f"[NullUI] 注册事件处理器: {component_id}.{event_type}")
            return True
        return False
    
    def get_component_tree(self) -> Dict[str, Any]:
        """获取组件树"""
        return {
            'components': self._components,
            'stats': self._stats,
            'timestamp': datetime.now().isoformat()
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取UI统计信息"""
        self._stats['components_count'] = len(self._components)
        return self._stats.copy()
    
    def cleanup(self) -> None:
        """清理UI组件（清空记录）"""
        self._components.clear()
        logger.debug("[NullUI] 清理UI组件")


class NullEventService(EventManagerInterface):
    """空事件服务
    
    不执行任何事件操作的空实现
    """
    
    def __init__(self):
        self._stats = {
            'events_published': 0,
            'subscriptions_created': 0,
            'subscriptions_removed': 0,
            'active_subscriptions': 0
        }
        self._event_log = []
    
    def subscribe(self, 
                  event_type: str,
                  callback: Callable,
                  priority: int = 0,
                  once: bool = False) -> str:
        """订阅事件（记录但不实际订阅）"""
        self._stats['subscriptions_created'] += 1
        self._stats['active_subscriptions'] += 1
        subscription_id = f"sub_{self._stats['subscriptions_created']}"
        
        logger.debug(f"[NullEvent] 订阅事件: {event_type} -> {callback.__name__}")
        return subscription_id
    
    def unsubscribe(self, subscription_id: str) -> bool:
        """取消订阅（记录但不实际取消）"""
        self._stats['subscriptions_removed'] += 1
        self._stats['active_subscriptions'] = max(0, self._stats['active_subscriptions'] - 1)
        
        logger.debug(f"[NullEvent] 取消订阅: {subscription_id}")
        return True
    
    def publish(self, event_type: str, data: Any = None) -> int:
        """发布事件（记录但不实际发布）"""
        self._stats['events_published'] += 1
        
        event_record = {
            'event_type': event_type,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }
        self._event_log.append(event_record)
        
        # 只保留最近100个事件记录
        if len(self._event_log) > 100:
            self._event_log = self._event_log[-100:]
        
        logger.debug(f"[NullEvent] 发布事件: {event_type}")
        return 0  # 返回0个处理器被调用
    
    def get_subscribers(self, event_type: str) -> List[str]:
        """获取订阅者列表（总是返回空列表）"""
        return []
    
    def clear_subscribers(self, event_type: Optional[str] = None) -> int:
        """清除订阅者（记录但不实际清除）"""
        if event_type:
            logger.debug(f"[NullEvent] 清除事件订阅者: {event_type}")
        else:
            logger.debug("[NullEvent] 清除所有事件订阅者")
            self._stats['active_subscriptions'] = 0
        return 0
    
    def get_event_history(self, 
                          event_type: Optional[str] = None,
                          limit: int = 50) -> List[Dict[str, Any]]:
        """获取事件历史"""
        if event_type:
            filtered_events = [
                event for event in self._event_log 
                if event['event_type'] == event_type
            ]
            return filtered_events[-limit:]
        else:
            return self._event_log[-limit:]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取事件统计信息"""
        return self._stats.copy()
    
    def cleanup(self) -> None:
        """清理事件服务（清空记录）"""
        self._event_log.clear()
        self._stats['active_subscriptions'] = 0
        logger.debug("[NullEvent] 清理事件服务")


class LoggingNullService:
    """带日志记录的空服务基类
    
    为空服务提供统一的日志记录功能
    """
    
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.logger = logging.getLogger(f"null_service.{service_name}")
        self.call_count = 0
        self.method_calls = {}
    
    def _log_call(self, method_name: str, *args, **kwargs):
        """记录方法调用"""
        self.call_count += 1
        if method_name not in self.method_calls:
            self.method_calls[method_name] = 0
        self.method_calls[method_name] += 1
        
        self.logger.debug(
            f"[{self.service_name}] {method_name} called "
            f"(#{self.method_calls[method_name]}) "
            f"with args={args}, kwargs={kwargs}"
        )
    
    def get_call_stats(self) -> Dict[str, Any]:
        """获取调用统计"""
        return {
            'service_name': self.service_name,
            'total_calls': self.call_count,
            'method_calls': self.method_calls.copy(),
            'timestamp': datetime.now().isoformat()
        }