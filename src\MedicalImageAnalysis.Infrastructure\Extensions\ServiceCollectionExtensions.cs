using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Infrastructure.Algorithms;
using MedicalImageAnalysis.Infrastructure.Services;
using MedicalImageAnalysis.Infrastructure.Pipelines;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace MedicalImageAnalysis.Infrastructure.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 注册医学影像分析服务
    /// </summary>
    public static IServiceCollection AddMedicalImageAnalysisServices(this IServiceCollection services)
    {
        // 注册算法库
        services.AddSingleton<MedicalImageProcessingAlgorithms>();
        services.AddSingleton<TextureAnalysisAlgorithms>();
        services.AddSingleton<SmartAnnotationAlgorithms>();
        services.AddSingleton<TrainingPipelineAlgorithms>();
        services.AddSingleton<MultiScaleAnalysisAlgorithms>();
        services.AddSingleton<AIAnnotationAlgorithms>();

        // 注册核心服务
        services.AddScoped<IImageProcessingService, ImageProcessingService>();
        services.AddScoped<IAdvancedImageProcessingService, AdvancedImageProcessingService>();
        services.AddScoped<ISmartAnnotationService, SmartAnnotationService>();
        services.AddScoped<IYoloService, YoloService>();
        services.AddScoped<INnUNetService, NnUNetService>();

        // 注册管道服务
        services.AddScoped<EndToEndTrainingPipeline>();

        // 注册其他服务
        services.AddScoped<TrainingMonitoringService>();

        return services;
    }

    /// <summary>
    /// 注册高级图像处理算法
    /// </summary>
    public static IServiceCollection AddAdvancedImageProcessingAlgorithms(this IServiceCollection services)
    {
        services.AddSingleton<MedicalImageProcessingAlgorithms>();
        services.AddSingleton<TextureAnalysisAlgorithms>();
        
        return services;
    }

    /// <summary>
    /// 注册智能标注算法
    /// </summary>
    public static IServiceCollection AddSmartAnnotationAlgorithms(this IServiceCollection services)
    {
        services.AddSingleton<SmartAnnotationAlgorithms>();
        
        return services;
    }

    /// <summary>
    /// 注册训练管道算法
    /// </summary>
    public static IServiceCollection AddTrainingPipelineAlgorithms(this IServiceCollection services)
    {
        services.AddSingleton<TrainingPipelineAlgorithms>();
        
        return services;
    }

    /// <summary>
    /// 配置日志记录
    /// </summary>
    public static IServiceCollection ConfigureMedicalImageAnalysisLogging(this IServiceCollection services)
    {
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.AddDebug();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        return services;
    }

    /// <summary>
    /// 注册多尺度分析算法
    /// </summary>
    public static IServiceCollection AddMultiScaleAnalysisAlgorithms(this IServiceCollection services)
    {
        services.AddSingleton<MultiScaleAnalysisAlgorithms>();

        return services;
    }

    /// <summary>
    /// 注册AI标注算法
    /// </summary>
    public static IServiceCollection AddAIAnnotationAlgorithms(this IServiceCollection services)
    {
        services.AddSingleton<AIAnnotationAlgorithms>();

        return services;
    }

    /// <summary>
    /// 注册训练管道
    /// </summary>
    public static IServiceCollection AddTrainingPipelines(this IServiceCollection services)
    {
        services.AddScoped<EndToEndTrainingPipeline>();

        return services;
    }

    /// <summary>
    /// 注册所有算法服务（一键注册）
    /// </summary>
    public static IServiceCollection AddAllMedicalImageAnalysisAlgorithms(this IServiceCollection services)
    {
        return services
            .AddAdvancedImageProcessingAlgorithms()
            .AddSmartAnnotationAlgorithms()
            .AddTrainingPipelineAlgorithms()
            .AddMultiScaleAnalysisAlgorithms()
            .AddAIAnnotationAlgorithms()
            .AddTrainingPipelines();
    }

    /// <summary>
    /// 验证服务注册
    /// </summary>
    public static IServiceCollection ValidateMedicalImageAnalysisServices(this IServiceCollection services)
    {
        // 验证必需的服务是否已注册
        var serviceProvider = services.BuildServiceProvider();
        
        try
        {
            // 验证算法库
            serviceProvider.GetRequiredService<MedicalImageProcessingAlgorithms>();
            serviceProvider.GetRequiredService<TextureAnalysisAlgorithms>();
            serviceProvider.GetRequiredService<SmartAnnotationAlgorithms>();
            serviceProvider.GetRequiredService<TrainingPipelineAlgorithms>();
            serviceProvider.GetRequiredService<MultiScaleAnalysisAlgorithms>();
            serviceProvider.GetRequiredService<AIAnnotationAlgorithms>();

            // 验证核心服务
            serviceProvider.GetRequiredService<IImageProcessingService>();
            serviceProvider.GetRequiredService<IAdvancedImageProcessingService>();
            serviceProvider.GetRequiredService<ISmartAnnotationService>();
            serviceProvider.GetRequiredService<IYoloService>();
            serviceProvider.GetRequiredService<INnUNetService>();

            // 验证管道服务
            serviceProvider.GetRequiredService<EndToEndTrainingPipeline>();

            Console.WriteLine("✅ 所有医学影像分析服务注册验证成功");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 服务注册验证失败: {ex.Message}");
            throw;
        }
        finally
        {
            serviceProvider.Dispose();
        }

        return services;
    }
}

/// <summary>
/// 算法配置选项
/// </summary>
public class AlgorithmOptions
{
    /// <summary>
    /// 是否启用并行处理
    /// </summary>
    public bool EnableParallelProcessing { get; set; } = true;

    /// <summary>
    /// 最大并行度
    /// </summary>
    public int MaxDegreeOfParallelism { get; set; } = Environment.ProcessorCount;

    /// <summary>
    /// 是否启用缓存
    /// </summary>
    public bool EnableCaching { get; set; } = true;

    /// <summary>
    /// 缓存大小限制（MB）
    /// </summary>
    public int CacheSizeLimitMB { get; set; } = 1024;

    /// <summary>
    /// 是否启用详细日志
    /// </summary>
    public bool EnableVerboseLogging { get; set; } = false;

    /// <summary>
    /// 临时文件目录
    /// </summary>
    public string TempDirectory { get; set; } = Path.GetTempPath();

    /// <summary>
    /// 输出目录
    /// </summary>
    public string OutputDirectory { get; set; } = "output";

    /// <summary>
    /// 模型目录
    /// </summary>
    public string ModelDirectory { get; set; } = "models";
}

/// <summary>
/// 图像处理配置选项
/// </summary>
public class ImageProcessingOptions
{
    /// <summary>
    /// 默认图像格式
    /// </summary>
    public string DefaultImageFormat { get; set; } = "PNG";

    /// <summary>
    /// 默认图像质量
    /// </summary>
    public int DefaultImageQuality { get; set; } = 95;

    /// <summary>
    /// 最大图像尺寸
    /// </summary>
    public int MaxImageSize { get; set; } = 4096;

    /// <summary>
    /// 是否保持纵横比
    /// </summary>
    public bool PreserveAspectRatio { get; set; } = true;

    /// <summary>
    /// 默认插值方法
    /// </summary>
    public string DefaultInterpolationMethod { get; set; } = "Lanczos3";
}

/// <summary>
/// 智能标注配置选项
/// </summary>
public class SmartAnnotationOptions
{
    /// <summary>
    /// 默认置信度阈值
    /// </summary>
    public double DefaultConfidenceThreshold { get; set; } = 0.7;

    /// <summary>
    /// 最大推荐数量
    /// </summary>
    public int MaxRecommendations { get; set; } = 10;

    /// <summary>
    /// 是否启用质量评估
    /// </summary>
    public bool EnableQualityAssessment { get; set; } = true;

    /// <summary>
    /// 是否启用自适应优化
    /// </summary>
    public bool EnableAdaptiveOptimization { get; set; } = true;

    /// <summary>
    /// 质量评估阈值
    /// </summary>
    public double QualityThreshold { get; set; } = 0.8;
}

/// <summary>
/// 训练管道配置选项
/// </summary>
public class TrainingPipelineOptions
{
    /// <summary>
    /// 默认优化方法
    /// </summary>
    public string DefaultOptimizationMethod { get; set; } = "BayesianOptimization";

    /// <summary>
    /// 最大优化试验次数
    /// </summary>
    public int MaxOptimizationTrials { get; set; } = 100;

    /// <summary>
    /// 是否启用早停
    /// </summary>
    public bool EnableEarlyStopping { get; set; } = true;

    /// <summary>
    /// 早停耐心值
    /// </summary>
    public int EarlyStoppingPatience { get; set; } = 10;

    /// <summary>
    /// 是否启用数据增强
    /// </summary>
    public bool EnableDataAugmentation { get; set; } = true;

    /// <summary>
    /// 数据增强比例
    /// </summary>
    public double DataAugmentationRatio { get; set; } = 2.0;

    /// <summary>
    /// 是否启用异常检测
    /// </summary>
    public bool EnableAnomalyDetection { get; set; } = true;

    /// <summary>
    /// 是否生成训练报告
    /// </summary>
    public bool GenerateTrainingReport { get; set; } = true;
}
