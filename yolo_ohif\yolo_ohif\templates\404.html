{% extends "base.html" %}

{% block title %}404 - 页面未找到{% endblock %}

{% block extra_css %}
<style>
  .error-container {
    text-align: center;
    padding: 100px 0;
  }
  
  .error-code {
    font-size: 120px;
    font-weight: bold;
    color: #0d6efd;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .error-message {
    font-size: 24px;
    margin-bottom: 30px;
    color: #495057;
  }
  
  .error-details {
    font-size: 16px;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto 40px;
  }
  
  .error-image {
    max-width: 300px;
    margin-bottom: 30px;
  }
  
  .btn-home {
    padding: 10px 30px;
    font-size: 18px;
    border-radius: 30px;
  }
</style>
{% endblock %}

{% block content %}
<div class="container error-container">
  <div class="row">
    <div class="col-md-12">
      <svg class="error-image" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
        <!-- 医学十字 -->
        <path d="M100,40 L100,160 M40,100 L160,100" stroke="#0d6efd" stroke-width="10" stroke-linecap="round"/>
        
        <!-- 404文字 -->
        <text x="100" y="100" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#0d6efd" dominant-baseline="middle">404</text>
        
        <!-- 问号 -->
        <path d="M130,80 C130,70 120,60 100,60 C80,60 70,70 70,80 C70,90 75,95 85,100 C95,105 95,110 95,115 L95,125" fill="none" stroke="#0d6efd" stroke-width="6" stroke-linecap="round"/>
        <circle cx="95" cy="140" r="5" fill="#0d6efd"/>
      </svg>
      
      <h1 class="error-code">404</h1>
      <h2 class="error-message">页面未找到</h2>
      <p class="error-details">
        您请求的页面不存在或已被移动。请检查URL是否正确，或返回首页继续浏览。
      </p>
      <a href="{{ url_for('index') }}" class="btn btn-primary btn-home">
        <i class="fas fa-home me-2"></i>返回首页
      </a>
    </div>
  </div>
</div>
{% endblock %}