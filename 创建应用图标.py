#!/usr/bin/env python3
"""
医学影像智能标注与模型训练系统 - 应用图标和界面图片生成脚本
"""

import os
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def create_app_icon():
    """创建应用程序图标"""
    # 创建256x256的图标
    size = 256
    img = Image.new('RGBA', (size, size), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # 背景渐变（医学蓝色主题）
    for y in range(size):
        alpha = int(255 * (1 - y / size * 0.3))
        color = (41, 128, 185, alpha)  # 医学蓝色
        draw.line([(0, y), (size, y)], fill=color)
    
    # 绘制医学十字
    cross_color = (255, 255, 255, 255)
    cross_width = 20
    cross_length = 120
    center = size // 2
    
    # 垂直线
    draw.rectangle([
        center - cross_width//2, 
        center - cross_length//2,
        center + cross_width//2, 
        center + cross_length//2
    ], fill=cross_color)
    
    # 水平线
    draw.rectangle([
        center - cross_length//2, 
        center - cross_width//2,
        center + cross_length//2, 
        center + cross_width//2
    ], fill=cross_color)
    
    # 绘制AI神经网络节点
    node_color = (52, 152, 219, 255)  # 亮蓝色
    node_radius = 8
    
    # 节点位置
    nodes = [
        (center - 60, center - 60),
        (center + 60, center - 60),
        (center - 60, center + 60),
        (center + 60, center + 60),
        (center - 80, center),
        (center + 80, center),
        (center, center - 80),
        (center, center + 80)
    ]
    
    # 绘制连接线
    line_color = (149, 165, 166, 180)
    for i, node1 in enumerate(nodes):
        for j, node2 in enumerate(nodes[i+1:], i+1):
            if abs(node1[0] - node2[0]) <= 120 and abs(node1[1] - node2[1]) <= 120:
                draw.line([node1, node2], fill=line_color, width=2)
    
    # 绘制节点
    for node in nodes:
        draw.ellipse([
            node[0] - node_radius, 
            node[1] - node_radius,
            node[0] + node_radius, 
            node[1] + node_radius
        ], fill=node_color)
    
    # 添加边框
    border_color = (52, 73, 94, 255)
    draw.rectangle([0, 0, size-1, size-1], outline=border_color, width=3)
    
    return img

def create_interface_preview():
    """创建界面预览图"""
    width, height = 1200, 800
    img = Image.new('RGB', (width, height), (248, 249, 250))
    draw = ImageDraw.Draw(img)
    
    # 标题栏
    title_height = 60
    draw.rectangle([0, 0, width, title_height], fill=(52, 73, 94))
    
    try:
        # 尝试使用系统字体
        title_font = ImageFont.truetype("arial.ttf", 24)
        subtitle_font = ImageFont.truetype("arial.ttf", 16)
        label_font = ImageFont.truetype("arial.ttf", 14)
    except:
        # 如果系统字体不可用，使用默认字体
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
        label_font = ImageFont.load_default()
    
    # 标题文字
    title_text = "医学影像智能标注与模型训练系统"
    title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    draw.text(((width - title_width) // 2, 18), title_text, 
              fill=(255, 255, 255), font=title_font)
    
    # 左侧导航栏
    nav_width = 200
    nav_color = (236, 240, 241)
    draw.rectangle([0, title_height, nav_width, height], fill=nav_color)
    
    # 导航菜单项
    menu_items = [
        "🏠 首页",
        "📁 DICOM上传", 
        "🔍 GDCM查看",
        "🖼️ 影像处理",
        "🎯 智能标注",
        "🏋️ 模型训练",
        "📊 统计分析"
    ]
    
    menu_y = title_height + 20
    for item in menu_items:
        # 高亮智能标注项
        if "智能标注" in item:
            draw.rectangle([5, menu_y-5, nav_width-5, menu_y+25], 
                          fill=(52, 152, 219), outline=(41, 128, 185))
            text_color = (255, 255, 255)
        else:
            text_color = (52, 73, 94)
        
        draw.text((15, menu_y), item, fill=text_color, font=subtitle_font)
        menu_y += 40
    
    # 主内容区域
    content_x = nav_width + 20
    content_y = title_height + 20
    content_width = width - nav_width - 40
    content_height = height - title_height - 40
    
    # 绘制医学影像显示区域
    image_area_width = content_width * 0.6
    image_area_height = content_height * 0.7
    
    # 影像显示框
    draw.rectangle([
        content_x, content_y,
        content_x + image_area_width, content_y + image_area_height
    ], fill=(0, 0, 0), outline=(149, 165, 166), width=2)
    
    # 模拟DICOM影像（脑部CT）
    brain_center_x = content_x + image_area_width // 2
    brain_center_y = content_y + image_area_height // 2
    
    # 绘制脑部轮廓
    brain_color = (100, 100, 100)
    draw.ellipse([
        brain_center_x - 120, brain_center_y - 100,
        brain_center_x + 120, brain_center_y + 100
    ], fill=brain_color)
    
    # 绘制脑室
    ventricle_color = (50, 50, 50)
    draw.ellipse([
        brain_center_x - 40, brain_center_y - 30,
        brain_center_x + 40, brain_center_y + 30
    ], fill=ventricle_color)
    
    # 绘制标注框（病灶区域）
    annotation_color = (231, 76, 60)  # 红色标注框
    draw.rectangle([
        brain_center_x - 60, brain_center_y - 20,
        brain_center_x - 20, brain_center_y + 20
    ], outline=annotation_color, width=3)
    
    # 标注标签
    draw.text((brain_center_x - 55, brain_center_y - 35), "病灶", 
              fill=annotation_color, font=label_font)
    
    # 右侧控制面板
    panel_x = content_x + image_area_width + 20
    panel_width = content_width - image_area_width - 20
    
    # 工具面板背景
    draw.rectangle([
        panel_x, content_y,
        panel_x + panel_width, content_y + content_height
    ], fill=(255, 255, 255), outline=(189, 195, 199), width=1)
    
    # 工具面板标题
    draw.text((panel_x + 10, content_y + 10), "标注工具", 
              fill=(52, 73, 94), font=subtitle_font)
    
    # 工具按钮
    tools = ["矩形", "椭圆", "多边形", "自由绘制"]
    tool_y = content_y + 50
    for i, tool in enumerate(tools):
        button_color = (52, 152, 219) if i == 0 else (189, 195, 199)
        text_color = (255, 255, 255) if i == 0 else (52, 73, 94)
        
        draw.rectangle([
            panel_x + 10, tool_y,
            panel_x + panel_width - 10, tool_y + 30
        ], fill=button_color, outline=(149, 165, 166))
        
        draw.text((panel_x + 20, tool_y + 8), tool, 
                  fill=text_color, font=label_font)
        tool_y += 40
    
    # AI设置区域
    ai_y = tool_y + 20
    draw.text((panel_x + 10, ai_y), "AI 辅助设置", 
              fill=(52, 73, 94), font=subtitle_font)
    
    ai_options = ["✓ 启用AI预标注", "✓ 智能边缘吸附", "置信度阈值: 0.7"]
    ai_y += 30
    for option in ai_options:
        draw.text((panel_x + 15, ai_y), option, 
                  fill=(39, 174, 96), font=label_font)
        ai_y += 25
    
    # 底部状态栏
    status_y = height - 30
    draw.rectangle([0, status_y, width, height], fill=(236, 240, 241))
    draw.text((10, status_y + 8), "就绪 | 已加载: DJ01.dcm | 标注数量: 3", 
              fill=(52, 73, 94), font=label_font)
    
    return img

def create_directory_structure_diagram():
    """创建目录结构图"""
    width, height = 1000, 700
    img = Image.new('RGB', (width, height), (255, 255, 255))
    draw = ImageDraw.Draw(img)

    try:
        title_font = ImageFont.truetype("arial.ttf", 24)
        folder_font = ImageFont.truetype("arial.ttf", 14)
        file_font = ImageFont.truetype("arial.ttf", 12)
        small_font = ImageFont.truetype("arial.ttf", 10)
    except:
        title_font = ImageFont.load_default()
        folder_font = ImageFont.load_default()
        file_font = ImageFont.load_default()
        small_font = ImageFont.load_default()

    # 背景渐变
    for y in range(height):
        alpha = int(255 * (1 - y / height * 0.1))
        color = (248, 249, 250)
        draw.line([(0, y), (width, y)], fill=color)

    # 标题
    title = "医学影像智能标注与模型训练系统 - 目录结构说明"
    title_bbox = draw.textbbox((0, 0), title, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    draw.text(((width - title_width) // 2, 20), title,
              fill=(52, 73, 94), font=title_font)

    # 绘制目录树结构
    tree_x = 50
    tree_y = 70

    # 主目录结构
    structure = [
        ("📁 Release/ (发布包根目录)", 0, (52, 152, 219), True),
        ("├── 启动应用程序.bat", 1, (231, 76, 60), False),
        ("├── 启动训练环境.bat", 1, (231, 76, 60), False),
        ("├── 用户指南.md", 1, (127, 140, 141), False),
        ("├── 📁 App/ (.NET应用程序)", 1, (39, 174, 96), True),
        ("│   ├── MedicalImageAnalysis.Wpf.exe", 2, (127, 140, 141), False),
        ("│   └── appsettings.json", 2, (127, 140, 141), False),
        ("├── 📁 Data/ (数据目录)", 1, (39, 174, 96), True),
        ("│   ├── 📁 DICOM/ (医学影像)", 2, (155, 89, 182), True),
        ("│   │   ├── DJ01.dcm (示例脑部CT)", 3, (127, 140, 141), False),
        ("│   │   └── [您的DICOM文件]", 3, (149, 165, 166), False),
        ("│   ├── 📁 Models/ (YOLO模型)", 2, (155, 89, 182), True),
        ("│   │   ├── yolo11n.pt (轻量级)", 3, (127, 140, 141), False),
        ("│   │   ├── yolo11x.pt (高精度)", 3, (127, 140, 141), False),
        ("│   │   └── [自定义模型]", 3, (149, 165, 166), False),
        ("│   ├── 📁 Datasets/ (训练数据)", 2, (155, 89, 182), True),
        ("│   │   ├── 📁 Train/ (训练集)", 3, (230, 126, 34), True),
        ("│   │   │   ├── images/ (图像)", 4, (127, 140, 141), False),
        ("│   │   │   └── labels/ (标签)", 4, (127, 140, 141), False),
        ("│   │   ├── 📁 Val/ (验证集)", 3, (230, 126, 34), True),
        ("│   │   └── 📁 Test/ (测试集)", 3, (230, 126, 34), True),
        ("│   ├── 📁 Output/ (输出结果)", 2, (155, 89, 182), True),
        ("│   └── 📁 Logs/ (日志文件)", 2, (155, 89, 182), True),
        ("├── 📁 Python/ (训练环境)", 1, (39, 174, 96), True),
        ("│   ├── venv/ (虚拟环境)", 2, (127, 140, 141), False),
        ("│   └── [训练脚本]", 2, (149, 165, 166), False),
        ("├── 📁 Docs/ (文档教程)", 1, (39, 174, 96), True),
        ("└── 📁 Scripts/ (辅助脚本)", 1, (39, 174, 96), True)
    ]

    line_height = 20
    for i, (text, level, color, is_folder) in enumerate(structure):
        y = tree_y + i * line_height
        x = tree_x + level * 20

        # 绘制文本
        font = folder_font if is_folder else file_font
        draw.text((x, y), text, fill=color, font=font)

    # 右侧说明区域
    info_x = 550
    info_y = 100
    info_width = 400
    info_height = 550

    # 说明框背景
    draw.rectangle([info_x, info_y, info_x + info_width, info_y + info_height],
                  fill=(255, 255, 255), outline=(189, 195, 199), width=2)

    # 说明标题
    draw.text((info_x + 20, info_y + 20), "📋 文件放置说明",
              fill=(52, 73, 94), font=title_font)

    # 详细说明
    explanations = [
        ("🏥 DICOM影像文件", info_y + 70, (52, 152, 219)),
        ("位置: Data/DICOM/", info_y + 90, (127, 140, 141)),
        ("格式: .dcm, .dicom", info_y + 105, (127, 140, 141)),
        ("说明: 将医学影像文件放入此目录", info_y + 120, (127, 140, 141)),

        ("🤖 YOLO模型文件", info_y + 150, (52, 152, 219)),
        ("位置: Data/Models/", info_y + 170, (127, 140, 141)),
        ("格式: .pt, .onnx, .engine", info_y + 185, (127, 140, 141)),
        ("推荐: yolo11n.pt (快速)", info_y + 200, (127, 140, 141)),
        ("     yolo11x.pt (高精度)", info_y + 215, (127, 140, 141)),

        ("📊 训练数据集", info_y + 245, (52, 152, 219)),
        ("训练集: Data/Datasets/Train/", info_y + 265, (127, 140, 141)),
        ("验证集: Data/Datasets/Val/", info_y + 280, (127, 140, 141)),
        ("测试集: Data/Datasets/Test/", info_y + 295, (127, 140, 141)),
        ("格式: YOLO格式 (图像+标签)", info_y + 310, (127, 140, 141)),

        ("🚀 快速开始", info_y + 340, (52, 152, 219)),
        ("1. 双击'启动应用程序.bat'", info_y + 360, (127, 140, 141)),
        ("2. 将DICOM文件放入DICOM目录", info_y + 375, (127, 140, 141)),
        ("3. 将模型文件放入Models目录", info_y + 390, (127, 140, 141)),
        ("4. 开始智能标注和训练", info_y + 405, (127, 140, 141)),

        ("📖 详细教程", info_y + 435, (52, 152, 219)),
        ("查看Docs目录中的完整教程:", info_y + 455, (127, 140, 141)),
        ("• 智能打标教程.md", info_y + 470, (127, 140, 141)),
        ("• 模型训练教程.md", info_y + 485, (127, 140, 141)),
        ("• 快速入门指南.md", info_y + 500, (127, 140, 141))
    ]

    for text, y, color in explanations:
        draw.text((info_x + 20, y), text, fill=color, font=file_font)

    # 底部版权信息
    copyright_text = "医学影像智能标注与模型训练系统 v1.0 - 仅供研究和教育使用"
    copyright_bbox = draw.textbbox((0, 0), copyright_text, font=small_font)
    copyright_width = copyright_bbox[2] - copyright_bbox[0]
    draw.text(((width - copyright_width) // 2, height - 30), copyright_text,
              fill=(149, 165, 166), font=small_font)

    return img

def main():
    """主函数"""
    print("🎨 创建应用程序图标和界面图片...")
    
    # 创建输出目录
    os.makedirs("Assets", exist_ok=True)
    
    # 创建应用图标
    print("📱 创建应用程序图标...")
    icon = create_app_icon()
    
    # 保存不同尺寸的图标
    icon_sizes = [16, 32, 48, 64, 128, 256]
    for size in icon_sizes:
        resized_icon = icon.resize((size, size), Image.Resampling.LANCZOS)
        resized_icon.save(f"Assets/app_icon_{size}x{size}.png")
    
    # 保存ICO格式图标
    icon.save("Assets/app_icon.ico", format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
    
    # 创建界面预览图
    print("🖥️ 创建界面预览图...")
    interface = create_interface_preview()
    interface.save("Assets/interface_preview.png")
    
    # 创建目录结构图
    print("📁 创建目录结构图...")
    directory_diagram = create_directory_structure_diagram()
    directory_diagram.save("Assets/directory_structure.png")
    
    print("✅ 图片创建完成！")
    print("📁 输出目录: Assets/")
    print("📄 文件列表:")
    print("  - app_icon.ico (应用程序图标)")
    print("  - app_icon_*.png (不同尺寸图标)")
    print("  - interface_preview.png (界面预览)")
    print("  - directory_structure.png (目录结构说明)")

if __name__ == "__main__":
    main()
