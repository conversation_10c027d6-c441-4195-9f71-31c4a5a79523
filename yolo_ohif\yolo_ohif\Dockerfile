# 基于Python 3.9官方镜像构建
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    FLASK_APP=app.py \
    FLASK_ENV=production

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    wget \
    unzip \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装Orthanc
RUN curl https://www.orthanc-server.com/downloads/get.php?path=/orthanc/1.9.7/Orthanc-1.9.7.tar.gz -o orthanc.tar.gz \
    && tar -xf orthanc.tar.gz \
    && rm orthanc.tar.gz \
    && mkdir -p /etc/orthanc

# 复制Orthanc配置文件
COPY orthanc.json /etc/orthanc/

# 复制应用代码
COPY . /app/

# 创建必要的目录
RUN mkdir -p /app/uploads \
    && mkdir -p /app/models \
    && mkdir -p /app/results \
    && mkdir -p /app/logs \
    && mkdir -p /app/instance

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 下载YOLO预训练模型（如果需要）
RUN python -c "from ultralytics import YOLO; YOLO('yolov8n.pt')"

# 暴露端口
EXPOSE 5000 8042

# 启动命令
CMD ["python", "run.py", "--host", "0.0.0.0", "--port", "5000"]