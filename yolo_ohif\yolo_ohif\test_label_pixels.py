#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试label_T2目录中图像的像素值分布
"""

import cv2
import numpy as np
from pathlib import Path

def analyze_image_pixels(image_path):
    """
    分析图像的像素值分布
    """
    img = cv2.imread(str(image_path), cv2.IMREAD_GRAYSCALE)
    if img is None:
        print(f"无法读取图像: {image_path}")
        return
    
    unique_values = np.unique(img)
    print(f"\n图像: {image_path.name}")
    print(f"图像尺寸: {img.shape}")
    print(f"像素值范围: {img.min()} - {img.max()}")
    print(f"唯一像素值: {unique_values}")
    print(f"像素值统计:")
    for val in unique_values:
        count = np.sum(img == val)
        percentage = count / img.size * 100
        print(f"  值 {val}: {count} 像素 ({percentage:.2f}%)")
    
    # 检查是否有白色像素
    white_pixels = np.sum(img == 255)
    print(f"白色像素(255): {white_pixels}")
    
    # 检查是否有非零像素
    nonzero_pixels = np.sum(img > 0)
    print(f"非零像素: {nonzero_pixels}")

def main():
    label_dir = Path(r"E:\Trae\yolo_ohif\sliced_output\sliced_images\label_T2")
    
    # 测试前几个图像
    test_files = [
        "130_slice_000.jpg",
        "130_slice_001.jpg", 
        "130_slice_002.jpg",
        "131_slice_005.jpg",
        "132_slice_010.jpg"
    ]
    
    print("=== 分析label图像像素值分布 ===")
    
    for filename in test_files:
        filepath = label_dir / filename
        if filepath.exists():
            analyze_image_pixels(filepath)
        else:
            print(f"文件不存在: {filename}")
    
    print("\n=== 分析完成 ===")

if __name__ == "__main__":
    main()