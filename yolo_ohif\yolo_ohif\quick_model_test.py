#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速模型测试脚本
用于快速测试训练好的模型在少量图像上的检测效果
"""

import os
import sys
import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from typing import List, Dict

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from services.detection_service import DetectionService
from config import Config

# 配置参数
TEST_IMAGE_DIR = r"E:\Trae\yolo_ohif\yolo11x_training_output\yolo_dataset\yolo_dataset\images\test"
MODEL_PATH = r"E:\Trae\yolo_ohif\yolo11x_training_output\training_results\yolo11x_from_scratch_20250719_135134\weights\best.pt"

def test_model_on_images(image_dir: str = None, 
                        num_images: int = 20,
                        confidence_threshold: float = None):
    """
    在指定目录的图像上测试模型
    
    Args:
        image_dir: 图像目录（默认使用配置的测试目录）
        num_images: 测试图像数量
        confidence_threshold: 置信度阈值，None则使用配置文件中的值
    """
    
    if image_dir is None:
        image_dir = TEST_IMAGE_DIR
    print("=" * 60)
    print("快速模型测试")
    print("=" * 60)
    
    # 初始化检测服务
    model_path = MODEL_PATH
    conf_thresh = confidence_threshold or Config.YOLO.CONFIDENCE_THRESHOLD
    
    print(f"模型路径: {model_path}")
    print(f"置信度阈值: {conf_thresh}")
    print(f"IoU阈值: {Config.YOLO.IOU_THRESHOLD}")
    
    detection_service = DetectionService(
        model_path=model_path,
        confidence_threshold=conf_thresh,
        iou_threshold=Config.YOLO.IOU_THRESHOLD
    )
    
    # 获取测试图像
    if not os.path.exists(image_dir):
        print(f"错误: 图像目录不存在: {image_dir}")
        return
    
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
        image_files.extend(Path(image_dir).glob(ext))
    
    if not image_files:
        print(f"错误: 在目录 {image_dir} 中没有找到图像文件")
        return
    
    # 限制测试图像数量
    image_files = image_files[:num_images]
    print(f"\n将测试 {len(image_files)} 张图像")
    
    results = []
    
    # 测试每张图像
    for i, img_path in enumerate(image_files):
        print(f"\n处理图像 {i+1}/{len(image_files)}: {img_path.name}")
        
        # 读取图像
        image = cv2.imread(str(img_path))
        if image is None:
            print(f"  错误: 无法读取图像")
            continue
        
        # 进行检测
        try:
            detections = detection_service._detect_image(image)
            print(f"  检测到 {len(detections)} 个目标")
            
            # 显示检测结果
            if detections:
                for j, det in enumerate(detections):
                    print(f"    目标 {j+1}: 类别={det['class_name']}, 置信度={det['confidence']:.3f}, "
                          f"位置=({det['x']:.0f}, {det['y']:.0f}, {det['width']:.0f}, {det['height']:.0f})")
            else:
                print("    未检测到任何目标")
            
            results.append({
                'image_path': str(img_path),
                'image_name': img_path.name,
                'detections': detections,
                'detection_count': len(detections)
            })
            
        except Exception as e:
            print(f"  检测失败: {str(e)}")
    
    # 统计结果
    print("\n" + "=" * 60)
    print("测试结果统计")
    print("=" * 60)
    
    total_detections = sum(r['detection_count'] for r in results)
    images_with_detections = len([r for r in results if r['detection_count'] > 0])
    
    print(f"总测试图像数: {len(results)}")
    print(f"有检测结果的图像数: {images_with_detections}")
    print(f"无检测结果的图像数: {len(results) - images_with_detections}")
    print(f"总检测目标数: {total_detections}")
    print(f"平均每张图像检测目标数: {total_detections/len(results):.2f}")
    
    # 按检测数量分组
    detection_counts = {}
    for result in results:
        count = result['detection_count']
        if count not in detection_counts:
            detection_counts[count] = []
        detection_counts[count].append(result['image_name'])
    
    print("\n按检测数量分组:")
    for count in sorted(detection_counts.keys()):
        images = detection_counts[count]
        print(f"  检测到 {count} 个目标的图像 ({len(images)} 张): {', '.join(images[:3])}{'...' if len(images) > 3 else ''}")
    
    return results

def visualize_detections(image_path: str = None, num_images: int = 3, confidence_threshold: float = None):
    """
    可视化检测结果
    
    Args:
        image_path: 图像路径或目录路径
        num_images: 要可视化的图像数量
        confidence_threshold: 置信度阈值
    """
    print(f"\n=== 可视化检测结果 ===")
    print(f"图像路径: {image_path or TEST_IMAGE_DIR}")
    print(f"可视化图像数量: {num_images}")
    
    # 如果传入的是目录，则随机选择图像
    if image_path is None or os.path.isdir(image_path):
        image_dir = image_path or TEST_IMAGE_DIR
        image_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
            image_files.extend(Path(image_dir).glob(ext))
        
        if not image_files:
            print(f"错误: 在目录 {image_dir} 中没有找到图像文件")
            return
        
        print(f"找到 {len(image_files)} 张图像")
        
        # 限制图像数量并处理每张图像
        image_files = image_files[:num_images]
        detection_count = 0
        for i, img_path in enumerate(image_files):
            print(f"\n--- 可视化第 {i+1}/{len(image_files)} 张图像 ---")
            detections = _visualize_single_image(str(img_path), confidence_threshold)
            if detections:
                detection_count += len(detections)
        
        print(f"\n=== 可视化总结 ===")
        print(f"处理图像数量: {len(image_files)}")
        print(f"总检测数量: {detection_count}")
        print(f"平均每张图像检测数: {detection_count/len(image_files):.2f}")
        return
    
    # 处理单张图像
    detections = _visualize_single_image(image_path, confidence_threshold)
    if detections:
        print(f"\n检测到 {len(detections)} 个目标")
    else:
        print("\n未检测到任何目标")

def _visualize_single_image(image_path: str, confidence_threshold: float = None):
    """
    可视化单张图像的检测结果
    
    Args:
        image_path: 图像路径
        confidence_threshold: 置信度阈值
    """
    print(f"\n可视化检测结果: {image_path}")
    
    # 初始化检测服务
    model_path = MODEL_PATH
    conf_thresh = confidence_threshold or Config.YOLO.CONFIDENCE_THRESHOLD
    
    detection_service = DetectionService(
        model_path=model_path,
        confidence_threshold=conf_thresh,
        iou_threshold=Config.YOLO.IOU_THRESHOLD
    )
    
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"错误: 无法读取图像 {image_path}")
        return
    
    # 进行检测
    detections = detection_service._detect_image(image)
    print(f"检测到 {len(detections)} 个目标")
    
    # 绘制检测结果
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 创建子图显示原图和检测结果
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 显示原图
    ax1.imshow(image_rgb)
    ax1.set_title(f"原图\n{os.path.basename(image_path)}")
    ax1.axis('off')
    
    # 显示检测结果
    ax2.imshow(image_rgb)
    
    # 绘制检测框
    colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange']
    for i, det in enumerate(detections):
        # 使用x1, y1, x2, y2坐标
        x1, y1, x2, y2 = det['x1'], det['y1'], det['x2'], det['y2']
        width = x2 - x1
        height = y2 - y1
        
        # 选择颜色
        color = colors[i % len(colors)]
        
        # 绘制边界框
        rect = plt.Rectangle((x1, y1), width, height, fill=False, color=color, linewidth=2)
        ax2.add_patch(rect)
        
        # 添加标签
        class_name = det.get('class', 'unknown')
        confidence = det.get('confidence', 0)
        label = f"{class_name}: {confidence:.2f}"
        ax2.text(x1, y1-10, label, color=color, fontsize=10, 
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        # 打印检测详情
        print(f"  检测 {i+1}: 类别={class_name}, 置信度={confidence:.3f}, "
              f"位置=({x1:.0f}, {y1:.0f}, {x2:.0f}, {y2:.0f})")
    
    ax2.set_title(f"检测结果 (置信度阈值: {conf_thresh})\n检测到 {len(detections)} 个目标")
    ax2.axis('off')
    
    plt.tight_layout()
    plt.show()
    
    return detections

def test_different_thresholds(image_path: str, thresholds: List[float] = None):
    """
    测试不同置信度阈值下的检测结果
    
    Args:
        image_path: 图像路径
        thresholds: 置信度阈值列表
    """
    if thresholds is None:
        thresholds = [0.1, 0.15, 0.2, 0.25, 0.3, 0.4, 0.5]
    
    print(f"\n测试不同置信度阈值: {image_path}")
    print(f"阈值列表: {thresholds}")
    
    # 初始化检测服务
    model_path = MODEL_PATH
    
    detection_service = DetectionService(
        model_path=model_path,
        confidence_threshold=0.1,  # 使用最低阈值初始化
        iou_threshold=Config.YOLO.IOU_THRESHOLD
    )
    
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"错误: 无法读取图像 {image_path}")
        return
    
    results = {}
    
    print("\n阈值测试结果:")
    print("-" * 50)
    
    for thresh in thresholds:
        # 设置置信度阈值
        detection_service.confidence_threshold = thresh
        
        # 进行检测
        detections = detection_service._detect_image(image)
        results[thresh] = detections
        
        print(f"阈值 {thresh:4.2f}: 检测到 {len(detections):2d} 个目标")
        
        # 显示检测详情
        if detections:
            for det in detections:
                print(f"         - {det['class_name']}: {det['confidence']:.3f}")
    
    return results

def test_different_confidence_thresholds(image_dir: str = None) -> Dict:
    """
    测试不同置信度阈值下的检测效果
    
    Args:
        image_dir: 测试图像目录路径（默认使用配置的测试目录）
        
    Returns:
        Dict: 测试结果
    """
    
    if image_dir is None:
        image_dir = TEST_IMAGE_DIR
    
    print("\n" + "=" * 60)
    print("不同置信度阈值测试")
    print("=" * 60)
    
    # 获取测试图像
    if not os.path.exists(image_dir):
        print(f"错误: 图像目录不存在: {image_dir}")
        return {}
    
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
        image_files.extend(Path(image_dir).glob(ext))
    
    if not image_files:
        print(f"错误: 在目录 {image_dir} 中没有找到图像文件")
        return {}
    
    # 选择第一张图像进行测试
    test_image = str(image_files[0])
    print(f"测试图像: {Path(test_image).name}")
    
    # 定义测试阈值
    thresholds = [0.1, 0.15, 0.2, 0.25, 0.3, 0.4, 0.5, 0.6, 0.7]
    
    # 初始化检测服务
    model_path = MODEL_PATH
    detection_service = DetectionService(
        model_path=model_path,
        confidence_threshold=0.1,  # 使用最低阈值初始化
        iou_threshold=Config.YOLO.IOU_THRESHOLD
    )
    
    # 读取图像
    image = cv2.imread(test_image)
    if image is None:
        print(f"错误: 无法读取图像 {test_image}")
        return {}
    
    results = {}
    
    print("\n阈值测试结果:")
    print("-" * 50)
    
    for thresh in thresholds:
        # 设置置信度阈值
        detection_service.confidence_threshold = thresh
        
        # 进行检测
        try:
            detections = detection_service._detect_image(image)
            results[thresh] = {
                'threshold': thresh,
                'detection_count': len(detections),
                'detections': detections
            }
            
            print(f"阈值 {thresh:4.2f}: 检测到 {len(detections):2d} 个目标")
            
            # 显示检测详情（只显示前3个）
            if detections:
                for i, det in enumerate(detections[:3]):
                    print(f"         - {det['class_name']}: {det['confidence']:.3f}")
                if len(detections) > 3:
                    print(f"         ... 还有 {len(detections) - 3} 个目标")
                    
        except Exception as e:
            print(f"阈值 {thresh:4.2f}: 检测失败 - {str(e)}")
            results[thresh] = {
                'threshold': thresh,
                'detection_count': 0,
                'detections': [],
                'error': str(e)
            }
    
    # 统计分析
    print("\n" + "-" * 50)
    print("阈值分析:")
    
    detection_counts = [results[t]['detection_count'] for t in thresholds if 'error' not in results[t]]
    if detection_counts:
        print(f"最大检测数量: {max(detection_counts)}")
        print(f"最小检测数量: {min(detection_counts)}")
        
        # 找到检测数量稳定的阈值范围
        stable_thresholds = []
        for i, thresh in enumerate(thresholds[:-1]):
            if ('error' not in results[thresh] and 
                'error' not in results[thresholds[i+1]] and
                results[thresh]['detection_count'] == results[thresholds[i+1]]['detection_count']):
                stable_thresholds.append(thresh)
        
        if stable_thresholds:
            print(f"检测数量稳定的阈值: {stable_thresholds}")
    
    return results

def main():
    """
    主函数 - 提供交互式测试选项
    """
    print("YOLO模型快速测试工具")
    print("=" * 50)
    
    while True:
        print("\n请选择测试选项:")
        print("1. 批量测试图像 (默认目录)")
        print("2. 批量测试图像 (自定义目录)")
        print("3. 可视化单张图像检测结果")
        print("4. 测试不同置信度阈值")
        print("5. 退出")
        
        choice = input("\n请输入选项 (1-5): ").strip()
        
        if choice == '1':
            # 批量测试 - 默认目录
            num_images = input("测试图像数量 (默认5): ").strip()
            num_images = int(num_images) if num_images.isdigit() else 5
            
            conf_thresh = input("置信度阈值 (默认使用配置文件): ").strip()
            conf_thresh = float(conf_thresh) if conf_thresh else None
            
            test_model_on_images(num_images=num_images, confidence_threshold=conf_thresh)
            
        elif choice == '2':
            # 批量测试 - 自定义目录
            image_dir = input("图像目录路径: ").strip()
            if not image_dir:
                print("错误: 请输入有效的目录路径")
                continue
            
            num_images = input("测试图像数量 (默认5): ").strip()
            num_images = int(num_images) if num_images.isdigit() else 5
            
            conf_thresh = input("置信度阈值 (默认使用配置文件): ").strip()
            conf_thresh = float(conf_thresh) if conf_thresh else None
            
            test_model_on_images(image_dir=image_dir, num_images=num_images, confidence_threshold=conf_thresh)
            
        elif choice == '3':
            # 可视化单张图像
            img_path = input("图像路径: ").strip()
            if not img_path or not os.path.exists(img_path):
                print("错误: 请输入有效的图像路径")
                continue
            
            conf_thresh = input("置信度阈值 (默认使用配置文件): ").strip()
            conf_thresh = float(conf_thresh) if conf_thresh else None
            
            visualize_detections(img_path, conf_thresh)
            
        elif choice == '4':
            # 测试不同阈值
            img_path = input("图像路径: ").strip()
            if not img_path or not os.path.exists(img_path):
                print("错误: 请输入有效的图像路径")
                continue
            
            test_different_thresholds(img_path)
            
        elif choice == '5':
            print("退出测试工具")
            break
            
        else:
            print("无效选项，请重新选择")

if __name__ == "__main__":
    print("YOLO模型快速测试工具")
    print("=" * 50)
    print(f"测试数据目录: {TEST_IMAGE_DIR}")
    print(f"模型路径: {MODEL_PATH}")
    
    # 基本测试
    print("\n1. 基本检测测试 (20张图像)")
    test_model_on_images(num_images=20)
    
    # 可视化测试
    print("\n2. 可视化检测测试")
    visualize_detections(image_path=TEST_IMAGE_DIR, num_images=3)
    
    # 不同置信度测试
    print("\n3. 不同置信度阈值测试")
    test_different_confidence_thresholds()