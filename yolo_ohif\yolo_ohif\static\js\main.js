/**
 * YOLO-OHIF医学图像疾病检测系统
 * 主JavaScript文件
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 初始化Bootstrap工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 初始化Bootstrap弹出框
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // 处理消息提示自动关闭
    var alerts = document.querySelectorAll('.alert-dismissible');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000); // 5秒后自动关闭
    });
    
    // 处理上传区域拖放功能
    setupFileUpload();
    
    // 处理研究过滤和排序
    setupStudyFilters();
    
    // 处理检测结果过滤
    setupDetectionFilters();
    
    // 处理密码强度检查
    setupPasswordStrengthCheck();
    
    // 处理表单验证
    setupFormValidation();
    
    // 处理图表初始化
    initializeCharts();
});

/**
 * 设置文件上传功能
 */
function setupFileUpload() {
    var uploadArea = document.querySelector('.upload-area');
    if (!uploadArea) return;
    
    var fileInput = document.querySelector('#file-input');
    var fileList = document.querySelector('.file-list');
    var uploadProgress = document.querySelector('.upload-progress');
    var progressBar = document.querySelector('.progress-bar');
    var uploadBtn = document.querySelector('#upload-btn');
    
    // 点击上传区域触发文件选择
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });
    
    // 处理文件选择
    fileInput.addEventListener('change', function(e) {
        handleFiles(e.target.files);
    });
    
    // 处理拖放
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        uploadArea.classList.remove('dragover');
        
        var files = e.dataTransfer.files;
        handleFiles(files);
    });
    
    // 处理文件列表
    function handleFiles(files) {
        if (files.length === 0) return;
        
        // 清空文件列表
        fileList.innerHTML = '';
        
        // 添加文件到列表
        for (var i = 0; i < files.length; i++) {
            var file = files[i];
            var fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            
            var fileIcon = document.createElement('div');
            fileIcon.className = 'file-icon';
            fileIcon.innerHTML = '<i class="fas fa-file-medical"></i>';
            
            var fileInfo = document.createElement('div');
            fileInfo.className = 'file-info';
            
            var fileName = document.createElement('div');
            fileName.className = 'file-name';
            fileName.textContent = file.name;
            
            var fileSize = document.createElement('div');
            fileSize.className = 'file-size';
            fileSize.textContent = formatFileSize(file.size);
            
            fileInfo.appendChild(fileName);
            fileInfo.appendChild(fileSize);
            
            var fileActions = document.createElement('div');
            fileActions.className = 'file-actions';
            fileActions.innerHTML = '<button class="btn btn-sm btn-outline-danger"><i class="fas fa-times"></i></button>';
            
            fileItem.appendChild(fileIcon);
            fileItem.appendChild(fileInfo);
            fileItem.appendChild(fileActions);
            
            fileList.appendChild(fileItem);
        }
        
        // 显示文件列表和上传按钮
        fileList.style.display = 'block';
        uploadBtn.style.display = 'block';
        
        // 绑定删除按钮事件
        var deleteButtons = fileList.querySelectorAll('.btn-outline-danger');
        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                var fileItem = this.closest('.file-item');
                fileItem.remove();
                
                // 如果没有文件了，隐藏文件列表和上传按钮
                if (fileList.children.length === 0) {
                    fileList.style.display = 'none';
                    uploadBtn.style.display = 'none';
                }
            });
        });
    }
    
    // 处理文件上传
    if (uploadBtn) {
        uploadBtn.addEventListener('click', function() {
            // 显示上传进度
            uploadProgress.style.display = 'block';
            
            // 模拟上传进度
            var progress = 0;
            var interval = setInterval(function() {
                progress += 5;
                progressBar.style.width = progress + '%';
                progressBar.setAttribute('aria-valuenow', progress);
                
                if (progress >= 100) {
                    clearInterval(interval);
                    
                    // 上传完成后，显示成功消息
                    setTimeout(function() {
                        uploadProgress.style.display = 'none';
                        showAlert('上传成功！文件已成功上传并添加到您的研究列表。', 'success');
                        
                        // 清空文件列表
                        fileList.innerHTML = '';
                        fileList.style.display = 'none';
                        uploadBtn.style.display = 'none';
                        
                        // 重置文件输入
                        fileInput.value = '';
                    }, 500);
                }
            }, 100);
        });
    }
    
    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

/**
 * 设置研究过滤和排序功能
 */
function setupStudyFilters() {
    var filterForm = document.querySelector('#study-filter-form');
    if (!filterForm) return;
    
    // 自动提交表单当过滤条件改变时
    var filterInputs = filterForm.querySelectorAll('select, input[type="radio"]');
    filterInputs.forEach(function(input) {
        input.addEventListener('change', function() {
            filterForm.submit();
        });
    });
    
    // 处理搜索框
    var searchInput = filterForm.querySelector('input[type="search"]');
    var searchTimeout;
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                filterForm.submit();
            }, 500); // 延迟500ms提交，避免频繁请求
        });
    }
}

/**
 * 设置检测结果过滤功能
 */
function setupDetectionFilters() {
    var filterForm = document.querySelector('#detection-filter-form');
    if (!filterForm) return;
    
    // 自动提交表单当过滤条件改变时
    var filterInputs = filterForm.querySelectorAll('select, input[type="range"]');
    filterInputs.forEach(function(input) {
        input.addEventListener('change', function() {
            filterForm.submit();
        });
    });
    
    // 处理置信度滑块
    var confidenceSlider = filterForm.querySelector('#confidence-threshold');
    var confidenceValue = filterForm.querySelector('#confidence-value');
    if (confidenceSlider && confidenceValue) {
        confidenceSlider.addEventListener('input', function() {
            confidenceValue.textContent = this.value + '%';
        });
    }
}

/**
 * 设置密码强度检查
 */
function setupPasswordStrengthCheck() {
    var passwordInput = document.querySelector('#new_password, #password');
    var strengthIndicator = document.querySelector('.password-strength');
    
    if (!passwordInput || !strengthIndicator) return;
    
    passwordInput.addEventListener('input', function() {
        var password = this.value;
        var strength = checkPasswordStrength(password);
        
        // 更新强度指示器
        strengthIndicator.className = 'password-strength';
        
        if (password.length === 0) {
            strengthIndicator.textContent = '';
        } else if (strength < 2) {
            strengthIndicator.textContent = '弱';
            strengthIndicator.classList.add('text-danger');
        } else if (strength < 4) {
            strengthIndicator.textContent = '中';
            strengthIndicator.classList.add('text-warning');
        } else {
            strengthIndicator.textContent = '强';
            strengthIndicator.classList.add('text-success');
        }
    });
    
    function checkPasswordStrength(password) {
        var strength = 0;
        
        // 长度检查
        if (password.length >= 8) strength += 1;
        if (password.length >= 12) strength += 1;
        
        // 复杂度检查
        if (/[a-z]/.test(password)) strength += 1; // 小写字母
        if (/[A-Z]/.test(password)) strength += 1; // 大写字母
        if (/[0-9]/.test(password)) strength += 1; // 数字
        if (/[^a-zA-Z0-9]/.test(password)) strength += 1; // 特殊字符
        
        return strength;
    }
}

/**
 * 设置表单验证
 */
function setupFormValidation() {
    var forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
    
    // 密码确认验证
    var passwordInput = document.querySelector('#new_password, #password');
    var confirmInput = document.querySelector('#confirm_password');
    
    if (passwordInput && confirmInput) {
        confirmInput.addEventListener('input', function() {
            if (this.value !== passwordInput.value) {
                this.setCustomValidity('密码不匹配');
            } else {
                this.setCustomValidity('');
            }
        });
        
        passwordInput.addEventListener('input', function() {
            if (confirmInput.value !== '') {
                if (confirmInput.value !== this.value) {
                    confirmInput.setCustomValidity('密码不匹配');
                } else {
                    confirmInput.setCustomValidity('');
                }
            }
        });
    }
}

/**
 * 初始化图表
 */
function initializeCharts() {
    // 检测结果饼图
    var abnormalityChartEl = document.getElementById('abnormality-chart');
    if (abnormalityChartEl) {
        var ctx = abnormalityChartEl.getContext('2d');
        
        // 从元素的data属性获取数据
        var labels = JSON.parse(abnormalityChartEl.dataset.labels || '[]');
        var values = JSON.parse(abnormalityChartEl.dataset.values || '[]');
        var colors = JSON.parse(abnormalityChartEl.dataset.colors || '[]');
        
        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: colors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: true,
                        text: '异常类型分布'
                    }
                }
            }
        });
    }
    
    // 仪表板统计图表
    var statsChartEl = document.getElementById('stats-chart');
    if (statsChartEl) {
        var ctx = statsChartEl.getContext('2d');
        
        // 从元素的data属性获取数据
        var dates = JSON.parse(statsChartEl.dataset.dates || '[]');
        var uploads = JSON.parse(statsChartEl.dataset.uploads || '[]');
        var detections = JSON.parse(statsChartEl.dataset.detections || '[]');
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [{
                    label: '上传研究',
                    data: uploads,
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'YOLO检测',
                    data: detections,
                    borderColor: '#198754',
                    backgroundColor: 'rgba(25, 135, 84, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: '过去30天活动'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
    }
}

/**
 * 显示提示消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, danger, warning, info)
 */
function showAlert(message, type) {
    var alertContainer = document.querySelector('.alert-container');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.className = 'alert-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(alertContainer);
    }
    
    var alert = document.createElement('div');
    alert.className = 'alert alert-' + type + ' alert-dismissible fade show';
    alert.innerHTML = message + 
        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
    
    alertContainer.appendChild(alert);
    
    // 5秒后自动关闭
    setTimeout(function() {
        var bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
    }, 5000);
}

/**
 * 确认对话框
 * @param {string} message - 确认消息
 * @param {function} callback - 确认后的回调函数
 */
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * YOLO检测功能
 * @param {string} studyId - 研究ID
 */
function runYoloDetection(studyId) {
    // 显示加载指示器
    var detectionBtn = document.querySelector('#detection-btn-' + studyId);
    if (detectionBtn) {
        detectionBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 检测中...';
        detectionBtn.disabled = true;
    }
    
    // 发送AJAX请求
    fetch('/api/detect/' + studyId, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // 检测成功，显示成功消息并重定向到结果页面
            showAlert('检测完成！正在跳转到结果页面...', 'success');
            setTimeout(() => {
                window.location.href = '/results/' + studyId;
            }, 1500);
        } else {
            // 检测失败，显示错误消息
            showAlert('检测失败：' + (data.message || data.error || '未知错误'), 'danger');
            
            // 恢复按钮状态
            if (detectionBtn) {
                detectionBtn.innerHTML = '<i class="fas fa-brain me-1"></i> YOLO检测';
                detectionBtn.disabled = false;
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('发生错误，请稍后重试。', 'danger');
        
        // 恢复按钮状态
        if (detectionBtn) {
            detectionBtn.innerHTML = '<i class="fas fa-brain me-1"></i> YOLO检测';
            detectionBtn.disabled = false;
        }
    });
}

/**
 * 获取CSRF令牌
 * @returns {string} CSRF令牌
 */
function getCsrfToken() {
    var csrfInput = document.querySelector('input[name="csrf_token"]');
    if (csrfInput) {
        return csrfInput.value;
    }
    return '';
}

/**
 * 删除研究
 * @param {string} studyId - 研究ID
 */
function deleteStudy(studyId) {
    confirmAction('确定要删除这个研究吗？此操作无法撤销。', function() {
        // 发送AJAX请求
        fetch('/api/studies/' + studyId, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 删除成功，从DOM中移除研究卡片
                var studyCard = document.querySelector('#study-card-' + studyId);
                if (studyCard) {
                    studyCard.remove();
                }
                showAlert('研究已成功删除。', 'success');
            } else {
                // 删除失败，显示错误消息
                showAlert('删除失败：' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('发生错误，请稍后重试。', 'danger');
        });
    });
}

/**
 * 打开OHIF查看器
 * @param {string} studyId - 研究ID
 */
function openOhifViewer(studyId) {
    // 获取OHIF查看器URL
    fetch('/api/ohif_url/' + studyId)
    .then(response => response.json())
    .then(data => {
        if (data.url) {
            // 在新窗口打开OHIF查看器
            window.open(data.url, '_blank');
        } else {
            showAlert('无法获取OHIF查看器URL：' + (data.error || '未知错误'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('发生错误，请稍后重试。', 'danger');
    });
}