using System.Drawing;

namespace MedicalImageAnalysis.Core.Models;

/// <summary>
/// 目标检测结果
/// </summary>
public class ObjectDetectionResult
{
    public ObjectDetectionConfig Config { get; set; } = new();
    public DateTime DetectionTime { get; set; }
    public List<Detection> Detections { get; set; } = new();
    public DetectionQualityMetrics QualityMetrics { get; set; } = new();
    public TimeSpan ProcessingTime { get; set; }
}

/// <summary>
/// 目标检测配置
/// </summary>
public class ObjectDetectionConfig
{
    public string ModelPath { get; set; } = string.Empty;
    public double ConfidenceThreshold { get; set; } = 0.5;
    public double IoUThreshold { get; set; } = 0.45;
    public int MaxDetections { get; set; } = 1000;
    public int InputSize { get; set; } = 640;
    public bool UseGPU { get; set; } = true;
    public int BatchSize { get; set; } = 1;
    public List<string> ClassNames { get; set; } = new();
}

/// <summary>
/// 检测结果
/// </summary>
public class Detection
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Rectangle BoundingBox { get; set; }
    public string Label { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public int ClassId { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
    public byte[]? Mask { get; set; }
    public List<Point> Keypoints { get; set; } = new();
}

/// <summary>
/// 检测进度
/// </summary>
public class DetectionProgress
{
    public string Stage { get; set; } = string.Empty;
    public double Progress { get; set; }
    public string Message { get; set; } = string.Empty;
    public TimeSpan ElapsedTime { get; set; }
}

/// <summary>
/// 检测质量指标
/// </summary>
public class DetectionQualityMetrics
{
    public double AverageConfidence { get; set; }
    public int DetectionCount { get; set; }
    public double QualityScore { get; set; }
    public Dictionary<string, int> ClassDistribution { get; set; } = new();
    public double BoundingBoxQuality { get; set; }
    public double LabelConsistency { get; set; }
}

/// <summary>
/// 集成检测结果
/// </summary>
public class EnsembleDetectionResult
{
    public List<ObjectDetectionConfig> ModelConfigs { get; set; } = new();
    public EnsembleConfig EnsembleConfig { get; set; } = new();
    public List<ObjectDetectionResult> IndividualResults { get; set; } = new();
    public List<Detection> FusedDetections { get; set; } = new();
    public EnsembleQualityMetrics EnsembleQuality { get; set; } = new();
}

/// <summary>
/// 集成配置
/// </summary>
public class EnsembleConfig
{
    public EnsembleMethod Method { get; set; } = EnsembleMethod.WeightedAverage;
    public Dictionary<string, double> ModelWeights { get; set; } = new();
    public double ConsensusThreshold { get; set; } = 0.5;
    public bool UseNMS { get; set; } = true;
    public double NMSThreshold { get; set; } = 0.5;
}

/// <summary>
/// 集成方法
/// </summary>
public enum EnsembleMethod
{
    Average,
    WeightedAverage,
    Voting,
    MaxConfidence,
    Consensus
}

/// <summary>
/// 集成质量指标
/// </summary>
public class EnsembleQualityMetrics
{
    public double ModelAgreement { get; set; }
    public double FusionQuality { get; set; }
    public double OverallQuality { get; set; }
    public Dictionary<string, double> ModelContributions { get; set; } = new();
}

/// <summary>
/// 语义分割结果
/// </summary>
public class SemanticSegmentationResult
{
    public SegmentationConfig Config { get; set; } = new();
    public DateTime SegmentationTime { get; set; }
    public byte[] SegmentationMask { get; set; } = Array.Empty<byte>();
    public List<SegmentationRegion> Regions { get; set; } = new();
    public SegmentationQualityMetrics QualityMetrics { get; set; } = new();
    public TimeSpan ProcessingTime { get; set; }
}

/// <summary>
/// 分割配置
/// </summary>
public class SegmentationConfig
{
    public string ModelPath { get; set; } = string.Empty;
    public int InputSize { get; set; } = 512;
    public int NumClasses { get; set; } = 2;
    public bool UseGPU { get; set; } = true;
    public double ConfidenceThreshold { get; set; } = 0.5;
    public List<string> ClassNames { get; set; } = new();
    public bool PostprocessMask { get; set; } = true;
}

/// <summary>
/// 分割区域
/// </summary>
public class SegmentationRegion
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public int ClassId { get; set; }
    public string Label { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public Rectangle BoundingBox { get; set; }
    public List<Point> Contour { get; set; } = new();
    public double Area { get; set; }
    public double Perimeter { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// 分割进度
/// </summary>
public class SegmentationProgress
{
    public string Stage { get; set; } = string.Empty;
    public double Progress { get; set; }
    public string Message { get; set; } = string.Empty;
    public TimeSpan ElapsedTime { get; set; }
}

/// <summary>
/// 分割质量指标
/// </summary>
public class SegmentationQualityMetrics
{
    public double MeanIoU { get; set; }
    public double PixelAccuracy { get; set; }
    public double MeanAccuracy { get; set; }
    public double FrequencyWeightedIoU { get; set; }
    public Dictionary<string, double> ClassIoU { get; set; } = new();
    public double BoundaryAccuracy { get; set; }
}

/// <summary>
/// 实例分割结果
/// </summary>
public class InstanceSegmentationResult
{
    public InstanceSegmentationConfig Config { get; set; } = new();
    public DateTime SegmentationTime { get; set; }
    public List<InstanceMask> Instances { get; set; } = new();
    public InstanceSegmentationQualityMetrics QualityMetrics { get; set; } = new();
    public TimeSpan ProcessingTime { get; set; }
}

/// <summary>
/// 实例分割配置
/// </summary>
public class InstanceSegmentationConfig
{
    public string ModelPath { get; set; } = string.Empty;
    public double ConfidenceThreshold { get; set; } = 0.7;
    public double MaskThreshold { get; set; } = 0.5;
    public int MaxInstances { get; set; } = 100;
    public bool UseGPU { get; set; } = true;
    public List<string> ClassNames { get; set; } = new();
}

/// <summary>
/// 实例掩码
/// </summary>
public class InstanceMask
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public int ClassId { get; set; }
    public string Label { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public Rectangle BoundingBox { get; set; }
    public byte[] Mask { get; set; } = Array.Empty<byte>();
    public double Area { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// 实例分割质量指标
/// </summary>
public class InstanceSegmentationQualityMetrics
{
    public double MeanAP { get; set; }
    public double AP50 { get; set; }
    public double AP75 { get; set; }
    public Dictionary<string, double> ClassAP { get; set; } = new();
    public double MaskQuality { get; set; }
    public double BoundingBoxQuality { get; set; }
}

/// <summary>
/// 主动学习结果
/// </summary>
public class ActiveLearningResult
{
    public ActiveLearningConfig Config { get; set; } = new();
    public DateTime SelectionTime { get; set; }
    public List<PixelData> SelectedSamples { get; set; } = new();
    public Dictionary<int, double> UncertaintyScores { get; set; } = new();
    public Dictionary<int, double> DiversityScores { get; set; } = new();
    public ActiveLearningQualityMetrics SelectionQuality { get; set; } = new();
}

/// <summary>
/// 主动学习配置
/// </summary>
public class ActiveLearningConfig
{
    public int SampleCount { get; set; } = 100;
    public UncertaintyMethod UncertaintyMethod { get; set; } = UncertaintyMethod.Entropy;
    public DiversityMethod DiversityMethod { get; set; } = DiversityMethod.KMeans;
    public double UncertaintyWeight { get; set; } = 0.7;
    public double DiversityWeight { get; set; } = 0.3;
    public string ModelPath { get; set; } = string.Empty;
}

/// <summary>
/// 不确定性方法
/// </summary>
public enum UncertaintyMethod
{
    Entropy,
    LeastConfidence,
    MarginSampling,
    VariationRatio,
    BALD
}

/// <summary>
/// 多样性方法
/// </summary>
public enum DiversityMethod
{
    KMeans,
    CoreSet,
    Random,
    FeatureDiversity
}

/// <summary>
/// 主动学习质量指标
/// </summary>
public class ActiveLearningQualityMetrics
{
    public double UncertaintyScore { get; set; }
    public double DiversityScore { get; set; }
    public double CoverageScore { get; set; }
    public double OverallQuality { get; set; }
}

/// <summary>
/// 伪标签结果
/// </summary>
public class PseudoLabelingResult
{
    public PseudoLabelingConfig Config { get; set; } = new();
    public DateTime GenerationTime { get; set; }
    public List<PseudoLabel> PseudoLabels { get; set; } = new();
    public PseudoLabelQualityMetrics QualityMetrics { get; set; } = new();
}

/// <summary>
/// 伪标签配置
/// </summary>
public class PseudoLabelingConfig
{
    public double ConfidenceThreshold { get; set; } = 0.9;
    public ObjectDetectionConfig DetectionConfig { get; set; } = new();
    public bool UseEnsemble { get; set; } = false;
    public List<string> ModelPaths { get; set; } = new();
    public int MaxLabelsPerImage { get; set; } = 50;
}

/// <summary>
/// 伪标签
/// </summary>
public class PseudoLabel
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public PixelData ImageData { get; set; } = new();
    public List<Detection> Detections { get; set; } = new();
    public double Confidence { get; set; }
    public string GenerationMethod { get; set; } = string.Empty;
    public DateTime CreatedTime { get; set; } = DateTime.UtcNow;
    public bool IsValidated { get; set; } = false;
}

/// <summary>
/// 伪标签质量指标
/// </summary>
public class PseudoLabelQualityMetrics
{
    public double AverageConfidence { get; set; }
    public int TotalLabels { get; set; }
    public Dictionary<string, int> ClassDistribution { get; set; } = new();
    public double QualityScore { get; set; }
    public double NoiseEstimate { get; set; }
}
