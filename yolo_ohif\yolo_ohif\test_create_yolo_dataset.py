#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试create_yolo_dataset.py脚本
验证从nii.gz到完整YOLO数据集的生成流程
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from create_yolo_dataset import YOLODatasetCreator
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dataset_creation():
    """
    测试YOLO数据集创建流程
    """
    logger.info("开始测试YOLO数据集创建流程...")
    
    # 检查数据集目录
    dataset_root = Path('./dataset')
    if not dataset_root.exists():
        logger.error(f"数据集目录不存在: {dataset_root}")
        return False
    
    # 检查必要的子目录
    required_dirs = ['image_T2', 'label_T2', 'image_T2_normal']
    for dir_name in required_dirs:
        dir_path = dataset_root / dir_name
        if not dir_path.exists():
            logger.error(f"必要目录不存在: {dir_path}")
            return False
        
        # 检查是否有nii.gz文件
        nii_files = list(dir_path.glob('*.nii.gz'))
        logger.info(f"{dir_name} 目录包含 {len(nii_files)} 个nii.gz文件")
        
        if len(nii_files) == 0:
            logger.warning(f"{dir_name} 目录为空")
    
    # 创建数据集创建器
    creator = YOLODatasetCreator(
        dataset_root='./dataset',
        output_root='./test_yolo_dataset_output',
        img_size=640
    )
    
    try:
        logger.info("开始创建YOLO数据集...")
        config_path = creator.create_dataset()
        
        # 验证生成的文件
        logger.info("验证生成的数据集结构...")
        
        # 检查YOLO数据集目录结构
        yolo_root = creator.yolo_dataset_root
        required_subdirs = [
            'images/train', 'images/val', 'images/test',
            'labels/train', 'labels/val', 'labels/test'
        ]
        
        for subdir in required_subdirs:
            subdir_path = yolo_root / subdir
            if subdir_path.exists():
                files = list(subdir_path.glob('*'))
                logger.info(f"{subdir}: {len(files)} 个文件")
            else:
                logger.warning(f"目录不存在: {subdir}")
        
        # 检查配置文件
        config_file = Path(config_path)
        if config_file.exists():
            logger.info(f"配置文件已生成: {config_path}")
            
            # 读取并显示配置内容
            import yaml
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"配置内容: {config}")
        else:
            logger.error(f"配置文件不存在: {config_path}")
            return False
        
        # 统计总的文件数量
        total_images = 0
        total_labels = 0
        
        for split in ['train', 'val', 'test']:
            img_dir = yolo_root / 'images' / split
            label_dir = yolo_root / 'labels' / split
            
            if img_dir.exists():
                img_count = len(list(img_dir.glob('*.jpg')))
                total_images += img_count
            
            if label_dir.exists():
                label_count = len(list(label_dir.glob('*.txt')))
                total_labels += label_count
        
        logger.info(f"数据集统计:")
        logger.info(f"  - 总图像数: {total_images}")
        logger.info(f"  - 总标注数: {total_labels}")
        logger.info(f"  - 标注率: {total_labels/total_images*100:.1f}%" if total_images > 0 else "  - 标注率: 0%")
        
        logger.info("YOLO数据集创建测试成功！")
        return True
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_functions():
    """
    测试各个功能模块
    """
    logger.info("测试各个功能模块...")
    
    creator = YOLODatasetCreator(
        dataset_root='./dataset',
        output_root='./test_individual_output',
        img_size=640
    )
    
    try:
        # 测试撕裂图像处理
        logger.info("测试撕裂图像处理...")
        tear_data = creator.process_tear_images()
        logger.info(f"撕裂图像处理完成，生成 {len(tear_data)} 个样本")
        
        # 测试正常图像处理
        logger.info("测试正常图像处理...")
        normal_data = creator.process_normal_images()
        logger.info(f"正常图像处理完成，生成 {len(normal_data)} 个样本")
        
        # 测试数据集划分
        logger.info("测试数据集划分...")
        all_data = tear_data + normal_data
        creator.split_dataset(all_data)
        logger.info("数据集划分完成")
        
        # 测试配置文件生成
        logger.info("测试配置文件生成...")
        config_path = creator.create_yaml_config()
        logger.info(f"配置文件生成完成: {config_path}")
        
        # 清理临时文件
        creator.cleanup_temp_files()
        
        logger.info("所有功能模块测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"功能模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("YOLO数据集创建脚本测试")
    print("=" * 60)
    
    # 测试1: 各个功能模块
    print("\n1. 测试各个功能模块...")
    success1 = test_individual_functions()
    
    # 测试2: 完整数据集创建流程
    print("\n2. 测试完整数据集创建流程...")
    success2 = test_dataset_creation()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"功能模块测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"完整流程测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
        print("\n📋 使用说明:")
        print("1. 运行完整数据集创建:")
        print("   python create_yolo_dataset.py")
        print("\n2. 自定义参数:")
        print("   python create_yolo_dataset.py --dataset_root ./dataset --output_root ./my_output --img_size 640")
        print("\n3. 训练模型:")
        print("   yolo train data=./yolo_dataset_output/yolo_dataset/dataset.yaml model=yolo11x.pt epochs=100")
        return True
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)