using System.Drawing;

namespace MedicalImageAnalysis.Core.Models;

/// <summary>
/// 数据集分析结果
/// </summary>
public class DatasetAnalysisResult
{
    public string DatasetPath { get; set; } = string.Empty;
    public DateTime AnalysisTime { get; set; }
    public DatasetStructure Structure { get; set; } = new();
    public ImageStatistics ImageStatistics { get; set; } = new();
    public AnnotationQualityMetrics AnnotationQuality { get; set; } = new();
    public ClassBalanceMetrics ClassBalance { get; set; } = new();
    public List<string> Issues { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// 数据集结构
/// </summary>
public class DatasetStructure
{
    public int TotalDirectories { get; set; }
    public int TotalFiles { get; set; }
    public List<string> ImageFormats { get; set; } = new();
    public List<string> AnnotationFormats { get; set; } = new();
    public Dictionary<string, int> DirectoryStructure { get; set; } = new();
}

/// <summary>
/// 图像统计信息
/// </summary>
public class ImageStatistics
{
    public int TotalImages { get; set; }
    public double AverageWidth { get; set; }
    public double AverageHeight { get; set; }
    public double AverageFileSize { get; set; }
    public Dictionary<string, int> ResolutionDistribution { get; set; } = new();
    public Dictionary<string, int> FormatDistribution { get; set; } = new();
}

/// <summary>
/// 标注质量指标
/// </summary>
public class AnnotationQualityMetrics
{
    public double AverageAnnotationsPerImage { get; set; }
    public double AnnotationConsistencyScore { get; set; }
    public double BoundingBoxQualityScore { get; set; }
    public double LabelAccuracyScore { get; set; }
    public Dictionary<string, double> ClassQualityScores { get; set; } = new();
}

/// <summary>
/// 类别平衡指标
/// </summary>
public class ClassBalanceMetrics
{
    public Dictionary<string, int> ClassDistribution { get; set; } = new();
    public double ImbalanceRatio { get; set; }
    public double MinorityClassRatio { get; set; }
    public double GiniCoefficient { get; set; }
    public List<string> UnderrepresentedClasses { get; set; } = new();
}

/// <summary>
/// 数据增强结果
/// </summary>
public class DataAugmentationResult
{
    public string InputPath { get; set; } = string.Empty;
    public string OutputPath { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public bool Success { get; set; }
    public int OriginalImageCount { get; set; }
    public int AugmentedImageCount { get; set; }
    public AugmentationStrategy AugmentationStrategy { get; set; } = new();
    public QualityMetrics QualityMetrics { get; set; } = new();
}

/// <summary>
/// 增强策略
/// </summary>
public class AugmentationStrategy
{
    public List<Transform> Transforms { get; set; } = new();
    public double AugmentationRatio { get; set; }
    public Dictionary<string, double> ClassSpecificRatios { get; set; } = new();
}

/// <summary>
/// 变换
/// </summary>
public class Transform
{
    public string Type { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public double Probability { get; set; }
}

/// <summary>
/// 质量指标
/// </summary>
public class QualityMetrics
{
    public double OverallQuality { get; set; }
    public double DiversityScore { get; set; }
    public double RealisticnessScore { get; set; }
    public Dictionary<string, double> MetricDetails { get; set; } = new();
}

/// <summary>
/// 数据集划分结果
/// </summary>
public class DatasetSplitResult
{
    public string DatasetPath { get; set; } = string.Empty;
    public DatasetSplitConfig Config { get; set; } = new();
    public Dictionary<string, int> ClassDistribution { get; set; } = new();
    public List<string> TrainingSplit { get; set; } = new();
    public List<string> ValidationSplit { get; set; } = new();
    public List<string> TestSplit { get; set; } = new();
    public SplitQualityMetrics SplitQuality { get; set; } = new();
    public List<string> ConfigFiles { get; set; } = new();
}

/// <summary>
/// 数据集划分配置
/// </summary>
public class DatasetSplitConfig
{
    public double TrainRatio { get; set; } = 0.7;
    public double ValidationRatio { get; set; } = 0.2;
    public double TestRatio { get; set; } = 0.1;
    public bool UseStratifiedSplit { get; set; } = true;
    public int RandomSeed { get; set; } = 42;
    public bool EnsureMinimumSamples { get; set; } = true;
    public int MinimumSamplesPerClass { get; set; } = 5;
}

/// <summary>
/// 数据集分割
/// </summary>
public class DatasetSplits
{
    public List<string> Training { get; set; } = new();
    public List<string> Validation { get; set; } = new();
    public List<string> Test { get; set; } = new();
}

/// <summary>
/// 分割质量指标
/// </summary>
public class SplitQualityMetrics
{
    public double StratificationScore { get; set; }
    public double BalanceScore { get; set; }
    public Dictionary<string, double> ClassDistributionSimilarity { get; set; } = new();
}

/// <summary>
/// 超参数优化结果
/// </summary>
public class HyperparameterOptimizationResult
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public HyperparameterOptimizationConfig Config { get; set; } = new();
    public SearchSpace SearchSpace { get; set; } = new();
    public Dictionary<string, object> BestParameters { get; set; } = new();
    public double BestScore { get; set; }
    public List<OptimizationTrial> Trials { get; set; } = new();
    public OptimizationStatistics Statistics { get; set; } = new();
}

/// <summary>
/// 超参数优化配置
/// </summary>
public class HyperparameterOptimizationConfig
{
    public OptimizationMethod OptimizationMethod { get; set; } = OptimizationMethod.BayesianOptimization;
    public int MaxTrials { get; set; } = 100;
    public int MaxEpochsPerTrial { get; set; } = 50;
    public string OptimizationMetric { get; set; } = "validation_accuracy";
    public OptimizationDirection Direction { get; set; } = OptimizationDirection.Maximize;
    public Dictionary<string, ParameterRange> ParameterRanges { get; set; } = new();
}

/// <summary>
/// 优化方法
/// </summary>
public enum OptimizationMethod
{
    GridSearch,
    RandomSearch,
    BayesianOptimization,
    Hyperband,
    BOHB
}

/// <summary>
/// 优化方向
/// </summary>
public enum OptimizationDirection
{
    Minimize,
    Maximize
}

/// <summary>
/// 搜索空间
/// </summary>
public class SearchSpace
{
    public Dictionary<string, ParameterRange> Parameters { get; set; } = new();
    public int TotalCombinations { get; set; }
}

/// <summary>
/// 参数范围
/// </summary>
public class ParameterRange
{
    public string Type { get; set; } = string.Empty; // "float", "int", "categorical"
    public object MinValue { get; set; } = 0;
    public object MaxValue { get; set; } = 1;
    public List<object> Values { get; set; } = new(); // For categorical parameters
    public string Distribution { get; set; } = "uniform"; // "uniform", "log_uniform", "normal"
}

/// <summary>
/// 优化试验
/// </summary>
public class OptimizationTrial
{
    public int TrialId { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public double Score { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TrialStatus Status { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
}

/// <summary>
/// 试验状态
/// </summary>
public enum TrialStatus
{
    Running,
    Completed,
    Failed,
    Pruned
}

/// <summary>
/// 优化统计
/// </summary>
public class OptimizationStatistics
{
    public int TotalTrials { get; set; }
    public int CompletedTrials { get; set; }
    public int FailedTrials { get; set; }
    public int PrunedTrials { get; set; }
    public TimeSpan TotalOptimizationTime { get; set; }
    public TimeSpan AverageTrialTime { get; set; }
    public double BestScoreImprovement { get; set; }
}

/// <summary>
/// 学习率调度
/// </summary>
public class LearningRateSchedule
{
    public ScheduleType ScheduleType { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public double ExpectedImprovement { get; set; }
    public List<LearningRatePoint> Schedule { get; set; } = new();
}

/// <summary>
/// 调度类型
/// </summary>
public enum ScheduleType
{
    Constant,
    StepDecay,
    ExponentialDecay,
    CosineAnnealing,
    ReduceOnPlateau,
    CyclicLR,
    OneCycleLR
}

/// <summary>
/// 学习率点
/// </summary>
public class LearningRatePoint
{
    public int Epoch { get; set; }
    public double LearningRate { get; set; }
}

/// <summary>
/// 数据集分析配置
/// </summary>
public class DatasetAnalysisConfig
{
    public bool AnalyzeImageQuality { get; set; } = true;
    public bool AnalyzeAnnotationQuality { get; set; } = true;
    public bool CheckDataBalance { get; set; } = true;
    public bool DetectDuplicates { get; set; } = true;
    public double QualityThreshold { get; set; } = 0.8;
}

/// <summary>
/// 数据增强配置
/// </summary>
public class DataAugmentationConfig
{
    public bool EnableRotation { get; set; } = true;
    public bool EnableFlipping { get; set; } = true;
    public bool EnableScaling { get; set; } = true;
    public bool EnableColorAugmentation { get; set; } = true;
    public bool EnableNoiseAddition { get; set; } = false;
    public double AugmentationRatio { get; set; } = 2.0;
    public bool PreserveAspectRatio { get; set; } = true;
    public Dictionary<string, double> TransformProbabilities { get; set; } = new();
}

/// <summary>
/// 超参数优化进度
/// </summary>
public class HyperparameterOptimizationProgress
{
    public int CurrentTrial { get; set; }
    public int TotalTrials { get; set; }
    public double BestScore { get; set; }
    public Dictionary<string, object> BestParameters { get; set; } = new();
    public TimeSpan ElapsedTime { get; set; }
    public TimeSpan EstimatedRemainingTime { get; set; }
}

/// <summary>
/// 学习率优化配置
/// </summary>
public class LearningRateOptimizationConfig
{
    public int LookbackEpochs { get; set; } = 10;
    public double ImprovementThreshold { get; set; } = 0.01;
    public bool EnableAdaptiveScheduling { get; set; } = true;
    public List<ScheduleType> AllowedScheduleTypes { get; set; } = new();
}

/// <summary>
/// 训练历史
/// </summary>
public class TrainingHistory
{
    public List<EpochMetrics> Epochs { get; set; } = new();
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
}

/// <summary>
/// 轮次指标
/// </summary>
public class EpochMetrics
{
    public int Epoch { get; set; }
    public double TrainingLoss { get; set; }
    public double ValidationLoss { get; set; }
    public double TrainingAccuracy { get; set; }
    public double ValidationAccuracy { get; set; }
    public double LearningRate { get; set; }
    public TimeSpan EpochTime { get; set; }
    public Dictionary<string, double> AdditionalMetrics { get; set; } = new();
}

/// <summary>
/// 早停决策
/// </summary>
public class EarlyStoppingDecision
{
    public bool ShouldStop { get; set; }
    public string Reason { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public ValidationTrend ValidationTrend { get; set; } = new();
    public double OverfittingScore { get; set; }
    public ConvergenceState ConvergenceState { get; set; } = new();
}

/// <summary>
/// 验证趋势
/// </summary>
public class ValidationTrend
{
    public TrendDirection Direction { get; set; }
    public double Slope { get; set; }
    public double Stability { get; set; }
    public int PlateauLength { get; set; }
}

/// <summary>
/// 趋势方向
/// </summary>
public enum TrendDirection
{
    Improving,
    Degrading,
    Stable,
    Oscillating
}

/// <summary>
/// 收敛状态
/// </summary>
public class ConvergenceState
{
    public bool HasConverged { get; set; }
    public double ConvergenceRate { get; set; }
    public int EstimatedEpochsToConvergence { get; set; }
    public double ConvergenceConfidence { get; set; }
}

/// <summary>
/// 早停配置
/// </summary>
public class EarlyStoppingConfig
{
    public int Patience { get; set; } = 10;
    public double MinDelta { get; set; } = 0.001;
    public string MonitorMetric { get; set; } = "validation_loss";
    public bool RestoreBestWeights { get; set; } = true;
    public double OverfittingThreshold { get; set; } = 0.1;
}

/// <summary>
/// 训练异常
/// </summary>
public class TrainingAnomaly
{
    public AnomalyType Type { get; set; }
    public string Description { get; set; } = string.Empty;
    public double Severity { get; set; }
    public int EpochDetected { get; set; }
    public Dictionary<string, object> Details { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// 异常类型
/// </summary>
public enum AnomalyType
{
    LossSpike,
    LossNaN,
    GradientExplosion,
    GradientVanishing,
    PerformanceDrop,
    ResourceExhaustion,
    DataLeakage,
    Overfitting
}

/// <summary>
/// 异常检测配置
/// </summary>
public class AnomalyDetectionConfig
{
    public double LossSpikeThreshold { get; set; } = 2.0;
    public double GradientThreshold { get; set; } = 10.0;
    public double PerformanceDropThreshold { get; set; } = 0.1;
    public int WindowSize { get; set; } = 5;
    public bool EnableResourceMonitoring { get; set; } = true;
}

/// <summary>
/// 模型评估结果
/// </summary>
public class ModelEvaluationResult
{
    public string ModelPath { get; set; } = string.Empty;
    public string TestDataPath { get; set; } = string.Empty;
    public DateTime EvaluationTime { get; set; }
    public BasicMetrics BasicMetrics { get; set; } = new();
    public AdvancedMetrics AdvancedMetrics { get; set; } = new();
    public RobustnessTestResult RobustnessTest { get; set; } = new();
    public ExplainabilityAnalysis ExplainabilityAnalysis { get; set; } = new();
    public BenchmarkComparison BenchmarkComparison { get; set; } = new();
    public string Report { get; set; } = string.Empty;
}

/// <summary>
/// 基础指标
/// </summary>
public class BasicMetrics
{
    public double Accuracy { get; set; }
    public double Precision { get; set; }
    public double Recall { get; set; }
    public double F1Score { get; set; }
    public double AUC { get; set; }
    public double Loss { get; set; }
    public double OverallScore { get; set; }
    public Dictionary<string, double> ClassSpecificMetrics { get; set; } = new();
}

/// <summary>
/// 高级指标
/// </summary>
public class AdvancedMetrics
{
    public double Specificity { get; set; }
    public double NPV { get; set; }
    public double MCC { get; set; }
    public double CohenKappa { get; set; }
    public double BalancedAccuracy { get; set; }
    public ConfusionMatrix ConfusionMatrix { get; set; } = new();
    public ROCCurve ROCCurve { get; set; } = new();
    public PRCurve PRCurve { get; set; } = new();
}

/// <summary>
/// 混淆矩阵
/// </summary>
public class ConfusionMatrix
{
    public int[,] Matrix { get; set; } = new int[0, 0];
    public List<string> ClassLabels { get; set; } = new();
    public Dictionary<string, ClassificationMetrics> ClassMetrics { get; set; } = new();
}

/// <summary>
/// 分类指标
/// </summary>
public class ClassificationMetrics
{
    public int TruePositives { get; set; }
    public int FalsePositives { get; set; }
    public int TrueNegatives { get; set; }
    public int FalseNegatives { get; set; }
    public double Precision { get; set; }
    public double Recall { get; set; }
    public double F1Score { get; set; }
}

/// <summary>
/// ROC曲线
/// </summary>
public class ROCCurve
{
    public List<ROCPoint> Points { get; set; } = new();
    public double AUC { get; set; }
    public double OptimalThreshold { get; set; }
}

/// <summary>
/// ROC点
/// </summary>
public class ROCPoint
{
    public double FalsePositiveRate { get; set; }
    public double TruePositiveRate { get; set; }
    public double Threshold { get; set; }
}

/// <summary>
/// PR曲线
/// </summary>
public class PRCurve
{
    public List<PRPoint> Points { get; set; } = new();
    public double AUC { get; set; }
    public double OptimalThreshold { get; set; }
}

/// <summary>
/// PR点
/// </summary>
public class PRPoint
{
    public double Precision { get; set; }
    public double Recall { get; set; }
    public double Threshold { get; set; }
}

/// <summary>
/// 鲁棒性测试结果
/// </summary>
public class RobustnessTestResult
{
    public double NoiseRobustness { get; set; }
    public double RotationRobustness { get; set; }
    public double ScaleRobustness { get; set; }
    public double BrightnessRobustness { get; set; }
    public double ContrastRobustness { get; set; }
    public double OverallRobustness { get; set; }
    public Dictionary<string, double> DetailedResults { get; set; } = new();
}

/// <summary>
/// 可解释性分析
/// </summary>
public class ExplainabilityAnalysis
{
    public List<FeatureImportance> FeatureImportances { get; set; } = new();
    public List<SaliencyMap> SaliencyMaps { get; set; } = new();
    public double ExplainabilityScore { get; set; }
    public string ExplanationSummary { get; set; } = string.Empty;
}

/// <summary>
/// 特征重要性
/// </summary>
public class FeatureImportance
{
    public string FeatureName { get; set; } = string.Empty;
    public double Importance { get; set; }
    public double Confidence { get; set; }
}

/// <summary>
/// 显著性图
/// </summary>
public class SaliencyMap
{
    public string ImagePath { get; set; } = string.Empty;
    public string SaliencyMapPath { get; set; } = string.Empty;
    public double AverageActivation { get; set; }
    public List<ActivationRegion> HighActivationRegions { get; set; } = new();
}

/// <summary>
/// 激活区域
/// </summary>
public class ActivationRegion
{
    public Rectangle BoundingBox { get; set; }
    public double ActivationStrength { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 基准对比
/// </summary>
public class BenchmarkComparison
{
    public Dictionary<string, double> BenchmarkScores { get; set; } = new();
    public double RelativePerformance { get; set; }
    public string PerformanceRank { get; set; } = string.Empty;
    public List<string> ComparisonInsights { get; set; } = new();
}

/// <summary>
/// 模型评估配置
/// </summary>
public class ModelEvaluationConfig
{
    public bool CalculateAdvancedMetrics { get; set; } = true;
    public bool PerformRobustnessTest { get; set; } = true;
    public bool GenerateExplainability { get; set; } = true;
    public bool CompareToBenchmarks { get; set; } = true;
    public List<string> BenchmarkModels { get; set; } = new();
    public double ConfidenceThreshold { get; set; } = 0.5;
}

/// <summary>
/// 性能预测
/// </summary>
public class PerformancePrediction
{
    public CurveAnalysis CurveAnalysis { get; set; } = new();
    public PerformanceModel PerformanceModel { get; set; } = new();
    public double PredictedFinalAccuracy { get; set; }
    public double PredictedFinalLoss { get; set; }
    public TimeSpan EstimatedTrainingTime { get; set; }
    public double Confidence { get; set; }
}

/// <summary>
/// 曲线分析
/// </summary>
public class CurveAnalysis
{
    public TrendDirection LossTrend { get; set; }
    public TrendDirection AccuracyTrend { get; set; }
    public double LossConvergenceRate { get; set; }
    public double AccuracyConvergenceRate { get; set; }
    public bool ShowsOverfitting { get; set; }
}

/// <summary>
/// 性能模型
/// </summary>
public class PerformanceModel
{
    public string ModelType { get; set; } = string.Empty;
    public Dictionary<string, double> Parameters { get; set; } = new();
    public double R2Score { get; set; }
    public double RMSE { get; set; }
}

/// <summary>
/// 性能预测配置
/// </summary>
public class PerformancePredictionConfig
{
    public int MinEpochsForPrediction { get; set; } = 20;
    public string PredictionModel { get; set; } = "polynomial";
    public int PolynomialDegree { get; set; } = 3;
    public double ConfidenceInterval { get; set; } = 0.95;
}

#region 端到端训练管道模型

/// <summary>
/// 训练管道配置
/// </summary>
public class TrainingPipelineConfig
{
    public string ExperimentName { get; set; } = string.Empty;
    public string DatasetPath { get; set; } = string.Empty;
    public string OutputDirectory { get; set; } = string.Empty;

    // 数据配置
    public bool AutoSplitDataset { get; set; } = true;
    public double TrainRatio { get; set; } = 0.7;
    public double ValidationRatio { get; set; } = 0.2;
    public double TestRatio { get; set; } = 0.1;

    // 增强配置
    public bool EnableDataAugmentation { get; set; } = true;
    public double AugmentationRatio { get; set; } = 2.0;

    // 优化配置
    public bool EnableHyperparameterOptimization { get; set; } = true;
    public int MaxOptimizationTrials { get; set; } = 50;

    // 部署配置
    public bool PrepareForDeployment { get; set; } = true;
    public List<string> DeploymentFormats { get; set; } = new() { "ONNX", "TensorRT" };

    // 其他配置
    public Dictionary<string, object> CustomParameters { get; set; } = new();
}

/// <summary>
/// 训练管道结果
/// </summary>
public class TrainingPipelineResult
{
    public Guid ExperimentId { get; set; }
    public TrainingPipelineConfig Config { get; set; } = new();
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public bool Success { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;

    // 各阶段结果
    public DataPreparationResult DataPreparationResult { get; set; } = new();
    public DataQualityResult DataQualityResult { get; set; } = new();
    public DataAugmentationResult? DataAugmentationResult { get; set; }
    public ModelSelectionResult ModelSelectionResult { get; set; } = new();
    public HyperparameterOptimizationResult? HyperparameterOptimizationResult { get; set; }
    public TrainingResult TrainingResult { get; set; } = new();
    public ValidationResult ValidationResult { get; set; } = new();
    public TestResult TestResult { get; set; } = new();
    public DeploymentResult? DeploymentResult { get; set; }
    public string Report { get; set; } = string.Empty;

    public TimeSpan TotalDuration => EndTime - StartTime;
}

/// <summary>
/// 训练管道进度
/// </summary>
public class TrainingPipelineProgress
{
    public TrainingStage Stage { get; set; }
    public double Progress { get; set; }
    public string Message { get; set; } = string.Empty;
    public TimeSpan ElapsedTime { get; set; }
    public TimeSpan EstimatedRemainingTime { get; set; }
    public Dictionary<string, object> StageDetails { get; set; } = new();
}

/// <summary>
/// 训练阶段
/// </summary>
public enum TrainingStage
{
    DataPreparation,
    DataQualityAssessment,
    DataAugmentation,
    ModelSelection,
    HyperparameterOptimization,
    Training,
    Validation,
    Testing,
    DeploymentPreparation,
    ReportGeneration,
    Completed
}

/// <summary>
/// 数据准备结果
/// </summary>
public class DataPreparationResult
{
    public DatasetAnalysisResult DatasetAnalysis { get; set; } = new();
    public DatasetSplitResult? DatasetSplit { get; set; }
    public List<string> PreparedFiles { get; set; } = new();
    public Dictionary<string, object> Statistics { get; set; } = new();
}

/// <summary>
/// 数据质量结果
/// </summary>
public class DataQualityResult
{
    public double OverallQuality { get; set; }
    public List<string> Issues { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public Dictionary<string, double> QualityMetrics { get; set; } = new();
}

/// <summary>
/// 数据集统计信息
/// </summary>
public class DatasetStatistics
{
    public string DatasetPath { get; set; } = string.Empty;
    public DateTime AnalysisTime { get; set; }
    public string DatasetType { get; set; } = string.Empty;
    public int ImageCount { get; set; }
    public int AnnotationCount { get; set; }
    public int SubfolderCount { get; set; }
    public long TotalSize { get; set; }
    public Dictionary<string, int> ImageFormats { get; set; } = new();
    public Dictionary<string, int> AnnotationFormats { get; set; } = new();
    public Dictionary<string, SplitStatistics> SplitStatistics { get; set; } = new();
}

/// <summary>
/// 分割统计信息
/// </summary>
public class SplitStatistics
{
    public int ImageCount { get; set; }
    public int LabelCount { get; set; }
    public Dictionary<string, int> ClassDistribution { get; set; } = new();
}

/// <summary>
/// 数据集验证结果
/// </summary>
public class DatasetValidationResult
{
    public string DatasetPath { get; set; } = string.Empty;
    public DateTime ValidationTime { get; set; }
    public bool IsValid { get; set; }
    public List<string> Issues { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public Dictionary<string, object> ValidationMetrics { get; set; } = new();
}

/// <summary>
/// 开源数据集信息
/// </summary>
public class OpenSourceDataset
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string DatasetType { get; set; } = string.Empty; // YOLO, COCO, DICOM等
    public long SizeBytes { get; set; }
    public int ImageCount { get; set; }
    public int AnnotationCount { get; set; }
    public List<string> Classes { get; set; } = new();
    public string DownloadUrl { get; set; } = string.Empty;
    public string License { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public DateTime PublishDate { get; set; }
    public string Version { get; set; } = string.Empty;
    public Dictionary<string, string> Metadata { get; set; } = new();
    public bool IsDownloaded { get; set; }
    public string LocalPath { get; set; } = string.Empty;
}

/// <summary>
/// 数据集下载进度
/// </summary>
public class DatasetDownloadProgress
{
    public string DatasetId { get; set; } = string.Empty;
    public string DatasetName { get; set; } = string.Empty;
    public long TotalBytes { get; set; }
    public long DownloadedBytes { get; set; }
    public double ProgressPercentage => TotalBytes > 0 ? (double)DownloadedBytes / TotalBytes * 100 : 0;
    public string Status { get; set; } = string.Empty; // Downloading, Extracting, Completed, Failed
    public string ErrorMessage { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? CompletedTime { get; set; }
    public TimeSpan EstimatedTimeRemaining { get; set; }
    public double DownloadSpeedBytesPerSecond { get; set; }
}

/// <summary>
/// 模型选择结果
/// </summary>
public class ModelSelectionResult
{
    public string SelectedModel { get; set; } = string.Empty;
    public string ModelArchitecture { get; set; } = string.Empty;
    public Dictionary<string, object> ModelParameters { get; set; } = new();
    public string SelectionReason { get; set; } = string.Empty;
    public List<string> AlternativeModels { get; set; } = new();
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    public double ValidationAccuracy { get; set; }
    public double ValidationLoss { get; set; }
    public Dictionary<string, double> ValidationMetrics { get; set; } = new();
    public List<string> ValidationIssues { get; set; } = new();
}

/// <summary>
/// 测试结果
/// </summary>
public class TestResult
{
    public double TestAccuracy { get; set; }
    public double TestLoss { get; set; }
    public Dictionary<string, double> TestMetrics { get; set; } = new();
    public ModelEvaluationResult DetailedEvaluation { get; set; } = new();
}

/// <summary>
/// 部署结果
/// </summary>
public class DeploymentResult
{
    public List<string> GeneratedModels { get; set; } = new();
    public Dictionary<string, string> ModelPaths { get; set; } = new();
    public List<string> DeploymentInstructions { get; set; } = new();
    public Dictionary<string, object> DeploymentMetadata { get; set; } = new();
}

/// <summary>
/// 自动模型选择结果
/// </summary>
public class AutoModelSelectionResult
{
    public List<string> CandidateModels { get; set; } = new();
    public ModelSelectionConfig Config { get; set; } = new();
    public DateTime SelectionTime { get; set; }
    public ModelEvaluationResult? BestModel { get; set; }
    public List<ModelEvaluationResult> ModelEvaluations { get; set; } = new();
    public string SelectionCriteria { get; set; } = string.Empty;
}

/// <summary>
/// 模型选择配置
/// </summary>
public class ModelSelectionConfig
{
    public string PrimaryMetric { get; set; } = "accuracy";
    public double MinAccuracy { get; set; } = 0.8;
    public double MaxInferenceTime { get; set; } = 100; // ms
    public long MaxModelSize { get; set; } = 100 * 1024 * 1024; // 100MB
    public List<string> RequiredMetrics { get; set; } = new();
}

/// <summary>
/// 持续学习结果
/// </summary>
public class ContinualLearningResult
{
    public ContinualLearningConfig Config { get; set; } = new();
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public bool Success { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;

    public ModelInfo BaseModelInfo { get; set; } = new();
    public List<PixelData> SelectedSamples { get; set; } = new();
    public List<PseudoLabel> PseudoLabels { get; set; } = new();
    public IncrementalTrainingResult IncrementalTrainingResult { get; set; } = new();
    public ForgettingAnalysis ForgettingAnalysis { get; set; } = new();
    public object? EnsembleModel { get; set; }
}

/// <summary>
/// 持续学习配置
/// </summary>
public class ContinualLearningConfig
{
    public string BaseModelPath { get; set; } = string.Empty;
    public List<PixelData> UnlabeledData { get; set; } = new();
    public List<PixelData> ValidationData { get; set; } = new();

    public bool UseActiveLearning { get; set; } = true;
    public ActiveLearningConfig ActiveLearningConfig { get; set; } = new();

    public bool UsePseudoLabeling { get; set; } = true;
    public PseudoLabelingConfig PseudoLabelingConfig { get; set; } = new();

    public bool UseModelEnsemble { get; set; } = true;
    public double ForgettingThreshold { get; set; } = 0.1;
}

/// <summary>
/// 持续学习进度
/// </summary>
public class ContinualLearningProgress
{
    public string Stage { get; set; } = string.Empty;
    public double Progress { get; set; }
    public string Message { get; set; } = string.Empty;
    public int ProcessedSamples { get; set; }
    public int TotalSamples { get; set; }
}

/// <summary>
/// 模型信息
/// </summary>
public class ModelInfo
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Architecture { get; set; } = string.Empty;
    public long ParameterCount { get; set; }
    public long ModelSize { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 增量训练结果
/// </summary>
public class IncrementalTrainingResult
{
    public object UpdatedModel { get; set; } = new();
    public double PerformanceImprovement { get; set; }
    public TimeSpan TrainingTime { get; set; }
    public Dictionary<string, double> Metrics { get; set; } = new();
}

/// <summary>
/// 遗忘分析
/// </summary>
public class ForgettingAnalysis
{
    public double ForgettingScore { get; set; }
    public Dictionary<string, double> TaskSpecificForgetting { get; set; } = new();
    public List<string> AffectedClasses { get; set; } = new();
    public string MitigationStrategy { get; set; } = string.Empty;
}

/// <summary>
/// 训练结果
/// </summary>
public class TrainingResult
{
    public bool Success { get; set; }
    public string ModelPath { get; set; } = string.Empty;
    public double FinalLoss { get; set; }
    public double FinalAccuracy { get; set; }
    public int TotalEpochs { get; set; }
    public TimeSpan TrainingTime { get; set; }
    public Dictionary<string, double> Metrics { get; set; } = new();
    public string ErrorMessage { get; set; } = string.Empty;
}

#endregion

#region nnUNet训练模型

/// <summary>
/// nnUNet训练配置
/// </summary>
public class NnUNetTrainingConfig
{
    /// <summary>
    /// 数据集路径
    /// </summary>
    public string DatasetPath { get; set; } = string.Empty;

    /// <summary>
    /// 数据集ID (nnUNet格式)
    /// </summary>
    public int DatasetId { get; set; } = 1;

    /// <summary>
    /// 数据集名称
    /// </summary>
    public string DatasetName { get; set; } = "Dataset001_Medical";

    /// <summary>
    /// 任务类型
    /// </summary>
    public NnUNetTaskType TaskType { get; set; } = NnUNetTaskType.Segmentation;

    /// <summary>
    /// 网络架构
    /// </summary>
    public NnUNetArchitecture Architecture { get; set; } = NnUNetArchitecture.ThreeD_FullRes;

    /// <summary>
    /// 训练器类型
    /// </summary>
    public string TrainerType { get; set; } = "nnUNetTrainer";

    /// <summary>
    /// 折数 (交叉验证)
    /// </summary>
    public int Fold { get; set; } = 0;

    /// <summary>
    /// 最大训练轮数
    /// </summary>
    public int MaxEpochs { get; set; } = 1000;

    /// <summary>
    /// 批次大小
    /// </summary>
    public int BatchSize { get; set; } = 2;

    /// <summary>
    /// 学习率
    /// </summary>
    public double LearningRate { get; set; } = 0.01;

    /// <summary>
    /// 设备类型
    /// </summary>
    public string Device { get; set; } = "cuda";

    /// <summary>
    /// 是否使用混合精度训练
    /// </summary>
    public bool UseMixedPrecision { get; set; } = true;

    /// <summary>
    /// 是否启用数据增强
    /// </summary>
    public bool EnableDataAugmentation { get; set; } = true;

    /// <summary>
    /// 是否使用深度监督
    /// </summary>
    public bool UseDeepSupervision { get; set; } = true;

    /// <summary>
    /// 输出目录
    /// </summary>
    public string OutputDirectory { get; set; } = "./nnunet_results";

    /// <summary>
    /// 实验名称
    /// </summary>
    public string ExperimentName { get; set; } = "nnunet_experiment";

    /// <summary>
    /// 是否继续训练
    /// </summary>
    public bool ContinueTraining { get; set; } = false;

    /// <summary>
    /// 检查点路径 (用于继续训练)
    /// </summary>
    public string? CheckpointPath { get; set; }

    /// <summary>
    /// 验证频率 (每N个epoch)
    /// </summary>
    public int ValidationFrequency { get; set; } = 50;

    /// <summary>
    /// 是否保存最终检查点
    /// </summary>
    public bool SaveFinalCheckpoint { get; set; } = true;

    /// <summary>
    /// 是否禁用进度条
    /// </summary>
    public bool DisableProgressBar { get; set; } = false;

    /// <summary>
    /// 环境变量配置
    /// </summary>
    public Dictionary<string, string> EnvironmentVariables { get; set; } = new();

    /// <summary>
    /// 自定义参数
    /// </summary>
    public Dictionary<string, object> CustomParameters { get; set; } = new();
}

/// <summary>
/// nnUNet数据集配置
/// </summary>
public class NnUNetDatasetConfig
{
    /// <summary>
    /// 数据集ID
    /// </summary>
    public int DatasetId { get; set; } = 1;

    /// <summary>
    /// 数据集名称
    /// </summary>
    public string DatasetName { get; set; } = "Dataset001_Medical";

    /// <summary>
    /// 数据集描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 参考文献
    /// </summary>
    public string Reference { get; set; } = string.Empty;

    /// <summary>
    /// 许可证
    /// </summary>
    public string License { get; set; } = string.Empty;

    /// <summary>
    /// 发布日期
    /// </summary>
    public string Release { get; set; } = string.Empty;

    /// <summary>
    /// 张量图像大小
    /// </summary>
    public string TensorImageSize { get; set; } = "4D";

    /// <summary>
    /// 模态信息
    /// </summary>
    public Dictionary<string, string> Modality { get; set; } = new();

    /// <summary>
    /// 标签信息
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new();

    /// <summary>
    /// 训练数据文件列表
    /// </summary>
    public List<NnUNetDataFile> TrainingData { get; set; } = new();

    /// <summary>
    /// 测试数据文件列表
    /// </summary>
    public List<NnUNetDataFile> TestData { get; set; } = new();

    /// <summary>
    /// 文件结尾
    /// </summary>
    public string FileEnding { get; set; } = ".nii.gz";

    /// <summary>
    /// 数据集路径
    /// </summary>
    public string DatasetPath { get; set; } = string.Empty;
}

/// <summary>
/// nnUNet数据文件
/// </summary>
public class NnUNetDataFile
{
    /// <summary>
    /// 图像文件路径
    /// </summary>
    public string Image { get; set; } = string.Empty;

    /// <summary>
    /// 标签文件路径 (可选，用于训练数据)
    /// </summary>
    public string? Label { get; set; }

    /// <summary>
    /// 文件ID
    /// </summary>
    public string FileId { get; set; } = string.Empty;
}

/// <summary>
/// nnUNet任务类型
/// </summary>
public enum NnUNetTaskType
{
    /// <summary>
    /// 分割任务
    /// </summary>
    Segmentation = 1,

    /// <summary>
    /// 分类任务
    /// </summary>
    Classification = 2,

    /// <summary>
    /// 检测任务
    /// </summary>
    Detection = 3
}

/// <summary>
/// nnUNet网络架构
/// </summary>
public enum NnUNetArchitecture
{
    /// <summary>
    /// 2D U-Net
    /// </summary>
    TwoD = 1,

    /// <summary>
    /// 3D U-Net 低分辨率
    /// </summary>
    ThreeD_LowRes = 2,

    /// <summary>
    /// 3D U-Net 全分辨率
    /// </summary>
    ThreeD_FullRes = 3,

    /// <summary>
    /// 3D U-Net 级联
    /// </summary>
    ThreeD_Cascade = 4
}

/// <summary>
/// nnUNet训练结果
/// </summary>
public class NnUNetTrainingResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 最佳模型路径
    /// </summary>
    public string BestModelPath { get; set; } = string.Empty;

    /// <summary>
    /// 最终模型路径
    /// </summary>
    public string FinalModelPath { get; set; } = string.Empty;

    /// <summary>
    /// 训练指标
    /// </summary>
    public NnUNetTrainingMetrics Metrics { get; set; } = new();

    /// <summary>
    /// 训练耗时
    /// </summary>
    public TimeSpan TrainingTime { get; set; }

    /// <summary>
    /// 输出目录
    /// </summary>
    public string OutputDirectory { get; set; } = string.Empty;

    /// <summary>
    /// 日志文件路径
    /// </summary>
    public string LogFilePath { get; set; } = string.Empty;

    /// <summary>
    /// 验证结果
    /// </summary>
    public NnUNetValidationResult? ValidationResult { get; set; }
}

/// <summary>
/// nnUNet训练指标
/// </summary>
public class NnUNetTrainingMetrics
{
    /// <summary>
    /// 最佳Dice系数
    /// </summary>
    public double BestDiceScore { get; set; }

    /// <summary>
    /// 最佳IoU
    /// </summary>
    public double BestIoU { get; set; }

    /// <summary>
    /// 最终训练损失
    /// </summary>
    public double FinalTrainingLoss { get; set; }

    /// <summary>
    /// 最终验证损失
    /// </summary>
    public double FinalValidationLoss { get; set; }

    /// <summary>
    /// 收敛轮数
    /// </summary>
    public int ConvergedEpoch { get; set; }

    /// <summary>
    /// 每个类别的Dice系数
    /// </summary>
    public Dictionary<string, double> ClassDiceScores { get; set; } = new();

    /// <summary>
    /// 每个类别的IoU
    /// </summary>
    public Dictionary<string, double> ClassIoUScores { get; set; } = new();

    /// <summary>
    /// 训练历史
    /// </summary>
    public List<NnUNetEpochMetrics> TrainingHistory { get; set; } = new();
}

/// <summary>
/// nnUNet轮次指标
/// </summary>
public class NnUNetEpochMetrics
{
    /// <summary>
    /// 轮次
    /// </summary>
    public int Epoch { get; set; }

    /// <summary>
    /// 训练损失
    /// </summary>
    public double TrainingLoss { get; set; }

    /// <summary>
    /// 验证损失
    /// </summary>
    public double ValidationLoss { get; set; }

    /// <summary>
    /// Dice系数
    /// </summary>
    public double DiceScore { get; set; }

    /// <summary>
    /// IoU
    /// </summary>
    public double IoU { get; set; }

    /// <summary>
    /// 学习率
    /// </summary>
    public double LearningRate { get; set; }

    /// <summary>
    /// 轮次耗时
    /// </summary>
    public TimeSpan EpochTime { get; set; }
}

/// <summary>
/// nnUNet验证结果
/// </summary>
public class NnUNetValidationResult
{
    /// <summary>
    /// 总体Dice系数
    /// </summary>
    public double OverallDiceScore { get; set; }

    /// <summary>
    /// 总体IoU
    /// </summary>
    public double OverallIoU { get; set; }

    /// <summary>
    /// 每个类别的指标
    /// </summary>
    public Dictionary<string, NnUNetClassMetrics> ClassMetrics { get; set; } = new();

    /// <summary>
    /// 验证时间
    /// </summary>
    public TimeSpan ValidationTime { get; set; }

    /// <summary>
    /// 验证报告路径
    /// </summary>
    public string ReportPath { get; set; } = string.Empty;
}

/// <summary>
/// nnUNet类别指标
/// </summary>
public class NnUNetClassMetrics
{
    /// <summary>
    /// Dice系数
    /// </summary>
    public double DiceScore { get; set; }

    /// <summary>
    /// IoU
    /// </summary>
    public double IoU { get; set; }

    /// <summary>
    /// 精确度
    /// </summary>
    public double Precision { get; set; }

    /// <summary>
    /// 召回率
    /// </summary>
    public double Recall { get; set; }

    /// <summary>
    /// Hausdorff距离
    /// </summary>
    public double HausdorffDistance { get; set; }

    /// <summary>
    /// 平均表面距离
    /// </summary>
    public double AverageSurfaceDistance { get; set; }
}

/// <summary>
/// nnUNet训练进度
/// </summary>
public class NnUNetTrainingProgress
{
    /// <summary>
    /// 当前轮数
    /// </summary>
    public int CurrentEpoch { get; set; }

    /// <summary>
    /// 总轮数
    /// </summary>
    public int TotalEpochs { get; set; }

    /// <summary>
    /// 当前阶段
    /// </summary>
    public string CurrentStage { get; set; } = string.Empty;

    /// <summary>
    /// 训练损失
    /// </summary>
    public double TrainingLoss { get; set; }

    /// <summary>
    /// 验证损失
    /// </summary>
    public double ValidationLoss { get; set; }

    /// <summary>
    /// Dice系数
    /// </summary>
    public double DiceScore { get; set; }

    /// <summary>
    /// 学习率
    /// </summary>
    public double LearningRate { get; set; }

    /// <summary>
    /// 估计剩余时间
    /// </summary>
    public TimeSpan EstimatedTimeRemaining { get; set; }

    /// <summary>
    /// 进度百分比
    /// </summary>
    public double ProgressPercentage => TotalEpochs > 0 ? (double)CurrentEpoch / TotalEpochs * 100 : 0;

    /// <summary>
    /// 状态消息
    /// </summary>
    public string StatusMessage { get; set; } = string.Empty;
}

/// <summary>
/// nnUNet推理配置
/// </summary>
public class NnUNetInferenceConfig
{
    /// <summary>
    /// 模型路径
    /// </summary>
    public string ModelPath { get; set; } = string.Empty;

    /// <summary>
    /// 输入图像路径
    /// </summary>
    public string InputPath { get; set; } = string.Empty;

    /// <summary>
    /// 输出路径
    /// </summary>
    public string OutputPath { get; set; } = string.Empty;

    /// <summary>
    /// 是否保存概率图
    /// </summary>
    public bool SaveProbabilities { get; set; } = false;

    /// <summary>
    /// 是否使用测试时增强
    /// </summary>
    public bool UseTestTimeAugmentation { get; set; } = true;

    /// <summary>
    /// 设备类型
    /// </summary>
    public string Device { get; set; } = "cuda";

    /// <summary>
    /// 是否使用混合精度
    /// </summary>
    public bool UseMixedPrecision { get; set; } = true;

    /// <summary>
    /// 步长因子
    /// </summary>
    public double StepSize { get; set; } = 0.5;

    /// <summary>
    /// 是否禁用进度条
    /// </summary>
    public bool DisableProgressBar { get; set; } = false;
}

/// <summary>
/// nnUNet数据预处理配置
/// </summary>
public class NnUNetPreprocessingConfig
{
    /// <summary>
    /// 源数据路径
    /// </summary>
    public string SourceDataPath { get; set; } = string.Empty;

    /// <summary>
    /// 目标数据路径
    /// </summary>
    public string TargetDataPath { get; set; } = string.Empty;

    /// <summary>
    /// 数据集ID
    /// </summary>
    public int DatasetId { get; set; } = 1;

    /// <summary>
    /// 是否验证数据集完整性
    /// </summary>
    public bool VerifyDatasetIntegrity { get; set; } = true;

    /// <summary>
    /// 线程数
    /// </summary>
    public int NumThreads { get; set; } = 8;

    /// <summary>
    /// 是否覆盖现有文件
    /// </summary>
    public bool OverwriteExisting { get; set; } = false;

    /// <summary>
    /// 是否详细输出
    /// </summary>
    public bool Verbose { get; set; } = true;
}

/// <summary>
/// nnUNet数据集分析结果
/// </summary>
public class NnUNetDatasetAnalysisResult
{
    /// <summary>
    /// 数据集路径
    /// </summary>
    public string DatasetPath { get; set; } = string.Empty;

    /// <summary>
    /// 实际数据集路径
    /// </summary>
    public string ActualDatasetPath { get; set; } = string.Empty;

    /// <summary>
    /// 数据集ID
    /// </summary>
    public int DatasetId { get; set; }

    /// <summary>
    /// 分析时间
    /// </summary>
    public DateTime AnalysisTime { get; set; }

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 训练图像数量
    /// </summary>
    public int TrainingImageCount { get; set; }

    /// <summary>
    /// 训练标签数量
    /// </summary>
    public int TrainingLabelCount { get; set; }

    /// <summary>
    /// 测试图像数量
    /// </summary>
    public int TestImageCount { get; set; }

    /// <summary>
    /// 图像格式列表
    /// </summary>
    public List<string> ImageFormats { get; set; } = new();

    /// <summary>
    /// 数据集配置
    /// </summary>
    public Dictionary<string, object>? DatasetConfig { get; set; }

    /// <summary>
    /// 问题列表
    /// </summary>
    public List<string> Issues { get; set; } = new();

    /// <summary>
    /// 建议列表
    /// </summary>
    public List<string> Recommendations { get; set; } = new();
}

#endregion
