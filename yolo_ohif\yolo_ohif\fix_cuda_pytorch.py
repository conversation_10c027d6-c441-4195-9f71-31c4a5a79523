#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CUDA PyTorch 环境诊断和修复脚本
解决 RTX 3090 + CUDA 12.7 环境下 PyTorch 无法检测到 GPU 的问题
"""

import subprocess
import sys
import os

def run_command(cmd):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8')
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return -1, "", str(e)

def check_current_environment():
    """检查当前环境状态"""
    print("=" * 60)
    print("🔍 当前环境诊断")
    print("=" * 60)
    
    # 检查 Python 版本
    print(f"Python 版本: {sys.version}")
    
    # 检查 NVIDIA 驱动
    code, stdout, stderr = run_command("nvidia-smi")
    if code == 0:
        lines = stdout.split('\n')
        for line in lines:
            if 'CUDA Version' in line:
                print(f"NVIDIA 驱动: ✅ 正常")
                print(f"CUDA 版本: {line.split('CUDA Version:')[1].strip().split()[0]}")
                break
    else:
        print("NVIDIA 驱动: ❌ 未检测到")
        return False
    
    # 检查当前 PyTorch
    try:
        import torch
        print(f"PyTorch 版本: {torch.__version__}")
        print(f"CUDA 可用: {'✅' if torch.cuda.is_available() else '❌'}")
        if torch.cuda.is_available():
            print(f"GPU 设备数: {torch.cuda.device_count()}")
            print(f"当前 GPU: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  PyTorch 无法检测到 CUDA 设备")
            print(f"PyTorch CUDA 版本: {torch.version.cuda if hasattr(torch.version, 'cuda') else '未知'}")
    except ImportError:
        print("PyTorch: ❌ 未安装")
    
    return True

def get_recommended_pytorch():
    """获取推荐的 PyTorch 版本"""
    print("\n" + "=" * 60)
    print("💡 推荐的 PyTorch 安装方案")
    print("=" * 60)
    
    # 对于 CUDA 12.7，推荐使用 PyTorch 2.1+ 的 CUDA 12.1 版本
    recommendations = [
        {
            "name": "PyTorch 2.1.2 + CUDA 12.1 (推荐)",
            "pip_cmd": "pip install torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 --index-url https://download.pytorch.org/whl/cu121",
            "conda_cmd": "conda install pytorch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 pytorch-cuda=12.1 -c pytorch -c nvidia"
        },
        {
            "name": "PyTorch 2.0.1 + CUDA 11.8 (稳定)",
            "pip_cmd": "pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cu118",
            "conda_cmd": "conda install pytorch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 pytorch-cuda=11.8 -c pytorch -c nvidia"
        },
        {
            "name": "最新版本 PyTorch + CUDA 12.1",
            "pip_cmd": "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121",
            "conda_cmd": "conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia"
        }
    ]
    
    for i, rec in enumerate(recommendations, 1):
        print(f"\n方案 {i}: {rec['name']}")
        print(f"  Pip 安装: {rec['pip_cmd']}")
        print(f"  Conda 安装: {rec['conda_cmd']}")
    
    return recommendations

def uninstall_current_pytorch():
    """卸载当前的 PyTorch"""
    print("\n" + "=" * 60)
    print("🗑️  卸载当前 PyTorch")
    print("=" * 60)
    
    packages_to_remove = [
        "torch", "torchvision", "torchaudio", "torchtext", "torchdata",
        "pytorch", "pytorch-cuda", "pytorch-mutex"
    ]
    
    for package in packages_to_remove:
        print(f"尝试卸载 {package}...")
        code, stdout, stderr = run_command(f"pip uninstall {package} -y")
        if code == 0:
            print(f"  ✅ {package} 已卸载")
        else:
            print(f"  ⚠️  {package} 未找到或卸载失败")

def install_pytorch_cuda(method="pip", version_index=0):
    """安装支持 CUDA 的 PyTorch"""
    recommendations = get_recommended_pytorch()
    
    if version_index >= len(recommendations):
        version_index = 0
    
    selected = recommendations[version_index]
    
    print(f"\n" + "=" * 60)
    print(f"📦 安装 {selected['name']}")
    print("=" * 60)
    
    if method.lower() == "conda":
        cmd = selected['conda_cmd']
    else:
        cmd = selected['pip_cmd']
    
    print(f"执行命令: {cmd}")
    print("安装中，请稍候...")
    
    code, stdout, stderr = run_command(cmd)
    
    if code == 0:
        print("✅ PyTorch 安装成功！")
        return True
    else:
        print("❌ PyTorch 安装失败")
        print(f"错误信息: {stderr}")
        return False

def verify_installation():
    """验证安装结果"""
    print("\n" + "=" * 60)
    print("🔍 验证安装结果")
    print("=" * 60)
    
    try:
        import torch
        print(f"✅ PyTorch 版本: {torch.__version__}")
        print(f"✅ CUDA 可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"✅ GPU 设备数: {torch.cuda.device_count()}")
            print(f"✅ 当前 GPU: {torch.cuda.get_device_name(0)}")
            print(f"✅ CUDA 版本: {torch.version.cuda}")
            
            # 测试简单的 CUDA 操作
            x = torch.randn(3, 3).cuda()
            y = torch.randn(3, 3).cuda()
            z = x + y
            print(f"✅ CUDA 计算测试: 通过 (结果形状: {z.shape})")
            
            return True
        else:
            print("❌ CUDA 仍然不可用")
            return False
            
    except ImportError as e:
        print(f"❌ PyTorch 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

def create_test_script():
    """创建 YOLO 训练测试脚本"""
    test_script = '''
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO CUDA 环境测试脚本
"""

import torch
from ultralytics import YOLO

def test_yolo_cuda():
    print("=" * 50)
    print("🧪 YOLO CUDA 环境测试")
    print("=" * 50)
    
    # 基础 CUDA 测试
    print(f"PyTorch 版本: {torch.__version__}")
    print(f"CUDA 可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"GPU 设备: {torch.cuda.get_device_name(0)}")
        print(f"GPU 内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        
        # 测试 YOLO 模型加载
        try:
            model = YOLO('yolo11n.pt')  # 使用轻量级模型测试
            model.to('cuda')
            print("✅ YOLO 模型成功加载到 GPU")
            
            # 测试推理
            import numpy as np
            dummy_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            results = model(dummy_image, device='cuda')
            print("✅ CUDA 推理测试通过")
            
            return True
            
        except Exception as e:
            print(f"❌ YOLO CUDA 测试失败: {e}")
            return False
    else:
        print("❌ CUDA 不可用，无法进行 GPU 测试")
        return False

if __name__ == "__main__":
    test_yolo_cuda()
'''
    
    with open("test_yolo_cuda.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("\n✅ 已创建 YOLO CUDA 测试脚本: test_yolo_cuda.py")

def main():
    """主函数"""
    print("🚀 CUDA PyTorch 环境修复工具")
    print("适用于 RTX 3090 + CUDA 12.7 环境")
    
    # 检查当前环境
    if not check_current_environment():
        print("❌ 环境检查失败，请确保 NVIDIA 驱动正常安装")
        return
    
    # 显示推荐方案
    recommendations = get_recommended_pytorch()
    
    print("\n" + "=" * 60)
    print("🔧 修复选项")
    print("=" * 60)
    print("1. 自动修复 (推荐) - 卸载当前版本并安装 PyTorch 2.1.2 + CUDA 12.1")
    print("2. 手动选择版本")
    print("3. 仅显示安装命令")
    print("4. 创建测试脚本")
    print("5. 退出")
    
    try:
        choice = input("\n请选择操作 (1-5): ").strip()
        
        if choice == "1":
            print("\n🔄 开始自动修复...")
            uninstall_current_pytorch()
            if install_pytorch_cuda("pip", 0):
                verify_installation()
                create_test_script()
                print("\n🎉 修复完成！请重启终端并运行 'python test_yolo_cuda.py' 测试")
        
        elif choice == "2":
            print("\n请选择要安装的版本:")
            for i, rec in enumerate(recommendations):
                print(f"{i+1}. {rec['name']}")
            
            version_choice = input("选择版本 (1-3): ").strip()
            method_choice = input("选择安装方式 (pip/conda): ").strip().lower()
            
            try:
                version_index = int(version_choice) - 1
                uninstall_current_pytorch()
                if install_pytorch_cuda(method_choice, version_index):
                    verify_installation()
                    create_test_script()
            except ValueError:
                print("❌ 无效选择")
        
        elif choice == "3":
            print("\n📋 安装命令:")
            # 已在 get_recommended_pytorch() 中显示
        
        elif choice == "4":
            create_test_script()
            print("\n✅ 测试脚本已创建")
        
        elif choice == "5":
            print("👋 退出")
        
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 操作失败: {e}")

if __name__ == "__main__":
    main()