/* 
 * YOLO-OHIF医学图像疾病检测系统
 * 主样式表
 */

/* 全局样式 */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --transition-speed: 0.3s;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--dark-color);
    background-color: #f5f8fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.content-wrapper {
    flex: 1;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    letter-spacing: 0.5px;
}

.navbar-brand img {
    margin-right: 8px;
}

.nav-link {
    font-weight: 500;
    transition: color var(--transition-speed);
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

.nav-link.active {
    color: var(--primary-color) !important;
    border-bottom: 2px solid var(--primary-color);
}

/* 页脚样式 */
footer {
    background-color: var(--light-color);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 2rem 0;
    margin-top: auto;
}

.footer-links a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color var(--transition-speed);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.social-links a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--light-color);
    color: var(--secondary-color);
    margin: 0 5px;
    transition: all var(--transition-speed);
}

.social-links a:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
}

.card-header {
    font-weight: 600;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.btn {
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
    transform: translateY(-2px);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
    transition: border-color var(--transition-speed), box-shadow var(--transition-speed);
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 50rem;
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem;
}

/* 进度条样式 */
.progress {
    height: 0.5rem;
    border-radius: 50rem;
    background-color: #e9ecef;
}

.progress-bar {
    background-color: var(--primary-color);
}

/* 列表组样式 */
.list-group-item {
    border: 1px solid rgba(0, 0, 0, 0.125);
    padding: 0.75rem 1.25rem;
    transition: background-color var(--transition-speed);
}

.list-group-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.list-group-item-action:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* 工具提示和弹出框 */
.tooltip {
    font-size: 0.875rem;
}

.popover {
    box-shadow: var(--box-shadow);
    border: none;
}

/* 分页样式 */
.pagination .page-link {
    color: var(--primary-color);
    border: 1px solid #dee2e6;
    transition: all var(--transition-speed);
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
    color: #0a58ca;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 加载动画 */
.spinner-border, .spinner-grow {
    color: var(--primary-color);
}

/* 自定义动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card:hover {
        transform: none;
    }
    
    .btn:hover {
        transform: none;
    }
}

/* 首页特定样式 */
.hero-section {
    background: linear-gradient(135deg, #0d6efd 0%, #0dcaf0 100%);
    color: white;
    padding: 5rem 0;
    margin-bottom: 3rem;
    border-radius: 0 0 10px 10px;
}

.hero-title {
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-weight: 300;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.feature-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

/* 仪表板特定样式 */
.dashboard-stats {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.stat-card {
    text-align: center;
    padding: 1rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin-top: 0.5rem;
}

.study-card {
    margin-bottom: 1.5rem;
}

.study-card .card-img-top {
    height: 180px;
    object-fit: cover;
}

.study-card .badge {
    margin-right: 0.5rem;
}

/* 上传页面特定样式 */
.upload-area {
    border: 2px dashed #ced4da;
    border-radius: var(--border-radius);
    padding: 3rem;
    text-align: center;
    background-color: #f8f9fa;
    transition: all var(--transition-speed);
    cursor: pointer;
}

.upload-area:hover, .upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.upload-icon {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.upload-area:hover .upload-icon, .upload-area.dragover .upload-icon {
    color: var(--primary-color);
}

.file-list {
    max-height: 300px;
    overflow-y: auto;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #e9ecef;
}

.file-item:last-child {
    border-bottom: none;
}

.file-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
    color: var(--secondary-color);
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.file-size {
    font-size: 0.75rem;
    color: var(--secondary-color);
}

.file-actions {
    display: flex;
    align-items: center;
}

/* 检测结果页面特定样式 */
.detection-image {
    border-radius: var(--border-radius);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    max-width: 100%;
    height: auto;
}

.detection-box {
    position: absolute;
    border: 2px solid;
    border-radius: 4px;
    pointer-events: none;
}

.detection-label {
    position: absolute;
    top: -25px;
    left: 0;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    color: white;
}

.confidence-bar {
    height: 6px;
    border-radius: 3px;
    margin-top: 5px;
}

.abnormality-type {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 50rem;
    font-size: 0.75rem;
    font-weight: 600;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

/* OHIF查看器集成样式 */
.ohif-viewer-frame {
    width: 100%;
    height: 80vh;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* 登录和注册页面样式 */
.auth-container {
    max-width: 450px;
    margin: 2rem auto;
    padding: 2rem;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.auth-logo {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-logo img {
    max-width: 150px;
}

.auth-title {
    text-align: center;
    margin-bottom: 2rem;
    font-weight: 600;
}

.auth-footer {
    text-align: center;
    margin-top: 2rem;
    font-size: 0.875rem;
    color: var(--secondary-color);
}

.auth-footer a {
    color: var(--primary-color);
    text-decoration: none;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* 个人资料页面样式 */
.profile-header {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid white;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.profile-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 2rem;
    text-align: center;
}

.profile-stat-item {
    padding: 1rem;
}

.profile-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.profile-stat-label {
    font-size: 0.875rem;
    color: var(--secondary-color);
}

/* 帮助页面样式 */
.help-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.faq-item {
    margin-bottom: 1.5rem;
    border-left: 3px solid var(--primary-color);
    padding-left: 1rem;
}

.faq-item h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* 关于页面样式 */
.about-section {
    margin-bottom: 3rem;
}

.about-section h2 {
    margin-bottom: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.team-member {
    text-align: center;
    margin-bottom: 2rem;
}

.team-member img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1rem;
    border: 5px solid white;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.team-member h5 {
    margin-bottom: 0.5rem;
}

.team-member p {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

.team-member .social-links {
    margin-top: 1rem;
}

.team-member .social-links a {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
}

/* 打印样式 */
@media print {
    .navbar, .footer, .no-print {
        display: none !important;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    body {
        background-color: white;
    }
}