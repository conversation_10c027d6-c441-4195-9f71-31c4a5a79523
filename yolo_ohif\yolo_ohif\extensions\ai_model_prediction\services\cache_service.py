"""缓存服务

提供内存缓存和可选的持久化缓存功能
"""

import logging
import time
import threading
from typing import Any, Optional, Dict, Set
from dataclasses import dataclass
from datetime import datetime, timedelta

from ..core.interfaces import CacheManagerInterface
from ..core.exceptions import CacheError

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """缓存条目"""
    value: Any
    created_at: datetime
    expires_at: Optional[datetime]
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at
    
    def access(self) -> Any:
        """访问缓存条目"""
        self.access_count += 1
        self.last_accessed = datetime.now()
        return self.value


class CacheService(CacheManagerInterface):
    """缓存服务
    
    提供线程安全的内存缓存功能
    支持TTL、LRU淘汰策略和统计信息
    """
    
    def __init__(self, 
                 max_size: int = 1000,
                 default_ttl: int = 3600,
                 cleanup_interval: int = 300):
        """初始化缓存服务
        
        Args:
            max_size: 最大缓存条目数
            default_ttl: 默认TTL（秒）
            cleanup_interval: 清理间隔（秒）
        """
        self._cache: Dict[str, CacheEntry] = {}
        self._max_size = max_size
        self._default_ttl = default_ttl
        self._cleanup_interval = cleanup_interval
        self._lock = threading.RLock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'evictions': 0,
            'expired_cleanups': 0
        }
        
        # 启动清理线程
        self._cleanup_thread = threading.Thread(
            target=self._periodic_cleanup,
            daemon=True
        )
        self._cleanup_thread.start()
        
        logger.info(f"缓存服务初始化完成 (最大大小: {max_size}, 默认TTL: {default_ttl}s)")
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            entry = self._cache.get(key)
            
            if entry is None:
                self._stats['misses'] += 1
                logger.debug(f"缓存未命中: {key}")
                return None
            
            if entry.is_expired():
                del self._cache[key]
                self._stats['misses'] += 1
                self._stats['expired_cleanups'] += 1
                logger.debug(f"缓存已过期: {key}")
                return None
            
            self._stats['hits'] += 1
            logger.debug(f"缓存命中: {key}")
            return entry.access()
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        with self._lock:
            # 检查是否需要淘汰
            if len(self._cache) >= self._max_size and key not in self._cache:
                self._evict_lru()
            
            # 计算过期时间
            expires_at = None
            if ttl is not None:
                expires_at = datetime.now() + timedelta(seconds=ttl)
            elif self._default_ttl > 0:
                expires_at = datetime.now() + timedelta(seconds=self._default_ttl)
            
            # 创建缓存条目
            entry = CacheEntry(
                value=value,
                created_at=datetime.now(),
                expires_at=expires_at
            )
            
            self._cache[key] = entry
            self._stats['sets'] += 1
            
            logger.debug(f"缓存设置: {key} (TTL: {ttl or self._default_ttl}s)")
    
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                self._stats['deletes'] += 1
                logger.debug(f"缓存删除: {key}")
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            cleared_count = len(self._cache)
            self._cache.clear()
            logger.info(f"缓存已清空，删除了 {cleared_count} 个条目")
    
    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        with self._lock:
            entry = self._cache.get(key)
            if entry is None:
                return False
            
            if entry.is_expired():
                del self._cache[key]
                self._stats['expired_cleanups'] += 1
                return False
            
            return True
    
    def keys(self) -> Set[str]:
        """获取所有有效的键"""
        with self._lock:
            valid_keys = set()
            expired_keys = []
            
            for key, entry in self._cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
                else:
                    valid_keys.add(key)
            
            # 清理过期键
            for key in expired_keys:
                del self._cache[key]
                self._stats['expired_cleanups'] += 1
            
            return valid_keys
    
    def size(self) -> int:
        """获取缓存大小"""
        with self._lock:
            return len(self._cache)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = (self._stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'size': len(self._cache),
                'max_size': self._max_size,
                'hit_rate': round(hit_rate, 2),
                'total_requests': total_requests,
                **self._stats
            }
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况（简化版本）"""
        with self._lock:
            import sys
            
            total_size = 0
            for entry in self._cache.values():
                total_size += sys.getsizeof(entry.value)
            
            return {
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / 1024 / 1024, 2),
                'entries_count': len(self._cache),
                'avg_size_bytes': total_size // len(self._cache) if self._cache else 0
            }
    
    def _evict_lru(self) -> None:
        """淘汰最近最少使用的条目"""
        if not self._cache:
            return
        
        # 找到最近最少使用的条目
        lru_key = None
        lru_time = datetime.now()
        
        for key, entry in self._cache.items():
            access_time = entry.last_accessed or entry.created_at
            if access_time < lru_time:
                lru_time = access_time
                lru_key = key
        
        if lru_key:
            del self._cache[lru_key]
            self._stats['evictions'] += 1
            logger.debug(f"LRU淘汰: {lru_key}")
    
    def _periodic_cleanup(self) -> None:
        """定期清理过期条目"""
        while True:
            try:
                time.sleep(self._cleanup_interval)
                self._cleanup_expired()
            except Exception as e:
                logger.error(f"定期清理失败: {e}")
    
    def _cleanup_expired(self) -> None:
        """清理过期条目"""
        with self._lock:
            expired_keys = []
            
            for key, entry in self._cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
                self._stats['expired_cleanups'] += 1
            
            if expired_keys:
                logger.debug(f"清理了 {len(expired_keys)} 个过期条目")
    
    def cleanup(self) -> None:
        """清理缓存服务"""
        self.clear()
        logger.info("缓存服务已清理")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()


class NullCacheService(CacheManagerInterface):
    """空缓存服务（用于禁用缓存）"""
    
    def get(self, key: str) -> Optional[Any]:
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        pass
    
    def delete(self, key: str) -> bool:
        return False
    
    def clear(self) -> None:
        pass
    
    def cleanup(self) -> None:
        pass


class PersistentCacheService(CacheService):
    """持久化缓存服务（扩展功能）"""
    
    def __init__(self, 
                 cache_file: str,
                 **kwargs):
        """初始化持久化缓存服务
        
        Args:
            cache_file: 缓存文件路径
            **kwargs: 其他参数传递给父类
        """
        super().__init__(**kwargs)
        self._cache_file = cache_file
        self._load_from_file()
        
        logger.info(f"持久化缓存服务初始化完成，缓存文件: {cache_file}")
    
    def _load_from_file(self) -> None:
        """从文件加载缓存"""
        try:
            import pickle
            import os
            
            if os.path.exists(self._cache_file):
                with open(self._cache_file, 'rb') as f:
                    self._cache = pickle.load(f)
                logger.info(f"从文件加载了 {len(self._cache)} 个缓存条目")
        except Exception as e:
            logger.warning(f"加载缓存文件失败: {e}")
    
    def _save_to_file(self) -> None:
        """保存缓存到文件"""
        try:
            import pickle
            import os
            
            os.makedirs(os.path.dirname(self._cache_file), exist_ok=True)
            with open(self._cache_file, 'wb') as f:
                pickle.dump(self._cache, f)
            logger.debug(f"缓存已保存到文件: {self._cache_file}")
        except Exception as e:
            logger.error(f"保存缓存文件失败: {e}")
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值并保存到文件"""
        super().set(key, value, ttl)
        self._save_to_file()
    
    def delete(self, key: str) -> bool:
        """删除缓存值并保存到文件"""
        result = super().delete(key)
        if result:
            self._save_to_file()
        return result
    
    def clear(self) -> None:
        """清空缓存并删除文件"""
        super().clear()
        try:
            import os
            if os.path.exists(self._cache_file):
                os.remove(self._cache_file)
        except Exception as e:
            logger.error(f"删除缓存文件失败: {e}")
    
    def cleanup(self) -> None:
        """清理并保存缓存"""
        self._save_to_file()
        super().cleanup()