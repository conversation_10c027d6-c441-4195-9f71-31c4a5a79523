"""验证服务

提供数据验证和输入检查功能
"""

import logging
import re
from typing import Any, Dict, List, Optional, Union, Callable, Tuple
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

from ..core.exceptions import ValidationError
from ..core.types import ModelConfig, BoundingBox, DetectionResult

logger = logging.getLogger(__name__)


@dataclass
class ValidationRule:
    """验证规则"""
    name: str
    validator: Callable[[Any], bool]
    error_message: str
    required: bool = True


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    field_errors: Dict[str, List[str]]


class ValidationService:
    """验证服务
    
    提供各种数据验证功能
    """
    
    def __init__(self):
        """初始化验证服务"""
        self._rules: Dict[str, List[ValidationRule]] = {}
        self._setup_default_rules()
        
        logger.info("验证服务初始化完成")
    
    def validate_image_data(self, image_data: Any) -> ValidationResult:
        """验证图像数据
        
        Args:
            image_data: 图像数据
            
        Returns:
            验证结果
        """
        errors = []
        warnings = []
        field_errors = {}
        
        try:
            # 检查图像数据是否为空
            if image_data is None:
                errors.append("图像数据不能为空")
                return ValidationResult(False, errors, warnings, field_errors)
            
            # 检查图像数据类型
            if hasattr(image_data, 'shape'):
                # NumPy数组
                shape = image_data.shape
                if len(shape) < 2:
                    errors.append("图像数据维度不足，至少需要2维")
                elif len(shape) > 4:
                    errors.append("图像数据维度过多，最多支持4维")
                
                # 检查图像尺寸
                if len(shape) >= 2:
                    height, width = shape[:2]
                    if height < 32 or width < 32:
                        warnings.append("图像尺寸过小，可能影响检测效果")
                    elif height > 4096 or width > 4096:
                        warnings.append("图像尺寸过大，可能影响处理速度")
                
                # 检查数据类型
                if hasattr(image_data, 'dtype'):
                    dtype = str(image_data.dtype)
                    if 'int' not in dtype and 'float' not in dtype:
                        errors.append(f"不支持的图像数据类型: {dtype}")
            
            elif isinstance(image_data, (bytes, bytearray)):
                # 二进制数据
                if len(image_data) == 0:
                    errors.append("图像二进制数据为空")
                elif len(image_data) > 50 * 1024 * 1024:  # 50MB
                    warnings.append("图像文件过大，可能影响处理速度")
            
            elif isinstance(image_data, str):
                # 文件路径或Base64
                if image_data.startswith('data:image/'):
                    # Base64图像
                    if len(image_data) < 100:
                        errors.append("Base64图像数据过短")
                elif Path(image_data).exists():
                    # 文件路径
                    file_path = Path(image_data)
                    if file_path.stat().st_size == 0:
                        errors.append("图像文件为空")
                    elif file_path.suffix.lower() not in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.dcm']:
                        warnings.append(f"可能不支持的图像格式: {file_path.suffix}")
                else:
                    errors.append("无效的图像路径或Base64数据")
            
            else:
                errors.append(f"不支持的图像数据类型: {type(image_data)}")
            
        except Exception as e:
            errors.append(f"图像数据验证失败: {str(e)}")
        
        is_valid = len(errors) == 0
        return ValidationResult(is_valid, errors, warnings, field_errors)
    
    def validate_model_config(self, config: Dict[str, Any]) -> ValidationResult:
        """验证模型配置
        
        Args:
            config: 模型配置
            
        Returns:
            验证结果
        """
        errors = []
        warnings = []
        field_errors = {}
        
        try:
            # 必需字段检查
            required_fields = ['id', 'name', 'api_endpoint', 'supported_modalities']
            for field in required_fields:
                if field not in config:
                    field_errors.setdefault(field, []).append(f"缺少必需字段: {field}")
                elif not config[field]:
                    field_errors.setdefault(field, []).append(f"字段不能为空: {field}")
            
            # ID验证
            if 'id' in config:
                model_id = config['id']
                if not isinstance(model_id, str):
                    field_errors.setdefault('id', []).append("模型ID必须是字符串")
                elif not re.match(r'^[a-zA-Z0-9_-]+$', model_id):
                    field_errors.setdefault('id', []).append("模型ID只能包含字母、数字、下划线和连字符")
                elif len(model_id) > 50:
                    field_errors.setdefault('id', []).append("模型ID长度不能超过50个字符")
            
            # 名称验证
            if 'name' in config:
                name = config['name']
                if not isinstance(name, str):
                    field_errors.setdefault('name', []).append("模型名称必须是字符串")
                elif len(name) > 100:
                    field_errors.setdefault('name', []).append("模型名称长度不能超过100个字符")
            
            # API端点验证
            if 'api_endpoint' in config:
                endpoint = config['api_endpoint']
                if not isinstance(endpoint, str):
                    field_errors.setdefault('api_endpoint', []).append("API端点必须是字符串")
                elif not self._is_valid_url(endpoint):
                    field_errors.setdefault('api_endpoint', []).append("API端点必须是有效的HTTP/HTTPS URL")
            
            # 支持的模态验证
            if 'supported_modalities' in config:
                modalities = config['supported_modalities']
                if not isinstance(modalities, list):
                    field_errors.setdefault('supported_modalities', []).append("支持的模态必须是列表")
                elif not modalities:
                    field_errors.setdefault('supported_modalities', []).append("至少需要支持一种模态")
                else:
                    valid_modalities = ['CR', 'DX', 'CT', 'MR', 'US', 'XA', 'RF', 'MG', 'PT', 'NM']
                    for modality in modalities:
                        if modality not in valid_modalities:
                            warnings.append(f"未知的模态类型: {modality}")
            
            # 阈值验证
            for threshold_field in ['confidence_threshold', 'iou_threshold']:
                if threshold_field in config:
                    threshold = config[threshold_field]
                    if not isinstance(threshold, (int, float)):
                        field_errors.setdefault(threshold_field, []).append(f"{threshold_field}必须是数值")
                    elif not 0 <= threshold <= 1:
                        field_errors.setdefault(threshold_field, []).append(f"{threshold_field}必须在0-1之间")
            
            # 输入尺寸验证
            if 'input_size' in config:
                input_size = config['input_size']
                if not isinstance(input_size, list):
                    field_errors.setdefault('input_size', []).append("输入尺寸必须是列表")
                elif len(input_size) != 2:
                    field_errors.setdefault('input_size', []).append("输入尺寸必须包含宽度和高度")
                else:
                    width, height = input_size
                    if not isinstance(width, int) or not isinstance(height, int):
                        field_errors.setdefault('input_size', []).append("输入尺寸必须是整数")
                    elif width < 32 or height < 32:
                        warnings.append("输入尺寸过小，可能影响检测效果")
                    elif width > 2048 or height > 2048:
                        warnings.append("输入尺寸过大，可能影响处理速度")
            
            # 类别名称验证
            if 'class_names' in config:
                class_names = config['class_names']
                if not isinstance(class_names, list):
                    field_errors.setdefault('class_names', []).append("类别名称必须是列表")
                else:
                    for i, class_name in enumerate(class_names):
                        if not isinstance(class_name, str):
                            field_errors.setdefault('class_names', []).append(f"类别名称[{i}]必须是字符串")
                        elif not class_name.strip():
                            field_errors.setdefault('class_names', []).append(f"类别名称[{i}]不能为空")
            
            # 收集所有字段错误到总错误列表
            for field, field_error_list in field_errors.items():
                errors.extend(field_error_list)
            
        except Exception as e:
            errors.append(f"模型配置验证失败: {str(e)}")
        
        is_valid = len(errors) == 0
        return ValidationResult(is_valid, errors, warnings, field_errors)
    
    def validate_prediction_result(self, result: Dict[str, Any]) -> ValidationResult:
        """验证预测结果
        
        Args:
            result: 预测结果
            
        Returns:
            验证结果
        """
        errors = []
        warnings = []
        field_errors = {}
        
        try:
            # 检查必需字段
            required_fields = ['detections', 'confidence_scores', 'processing_time']
            for field in required_fields:
                if field not in result:
                    field_errors.setdefault(field, []).append(f"缺少必需字段: {field}")
            
            # 验证检测结果
            if 'detections' in result:
                detections = result['detections']
                if not isinstance(detections, list):
                    field_errors.setdefault('detections', []).append("检测结果必须是列表")
                else:
                    for i, detection in enumerate(detections):
                        detection_errors = self._validate_detection(detection)
                        if detection_errors:
                            field_errors.setdefault('detections', []).extend(
                                [f"检测[{i}]: {error}" for error in detection_errors]
                            )
            
            # 验证置信度分数
            if 'confidence_scores' in result:
                scores = result['confidence_scores']
                if not isinstance(scores, list):
                    field_errors.setdefault('confidence_scores', []).append("置信度分数必须是列表")
                else:
                    for i, score in enumerate(scores):
                        if not isinstance(score, (int, float)):
                            field_errors.setdefault('confidence_scores', []).append(
                                f"置信度分数[{i}]必须是数值"
                            )
                        elif not 0 <= score <= 1:
                            field_errors.setdefault('confidence_scores', []).append(
                                f"置信度分数[{i}]必须在0-1之间"
                            )
            
            # 验证处理时间
            if 'processing_time' in result:
                processing_time = result['processing_time']
                if not isinstance(processing_time, (int, float)):
                    field_errors.setdefault('processing_time', []).append("处理时间必须是数值")
                elif processing_time < 0:
                    field_errors.setdefault('processing_time', []).append("处理时间不能为负数")
                elif processing_time > 300:  # 5分钟
                    warnings.append("处理时间过长，可能存在性能问题")
            
            # 验证数据一致性
            if 'detections' in result and 'confidence_scores' in result:
                detections = result['detections']
                scores = result['confidence_scores']
                if isinstance(detections, list) and isinstance(scores, list):
                    if len(detections) != len(scores):
                        errors.append("检测结果数量与置信度分数数量不匹配")
            
            # 收集所有字段错误到总错误列表
            for field, field_error_list in field_errors.items():
                errors.extend(field_error_list)
            
        except Exception as e:
            errors.append(f"预测结果验证失败: {str(e)}")
        
        is_valid = len(errors) == 0
        return ValidationResult(is_valid, errors, warnings, field_errors)
    
    def validate_bounding_box(self, bbox: Dict[str, Any]) -> ValidationResult:
        """验证边界框
        
        Args:
            bbox: 边界框数据
            
        Returns:
            验证结果
        """
        errors = []
        warnings = []
        field_errors = {}
        
        try:
            # 检查必需字段
            required_fields = ['x', 'y', 'width', 'height']
            for field in required_fields:
                if field not in bbox:
                    field_errors.setdefault(field, []).append(f"缺少必需字段: {field}")
            
            # 验证坐标和尺寸
            for field in required_fields:
                if field in bbox:
                    value = bbox[field]
                    if not isinstance(value, (int, float)):
                        field_errors.setdefault(field, []).append(f"{field}必须是数值")
                    elif value < 0:
                        field_errors.setdefault(field, []).append(f"{field}不能为负数")
            
            # 验证边界框合理性
            if all(field in bbox for field in required_fields):
                x, y, width, height = bbox['x'], bbox['y'], bbox['width'], bbox['height']
                if isinstance(x, (int, float)) and isinstance(y, (int, float)) and \
                   isinstance(width, (int, float)) and isinstance(height, (int, float)):
                    
                    if width == 0 or height == 0:
                        errors.append("边界框宽度和高度不能为0")
                    elif width < 5 or height < 5:
                        warnings.append("边界框过小，可能是误检")
                    
                    # 检查坐标是否在合理范围内（假设图像最大尺寸为10000x10000）
                    if x > 10000 or y > 10000:
                        warnings.append("边界框坐标可能超出图像范围")
            
            # 验证可选字段
            if 'confidence' in bbox:
                confidence = bbox['confidence']
                if not isinstance(confidence, (int, float)):
                    field_errors.setdefault('confidence', []).append("置信度必须是数值")
                elif not 0 <= confidence <= 1:
                    field_errors.setdefault('confidence', []).append("置信度必须在0-1之间")
            
            if 'class_id' in bbox:
                class_id = bbox['class_id']
                if not isinstance(class_id, int):
                    field_errors.setdefault('class_id', []).append("类别ID必须是整数")
                elif class_id < 0:
                    field_errors.setdefault('class_id', []).append("类别ID不能为负数")
            
            # 收集所有字段错误到总错误列表
            for field, field_error_list in field_errors.items():
                errors.extend(field_error_list)
            
        except Exception as e:
            errors.append(f"边界框验证失败: {str(e)}")
        
        is_valid = len(errors) == 0
        return ValidationResult(is_valid, errors, warnings, field_errors)
    
    def validate_api_request(self, request_data: Dict[str, Any]) -> ValidationResult:
        """验证API请求数据
        
        Args:
            request_data: 请求数据
            
        Returns:
            验证结果
        """
        errors = []
        warnings = []
        field_errors = {}
        
        try:
            # 检查必需字段
            if 'image' not in request_data:
                field_errors.setdefault('image', []).append("缺少图像数据")
            else:
                # 验证图像数据
                image_validation = self.validate_image_data(request_data['image'])
                if not image_validation.is_valid:
                    field_errors.setdefault('image', []).extend(image_validation.errors)
                warnings.extend(image_validation.warnings)
            
            # 验证可选参数
            if 'confidence_threshold' in request_data:
                threshold = request_data['confidence_threshold']
                if not isinstance(threshold, (int, float)):
                    field_errors.setdefault('confidence_threshold', []).append("置信度阈值必须是数值")
                elif not 0 <= threshold <= 1:
                    field_errors.setdefault('confidence_threshold', []).append("置信度阈值必须在0-1之间")
            
            if 'iou_threshold' in request_data:
                threshold = request_data['iou_threshold']
                if not isinstance(threshold, (int, float)):
                    field_errors.setdefault('iou_threshold', []).append("IoU阈值必须是数值")
                elif not 0 <= threshold <= 1:
                    field_errors.setdefault('iou_threshold', []).append("IoU阈值必须在0-1之间")
            
            if 'max_detections' in request_data:
                max_det = request_data['max_detections']
                if not isinstance(max_det, int):
                    field_errors.setdefault('max_detections', []).append("最大检测数必须是整数")
                elif max_det <= 0:
                    field_errors.setdefault('max_detections', []).append("最大检测数必须大于0")
                elif max_det > 1000:
                    warnings.append("最大检测数过大，可能影响性能")
            
            # 收集所有字段错误到总错误列表
            for field, field_error_list in field_errors.items():
                errors.extend(field_error_list)
            
        except Exception as e:
            errors.append(f"API请求验证失败: {str(e)}")
        
        is_valid = len(errors) == 0
        return ValidationResult(is_valid, errors, warnings, field_errors)
    
    def _validate_detection(self, detection: Dict[str, Any]) -> List[str]:
        """验证单个检测结果"""
        errors = []
        
        # 检查必需字段
        required_fields = ['bbox', 'confidence', 'class_id']
        for field in required_fields:
            if field not in detection:
                errors.append(f"缺少必需字段: {field}")
        
        # 验证边界框
        if 'bbox' in detection:
            bbox_validation = self.validate_bounding_box(detection['bbox'])
            if not bbox_validation.is_valid:
                errors.extend([f"边界框: {error}" for error in bbox_validation.errors])
        
        # 验证置信度
        if 'confidence' in detection:
            confidence = detection['confidence']
            if not isinstance(confidence, (int, float)):
                errors.append("置信度必须是数值")
            elif not 0 <= confidence <= 1:
                errors.append("置信度必须在0-1之间")
        
        # 验证类别ID
        if 'class_id' in detection:
            class_id = detection['class_id']
            if not isinstance(class_id, int):
                errors.append("类别ID必须是整数")
            elif class_id < 0:
                errors.append("类别ID不能为负数")
        
        return errors
    
    def _is_valid_url(self, url: str) -> bool:
        """验证URL格式"""
        url_pattern = r'^https?://[\w\.-]+(?:\:[0-9]+)?(?:/.*)?$'
        return bool(re.match(url_pattern, url))
    
    def _setup_default_rules(self) -> None:
        """设置默认验证规则"""
        # 这里可以添加更多默认验证规则
        pass
    
    def add_custom_rule(self, 
                        entity_type: str, 
                        rule: ValidationRule) -> None:
        """添加自定义验证规则
        
        Args:
            entity_type: 实体类型
            rule: 验证规则
        """
        if entity_type not in self._rules:
            self._rules[entity_type] = []
        
        self._rules[entity_type].append(rule)
        logger.debug(f"添加自定义验证规则: {entity_type}.{rule.name}")
    
    def remove_custom_rule(self, entity_type: str, rule_name: str) -> bool:
        """移除自定义验证规则
        
        Args:
            entity_type: 实体类型
            rule_name: 规则名称
            
        Returns:
            是否成功移除
        """
        if entity_type not in self._rules:
            return False
        
        original_count = len(self._rules[entity_type])
        self._rules[entity_type] = [
            rule for rule in self._rules[entity_type]
            if rule.name != rule_name
        ]
        
        removed = len(self._rules[entity_type]) < original_count
        if removed:
            logger.debug(f"移除自定义验证规则: {entity_type}.{rule_name}")
        
        return removed
    
    def get_validation_summary(self, results: List[ValidationResult]) -> Dict[str, Any]:
        """获取验证摘要
        
        Args:
            results: 验证结果列表
            
        Returns:
            验证摘要
        """
        total_count = len(results)
        valid_count = sum(1 for result in results if result.is_valid)
        invalid_count = total_count - valid_count
        
        all_errors = []
        all_warnings = []
        
        for result in results:
            all_errors.extend(result.errors)
            all_warnings.extend(result.warnings)
        
        return {
            'total_validations': total_count,
            'valid_count': valid_count,
            'invalid_count': invalid_count,
            'success_rate': (valid_count / total_count * 100) if total_count > 0 else 0,
            'total_errors': len(all_errors),
            'total_warnings': len(all_warnings),
            'common_errors': self._get_common_items(all_errors),
            'common_warnings': self._get_common_items(all_warnings)
        }
    
    def _get_common_items(self, items: List[str], top_n: int = 5) -> List[Tuple[str, int]]:
        """获取最常见的项目"""
        from collections import Counter
        counter = Counter(items)
        return counter.most_common(top_n)