using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Models;

namespace MedicalImageAnalysis.Core.Interfaces;

/// <summary>
/// 标注服务接口，提供智能标注和标注管理功能
/// </summary>
public interface IAnnotationService
{
    /// <summary>
    /// 自动生成标注
    /// </summary>
    /// <param name="instance">DICOM 实例</param>
    /// <param name="annotationConfig">标注配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>生成的标注集合</returns>
    Task<List<Annotation>> GenerateAnnotationsAsync(DicomInstance instance, AutoAnnotationConfig annotationConfig, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量自动标注
    /// </summary>
    /// <param name="instances">DICOM 实例集合</param>
    /// <param name="annotationConfig">标注配置</param>
    /// <param name="progressCallback">进度回调</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量标注结果</returns>
    Task<BatchAnnotationResult> BatchGenerateAnnotationsAsync(IEnumerable<DicomInstance> instances, AutoAnnotationConfig annotationConfig, IProgress<AnnotationProgress>? progressCallback = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证标注质量
    /// </summary>
    /// <param name="annotations">标注集合</param>
    /// <param name="validationRules">验证规则</param>
    /// <returns>验证结果</returns>
    Task<AnnotationValidationResult> ValidateAnnotationsAsync(IEnumerable<Annotation> annotations, AnnotationValidationRules validationRules);

    /// <summary>
    /// 优化标注边界框
    /// </summary>
    /// <param name="annotation">原始标注</param>
    /// <param name="pixelData">像素数据</param>
    /// <param name="optimizationConfig">优化配置</param>
    /// <returns>优化后的标注</returns>
    Task<Annotation> OptimizeBoundingBoxAsync(Annotation annotation, PixelData pixelData, BoundingBoxOptimizationConfig optimizationConfig);

    /// <summary>
    /// 转换标注格式
    /// </summary>
    /// <param name="annotations">标注集合</param>
    /// <param name="targetFormat">目标格式</param>
    /// <param name="imageSize">图像尺寸</param>
    /// <param name="classMapping">类别映射</param>
    /// <returns>转换后的标注数据</returns>
    Task<string> ConvertAnnotationFormatAsync(IEnumerable<Annotation> annotations, AnnotationFormat targetFormat, (int Width, int Height) imageSize, Dictionary<string, int>? classMapping = null);

    /// <summary>
    /// 导出训练数据集
    /// </summary>
    /// <param name="study">研究实体</param>
    /// <param name="exportConfig">导出配置</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="progressCallback">进度回调</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>导出结果</returns>
    Task<DatasetExportResult> ExportTrainingDatasetAsync(DicomStudy study, DatasetExportConfig exportConfig, string outputPath, IProgress<ExportProgress>? progressCallback = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 生成标注统计信息
    /// </summary>
    /// <param name="annotations">标注集合</param>
    /// <returns>统计信息</returns>
    Task<AnnotationStatistics> GenerateAnnotationStatisticsAsync(IEnumerable<Annotation> annotations);

    /// <summary>
    /// 检测标注异常
    /// </summary>
    /// <param name="annotations">标注集合</param>
    /// <param name="detectionConfig">检测配置</param>
    /// <returns>异常检测结果</returns>
    Task<List<AnnotationAnomaly>> DetectAnnotationAnomaliesAsync(IEnumerable<Annotation> annotations, Entities.AnomalyDetectionConfig detectionConfig);

    /// <summary>
    /// 智能推荐标注
    /// </summary>
    /// <param name="instance">DICOM 实例</param>
    /// <param name="existingAnnotations">已有标注</param>
    /// <param name="recommendationConfig">推荐配置</param>
    /// <returns>推荐的标注</returns>
    Task<List<AnnotationRecommendation>> RecommendAnnotationsAsync(DicomInstance instance, IEnumerable<Annotation> existingAnnotations, AnnotationRecommendationConfig recommendationConfig);

    /// <summary>
    /// 合并重叠标注
    /// </summary>
    /// <param name="annotations">标注集合</param>
    /// <param name="mergeConfig">合并配置</param>
    /// <returns>合并后的标注</returns>
    Task<List<Annotation>> MergeOverlappingAnnotationsAsync(IEnumerable<Annotation> annotations, AnnotationMergeConfig mergeConfig);
}

/// <summary>
/// 自动标注配置
/// </summary>
public class AutoAnnotationConfig
{
    /// <summary>
    /// 使用的模型路径
    /// </summary>
    public string ModelPath { get; set; } = string.Empty;

    /// <summary>
    /// 置信度阈值
    /// </summary>
    public double ConfidenceThreshold { get; set; } = 0.5;

    /// <summary>
    /// IoU 阈值
    /// </summary>
    public double IouThreshold { get; set; } = 0.45;

    /// <summary>
    /// 目标类别 (为空则检测所有类别)
    /// </summary>
    public List<string> TargetClasses { get; set; } = new();

    /// <summary>
    /// 最小边界框尺寸 (像素)
    /// </summary>
    public (int Width, int Height) MinBoundingBoxSize { get; set; } = (10, 10);

    /// <summary>
    /// 最大边界框尺寸 (像素)
    /// </summary>
    public (int Width, int Height) MaxBoundingBoxSize { get; set; } = (1000, 1000);

    /// <summary>
    /// 是否启用后处理优化
    /// </summary>
    public bool EnablePostProcessing { get; set; } = true;

    /// <summary>
    /// 是否自动调整窗宽窗位
    /// </summary>
    public bool AutoAdjustWindowLevel { get; set; } = true;

    /// <summary>
    /// 预处理配置
    /// </summary>
    public ImagePreprocessingOptions? PreprocessingOptions { get; set; }
}

/// <summary>
/// 标注验证规则
/// </summary>
public class AnnotationValidationRules
{
    /// <summary>
    /// 最小置信度
    /// </summary>
    public double MinConfidence { get; set; } = 0.1;

    /// <summary>
    /// 最大置信度
    /// </summary>
    public double MaxConfidence { get; set; } = 1.0;

    /// <summary>
    /// 最小边界框面积 (归一化)
    /// </summary>
    public double MinBoundingBoxArea { get; set; } = 0.0001;

    /// <summary>
    /// 最大边界框面积 (归一化)
    /// </summary>
    public double MaxBoundingBoxArea { get; set; } = 0.9;

    /// <summary>
    /// 允许的标签集合
    /// </summary>
    public HashSet<string> AllowedLabels { get; set; } = new();

    /// <summary>
    /// 是否检查边界框越界
    /// </summary>
    public bool CheckBoundingBoxBounds { get; set; } = true;

    /// <summary>
    /// 是否检查重复标注
    /// </summary>
    public bool CheckDuplicateAnnotations { get; set; } = true;

    /// <summary>
    /// 重复检测的 IoU 阈值
    /// </summary>
    public double DuplicateIouThreshold { get; set; } = 0.8;
}

/// <summary>
/// 边界框优化配置
/// </summary>
public class BoundingBoxOptimizationConfig
{
    /// <summary>
    /// 优化方法
    /// </summary>
    public BoundingBoxOptimizationMethod Method { get; set; } = BoundingBoxOptimizationMethod.EdgeDetection;

    /// <summary>
    /// 扩展边距 (像素)
    /// </summary>
    public int ExpansionMargin { get; set; } = 5;

    /// <summary>
    /// 是否使用形态学操作
    /// </summary>
    public bool UseMorphologicalOperations { get; set; } = true;

    /// <summary>
    /// 边缘检测阈值
    /// </summary>
    public double EdgeDetectionThreshold { get; set; } = 0.1;

    /// <summary>
    /// 最大迭代次数
    /// </summary>
    public int MaxIterations { get; set; } = 10;
}

/// <summary>
/// 数据集导出配置
/// </summary>
public class DatasetExportConfig
{
    /// <summary>
    /// 导出格式
    /// </summary>
    public DatasetFormat Format { get; set; } = DatasetFormat.YOLO;

    /// <summary>
    /// 训练集比例
    /// </summary>
    public double TrainRatio { get; set; } = 0.8;

    /// <summary>
    /// 验证集比例
    /// </summary>
    public double ValidationRatio { get; set; } = 0.15;

    /// <summary>
    /// 测试集比例
    /// </summary>
    public double TestRatio { get; set; } = 0.05;

    /// <summary>
    /// 图像格式
    /// </summary>
    public ImageFormat ImageFormat { get; set; } = ImageFormat.Png;

    /// <summary>
    /// 图像质量 (1-100)
    /// </summary>
    public int ImageQuality { get; set; } = 95;

    /// <summary>
    /// 目标图像尺寸
    /// </summary>
    public (int Width, int Height)? TargetImageSize { get; set; }

    /// <summary>
    /// 是否保持宽高比
    /// </summary>
    public bool MaintainAspectRatio { get; set; } = true;

    /// <summary>
    /// 是否包含负样本 (无标注的图像)
    /// </summary>
    public bool IncludeNegativeSamples { get; set; } = false;

    /// <summary>
    /// 负样本比例
    /// </summary>
    public double NegativeSampleRatio { get; set; } = 0.1;

    /// <summary>
    /// 类别映射
    /// </summary>
    public Dictionary<string, int> ClassMapping { get; set; } = new();

    /// <summary>
    /// 是否生成数据增强
    /// </summary>
    public bool GenerateAugmentations { get; set; } = false;

    /// <summary>
    /// 数据增强配置
    /// </summary>
    public DataAugmentationConfig? AugmentationConfig { get; set; }
}

/// <summary>
/// 标注进度
/// </summary>
public class AnnotationProgress
{
    /// <summary>
    /// 当前处理的实例索引
    /// </summary>
    public int CurrentIndex { get; set; }

    /// <summary>
    /// 总实例数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 当前实例的标注数量
    /// </summary>
    public int CurrentAnnotationCount { get; set; }

    /// <summary>
    /// 总标注数量
    /// </summary>
    public int TotalAnnotationCount { get; set; }

    /// <summary>
    /// 当前处理的实例 UID
    /// </summary>
    public string CurrentInstanceUid { get; set; } = string.Empty;

    /// <summary>
    /// 进度百分比 (0-100)
    /// </summary>
    public double ProgressPercentage => TotalCount > 0 ? (double)CurrentIndex / TotalCount * 100 : 0;
}

/// <summary>
/// 批量标注结果
/// </summary>
public class BatchAnnotationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 处理的实例数量
    /// </summary>
    public int ProcessedInstanceCount { get; set; }

    /// <summary>
    /// 生成的标注数量
    /// </summary>
    public int GeneratedAnnotationCount { get; set; }

    /// <summary>
    /// 处理耗时 (秒)
    /// </summary>
    public double ProcessingTimeSeconds { get; set; }

    /// <summary>
    /// 每个实例的标注结果
    /// </summary>
    public Dictionary<string, List<Annotation>> InstanceAnnotations { get; set; } = new();

    /// <summary>
    /// 处理失败的实例
    /// </summary>
    public List<string> FailedInstances { get; set; } = new();
}

/// <summary>
/// 标注验证结果
/// </summary>
public class AnnotationValidationResult
{
    /// <summary>
    /// 是否通过验证
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 验证错误
    /// </summary>
    public List<ValidationError> Errors { get; set; } = new();

    /// <summary>
    /// 验证警告
    /// </summary>
    public List<ValidationWarning> Warnings { get; set; } = new();

    /// <summary>
    /// 有效标注数量
    /// </summary>
    public int ValidAnnotationCount { get; set; }

    /// <summary>
    /// 无效标注数量
    /// </summary>
    public int InvalidAnnotationCount { get; set; }
}

/// <summary>
/// 验证错误
/// </summary>
public class ValidationError
{
    /// <summary>
    /// 标注 ID
    /// </summary>
    public Guid AnnotationId { get; set; }

    /// <summary>
    /// 错误类型
    /// </summary>
    public ValidationErrorType Type { get; set; }

    /// <summary>
    /// 错误描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 验证警告
/// </summary>
public class ValidationWarning
{
    /// <summary>
    /// 标注 ID
    /// </summary>
    public Guid AnnotationId { get; set; }

    /// <summary>
    /// 警告类型
    /// </summary>
    public ValidationWarningType Type { get; set; }

    /// <summary>
    /// 警告描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 标注格式枚举
/// </summary>
public enum AnnotationFormat
{
    YOLO = 1,
    COCO = 2,
    PascalVOC = 3,
    CVAT = 4,
    LabelMe = 5
}

/// <summary>
/// 数据集格式枚举
/// </summary>
public enum DatasetFormat
{
    YOLO = 1,
    COCO = 2,
    PascalVOC = 3,
    ImageNet = 4,
    Custom = 5
}

/// <summary>
/// 边界框优化方法枚举
/// </summary>
public enum BoundingBoxOptimizationMethod
{
    EdgeDetection = 1,
    ContourFitting = 2,
    RegionGrowing = 3,
    ActiveContour = 4
}

/// <summary>
/// 验证错误类型枚举
/// </summary>
public enum ValidationErrorType
{
    InvalidConfidence = 1,
    InvalidBoundingBox = 2,
    InvalidLabel = 3,
    OutOfBounds = 4,
    DuplicateAnnotation = 5
}

/// <summary>
/// 验证警告类型枚举
/// </summary>
public enum ValidationWarningType
{
    LowConfidence = 1,
    SmallBoundingBox = 2,
    LargeBoundingBox = 3,
    UncommonLabel = 4
}
