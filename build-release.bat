@echo off
title Medical Image Analysis System - Build Release Package

echo.
echo ========================================
echo   Medical Image Analysis System
echo        Build Release Package
echo ========================================
echo.

:: Set variables
set OUTPUT_DIR=Release
set APP_NAME=MedicalImageAnalysis

:: Check environment
echo Checking build environment...
dotnet --version > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: .NET SDK not found. Please install .NET 8 or higher.
    pause
    exit /b 1
)

python --version > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo WARNING: Python not found. Python environment will be skipped.
    set INCLUDE_PYTHON=false
) else (
    set INCLUDE_PYTHON=true
)

echo Environment check completed.

:: Clean old release directory
echo.
echo Cleaning old release directory...
if exist %OUTPUT_DIR% (
    rmdir /s /q %OUTPUT_DIR%
)

:: Create directory structure
echo.
echo Creating directory structure...
mkdir %OUTPUT_DIR%\App
mkdir %OUTPUT_DIR%\Data\DICOM
mkdir %OUTPUT_DIR%\Data\Models
mkdir %OUTPUT_DIR%\Data\Datasets\Train\images
mkdir %OUTPUT_DIR%\Data\Datasets\Train\labels
mkdir %OUTPUT_DIR%\Data\Datasets\Val\images
mkdir %OUTPUT_DIR%\Data\Datasets\Val\labels
mkdir %OUTPUT_DIR%\Data\Datasets\Test\images
mkdir %OUTPUT_DIR%\Data\Datasets\Test\labels
mkdir %OUTPUT_DIR%\Data\Output
mkdir %OUTPUT_DIR%\Data\Temp
mkdir %OUTPUT_DIR%\Data\Logs
mkdir %OUTPUT_DIR%\Python
mkdir %OUTPUT_DIR%\Scripts
mkdir %OUTPUT_DIR%\Docs
mkdir %OUTPUT_DIR%\Examples
mkdir %OUTPUT_DIR%\Assets

echo Directory structure created.

:: Publish .NET application
echo.
echo Publishing .NET application...
dotnet publish src\MedicalImageAnalysis.Wpf\MedicalImageAnalysis.Wpf.csproj ^
    --configuration Release ^
    --runtime win-x64 ^
    --self-contained true ^
    --output %OUTPUT_DIR%\App ^
    --verbosity minimal

if %ERRORLEVEL% neq 0 (
    echo ERROR: .NET application publish failed.
    pause
    exit /b 1
)

echo .NET application published successfully.

:: Copy Python environment and scripts
if "%INCLUDE_PYTHON%"=="true" (
    echo.
    echo Configuring Python environment...
    
    :: Create virtual environment
    python -m venv %OUTPUT_DIR%\Python\venv
    
    :: Activate virtual environment and install dependencies
    call %OUTPUT_DIR%\Python\venv\Scripts\activate.bat
    
    :: Install basic dependencies
    pip install ultralytics torch torchvision opencv-python pillow numpy pyyaml tqdm matplotlib --no-cache-dir
    
    :: Copy training scripts
    if exist yolo_ohif\yolo_ohif\*.py (
        copy yolo_ohif\yolo_ohif\*.py %OUTPUT_DIR%\Python\
    )
    
    :: Deactivate virtual environment
    call deactivate
    
    echo Python environment configured successfully.
)

:: Copy data files
echo.
echo Copying data files...

:: Copy sample DICOM files
if exist Brain (
    copy Brain\*.dcm %OUTPUT_DIR%\Data\DICOM\ > nul 2>&1
    echo   Sample DICOM files copied.
)

:: Copy model files
if exist yolo_ohif\yolo_ohif\models (
    xcopy yolo_ohif\yolo_ohif\models %OUTPUT_DIR%\Data\Models\ /E /I /Q > nul 2>&1
    echo   Model files copied.
)

:: Copy configuration files
copy src\MedicalImageAnalysis.Wpf\appsettings.json %OUTPUT_DIR%\App\ > nul 2>&1

:: Copy documentation
copy *.md %OUTPUT_DIR%\Docs\ > nul 2>&1
echo   Documentation files copied.

:: Copy scripts
copy *.py %OUTPUT_DIR%\Scripts\ > nul 2>&1
echo   Script files copied.

:: Copy icons and images
if exist Assets (
    xcopy Assets %OUTPUT_DIR%\Assets\ /E /I /Q > nul 2>&1
    echo   Icons and images copied.
)

echo Data files copied successfully.

:: Create launch scripts
echo.
echo Creating launch scripts...

:: Main launch script
echo @echo off > %OUTPUT_DIR%\StartApp.bat
echo title Medical Image Analysis System >> %OUTPUT_DIR%\StartApp.bat
echo. >> %OUTPUT_DIR%\StartApp.bat
echo echo. >> %OUTPUT_DIR%\StartApp.bat
echo echo ======================================== >> %OUTPUT_DIR%\StartApp.bat
echo echo   Medical Image Analysis System >> %OUTPUT_DIR%\StartApp.bat
echo echo ======================================== >> %OUTPUT_DIR%\StartApp.bat
echo echo. >> %OUTPUT_DIR%\StartApp.bat
echo. >> %OUTPUT_DIR%\StartApp.bat
echo cd /d "%%~dp0" >> %OUTPUT_DIR%\StartApp.bat
echo. >> %OUTPUT_DIR%\StartApp.bat
echo echo Starting application... >> %OUTPUT_DIR%\StartApp.bat
echo start "" "App\MedicalImageAnalysis.Wpf.exe" >> %OUTPUT_DIR%\StartApp.bat
echo. >> %OUTPUT_DIR%\StartApp.bat
echo echo. >> %OUTPUT_DIR%\StartApp.bat
echo echo Application started! >> %OUTPUT_DIR%\StartApp.bat
echo echo. >> %OUTPUT_DIR%\StartApp.bat
echo echo For help, check the Docs directory >> %OUTPUT_DIR%\StartApp.bat
echo echo. >> %OUTPUT_DIR%\StartApp.bat
echo pause >> %OUTPUT_DIR%\StartApp.bat

:: Python training environment launch script
if "%INCLUDE_PYTHON%"=="true" (
    echo @echo off > %OUTPUT_DIR%\StartTraining.bat
    echo title YOLO Model Training Environment >> %OUTPUT_DIR%\StartTraining.bat
    echo. >> %OUTPUT_DIR%\StartTraining.bat
    echo echo. >> %OUTPUT_DIR%\StartTraining.bat
    echo echo ======================================== >> %OUTPUT_DIR%\StartTraining.bat
    echo echo        YOLO Model Training >> %OUTPUT_DIR%\StartTraining.bat
    echo echo ======================================== >> %OUTPUT_DIR%\StartTraining.bat
    echo echo. >> %OUTPUT_DIR%\StartTraining.bat
    echo. >> %OUTPUT_DIR%\StartTraining.bat
    echo cd /d "%%~dp0" >> %OUTPUT_DIR%\StartTraining.bat
    echo. >> %OUTPUT_DIR%\StartTraining.bat
    echo echo Activating Python environment... >> %OUTPUT_DIR%\StartTraining.bat
    echo call Python\venv\Scripts\activate.bat >> %OUTPUT_DIR%\StartTraining.bat
    echo. >> %OUTPUT_DIR%\StartTraining.bat
    echo echo. >> %OUTPUT_DIR%\StartTraining.bat
    echo echo Python environment activated. >> %OUTPUT_DIR%\StartTraining.bat
    echo echo You can run training scripts: >> %OUTPUT_DIR%\StartTraining.bat
    echo echo. >> %OUTPUT_DIR%\StartTraining.bat
    echo echo 1. python Python\start_yolo11x_training.py >> %OUTPUT_DIR%\StartTraining.bat
    echo echo 2. python Scripts\验证训练环境.py >> %OUTPUT_DIR%\StartTraining.bat
    echo echo. >> %OUTPUT_DIR%\StartTraining.bat
    echo. >> %OUTPUT_DIR%\StartTraining.bat
    echo cmd /k >> %OUTPUT_DIR%\StartTraining.bat
)

echo Launch scripts created successfully.

:: Create user guide
echo.
echo Creating user guide...

echo # Medical Image Analysis System - User Guide > %OUTPUT_DIR%\UserGuide.md
echo. >> %OUTPUT_DIR%\UserGuide.md
echo ## Quick Start >> %OUTPUT_DIR%\UserGuide.md
echo. >> %OUTPUT_DIR%\UserGuide.md
echo ### 1. Start Application >> %OUTPUT_DIR%\UserGuide.md
echo Double-click `StartApp.bat` to launch the main application. >> %OUTPUT_DIR%\UserGuide.md
echo. >> %OUTPUT_DIR%\UserGuide.md
echo ### 2. File Placement >> %OUTPUT_DIR%\UserGuide.md
echo. >> %OUTPUT_DIR%\UserGuide.md
echo #### DICOM Image Files >> %OUTPUT_DIR%\UserGuide.md
echo **Location**: `Data\DICOM\` >> %OUTPUT_DIR%\UserGuide.md
echo **Format**: .dcm, .dicom >> %OUTPUT_DIR%\UserGuide.md
echo **Description**: Place your DICOM medical image files here >> %OUTPUT_DIR%\UserGuide.md
echo **Examples**: Sample brain CT files included (DJ01.dcm - DJ10.dcm) >> %OUTPUT_DIR%\UserGuide.md
echo. >> %OUTPUT_DIR%\UserGuide.md
echo #### YOLO Model Files >> %OUTPUT_DIR%\UserGuide.md
echo **Location**: `Data\Models\` >> %OUTPUT_DIR%\UserGuide.md
echo **Format**: .pt, .onnx, .engine >> %OUTPUT_DIR%\UserGuide.md
echo **Description**: Place trained YOLO model files here >> %OUTPUT_DIR%\UserGuide.md
echo **Recommended**: >> %OUTPUT_DIR%\UserGuide.md
echo - `yolo11n.pt` - Lightweight model (fast) >> %OUTPUT_DIR%\UserGuide.md
echo - `yolo11x.pt` - High accuracy model (recommended) >> %OUTPUT_DIR%\UserGuide.md
echo. >> %OUTPUT_DIR%\UserGuide.md
echo #### Training Dataset >> %OUTPUT_DIR%\UserGuide.md
echo **Training Set**: `Data\Datasets\Train\` >> %OUTPUT_DIR%\UserGuide.md
echo   - `images\` - Training image files >> %OUTPUT_DIR%\UserGuide.md
echo   - `labels\` - Training label files (YOLO format) >> %OUTPUT_DIR%\UserGuide.md
echo. >> %OUTPUT_DIR%\UserGuide.md
echo **Validation Set**: `Data\Datasets\Val\` >> %OUTPUT_DIR%\UserGuide.md
echo   - `images\` - Validation image files >> %OUTPUT_DIR%\UserGuide.md
echo   - `labels\` - Validation label files >> %OUTPUT_DIR%\UserGuide.md
echo. >> %OUTPUT_DIR%\UserGuide.md
echo **Test Set**: `Data\Datasets\Test\` >> %OUTPUT_DIR%\UserGuide.md
echo   - `images\` - Test image files >> %OUTPUT_DIR%\UserGuide.md
echo   - `labels\` - Test label files >> %OUTPUT_DIR%\UserGuide.md
echo. >> %OUTPUT_DIR%\UserGuide.md
echo ## Detailed Tutorials >> %OUTPUT_DIR%\UserGuide.md
echo. >> %OUTPUT_DIR%\UserGuide.md
echo Check the `Docs\` directory for detailed tutorials: >> %OUTPUT_DIR%\UserGuide.md
echo - Smart annotation tutorial >> %OUTPUT_DIR%\UserGuide.md
echo - Model training tutorial >> %OUTPUT_DIR%\UserGuide.md
echo - Quick start guide >> %OUTPUT_DIR%\UserGuide.md
echo. >> %OUTPUT_DIR%\UserGuide.md
echo ## Technical Support >> %OUTPUT_DIR%\UserGuide.md
echo. >> %OUTPUT_DIR%\UserGuide.md
echo ### Log Files >> %OUTPUT_DIR%\UserGuide.md
echo - **Application logs**: `Data\Logs\` >> %OUTPUT_DIR%\UserGuide.md
echo - **Training logs**: `Python\runs\train\` >> %OUTPUT_DIR%\UserGuide.md
echo. >> %OUTPUT_DIR%\UserGuide.md
echo ### Getting Help >> %OUTPUT_DIR%\UserGuide.md
echo 1. Check detailed tutorials in `Docs\` directory >> %OUTPUT_DIR%\UserGuide.md
echo 2. Check log files for error information >> %OUTPUT_DIR%\UserGuide.md
echo 3. Run environment validation script >> %OUTPUT_DIR%\UserGuide.md

echo User guide created successfully.

:: Create README file
echo # Medical Image Analysis System > %OUTPUT_DIR%\README.md
echo. >> %OUTPUT_DIR%\README.md
echo This is a complete medical image annotation and AI model training system. >> %OUTPUT_DIR%\README.md
echo. >> %OUTPUT_DIR%\README.md
echo ## Quick Start >> %OUTPUT_DIR%\README.md
echo. >> %OUTPUT_DIR%\README.md
echo 1. Double-click `StartApp.bat` to launch the main application >> %OUTPUT_DIR%\README.md
echo 2. Check `UserGuide.md` for detailed usage instructions >> %OUTPUT_DIR%\README.md
echo 3. Check `Docs\` directory for complete tutorials >> %OUTPUT_DIR%\README.md
echo. >> %OUTPUT_DIR%\README.md
echo ## System Requirements >> %OUTPUT_DIR%\README.md
echo. >> %OUTPUT_DIR%\README.md
echo - Windows 10/11 (x64) >> %OUTPUT_DIR%\README.md
echo - At least 8GB RAM >> %OUTPUT_DIR%\README.md
echo - At least 10GB free space >> %OUTPUT_DIR%\README.md
echo - NVIDIA GPU (recommended for training) >> %OUTPUT_DIR%\README.md

:: Package completion
echo.
echo Package build completed!
echo.
echo Release package location: %OUTPUT_DIR%\
echo.
echo Package contents:
echo   - .NET application (self-contained)
if "%INCLUDE_PYTHON%"=="true" (
    echo   - Python training environment
)
echo   - Sample data files
echo   - Complete documentation
echo   - Launch scripts
echo   - User guide
echo.
echo The entire %OUTPUT_DIR% folder can be distributed to users!
echo.
echo User instructions:
echo   1. Extract the package to any directory
echo   2. Double-click "StartApp.bat" to use
echo   3. Check "UserGuide.md" for detailed instructions
echo.

pause
