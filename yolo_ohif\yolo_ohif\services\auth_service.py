import os
import jwt
import logging
import datetime
import hashlib
import secrets
from functools import wraps
from flask import request, jsonify, g

logger = logging.getLogger(__name__)

class AuthService:
    """用户认证服务"""
    
    def __init__(self, db_service, secret_key=None, token_expiry=24):
        """初始化认证服务
        
        Args:
            db_service: 数据库服务实例
            secret_key: JWT密钥
            token_expiry: 令牌过期时间（小时）
        """
        self.db_service = db_service
        self.secret_key = secret_key or secrets.token_hex(32)
        self.token_expiry = token_expiry
    
    def hash_password(self, password, salt=None):
        """哈希密码
        
        Args:
            password: 明文密码
            salt: 盐值（如果为None则生成新的）
            
        Returns:
            (哈希密码, 盐值)
        """
        if salt is None:
            salt = secrets.token_hex(16)
        
        # 使用PBKDF2算法哈希密码
        key = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000
        )
        
        # 将哈希和盐值组合
        password_hash = salt + ':' + key.hex()
        
        return password_hash, salt
    
    def verify_password(self, stored_password, provided_password):
        """验证密码
        
        Args:
            stored_password: 存储的哈希密码
            provided_password: 提供的明文密码
            
        Returns:
            密码是否匹配
        """
        try:
            # 从存储的哈希中提取盐值
            salt = stored_password.split(':')[0]
            
            # 使用相同的盐值哈希提供的密码
            hashed_provided, _ = self.hash_password(provided_password, salt)
            
            # 比较哈希值
            return hashed_provided == stored_password
        except Exception as e:
            logger.error(f"验证密码时出错: {str(e)}")
            return False
    
    def register_user(self, username, password, email=None, role='user'):
        """注册新用户
        
        Args:
            username: 用户名
            password: 密码
            email: 电子邮件
            role: 用户角色
            
        Returns:
            用户ID
        """
        try:
            # 检查用户是否已存在
            existing_user = self.db_service.get_user(username)
            if existing_user:
                raise ValueError(f"用户名已存在: {username}")
            
            # 哈希密码
            password_hash, _ = self.hash_password(password)
            
            # 添加用户到数据库
            user_id = self.db_service.add_user(username, password_hash, email, role)
            
            # 记录审计日志
            self.db_service.add_audit_log(
                user_id=user_id,
                action='register',
                entity_type='user',
                entity_id=str(user_id),
                details={'username': username, 'role': role},
                ip_address=request.remote_addr if request else None
            )
            
            return user_id
        except Exception as e:
            logger.error(f"注册用户时出错: {str(e)}")
            raise
    
    def login(self, username, password):
        """用户登录
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            (JWT令牌, 用户信息)
        """
        try:
            # 获取用户信息
            user = self.db_service.get_user(username)
            if not user:
                raise ValueError(f"用户不存在: {username}")
            
            # 验证密码
            if not self.verify_password(user['password_hash'], password):
                raise ValueError("密码错误")
            
            # 更新最后登录时间
            self.db_service.update_last_login(user['id'])
            
            # 生成JWT令牌
            token = self.generate_token(user)
            
            # 记录审计日志
            self.db_service.add_audit_log(
                user_id=user['id'],
                action='login',
                entity_type='user',
                entity_id=str(user['id']),
                ip_address=request.remote_addr if request else None
            )
            
            # 移除敏感信息
            user.pop('password_hash', None)
            
            return token, user
        except Exception as e:
            logger.error(f"用户登录时出错: {str(e)}")
            raise
    
    def generate_token(self, user):
        """生成JWT令牌
        
        Args:
            user: 用户信息
            
        Returns:
            JWT令牌
        """
        try:
            # 设置过期时间
            expiry = datetime.datetime.utcnow() + datetime.timedelta(hours=self.token_expiry)
            
            # 创建令牌负载
            payload = {
                'user_id': user['id'],
                'username': user['username'],
                'role': user['role'],
                'exp': expiry
            }
            
            # 生成令牌
            token = jwt.encode(payload, self.secret_key, algorithm='HS256')
            
            return token
        except Exception as e:
            logger.error(f"生成令牌时出错: {str(e)}")
            raise
    
    def verify_token(self, token):
        """验证JWT令牌
        
        Args:
            token: JWT令牌
            
        Returns:
            用户信息
        """
        try:
            # 解码令牌
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            
            # 获取用户信息
            user = self.db_service.get_user(payload['username'])
            if not user:
                raise ValueError(f"令牌中的用户不存在: {payload['username']}")
            
            # 移除敏感信息
            user.pop('password_hash', None)
            
            return user
        except jwt.ExpiredSignatureError:
            logger.warning("令牌已过期")
            raise ValueError("令牌已过期")
        except jwt.InvalidTokenError:
            logger.warning("无效的令牌")
            raise ValueError("无效的令牌")
        except Exception as e:
            logger.error(f"验证令牌时出错: {str(e)}")
            raise
    
    def token_required(self, f):
        """JWT令牌验证装饰器
        
        Args:
            f: 被装饰的函数
            
        Returns:
            装饰后的函数
        """
        @wraps(f)
        def decorated(*args, **kwargs):
            token = None
            
            # 从请求头中获取令牌
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
            
            # 如果没有令牌，返回错误
            if not token:
                return jsonify({'message': '缺少认证令牌'}), 401
            
            try:
                # 验证令牌
                user = self.verify_token(token)
                
                # 将用户信息存储在g对象中，以便在视图函数中使用
                g.user = user
            except ValueError as e:
                return jsonify({'message': str(e)}), 401
            except Exception as e:
                return jsonify({'message': '认证失败'}), 401
            
            return f(*args, **kwargs)
        
        return decorated
    
    def role_required(self, roles):
        """角色验证装饰器
        
        Args:
            roles: 允许的角色列表
            
        Returns:
            装饰器函数
        """
        def decorator(f):
            @wraps(f)
            @self.token_required
            def decorated(*args, **kwargs):
                # 检查用户角色
                if g.user['role'] not in roles:
                    return jsonify({'message': '权限不足'}), 403
                
                return f(*args, **kwargs)
            
            return decorated
        
        return decorator
    
    def change_password(self, user_id, current_password, new_password):
        """修改密码
        
        Args:
            user_id: 用户ID
            current_password: 当前密码
            new_password: 新密码
            
        Returns:
            是否成功
        """
        try:
            # 获取用户信息
            user = self.db_service.get_user_by_id(user_id)
            if not user:
                raise ValueError(f"用户不存在: ID {user_id}")
            
            # 验证当前密码
            if not self.verify_password(user['password_hash'], current_password):
                raise ValueError("当前密码错误")
            
            # 哈希新密码
            new_password_hash, _ = self.hash_password(new_password)
            
            # 更新密码
            self.db_service.update_user_password(user_id, new_password_hash)
            
            # 记录审计日志
            self.db_service.add_audit_log(
                user_id=user_id,
                action='change_password',
                entity_type='user',
                entity_id=str(user_id),
                ip_address=request.remote_addr if request else None
            )
            
            return True
        except Exception as e:
            logger.error(f"修改密码时出错: {str(e)}")
            raise
    
    def reset_password(self, username, new_password):
        """重置密码（管理员功能）
        
        Args:
            username: 用户名
            new_password: 新密码
            
        Returns:
            是否成功
        """
        try:
            # 获取用户信息
            user = self.db_service.get_user(username)
            if not user:
                raise ValueError(f"用户不存在: {username}")
            
            # 哈希新密码
            new_password_hash, _ = self.hash_password(new_password)
            
            # 更新密码
            self.db_service.update_user_password(user['id'], new_password_hash)
            
            # 记录审计日志
            admin_id = g.user['id'] if hasattr(g, 'user') else None
            self.db_service.add_audit_log(
                user_id=admin_id,
                action='reset_password',
                entity_type='user',
                entity_id=str(user['id']),
                details={'target_username': username},
                ip_address=request.remote_addr if request else None
            )
            
            return True
        except Exception as e:
            logger.error(f"重置密码时出错: {str(e)}")
            raise