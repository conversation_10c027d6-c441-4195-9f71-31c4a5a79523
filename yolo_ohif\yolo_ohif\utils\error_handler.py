import logging
import traceback
from functools import wraps
from flask import jsonify, request
from datetime import datetime

logger = logging.getLogger(__name__)

class APIError(Exception):
    """API专用异常类"""
    def __init__(self, message, status_code=500, error_code=None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.error_code = error_code

class DetectionError(APIError):
    """检测服务专用异常"""
    def __init__(self, message, status_code=500):
        super().__init__(message, status_code, 'DETECTION_ERROR')

class OrthancError(APIError):
    """Orthanc服务专用异常"""
    def __init__(self, message, status_code=500):
        super().__init__(message, status_code, 'ORTHANC_ERROR')

class DatabaseError(APIError):
    """数据库专用异常"""
    def __init__(self, message, status_code=500):
        super().__init__(message, status_code, 'DATABASE_ERROR')

class AuthenticationError(APIError):
    """认证专用异常"""
    def __init__(self, message, status_code=401):
        super().__init__(message, status_code, 'AUTH_ERROR')

def handle_api_error(error):
    """统一API错误处理器"""
    logger.error(f"API错误: {error.message}", exc_info=True)
    
    response = {
        'error': {
            'message': error.message,
            'code': error.error_code,
            'timestamp': datetime.utcnow().isoformat()
        }
    }
    
    return jsonify(response), error.status_code

def handle_generic_error(error):
    """通用错误处理器"""
    logger.error(f"未处理的错误: {str(error)}", exc_info=True)
    
    response = {
        'error': {
            'message': '服务器内部错误',
            'code': 'INTERNAL_ERROR',
            'timestamp': datetime.utcnow().isoformat()
        }
    }
    
    return jsonify(response), 500

def log_request_info():
    """记录请求信息"""
    logger.info(f"{request.method} {request.path} - IP: {request.remote_addr}")

def api_error_handler(f):
    """API错误处理装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except APIError as e:
            return handle_api_error(e)
        except Exception as e:
            return handle_generic_error(e)
    return decorated_function

def safe_execute(operation, error_message="操作失败", default_return=None):
    """安全执行操作的辅助函数"""
    try:
        return operation()
    except Exception as e:
        logger.error(f"{error_message}: {str(e)}", exc_info=True)
        return default_return

def validate_required_fields(data, required_fields):
    """验证必需字段"""
    missing_fields = [field for field in required_fields if field not in data or not data[field]]
    if missing_fields:
        raise APIError(f"缺少必需字段: {', '.join(missing_fields)}", 400)

def setup_error_handlers(app):
    """设置Flask应用的错误处理器"""
    
    @app.errorhandler(APIError)
    def handle_api_error_flask(error):
        return handle_api_error(error)
    
    @app.errorhandler(404)
    def handle_not_found(error):
        return jsonify({
            'error': {
                'message': '请求的资源不存在',
                'code': 'NOT_FOUND',
                'timestamp': datetime.utcnow().isoformat()
            }
        }), 404
    
    @app.errorhandler(500)
    def handle_internal_error(error):
        return handle_generic_error(error)
    
    @app.before_request
    def before_request():
        log_request_info()