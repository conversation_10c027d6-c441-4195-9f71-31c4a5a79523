import json
import inspect
from typing import Dict, Any, List, Optional, Callable
from functools import wraps
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class APIEndpoint:
    """API端点信息"""
    path: str
    method: str
    description: str
    parameters: List[Dict[str, Any]]
    responses: Dict[str, Dict[str, Any]]
    examples: Dict[str, Any]
    tags: List[str]
    requires_auth: bool = False
    deprecated: bool = False

@dataclass
class APIParameter:
    """API参数信息"""
    name: str
    type: str
    description: str
    required: bool = True
    default: Any = None
    example: Any = None
    location: str = "query"  # query, path, body, header

class APIDocumentationGenerator:
    """API文档生成器"""
    
    def __init__(self):
        self.endpoints: List[APIEndpoint] = []
        self.schemas: Dict[str, Dict[str, Any]] = {}
        self.info = {
            "title": "YOLO-OHIF Medical Image Detection API",
            "version": "1.0.0",
            "description": "医学图像检测系统API文档",
            "contact": {
                "name": "API Support",
                "email": "<EMAIL>"
            }
        }
    
    def add_endpoint(self, endpoint: APIEndpoint):
        """添加API端点"""
        self.endpoints.append(endpoint)
        logger.debug(f"添加API端点: {endpoint.method} {endpoint.path}")
    
    def add_schema(self, name: str, schema: Dict[str, Any]):
        """添加数据模式"""
        self.schemas[name] = schema
        logger.debug(f"添加数据模式: {name}")
    
    def generate_openapi_spec(self) -> Dict[str, Any]:
        """生成OpenAPI规范"""
        spec = {
            "openapi": "3.0.0",
            "info": self.info,
            "servers": [
                {
                    "url": "http://localhost:5000",
                    "description": "开发服务器"
                }
            ],
            "paths": {},
            "components": {
                "schemas": self.schemas,
                "securitySchemes": {
                    "bearerAuth": {
                        "type": "http",
                        "scheme": "bearer",
                        "bearerFormat": "JWT"
                    }
                }
            },
            "tags": [
                {"name": "authentication", "description": "用户认证相关接口"},
                {"name": "detection", "description": "图像检测相关接口"},
                {"name": "studies", "description": "研究管理相关接口"},
                {"name": "system", "description": "系统监控相关接口"}
            ]
        }
        
        # 生成路径信息
        for endpoint in self.endpoints:
            if endpoint.path not in spec["paths"]:
                spec["paths"][endpoint.path] = {}
            
            operation = {
                "summary": endpoint.description,
                "tags": endpoint.tags,
                "parameters": [],
                "responses": endpoint.responses
            }
            
            # 添加参数
            for param in endpoint.parameters:
                operation["parameters"].append(param)
            
            # 添加安全要求
            if endpoint.requires_auth:
                operation["security"] = [{"bearerAuth": []}]
            
            # 添加弃用标记
            if endpoint.deprecated:
                operation["deprecated"] = True
            
            # 添加示例
            if endpoint.examples:
                operation["examples"] = endpoint.examples
            
            spec["paths"][endpoint.path][endpoint.method.lower()] = operation
        
        return spec
    
    def generate_html_documentation(self) -> str:
        """生成HTML文档"""
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title} - API文档</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #34495e;
            margin-top: 30px;
        }}
        h3 {{
            color: #7f8c8d;
        }}
        .endpoint {{
            border: 1px solid #ddd;
            margin: 20px 0;
            border-radius: 5px;
            overflow: hidden;
        }}
        .endpoint-header {{
            background: #ecf0f1;
            padding: 15px;
            font-weight: bold;
        }}
        .method {{
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            color: white;
            font-size: 12px;
            margin-right: 10px;
        }}
        .method.get {{ background: #27ae60; }}
        .method.post {{ background: #e74c3c; }}
        .method.put {{ background: #f39c12; }}
        .method.delete {{ background: #e67e22; }}
        .endpoint-body {{
            padding: 15px;
        }}
        .parameters, .responses {{
            margin: 15px 0;
        }}
        .param {{
            background: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #3498db;
        }}
        .required {{
            color: #e74c3c;
            font-weight: bold;
        }}
        .optional {{
            color: #95a5a6;
        }}
        .response {{
            background: #f1f2f6;
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #2ecc71;
        }}
        .code {{
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }}
        .tag {{
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            margin: 2px;
        }}
        .auth-required {{
            background: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }}
        .deprecated {{
            background: #e74c3c;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{title}</h1>
        <p><strong>版本:</strong> {version}</p>
        <p><strong>描述:</strong> {description}</p>
        
        <h2>API端点</h2>
        {endpoints_html}
        
        <h2>数据模式</h2>
        {schemas_html}
    </div>
</body>
</html>
        """
        
        # 生成端点HTML
        endpoints_html = ""
        for endpoint in self.endpoints:
            tags_html = "".join([f'<span class="tag">{tag}</span>' for tag in endpoint.tags])
            
            auth_html = '<span class="auth-required">需要认证</span>' if endpoint.requires_auth else ''
            deprecated_html = '<span class="deprecated">已弃用</span>' if endpoint.deprecated else ''
            
            # 生成参数HTML
            params_html = ""
            for param in endpoint.parameters:
                required_class = "required" if param.get('required', True) else "optional"
                params_html += f"""
                <div class="param">
                    <strong class="{required_class}">{param.get('name', 'N/A')}</strong> 
                    ({param.get('schema', {}).get('type', 'string')}) - {param.get('description', '无描述')}
                    {f"<br><em>默认值: {param.get('schema', {}).get('default', 'N/A')}</em>" if param.get('schema', {}).get('default') else ""}
                </div>
                """
            
            # 生成响应HTML
            responses_html = ""
            for status_code, response_info in endpoint.responses.items():
                responses_html += f"""
                <div class="response">
                    <strong>{status_code}</strong> - {response_info.get('description', '无描述')}
                </div>
                """
            
            endpoints_html += f"""
            <div class="endpoint">
                <div class="endpoint-header">
                    <span class="method {endpoint.method.lower()}">{endpoint.method}</span>
                    <code>{endpoint.path}</code>
                    {tags_html} {auth_html} {deprecated_html}
                </div>
                <div class="endpoint-body">
                    <p>{endpoint.description}</p>
                    
                    <div class="parameters">
                        <h4>参数:</h4>
                        {params_html if params_html else '<p>无参数</p>'}
                    </div>
                    
                    <div class="responses">
                        <h4>响应:</h4>
                        {responses_html}
                    </div>
                </div>
            </div>
            """
        
        # 生成模式HTML
        schemas_html = ""
        for schema_name, schema_def in self.schemas.items():
            schemas_html += f"""
            <h3>{schema_name}</h3>
            <div class="code">
                <pre>{json.dumps(schema_def, indent=2, ensure_ascii=False)}</pre>
            </div>
            """
        
        return html_template.format(
            title=self.info["title"],
            version=self.info["version"],
            description=self.info["description"],
            endpoints_html=endpoints_html,
            schemas_html=schemas_html
        )
    
    def save_documentation(self, output_dir: str = "docs"):
        """保存文档到文件"""
        import os
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存OpenAPI规范
        openapi_spec = self.generate_openapi_spec()
        with open(os.path.join(output_dir, "openapi.json"), "w", encoding="utf-8") as f:
            json.dump(openapi_spec, f, indent=2, ensure_ascii=False)
        
        # 保存HTML文档
        html_doc = self.generate_html_documentation()
        with open(os.path.join(output_dir, "api_documentation.html"), "w", encoding="utf-8") as f:
            f.write(html_doc)
        
        logger.info(f"API文档已保存到: {output_dir}")

def document_api(path: str, method: str = "GET", description: str = "", 
                tags: List[str] = None, requires_auth: bool = False,
                deprecated: bool = False):
    """API文档装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        
        # 从函数签名提取参数信息
        sig = inspect.signature(func)
        parameters = []
        
        for param_name, param in sig.parameters.items():
            if param_name not in ['self', 'args', 'kwargs']:
                param_info = {
                    "name": param_name,
                    "in": "query",
                    "description": f"参数 {param_name}",
                    "required": param.default == inspect.Parameter.empty,
                    "schema": {
                        "type": "string"
                    }
                }
                
                if param.default != inspect.Parameter.empty:
                    param_info["schema"]["default"] = param.default
                
                parameters.append(param_info)
        
        # 创建端点信息
        endpoint = APIEndpoint(
            path=path,
            method=method,
            description=description or func.__doc__ or f"{method} {path}",
            parameters=parameters,
            responses={
                "200": {
                    "description": "成功响应",
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object"
                            }
                        }
                    }
                },
                "400": {
                    "description": "请求错误"
                },
                "500": {
                    "description": "服务器错误"
                }
            },
            examples={},
            tags=tags or ["default"],
            requires_auth=requires_auth,
            deprecated=deprecated
        )
        
        # 将端点信息存储在函数属性中
        wrapper._api_endpoint = endpoint
        
        return wrapper
    
    return decorator

# 预定义的数据模式
DEFAULT_SCHEMAS = {
    "User": {
        "type": "object",
        "properties": {
            "id": {"type": "integer", "description": "用户ID"},
            "username": {"type": "string", "description": "用户名"},
            "email": {"type": "string", "format": "email", "description": "邮箱地址"},
            "role": {"type": "string", "enum": ["admin", "doctor", "technician"], "description": "用户角色"},
            "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}
        },
        "required": ["username", "email", "role"]
    },
    "Study": {
        "type": "object",
        "properties": {
            "id": {"type": "integer", "description": "研究ID"},
            "orthanc_id": {"type": "string", "description": "Orthanc系统ID"},
            "patient_id": {"type": "string", "description": "患者ID"},
            "patient_name": {"type": "string", "description": "患者姓名"},
            "study_date": {"type": "string", "format": "date", "description": "检查日期"},
            "modality": {"type": "string", "description": "检查方式"},
            "description": {"type": "string", "description": "研究描述"}
        },
        "required": ["orthanc_id", "patient_id"]
    },
    "DetectionResult": {
        "type": "object",
        "properties": {
            "id": {"type": "integer", "description": "检测结果ID"},
            "study_id": {"type": "integer", "description": "研究ID"},
            "user_id": {"type": "integer", "description": "用户ID"},
            "detection_time": {"type": "string", "format": "date-time", "description": "检测时间"},
            "results": {"type": "object", "description": "检测结果JSON"},
            "confidence_threshold": {"type": "number", "description": "置信度阈值"},
            "model_version": {"type": "string", "description": "模型版本"}
        },
        "required": ["study_id", "user_id", "results"]
    },
    "HealthStatus": {
        "type": "object",
        "properties": {
            "status": {"type": "string", "enum": ["healthy", "unhealthy"], "description": "健康状态"},
            "orthanc": {"type": "object", "description": "Orthanc服务状态"},
            "detection_service": {"type": "object", "description": "检测服务状态"},
            "database": {"type": "object", "description": "数据库状态"},
            "timestamp": {"type": "string", "format": "date-time", "description": "检查时间"}
        }
    },
    "ErrorResponse": {
        "type": "object",
        "properties": {
            "error": {"type": "string", "description": "错误信息"},
            "code": {"type": "integer", "description": "错误代码"},
            "details": {"type": "object", "description": "错误详情"}
        },
        "required": ["error"]
    }
}

# 全局文档生成器实例
_doc_generator: Optional[APIDocumentationGenerator] = None

def get_documentation_generator() -> APIDocumentationGenerator:
    """获取全局文档生成器"""
    global _doc_generator
    if not _doc_generator:
        _doc_generator = APIDocumentationGenerator()
        # 添加默认模式
        for schema_name, schema_def in DEFAULT_SCHEMAS.items():
            _doc_generator.add_schema(schema_name, schema_def)
    
    return _doc_generator

def generate_api_documentation(output_dir: str = "docs"):
    """生成API文档"""
    doc_gen = get_documentation_generator()
    doc_gen.save_documentation(output_dir)
    return doc_gen