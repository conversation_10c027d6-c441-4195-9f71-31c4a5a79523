# DICOM功能测试脚本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "DICOM功能测试脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查示例DICOM文件
Write-Host "检查示例DICOM文件..." -ForegroundColor Yellow
$brainFiles = Get-ChildItem -Path "Brain" -Filter "*.dcm" -ErrorAction SilentlyContinue
if ($brainFiles) {
    Write-Host "✓ 发现 $($brainFiles.Count) 个DICOM示例文件:" -ForegroundColor Green
    $brainFiles | Select-Object -First 3 | ForEach-Object { 
        Write-Host "  - $($_.Name) ($([math]::Round($_.Length/1KB, 2)) KB)" -ForegroundColor Gray 
    }
    if ($brainFiles.Count -gt 3) {
        Write-Host "  ... 还有 $($brainFiles.Count - 3) 个文件" -ForegroundColor Gray
    }
} else {
    Write-Host "✗ 未找到示例DICOM文件" -ForegroundColor Red
    exit 1
}

# 构建项目
Write-Host ""
Write-Host "构建WpfClient项目..." -ForegroundColor Yellow
dotnet build src/MedicalImageAnalysis.WpfClient/MedicalImageAnalysis.WpfClient.csproj --verbosity quiet 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 项目构建成功" -ForegroundColor Green
} else {
    Write-Host "✗ 项目构建失败" -ForegroundColor Red
    exit 1
}

# 检查应用程序文件
$appPath = "src\MedicalImageAnalysis.WpfClient\bin\Debug\net8.0-windows\MedicalImageAnalysis.WpfClient.exe"
if (Test-Path $appPath) {
    Write-Host "✓ 应用程序文件存在" -ForegroundColor Green
} else {
    Write-Host "✗ 应用程序文件不存在" -ForegroundColor Red
    exit 1
}

# 启动应用程序
Write-Host ""
Write-Host "启动应用程序..." -ForegroundColor Yellow
Start-Process -FilePath $appPath -WorkingDirectory (Get-Location) -ErrorAction SilentlyContinue
if ($?) {
    Write-Host "✓ 应用程序启动命令已执行" -ForegroundColor Green

    # 等待应用程序启动
    Start-Sleep -Seconds 3

    # 检查进程
    $process = Get-Process -Name "MedicalImageAnalysis.WpfClient" -ErrorAction SilentlyContinue
    if ($process) {
        Write-Host "✓ 应用程序进程正在运行 (PID: $($process.Id))" -ForegroundColor Green
    } else {
        Write-Host "⚠ 应用程序可能启动失败或立即退出" -ForegroundColor Yellow
    }
} else {
    Write-Host "✗ 启动应用程序失败" -ForegroundColor Red
}

# 显示使用说明
Write-Host ""
Write-Host "DICOM功能测试说明:" -ForegroundColor Yellow
Write-Host "1. 在应用程序中点击 'DICOM查看器' 选项卡" -ForegroundColor White
Write-Host "2. 点击 '打开DICOM' 按钮" -ForegroundColor White
Write-Host "3. 选择 Brain 目录中的任意 .dcm 文件" -ForegroundColor White
Write-Host "4. 验证以下功能:" -ForegroundColor White
Write-Host "   - DICOM文件加载和显示" -ForegroundColor Gray
Write-Host "   - 患者信息显示" -ForegroundColor Gray
Write-Host "   - 窗宽窗位调整" -ForegroundColor Gray
Write-Host "   - 查看DICOM标签" -ForegroundColor Gray
Write-Host "   - AI分析功能" -ForegroundColor Gray
Write-Host "   - 图像导出功能" -ForegroundColor Gray

Write-Host ""
Write-Host "智能标注功能测试:" -ForegroundColor Yellow
Write-Host "1. 在应用程序中点击 '智能标注' 选项卡" -ForegroundColor White
Write-Host "2. 点击 '打开图像' 按钮" -ForegroundColor White
Write-Host "3. 选择 DICOM 文件或标准图像文件" -ForegroundColor White
Write-Host "4. 测试 AI 推理功能" -ForegroundColor White

Write-Host ""
Write-Host "修复内容:" -ForegroundColor Green
Write-Host "✓ 修复了 'DICOM文件处理功能正在开发中' 的提示" -ForegroundColor Green
Write-Host "✓ 实现了真实的DICOM标签查看功能" -ForegroundColor Green
Write-Host "✓ 改进了AI分析功能，能够处理DICOM文件" -ForegroundColor Green
Write-Host "✓ 增强了智能标注中的DICOM文件支持" -ForegroundColor Green
Write-Host "✓ 修复了标注文件加载功能" -ForegroundColor Green

Write-Host ""
Write-Host "注意事项:" -ForegroundColor Yellow
Write-Host "- DICOM处理基于fo-dicom库，功能完整可用" -ForegroundColor Gray
Write-Host "- AI模型功能仍在开发中，当前为演示模式" -ForegroundColor Gray
Write-Host "- 应用程序日志保存在 logs/ 目录中" -ForegroundColor Gray

Write-Host ""
Write-Host "测试完成！请在应用程序中验证DICOM功能。" -ForegroundColor Green
