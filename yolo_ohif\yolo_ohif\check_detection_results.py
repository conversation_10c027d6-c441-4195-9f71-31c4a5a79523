import sqlite3
import json

conn = sqlite3.connect('e:/Trae/yolo_ohif/data/app.db')
cursor = conn.cursor()

# 查询最新的检测结果
cursor.execute('''
    SELECT id, study_id, model_name, detection_time, result_data 
    FROM detection_results 
    WHERE study_id = ? 
    ORDER BY detection_time DESC 
    LIMIT 1
''', ('927e4af3-ffe70e59-686ea538-61a3431d-398b89f1',))

result = cursor.fetchone()

if result:
    print('最新检测结果:')
    print(f'ID: {result[0]}')
    print(f'研究ID: {result[1]}')
    print(f'模型: {result[2]}')
    print(f'时间: {result[3]}')
    
    # 解析检测结果数据
    if result[4]:
        try:
            result_data = json.loads(result[4])
            print(f'检测结果数据类型: {type(result_data)}')
            
            if isinstance(result_data, dict):
                print(f'检测结果键: {list(result_data.keys())}')
            elif isinstance(result_data, list):
                print(f'检测结果列表长度: {len(result_data)}')
                if len(result_data) > 0:
                    print(f'第一个文件的检测结果: {result_data[0]}')
                    if 'detections' in result_data[0]:
                        detections = result_data[0]['detections']
                        print(f'检测到的目标数量: {len(detections)}')
                        if len(detections) > 0:
                            print(f'第一个检测目标: {detections[0]}')
            else:
                print(f'检测结果数据: {result_data}')
        except json.JSONDecodeError as e:
            print(f'JSON解析错误: {e}')
            print(f'原始数据: {result[4][:200]}...')
    else:
        print('检测结果数据为空')
else:
    print('未找到检测结果')

conn.close()