namespace MedicalImageAnalysis.Core.Models;

/// <summary>
/// 像素数据
/// </summary>
public class PixelData
{
    /// <summary>
    /// 图像宽度
    /// </summary>
    public int Width { get; set; }

    /// <summary>
    /// 图像高度
    /// </summary>
    public int Height { get; set; }

    /// <summary>
    /// 每像素位数
    /// </summary>
    public int BitsPerPixel { get; set; }

    /// <summary>
    /// 是否为有符号数据
    /// </summary>
    public bool IsSigned { get; set; }

    /// <summary>
    /// 光度解释
    /// </summary>
    public string PhotometricInterpretation { get; set; } = string.Empty;

    /// <summary>
    /// 像素数据数组
    /// </summary>
    public Array? Data { get; set; }

    /// <summary>
    /// 数据类型
    /// </summary>
    public Type? DataType { get; set; }

    /// <summary>
    /// 样本数/像素
    /// </summary>
    public int SamplesPerPixel { get; set; } = 1;

    /// <summary>
    /// 平面配置
    /// </summary>
    public int PlanarConfiguration { get; set; } = 0;

    /// <summary>
    /// 像素表示
    /// </summary>
    public int PixelRepresentation { get; set; } = 0;

    /// <summary>
    /// 分配位数
    /// </summary>
    public int BitsAllocated { get; set; }

    /// <summary>
    /// 存储位数
    /// </summary>
    public int BitsStored { get; set; }

    /// <summary>
    /// 高位
    /// </summary>
    public int HighBit { get; set; }

    /// <summary>
    /// 重缩放斜率
    /// </summary>
    public double RescaleSlope { get; set; } = 1.0;

    /// <summary>
    /// 重缩放截距
    /// </summary>
    public double RescaleIntercept { get; set; } = 0.0;

    /// <summary>
    /// 窗宽
    /// </summary>
    public double WindowWidth { get; set; }

    /// <summary>
    /// 窗位
    /// </summary>
    public double WindowCenter { get; set; }

    /// <summary>
    /// 数据大小（字节）
    /// </summary>
    public long DataSize => Data?.Length * (BitsAllocated / 8) ?? 0;

    /// <summary>
    /// 获取指定位置的像素值
    /// </summary>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <returns>像素值</returns>
    public double GetPixelValue(int x, int y)
    {
        if (Data == null || x < 0 || x >= Width || y < 0 || y >= Height)
            return 0;

        var index = y * Width + x;
        
        return DataType?.Name switch
        {
            nameof(Byte) => ((byte[])Data)[index],
            nameof(SByte) => ((sbyte[])Data)[index],
            nameof(UInt16) => ((ushort[])Data)[index],
            nameof(Int16) => ((short[])Data)[index],
            nameof(UInt32) => ((uint[])Data)[index],
            nameof(Int32) => ((int[])Data)[index],
            nameof(Single) => ((float[])Data)[index],
            nameof(Double) => ((double[])Data)[index],
            _ => 0
        };
    }

    /// <summary>
    /// 应用窗宽窗位变换
    /// </summary>
    /// <param name="pixelValue">原始像素值</param>
    /// <returns>变换后的值</returns>
    public double ApplyWindowLevelTransform(double pixelValue)
    {
        // 首先应用重缩放变换
        var hounsfield = pixelValue * RescaleSlope + RescaleIntercept;
        
        // 然后应用窗宽窗位变换
        var minValue = WindowCenter - WindowWidth / 2.0;
        var maxValue = WindowCenter + WindowWidth / 2.0;
        
        if (hounsfield <= minValue)
            return 0;
        else if (hounsfield >= maxValue)
            return 255;
        else
            return (hounsfield - minValue) / WindowWidth * 255;
    }
}
