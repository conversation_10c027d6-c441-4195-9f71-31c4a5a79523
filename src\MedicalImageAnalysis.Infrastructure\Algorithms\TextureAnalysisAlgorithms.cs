using MedicalImageAnalysis.Core.Models;
using Microsoft.Extensions.Logging;
using System.Numerics;

namespace MedicalImageAnalysis.Infrastructure.Algorithms;

/// <summary>
/// 纹理分析算法库
/// 提供专业级的医学影像纹理分析功能
/// </summary>
public class TextureAnalysisAlgorithms
{
    private readonly ILogger<TextureAnalysisAlgorithms> _logger;

    public TextureAnalysisAlgorithms(ILogger<TextureAnalysisAlgorithms> logger)
    {
        _logger = logger;
    }

    #region GLCM (Gray Level Co-occurrence Matrix) 特征

    /// <summary>
    /// 计算灰度共生矩阵特征
    /// </summary>
    public async Task<GLCMFeatures> ComputeGLCMFeaturesAsync(PixelData pixelData, int distance = 1, double angle = 0, int grayLevels = 256)
    {
        _logger.LogInformation("计算GLCM特征，距离: {Distance}, 角度: {Angle}", distance, angle);

        var width = pixelData.Width;
        var height = pixelData.Height;
        var data = ConvertToByteArray(pixelData, grayLevels);

        // 计算偏移量
        var dx = (int)Math.Round(distance * Math.Cos(angle * Math.PI / 180));
        var dy = (int)Math.Round(distance * Math.Sin(angle * Math.PI / 180));

        // 构建GLCM矩阵
        var glcm = new double[grayLevels, grayLevels];
        int pairCount = 0;

        await Task.Run(() =>
        {
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    int nx = x + dx;
                    int ny = y + dy;

                    if (nx >= 0 && nx < width && ny >= 0 && ny < height)
                    {
                        var i = data[y * width + x];
                        var j = data[ny * width + nx];
                        glcm[i, j]++;
                        pairCount++;
                    }
                }
            }

            // 归一化GLCM
            if (pairCount > 0)
            {
                for (int i = 0; i < grayLevels; i++)
                {
                    for (int j = 0; j < grayLevels; j++)
                    {
                        glcm[i, j] /= pairCount;
                    }
                }
            }
        });

        return ComputeGLCMStatistics(glcm, grayLevels);
    }

    /// <summary>
    /// 计算多方向GLCM特征
    /// </summary>
    public async Task<MultiDirectionalGLCMFeatures> ComputeMultiDirectionalGLCMAsync(PixelData pixelData, int distance = 1, int grayLevels = 256)
    {
        _logger.LogInformation("计算多方向GLCM特征");

        var angles = new double[] { 0, 45, 90, 135 };
        var features = new List<GLCMFeatures>();

        foreach (var angle in angles)
        {
            var glcmFeatures = await ComputeGLCMFeaturesAsync(pixelData, distance, angle, grayLevels);
            features.Add(glcmFeatures);
        }

        return new MultiDirectionalGLCMFeatures
        {
            DirectionalFeatures = features,
            MeanContrast = features.Average(f => f.Contrast),
            MeanCorrelation = features.Average(f => f.Correlation),
            MeanEnergy = features.Average(f => f.Energy),
            MeanHomogeneity = features.Average(f => f.Homogeneity),
            MeanEntropy = features.Average(f => f.Entropy),
            ContrastVariance = ComputeVariance(features.Select(f => f.Contrast)),
            CorrelationVariance = ComputeVariance(features.Select(f => f.Correlation)),
            EnergyVariance = ComputeVariance(features.Select(f => f.Energy)),
            HomogeneityVariance = ComputeVariance(features.Select(f => f.Homogeneity))
        };
    }

    #endregion

    #region LBP (Local Binary Pattern) 特征

    /// <summary>
    /// 计算局部二值模式特征
    /// </summary>
    public async Task<LBPFeatures> ComputeLBPFeaturesAsync(PixelData pixelData, int radius = 1, int neighbors = 8)
    {
        _logger.LogInformation("计算LBP特征，半径: {Radius}, 邻居数: {Neighbors}", radius, neighbors);

        var width = pixelData.Width;
        var height = pixelData.Height;
        var data = ConvertToByteArray(pixelData);
        var lbpData = new byte[width * height];
        var histogram = new int[256];

        await Task.Run(() =>
        {
            for (int y = radius; y < height - radius; y++)
            {
                for (int x = radius; x < width - radius; x++)
                {
                    var centerIdx = y * width + x;
                    var centerValue = data[centerIdx];
                    byte lbpValue = 0;

                    for (int i = 0; i < neighbors; i++)
                    {
                        var angle = 2.0 * Math.PI * i / neighbors;
                        var nx = (int)Math.Round(x + radius * Math.Cos(angle));
                        var ny = (int)Math.Round(y + radius * Math.Sin(angle));

                        // 边界检查
                        nx = Math.Max(0, Math.Min(width - 1, nx));
                        ny = Math.Max(0, Math.Min(height - 1, ny));

                        var neighborValue = data[ny * width + nx];
                        if (neighborValue >= centerValue)
                        {
                            lbpValue |= (byte)(1 << i);
                        }
                    }

                    lbpData[centerIdx] = lbpValue;
                    histogram[lbpValue]++;
                }
            }
        });

        // 计算统计特征
        var totalPixels = (width - 2 * radius) * (height - 2 * radius);
        var normalizedHistogram = histogram.Select(h => (double)h / totalPixels).ToArray();

        return new LBPFeatures
        {
            Histogram = normalizedHistogram,
            Uniformity = ComputeUniformity(normalizedHistogram),
            Entropy = ComputeEntropy(normalizedHistogram),
            Energy = normalizedHistogram.Sum(p => p * p),
            Contrast = ComputeLBPContrast(normalizedHistogram),
            Variance = ComputeLBPVariance(normalizedHistogram)
        };
    }

    /// <summary>
    /// 计算旋转不变LBP特征
    /// </summary>
    public async Task<RotationInvariantLBPFeatures> ComputeRotationInvariantLBPAsync(PixelData pixelData, int radius = 1, int neighbors = 8)
    {
        _logger.LogInformation("计算旋转不变LBP特征");

        var width = pixelData.Width;
        var height = pixelData.Height;
        var data = ConvertToByteArray(pixelData);
        var riLbpData = new byte[width * height];
        var uniformPatterns = GenerateUniformPatterns(neighbors);
        var histogram = new int[uniformPatterns.Count + 1]; // +1 for non-uniform patterns

        await Task.Run(() =>
        {
            for (int y = radius; y < height - radius; y++)
            {
                for (int x = radius; x < width - radius; x++)
                {
                    var centerIdx = y * width + x;
                    var centerValue = data[centerIdx];
                    var pattern = new bool[neighbors];

                    // 计算二值模式
                    for (int i = 0; i < neighbors; i++)
                    {
                        var angle = 2.0 * Math.PI * i / neighbors;
                        var nx = (int)Math.Round(x + radius * Math.Cos(angle));
                        var ny = (int)Math.Round(y + radius * Math.Sin(angle));

                        nx = Math.Max(0, Math.Min(width - 1, nx));
                        ny = Math.Max(0, Math.Min(height - 1, ny));

                        var neighborValue = data[ny * width + nx];
                        pattern[i] = neighborValue >= centerValue;
                    }

                    // 找到旋转不变的最小模式
                    var minPattern = GetMinimalRotation(pattern);
                    var patternIndex = uniformPatterns.IndexOf(minPattern);
                    
                    if (patternIndex >= 0)
                    {
                        riLbpData[centerIdx] = (byte)patternIndex;
                        histogram[patternIndex]++;
                    }
                    else
                    {
                        // 非均匀模式
                        riLbpData[centerIdx] = (byte)uniformPatterns.Count;
                        histogram[uniformPatterns.Count]++;
                    }
                }
            }
        });

        var totalPixels = (width - 2 * radius) * (height - 2 * radius);
        var normalizedHistogram = histogram.Select(h => (double)h / totalPixels).ToArray();

        return new RotationInvariantLBPFeatures
        {
            Histogram = normalizedHistogram,
            UniformPatternCount = uniformPatterns.Count,
            NonUniformRatio = normalizedHistogram.Last(),
            Entropy = ComputeEntropy(normalizedHistogram),
            Energy = normalizedHistogram.Sum(p => p * p)
        };
    }

    #endregion

    #region Gabor 滤波器特征

    /// <summary>
    /// 计算Gabor滤波器特征
    /// </summary>
    public async Task<GaborFeatures> ComputeGaborFeaturesAsync(PixelData pixelData, double[] frequencies, double[] orientations, double sigma = 2.0)
    {
        _logger.LogInformation("计算Gabor特征，频率数: {FreqCount}, 方向数: {OrientCount}", frequencies.Length, orientations.Length);

        var width = pixelData.Width;
        var height = pixelData.Height;
        var data = ConvertToFloatArray(pixelData);
        var responses = new List<GaborResponse>();

        await Task.Run(() =>
        {
            foreach (var frequency in frequencies)
            {
                foreach (var orientation in orientations)
                {
                    var response = ApplyGaborFilter(data, width, height, frequency, orientation, sigma);
                    responses.Add(new GaborResponse
                    {
                        Frequency = frequency,
                        Orientation = orientation,
                        Mean = response.Average(),
                        StandardDeviation = ComputeStandardDeviation(response),
                        Energy = response.Sum(r => r * r),
                        Entropy = ComputeEntropy(response.Select(r => (double)Math.Abs(r)).ToArray())
                    });
                }
            }
        });

        return new GaborFeatures
        {
            Responses = responses,
            MeanEnergy = responses.Average(r => r.Energy),
            MeanEntropy = responses.Average(r => r.Entropy),
            EnergyVariance = ComputeVariance(responses.Select(r => r.Energy)),
            EntropyVariance = ComputeVariance(responses.Select(r => r.Entropy))
        };
    }

    #endregion

    #region 辅助方法

    private byte[] ConvertToByteArray(PixelData pixelData, int grayLevels = 256)
    {
        var data = pixelData.Data;
        var length = pixelData.Width * pixelData.Height;
        var result = new byte[length];

        if (data is byte[] byteArray)
        {
            Array.Copy(byteArray, result, length);
        }
        else if (data is float[] floatArray)
        {
            var min = floatArray.Min();
            var max = floatArray.Max();
            var range = max - min;
            
            for (int i = 0; i < length; i++)
            {
                var normalized = (floatArray[i] - min) / range;
                result[i] = (byte)Math.Min(grayLevels - 1, Math.Max(0, normalized * grayLevels));
            }
        }
        else if (data is ushort[] ushortArray)
        {
            var min = ushortArray.Min();
            var max = ushortArray.Max();
            var range = max - min;
            
            for (int i = 0; i < length; i++)
            {
                var normalized = (double)(ushortArray[i] - min) / range;
                result[i] = (byte)Math.Min(grayLevels - 1, Math.Max(0, normalized * grayLevels));
            }
        }

        return result;
    }

    private float[] ConvertToFloatArray(PixelData pixelData)
    {
        var data = pixelData.Data;
        var length = pixelData.Width * pixelData.Height;
        var result = new float[length];

        if (data is float[] floatArray)
        {
            Array.Copy(floatArray, result, length);
        }
        else if (data is byte[] byteArray)
        {
            for (int i = 0; i < length; i++)
                result[i] = byteArray[i] / 255.0f;
        }
        else if (data is ushort[] ushortArray)
        {
            var max = ushortArray.Max();
            for (int i = 0; i < length; i++)
                result[i] = ushortArray[i] / (float)max;
        }

        return result;
    }

    private GLCMFeatures ComputeGLCMStatistics(double[,] glcm, int grayLevels)
    {
        double contrast = 0, correlation = 0, energy = 0, homogeneity = 0, entropy = 0;
        double meanI = 0, meanJ = 0, stdI = 0, stdJ = 0;

        // 计算均值
        for (int i = 0; i < grayLevels; i++)
        {
            for (int j = 0; j < grayLevels; j++)
            {
                meanI += i * glcm[i, j];
                meanJ += j * glcm[i, j];
            }
        }

        // 计算标准差
        for (int i = 0; i < grayLevels; i++)
        {
            for (int j = 0; j < grayLevels; j++)
            {
                stdI += (i - meanI) * (i - meanI) * glcm[i, j];
                stdJ += (j - meanJ) * (j - meanJ) * glcm[i, j];
            }
        }
        stdI = Math.Sqrt(stdI);
        stdJ = Math.Sqrt(stdJ);

        // 计算特征
        for (int i = 0; i < grayLevels; i++)
        {
            for (int j = 0; j < grayLevels; j++)
            {
                var p = glcm[i, j];
                if (p > 0)
                {
                    contrast += (i - j) * (i - j) * p;
                    energy += p * p;
                    homogeneity += p / (1 + Math.Abs(i - j));
                    entropy -= p * Math.Log(p, 2);
                    
                    if (stdI > 0 && stdJ > 0)
                    {
                        correlation += ((i - meanI) * (j - meanJ) * p) / (stdI * stdJ);
                    }
                }
            }
        }

        return new GLCMFeatures
        {
            Contrast = contrast,
            Correlation = correlation,
            Energy = energy,
            Homogeneity = homogeneity,
            Entropy = entropy
        };
    }

    private double ComputeVariance(IEnumerable<double> values)
    {
        var mean = values.Average();
        return values.Average(v => (v - mean) * (v - mean));
    }

    private double ComputeEntropy(double[] histogram)
    {
        return -histogram.Where(p => p > 0).Sum(p => p * Math.Log(p, 2));
    }

    private double ComputeUniformity(double[] histogram)
    {
        return histogram.Sum(p => p * p);
    }

    private double ComputeLBPContrast(double[] histogram)
    {
        double contrast = 0;
        for (int i = 0; i < histogram.Length; i++)
        {
            for (int j = 0; j < histogram.Length; j++)
            {
                contrast += (i - j) * (i - j) * histogram[i] * histogram[j];
            }
        }
        return contrast;
    }

    private double ComputeLBPVariance(double[] histogram)
    {
        var mean = 0.0;
        for (int i = 0; i < histogram.Length; i++)
        {
            mean += i * histogram[i];
        }

        var variance = 0.0;
        for (int i = 0; i < histogram.Length; i++)
        {
            variance += (i - mean) * (i - mean) * histogram[i];
        }
        return variance;
    }

    private List<string> GenerateUniformPatterns(int neighbors)
    {
        var patterns = new List<string>();
        
        for (int i = 0; i < (1 << neighbors); i++)
        {
            var pattern = Convert.ToString(i, 2).PadLeft(neighbors, '0');
            if (IsUniformPattern(pattern))
            {
                patterns.Add(pattern);
            }
        }
        
        return patterns;
    }

    private bool IsUniformPattern(string pattern)
    {
        int transitions = 0;
        for (int i = 0; i < pattern.Length; i++)
        {
            int next = (i + 1) % pattern.Length;
            if (pattern[i] != pattern[next])
            {
                transitions++;
            }
        }
        return transitions <= 2;
    }

    private string GetMinimalRotation(bool[] pattern)
    {
        var minPattern = pattern;
        var minValue = PatternToString(pattern);

        for (int rotation = 1; rotation < pattern.Length; rotation++)
        {
            var rotated = new bool[pattern.Length];
            for (int i = 0; i < pattern.Length; i++)
            {
                rotated[i] = pattern[(i + rotation) % pattern.Length];
            }
            
            var rotatedString = PatternToString(rotated);
            if (string.Compare(rotatedString, minValue) < 0)
            {
                minValue = rotatedString;
                minPattern = rotated;
            }
        }

        return minValue;
    }

    private string PatternToString(bool[] pattern)
    {
        return string.Join("", pattern.Select(b => b ? "1" : "0"));
    }

    private float[] ApplyGaborFilter(float[] data, int width, int height, double frequency, double orientation, double sigma)
    {
        var result = new float[width * height];
        int kernelSize = (int)(6 * sigma);
        if (kernelSize % 2 == 0) kernelSize++;
        int radius = kernelSize / 2;

        // 生成Gabor核
        var kernel = GenerateGaborKernel(kernelSize, frequency, orientation, sigma);

        for (int y = radius; y < height - radius; y++)
        {
            for (int x = radius; x < width - radius; x++)
            {
                float sum = 0;
                
                for (int ky = -radius; ky <= radius; ky++)
                {
                    for (int kx = -radius; kx <= radius; kx++)
                    {
                        var pixelValue = data[(y + ky) * width + (x + kx)];
                        var kernelValue = kernel[ky + radius, kx + radius];
                        sum += pixelValue * kernelValue;
                    }
                }
                
                result[y * width + x] = sum;
            }
        }

        return result;
    }

    private float[,] GenerateGaborKernel(int size, double frequency, double orientation, double sigma)
    {
        var kernel = new float[size, size];
        int center = size / 2;
        double cosTheta = Math.Cos(orientation);
        double sinTheta = Math.Sin(orientation);

        for (int y = 0; y < size; y++)
        {
            for (int x = 0; x < size; x++)
            {
                double xPrime = (x - center) * cosTheta + (y - center) * sinTheta;
                double yPrime = -(x - center) * sinTheta + (y - center) * cosTheta;
                
                double gaussian = Math.Exp(-(xPrime * xPrime + yPrime * yPrime) / (2 * sigma * sigma));
                double sinusoid = Math.Cos(2 * Math.PI * frequency * xPrime);
                
                kernel[y, x] = (float)(gaussian * sinusoid);
            }
        }

        return kernel;
    }

    private double ComputeStandardDeviation(float[] data)
    {
        var mean = data.Average();
        return Math.Sqrt(data.Average(x => (x - mean) * (x - mean)));
    }

    #endregion
}
