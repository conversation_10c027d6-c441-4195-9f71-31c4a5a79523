#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试有标签的图像
"""

import os
import sys
import cv2
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from services.detection_service import DetectionService
from config import Config

# 配置参数
TEST_IMAGE_DIR = r"E:\Trae\yolo_ohif\yolo11x_training_output\yolo_dataset\yolo_dataset\images\test"
TEST_LABEL_DIR = r"E:\Trae\yolo_ohif\yolo11x_training_output\yolo_dataset\yolo_dataset\labels\test"
MODEL_PATH = r"E:\Trae\yolo_ohif\yolo11x_training_output\training_results\yolo11x_from_scratch_20250719_135134\weights\best.pt"

def test_labeled_images():
    """
    测试有标签的图像
    """
    print("=" * 60)
    print("测试有标签的图像")
    print("=" * 60)
    
    # 初始化检测服务
    detection_service = DetectionService(
        model_path=MODEL_PATH,
        confidence_threshold=0.1,  # 使用低置信度阈值
        iou_threshold=Config.YOLO.IOU_THRESHOLD
    )
    
    print(f"模型路径: {MODEL_PATH}")
    print(f"置信度阈值: 0.1")
    print(f"IoU阈值: {Config.YOLO.IOU_THRESHOLD}")
    
    # 获取所有标签文件
    label_files = list(Path(TEST_LABEL_DIR).glob("*.txt"))
    print(f"\n找到 {len(label_files)} 个标签文件")
    
    if not label_files:
        print("错误: 没有找到标签文件")
        return
    
    # 测试前10个有标签的图像
    test_count = min(10, len(label_files))
    print(f"将测试前 {test_count} 个有标签的图像")
    
    results = []
    
    for i, label_file in enumerate(label_files[:test_count]):
        # 构造对应的图像文件路径
        image_name = label_file.stem + ".jpg"
        image_path = Path(TEST_IMAGE_DIR) / image_name
        
        print(f"\n处理图像 {i+1}/{test_count}: {image_name}")
        
        if not image_path.exists():
            print(f"  错误: 图像文件不存在: {image_path}")
            continue
        
        # 读取标签文件
        try:
            with open(label_file, 'r') as f:
                labels = f.readlines()
            ground_truth_count = len([line.strip() for line in labels if line.strip()])
            print(f"  标签文件中的目标数量: {ground_truth_count}")
        except Exception as e:
            print(f"  错误: 无法读取标签文件: {e}")
            continue
        
        # 读取图像
        image = cv2.imread(str(image_path))
        if image is None:
            print(f"  错误: 无法读取图像")
            continue
        
        print(f"  图像尺寸: {image.shape}")
        
        # 进行检测
        try:
            detections = detection_service._detect_image(image)
            print(f"  检测到 {len(detections)} 个目标")
            
            # 显示检测结果
            if detections:
                for j, det in enumerate(detections):
                    print(f"    目标 {j+1}: 类别={det['class_name']}, 置信度={det['confidence']:.3f}, "
                          f"位置=({det['x']:.0f}, {det['y']:.0f}, {det['width']:.0f}, {det['height']:.0f})")
            else:
                print("    未检测到任何目标")
            
            results.append({
                'image_name': image_name,
                'ground_truth_count': ground_truth_count,
                'detection_count': len(detections),
                'detections': detections
            })
            
        except Exception as e:
            print(f"  检测失败: {str(e)}")
    
    # 统计结果
    print("\n" + "=" * 60)
    print("测试结果统计")
    print("=" * 60)
    
    if results:
        total_ground_truth = sum(r['ground_truth_count'] for r in results)
        total_detections = sum(r['detection_count'] for r in results)
        images_with_detections = len([r for r in results if r['detection_count'] > 0])
        
        print(f"总测试图像数: {len(results)}")
        print(f"标签中总目标数: {total_ground_truth}")
        print(f"检测到总目标数: {total_detections}")
        print(f"有检测结果的图像数: {images_with_detections}")
        print(f"检测率: {total_detections/total_ground_truth*100:.1f}%" if total_ground_truth > 0 else "检测率: N/A")
        
        print("\n详细结果:")
        for result in results:
            print(f"  {result['image_name']}: 标签={result['ground_truth_count']}, 检测={result['detection_count']}")
    else:
        print("没有成功处理的图像")

if __name__ == "__main__":
    test_labeled_images()