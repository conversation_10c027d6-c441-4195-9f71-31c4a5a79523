# 医学影像解析系统 (Medical Image Analysis System)

基于 .NET 8 的现代化医学影像解析系统，专为 DICOM 文件处理和医学影像分析优化。提供功能完整的 WPF 桌面应用程序。

## 🚀 主要特性

- **高精度 DICOM 处理**: 使用 fo-dicom 库实现精准的 DICOM 文件解析和元数据提取
- **智能影像处理**: 基于 ImageSharp 的高性能影像预处理和增强
- **原生 WPF 界面**: 功能完整的桌面应用程序，支持 DICOM 查看、标注和处理
- **智能标注系统**: 适配多种医学影像类型的手动和自动标注功能
- **现代化架构**: 采用清洁架构和 DDD 设计模式
- **高性能**: 异步处理和内存优化
- **易于使用**: 一键启动的桌面应用程序

## 🏗️ 系统架构

```
src/
├── MedicalImageAnalysis.Core/          # 核心业务逻辑和领域模型
├── MedicalImageAnalysis.Application/   # 应用服务和用例
├── MedicalImageAnalysis.Infrastructure/ # 基础设施和外部依赖
└── MedicalImageAnalysis.Wpf/           # WPF 桌面应用程序 (主要界面)

data/
├── logs/                               # 应用程序日志
├── temp/                               # 临时文件
├── output/                             # 输出文件
└── models/                             # AI 模型文件

Brain/                                  # 示例 DICOM 文件
```

## 🛠️ 技术栈

- **.NET 8**: 最新 LTS 框架
- **WPF**: Windows 桌面应用程序框架
- **fo-dicom**: DICOM 文件处理
- **ImageSharp**: 高性能图像处理
- **ML.NET + ONNX**: 机器学习和模型集成 (开发中)
- **Entity Framework Core**: ORM
- **SQLite**: 本地数据存储
- **Serilog**: 结构化日志
- **Microsoft.Extensions.DependencyInjection**: 依赖注入
- **Microsoft.Extensions.Configuration**: 配置管理

## 📋 系统要求

### 运行环境
- **操作系统**: Windows 10/11 (x64)
- **.NET Runtime**: .NET 8.0 或更高版本
- **内存**: 至少 4GB RAM (推荐 8GB)
- **存储**: 至少 2GB 可用空间

### 开发环境
- **.NET 8 SDK**
- **Visual Studio 2022** 或 **VS Code**
- **Git** (版本控制)
- **支持 CUDA 的 GPU** (可选，用于 AI 训练)

## 🚀 快速开始

### 启动桌面应用程序

#### 1. 系统要求
- Windows 10/11 (x64)
- .NET 8 Runtime 或 SDK
- 至少 4GB RAM

#### 2. 启动方式

**方式一：使用批处理文件 (推荐)**
```cmd
启动桌面端应用.bat
```

**方式二：使用 PowerShell**
```powershell
.\启动桌面端应用.ps1
```

**方式三：直接运行可执行文件**
```cmd
.\src\MedicalImageAnalysis.Wpf\bin\Debug\net8.0-windows\MedicalImageAnalysis.Wpf.exe
```

**方式四：手动构建和运行**
```bash
# 构建项目
dotnet build src/MedicalImageAnalysis.Wpf/MedicalImageAnalysis.Wpf.csproj

# 运行应用程序
dotnet run --project src/MedicalImageAnalysis.Wpf
```

#### 3. 应用程序功能
- **DICOM 查看器**: 加载和查看医学影像，支持窗宽窗位调整
- **影像处理**: 图像增强、滤波、格式转换等功能
- **智能标注**: 手动标注工具，支持多种标注类型
- **模型训练**: AI 模型训练和管理界面
- **统计分析**: 数据统计和可视化功能

### 开发环境设置

#### 1. 安装依赖
```bash
dotnet restore
```

#### 2. 构建项目
```bash
# 构建整个解决方案
dotnet build MedicalImageAnalysis.sln

# 或者只构建 WPF 应用
dotnet build src/MedicalImageAnalysis.Wpf
```

#### 3. 运行应用
```bash
dotnet run --project src/MedicalImageAnalysis.Wpf
```

## 📚 功能模块

### WPF 桌面应用程序
- ✅ **现代化界面**: 基于 .NET 8 和 WPF 的原生桌面应用
- ✅ **功能导航**: 首页、DICOM上传、GDCM查看、影像处理等模块
- ✅ **DICOM 查看器**: 完整的医学影像显示和操作功能
- ✅ **图像处理**: 窗宽窗位调整、缩放、导出等功能
- ✅ **智能标注**: 支持多种标注工具和类型
- ✅ **模型训练**: AI 模型训练和管理界面
- ✅ **统计分析**: 数据统计和可视化功能

## 📖 详细教程

### 📚 教程总览
完整的智能标注和模型训练教程体系：

📄 **[教程使用说明.md](教程使用说明.md)** - 教程使用指南和学习路线图

### 🎯 智能标注教程
详细的智能标注功能使用指南，包含：
- **基础操作**: 图像加载、工具使用、标注创建
- **AI辅助**: 自动标注生成、智能建议、质量控制
- **实战案例**: 肺部结节、骨折、脑部病灶标注示例
- **高级技巧**: 批量操作、快捷键、工作流程优化

📄 **查看教程**: [智能打标教程.md](智能打标教程.md)

### 🏋️ 模型训练教程
完整的AI模型训练指南，包含：
- **环境配置**: Python、PyTorch、YOLO环境搭建
- **数据准备**: 数据集创建、格式转换、质量检查
- **训练流程**: WPF界面训练、Python脚本训练
- **模型优化**: 参数调优、性能分析、部署方案

📄 **查看教程**: [模型训练教程.md](模型训练教程.md)

### 🚀 快速入门指南
5分钟快速上手指南，包含：
- **快速开始**: 应用启动、基础操作、示例演示
- **操作指南**: 界面布局、快捷键、常用功能
- **问题解决**: 常见问题、故障排除、技术支持

📄 **查看指南**: [快速入门指南.md](快速入门指南.md)

### 🔧 环境验证工具
自动化环境检查脚本：
```bash
# 验证训练环境是否正确配置
python 验证训练环境.py
```

该脚本会检查：
- Python版本和依赖包
- GPU支持和CUDA环境
- 数据集结构和训练脚本
- .NET环境和WPF项目

### 🎓 学习路径推荐
1. **新手用户**: 教程使用说明 → 快速入门 → 智能标注 → 模型训练
2. **专业用户**: 环境验证 → 智能标注 → 模型训练 → 高级应用

### DICOM 处理 (核心功能)
- ✅ **标准格式支持**: 支持所有标准 DICOM 格式
- ✅ **元数据提取**: 完整的 DICOM 标签解析和显示
- ✅ **图像加载显示**: 基于 fo-dicom 的高质量图像渲染
- ✅ **标签查看**: 完整的 DICOM 标签信息查看
- ✅ **影像方向检测**: 自动识别轴位/矢状位/冠状位
- 🚧 **Hounsfield 单位转换**: 精确的 CT 值计算 (开发中)
- 🚧 **多序列支持**: 复杂多序列研究处理 (开发中)

### 影像处理
- ✅ **基础显示**: 窗宽窗位调整、缩放、平移
- ✅ **DICOM 渲染**: 高质量的医学影像显示
- ✅ **格式转换**: DICOM → PNG/JPEG/BMP/TIFF 等
- ✅ **图像导出**: 支持多种格式的图像导出
- 🚧 **高级滤波**: 高斯、中值、双边滤波 (开发中)
- 🚧 **图像增强**: 直方图均衡化、对比度调整 (开发中)

### AI 模型集成
- 🚧 **模型集成**: 支持多种 AI 模型的集成 (架构完成)
- 🚧 **模型训练**: 自定义数据集训练界面 (开发中)
- 🚧 **推理引擎**: 实时推理和批量处理 (开发中)
- 🚧 **模型管理**: 版本控制和性能评估 (开发中)

### 智能标注系统
- ✅ **手动标注**: 支持多种标注工具
- ✅ **DICOM 支持**: 完整支持 DICOM 文件标注
- ✅ **图像加载**: 支持 DICOM 和标准图像格式
- ✅ **AI 框架**: 基础 AI 推理框架完成
- 🚧 **自动标注**: AI 辅助标注生成 (开发中)
- 🚧 **格式支持**: YOLO、COCO、Pascal VOC 等 (开发中)
- 🚧 **质量验证**: 标注质量检查 (开发中)

### 数据管理
- ✅ **文件组织**: 项目和文件管理
- ✅ **导入导出**: 支持多种文件格式
- ✅ **日志系统**: 完整的操作日志记录
- ✅ **配置存储**: 用户设置和偏好保存

## 📖 使用指南

### 1. 启动应用程序

**方法一：使用批处理文件 (推荐)**
```cmd
# 双击运行或在命令行执行
启动桌面端应用.bat
```

**方法二：使用 PowerShell**
```powershell
# 在 PowerShell 中执行
.\启动桌面端应用.ps1
```

**方法三：直接运行可执行文件**
```cmd
.\src\MedicalImageAnalysis.Wpf\bin\Debug\net8.0-windows\MedicalImageAnalysis.Wpf.exe
```

**方法四：手动构建和运行**
```bash
# 构建项目
dotnet build src/MedicalImageAnalysis.Wpf

# 运行应用程序
dotnet run --project src/MedicalImageAnalysis.Wpf
```

### 2. 加载 DICOM 文件

1. 启动应用程序后，点击左侧导航栏的 **"GDCM 查看"** 或 **"DICOM上传"**
2. 使用 **"打开DICOM文件"** 按钮选择 DICOM 文件
3. 应用程序将自动加载并显示 DICOM 图像和相关信息

### 3. 使用示例数据

项目包含示例 DICOM 文件：
```
Brain/
├── DJ01.dcm
├── DJ02.dcm
├── DJ03.dcm
└── ... (更多文件)
```

### 4. 使用标注功能

1. 在 DICOM 查看器中加载影像
2. 点击 **"影像处理"** 或 **"单独处理"** 模块
3. 选择标注工具进行手动标注
4. 保存标注结果

### 5. 查看日志

应用程序日志保存在：
```
src/MedicalImageAnalysis.Wpf/bin/Debug/net8.0-windows/logs/
└── medical-image-analysis-YYYYMMDD.txt
```

## 🧪 测试和验证

### 构建测试
```bash
# 测试核心模块构建
dotnet build src/MedicalImageAnalysis.Core

# 测试 WPF 应用构建
dotnet build src/MedicalImageAnalysis.Wpf

# 构建整个解决方案
dotnet build MedicalImageAnalysis.sln
```

### 功能测试
1. **启动测试**: 确认应用程序能正常启动
2. **DICOM 加载测试**: 使用 Brain/ 目录中的示例文件
3. **界面响应测试**: 测试各个功能模块的界面
4. **日志检查**: 查看 logs/ 目录中的日志文件

### 故障排除

**应用程序无法启动**
- 检查是否安装了 .NET 8 Runtime
- 查看日志文件中的错误信息
- 确认所有依赖项已正确安装

**DICOM 文件无法加载**
- 确认文件格式为标准 DICOM (.dcm)
- 检查文件是否损坏
- 查看应用程序日志中的错误信息

**性能问题**
- 确保系统有足够的可用内存
- 关闭不必要的后台程序
- 检查磁盘空间是否充足

## 🔧 配置说明

### 环境变量
- `ASPNETCORE_ENVIRONMENT`: 运行环境 (Development/Production)
- `ASPNETCORE_URLS`: 监听地址
- `Serilog__MinimumLevel__Default`: 日志级别

### 配置文件
主要配置在 `src/MedicalImageAnalysis.Wpf/appsettings.json` 中：

```json
{
  "MedicalImageAnalysis": {
    "MaxFileSize": 524288000,
    "SupportedFormats": ["dcm", "dicom"],
    "Processing": {
      "DefaultConfidenceThreshold": 0.5,
      "DefaultIouThreshold": 0.45
    }
  }
}
```

## 🏗️ 详细项目结构

```
medical-imaging/
├── src/                                    # 源代码目录
│   ├── MedicalImageAnalysis.Core/          # 核心业务逻辑层
│   │   ├── Entities/                       # 领域实体
│   │   ├── Interfaces/                     # 接口定义
│   │   ├── Models/                         # 数据模型
│   │   └── Data/                           # 数据访问
│   ├── MedicalImageAnalysis.Application/   # 应用服务层
│   │   └── Services/                       # 应用服务
│   ├── MedicalImageAnalysis.Infrastructure/ # 基础设施层
│   │   ├── Services/                       # 服务实现
│   │   ├── Algorithms/                     # 算法实现
│   │   └── Extensions/                     # 扩展方法
│   └── MedicalImageAnalysis.Wpf/           # WPF 桌面应用程序
│       ├── Views/                          # 用户界面视图
│       ├── Windows/                        # 窗口定义
│       ├── Services/                       # 应用服务
│       ├── Converters/                     # 数据转换器
│       └── Properties/                     # 应用程序属性
├── Brain/                                  # 示例 DICOM 文件
│   ├── DJ01.dcm ~ DJ10.dcm                 # 脑部 CT 示例
├── data/                                   # 数据目录
│   ├── logs/                               # 应用程序日志
│   ├── temp/                               # 临时文件
│   ├── output/                             # 处理结果
│   └── models/                             # AI 模型文件
├── logs/                                   # 运行时日志
├── scripts/                                # 辅助脚本
├── yolo_ohif/                              # YOLO + OHIF 集成
├── 启动桌面端应用.bat                        # Windows 启动脚本
├── 启动桌面端应用.ps1                        # PowerShell 启动脚本
├── MedicalImageAnalysis.sln                # Visual Studio 解决方案
├── docker-compose.yml                      # Docker 配置
└── README.md                               # 项目文档
```

## 🎯 核心特性

### 1. DICOM 处理引擎
- **完整的 DICOM 支持**: 基于 fo-dicom 库，支持所有标准 DICOM 格式
- **智能解析**: 自动检测影像方向 (轴位/矢状位/冠状位)
- **元数据提取**: 完整的 DICOM 标签解析和验证
- **Hounsfield 单位转换**: 精确的 CT 值计算
- **多序列支持**: 支持复杂的多序列研究

### 2. 高性能影像处理
- **多种滤波算法**: 高斯、中值、双边、非局部均值等
- **自适应增强**: 直方图均衡化、CLAHE、伽马校正
- **多平面重建**: MPR 功能，支持任意角度重建
- **格式转换**: 支持 PNG、JPEG、BMP、TIFF、WebP 等格式
- **实时预览**: 快速生成缩略图和预览

### 3. AI 模型集成
- **模型训练**: 完整的训练流程，支持自定义数据集
- **实时推理**: 高性能推理引擎，支持批量处理
- **模型管理**: 模型版本控制、性能评估、格式转换
- **数据增强**: 多种增强策略，提升模型泛化能力

### 4. 智能标注系统
- **自动标注**: AI 驱动的智能标注生成
- **多格式支持**: YOLO、COCO、Pascal VOC、CVAT、LabelMe
- **质量验证**: 标注质量检查和异常检测
- **标注优化**: 边界框优化和重叠处理

### 5. 现代化桌面界面
- **原生 WPF**: 基于 .NET 8 的现代化桌面应用
- **直观操作**: 友好的用户界面和交互体验
- **功能完整**: 涵盖 DICOM 查看、处理、标注等全流程
- **高性能**: 原生应用程序，响应速度快

## 🔬 技术亮点

### 架构设计
- **清洁架构**: 分层设计，职责分离，易于维护和扩展
- **依赖注入**: 基于 .NET 8 的原生 DI 容器
- **异步编程**: 全面采用 async/await 模式，提升性能
- **错误处理**: 统一的异常处理和日志记录

### 性能优化
- **内存管理**: 优化的内存使用，支持大文件处理
- **并发处理**: 支持多线程并发处理
- **缓存策略**: 智能缓存机制，提升响应速度
- **流式处理**: 大文件流式处理，降低内存占用

### 安全性
- **输入验证**: 严格的输入验证和文件类型检查
- **错误隐藏**: 生产环境下隐藏敏感错误信息
- **资源限制**: 文件大小和处理时间限制
- **日志审计**: 完整的操作日志记录

## � 部署和分发

### 桌面应用程序部署
桌面应用程序可以通过以下方式部署：

1. **直接运行**: 使用已编译的可执行文件
2. **自包含部署**: 包含 .NET 运行时的独立应用
3. **安装包**: 创建 MSI 或其他安装包格式

### 创建自包含部署
```bash
# 发布为自包含应用程序
dotnet publish src/MedicalImageAnalysis.Wpf -c Release -r win-x64 --self-contained

# 输出目录
src/MedicalImageAnalysis.Wpf/bin/Release/net8.0-windows/win-x64/publish/
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如有问题或建议，请创建 [Issue](../../issues) 或联系开发团队。

## 🔄 版本历史

- **v1.0.0** - 当前版本 (开发中)
  - ✅ 完整的 WPF 桌面应用程序
  - ✅ DICOM 文件加载和显示
  - ✅ 图像处理和标注功能
  - ✅ 多模块功能界面
  - ✅ 系统监控和日志
  - 🚧 AI 模型集成 (开发中)
  - 🚧 高级图像处理算法 (开发中)

## 📝 开发状态

### 已完成功能
- ✅ 完整的 WPF 桌面应用程序
- ✅ DICOM 文件处理和显示
- ✅ 多模块功能界面（首页、DICOM上传、GDCM查看等）
- ✅ 图像处理和标注工具
- ✅ 日志系统和配置管理
- ✅ 清洁的项目架构设计

### 开发中功能
- 🚧 AI 模型训练和推理
- 🚧 高级图像处理算法
- 🚧 数据导入导出功能
- 🚧 更多标注工具和格式支持

### 计划功能
- 📋 更多 AI 模型集成
- 📋 批量处理功能
- 📋 插件系统
- 📋 多语言支持

---

**重要提示**:
- 本系统目前处于开发阶段，部分功能可能不完整
- 仅用于研究和教育目的，不应用于临床诊断
- 使用前请仔细阅读文档和测试功能
