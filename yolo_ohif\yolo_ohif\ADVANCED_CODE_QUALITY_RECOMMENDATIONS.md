# 高级代码质量与可维护性提升建议

## 🚨 当前问题分析

### 文件访问冲突问题 (WinError 32)

**问题描述**: 
```
[WinError 32] 另一个程序正在使用此文件，进程无法访问。
```

**根本原因**:
1. **文件句柄未正确关闭**: 可能存在文件操作后未及时关闭文件句柄
2. **并发访问冲突**: 多个进程或线程同时访问同一文件
3. **杀毒软件干扰**: 实时扫描可能锁定文件
4. **系统缓存问题**: Windows文件系统缓存延迟释放

## 🛠️ 立即解决方案

### 1. 文件操作安全性改进 ✅ 已实现

**实现状态**: 已在 `create_yolo_dataset.py` 中成功实现安全文件写入机制

**实现详情**:
- ✅ 在 `YOLODatasetCreator` 类中添加了 `safe_file_operation` 上下文管理器
- ✅ 实现了 `safe_write_file` 方法，使用原子性操作和重试机制
- ✅ 对标签文件(.txt)和YAML配置文件都应用了安全写入
- ✅ 添加了文件操作重试配置（默认3次重试，0.5秒延迟）

**测试验证**: 运行 `python test_file_safety.py` 验证修复效果

```python
import time
import os
from pathlib import Path
import psutil
from contextlib import contextmanager

@contextmanager
def safe_file_operation(file_path, max_retries=5, delay=1.0):
    """
    安全的文件操作上下文管理器 ✅ 已实现
    """
    for attempt in range(max_retries):
        try:
            yield file_path
            break
        except (PermissionError, OSError) as e:
            if attempt == max_retries - 1:
                raise
            logger.warning(f"文件操作失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            time.sleep(delay * (attempt + 1))  # 指数退避

def force_close_file_handles(file_path):
    """
    强制关闭文件句柄
    """
    try:
        for proc in psutil.process_iter(['pid', 'name', 'open_files']):
            try:
                for file_info in proc.info['open_files'] or []:
                    if file_info.path == str(file_path):
                        logger.warning(f"发现进程 {proc.info['name']} (PID: {proc.info['pid']}) 正在使用文件")
                        # 可选择性终止进程或等待
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    except Exception as e:
        logger.error(f"检查文件句柄失败: {e}")
```

### 2. 改进的文件写入机制 ✅ 已实现

```python
def safe_write_file(file_path, content, encoding='utf-8'):
    """
    安全的文件写入函数 ✅ 已实现
    """
    file_path = Path(file_path)
    temp_path = file_path.with_suffix(file_path.suffix + '.tmp')
    
    try:
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入临时文件
        with safe_file_operation(temp_path):
            with open(temp_path, 'w', encoding=encoding) as f:
                f.write(content)
                f.flush()  # 强制刷新缓冲区
                os.fsync(f.fileno())  # 强制写入磁盘
        
        # 原子性重命名
        if file_path.exists():
            backup_path = file_path.with_suffix(file_path.suffix + '.bak')
            file_path.rename(backup_path)
        
        temp_path.rename(file_path)
        
        # 清理备份文件
        if backup_path.exists():
            backup_path.unlink()
            
    except Exception as e:
        # 清理临时文件
        if temp_path.exists():
            temp_path.unlink()
        raise
```

## 🏗️ 架构级改进建议

### 1. 配置管理系统

```python
# config/training_config.py
from dataclasses import dataclass, field
from typing import Dict, Any, Optional
import yaml
from pathlib import Path

@dataclass
class TrainingConfig:
    """训练配置类"""
    # 基础配置
    model_name: str = 'yolo11x'
    epochs: int = 200
    batch_size: int = 16
    learning_rate: float = 0.01
    img_size: int = 640
    
    # 路径配置
    dataset_root: str = './dataset'
    output_root: str = './yolo11x_training_output'
    
    # 设备配置
    device: str = 'auto'
    workers: int = 4
    
    # 文件操作配置
    file_retry_count: int = 5
    file_retry_delay: float = 1.0
    temp_cleanup: bool = True
    
    # 高级配置
    advanced_options: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def from_yaml(cls, config_path: str) -> 'TrainingConfig':
        """从YAML文件加载配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        return cls(**data)
    
    def to_yaml(self, config_path: str):
        """保存配置到YAML文件"""
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.__dict__, f, default_flow_style=False, allow_unicode=True)
    
    def validate(self) -> bool:
        """验证配置有效性"""
        if self.epochs <= 0:
            raise ValueError("epochs必须大于0")
        if self.batch_size <= 0:
            raise ValueError("batch_size必须大于0")
        if not Path(self.dataset_root).exists():
            raise ValueError(f"数据集路径不存在: {self.dataset_root}")
        return True
```

### 2. 错误处理与重试机制

```python
# utils/error_handling.py
import functools
import time
import logging
from typing import Callable, Type, Tuple, Any

def retry_on_error(
    exceptions: Tuple[Type[Exception], ...] = (Exception,),
    max_retries: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    logger: Optional[logging.Logger] = None
):
    """
    重试装饰器
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt == max_retries:
                        break
                    
                    wait_time = delay * (backoff ** attempt)
                    if logger:
                        logger.warning(
                            f"函数 {func.__name__} 执行失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}"
                            f"等待 {wait_time:.1f}s 后重试..."
                        )
                    time.sleep(wait_time)
            
            raise last_exception
        return wrapper
    return decorator

class ErrorContext:
    """错误上下文管理器"""
    
    def __init__(self, operation_name: str, logger: logging.Logger):
        self.operation_name = operation_name
        self.logger = logger
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.logger.info(f"开始执行: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        
        if exc_type is None:
            self.logger.info(f"✅ {self.operation_name} 完成 (耗时: {duration:.2f}s)")
        else:
            self.logger.error(
                f"❌ {self.operation_name} 失败 (耗时: {duration:.2f}s): {exc_val}"
            )
        
        return False  # 不抑制异常
```

### 3. 性能监控与资源管理

```python
# utils/performance_monitor.py
import psutil
import time
import threading
from dataclasses import dataclass
from typing import Dict, List, Optional

@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_available_gb: float
    gpu_memory_used_gb: Optional[float] = None
    gpu_memory_total_gb: Optional[float] = None
    disk_usage_percent: float = 0.0

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, interval: float = 5.0):
        self.interval = interval
        self.metrics: List[SystemMetrics] = []
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                metrics = self._collect_metrics()
                self.metrics.append(metrics)
                
                # 保持最近1000个数据点
                if len(self.metrics) > 1000:
                    self.metrics = self.metrics[-1000:]
                
                time.sleep(self.interval)
            except Exception as e:
                logger.error(f"性能监控错误: {e}")
    
    def _collect_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        memory = psutil.virtual_memory()
        
        metrics = SystemMetrics(
            timestamp=time.time(),
            cpu_percent=psutil.cpu_percent(),
            memory_percent=memory.percent,
            memory_available_gb=memory.available / 1024**3,
            disk_usage_percent=psutil.disk_usage('/').percent
        )
        
        # GPU指标（如果可用）
        try:
            import torch
            if torch.cuda.is_available():
                gpu_memory = torch.cuda.memory_stats()
                metrics.gpu_memory_used_gb = gpu_memory['allocated_bytes.all.current'] / 1024**3
                metrics.gpu_memory_total_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
        except:
            pass
        
        return metrics
    
    def get_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics:
            return {}
        
        cpu_values = [m.cpu_percent for m in self.metrics]
        memory_values = [m.memory_percent for m in self.metrics]
        
        return {
            'monitoring_duration_minutes': (self.metrics[-1].timestamp - self.metrics[0].timestamp) / 60,
            'cpu_usage': {
                'avg': sum(cpu_values) / len(cpu_values),
                'max': max(cpu_values),
                'min': min(cpu_values)
            },
            'memory_usage': {
                'avg': sum(memory_values) / len(memory_values),
                'max': max(memory_values),
                'min': min(memory_values)
            },
            'sample_count': len(self.metrics)
        }
```

### 4. 单元测试框架

```python
# tests/test_training_pipeline.py
import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

class TestTrainingPipeline:
    """训练流水线测试"""
    
    @pytest.fixture
    def temp_workspace(self):
        """临时工作空间"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_dataset(self, temp_workspace):
        """模拟数据集"""
        dataset_dir = temp_workspace / 'dataset'
        for subdir in ['image_T2', 'image_T2_normal', 'label_T2']:
            (dataset_dir / subdir).mkdir(parents=True)
            # 创建模拟文件
            for i in range(3):
                (dataset_dir / subdir / f'test_{i}.nii.gz').touch()
        return dataset_dir
    
    def test_dataset_detection(self, temp_workspace, mock_dataset):
        """测试数据集检测功能"""
        from train_yolo11x_from_scratch import YOLO11xTrainer
        
        trainer = YOLO11xTrainer(
            dataset_root=str(mock_dataset),
            output_root=str(temp_workspace / 'output')
        )
        
        # 测试不存在数据集的情况
        result = trainer.check_existing_dataset()
        assert result is None
    
    def test_device_detection(self):
        """测试设备检测功能"""
        from train_yolo11x_from_scratch import YOLO11xTrainer
        
        trainer = YOLO11xTrainer(
            dataset_root='./test',
            output_root='./test_output'
        )
        
        device = trainer._detect_device()
        assert device in ['cpu', '0', 'cuda', 'cuda:0']
    
    @patch('torch.cuda.is_available', return_value=False)
    def test_cpu_fallback(self, mock_cuda):
        """测试CPU回退机制"""
        from train_yolo11x_from_scratch import YOLO11xTrainer
        
        trainer = YOLO11xTrainer(
            dataset_root='./test',
            output_root='./test_output'
        )
        
        device = trainer._detect_device()
        assert device == 'cpu'
    
    def test_file_safety_operations(self, temp_workspace):
        """测试文件安全操作"""
        from utils.file_operations import safe_write_file
        
        test_file = temp_workspace / 'test.txt'
        test_content = 'Hello, World!'
        
        # 测试正常写入
        safe_write_file(test_file, test_content)
        assert test_file.exists()
        assert test_file.read_text() == test_content
        
        # 测试覆盖写入
        new_content = 'Updated content'
        safe_write_file(test_file, new_content)
        assert test_file.read_text() == new_content
```

## 🔧 CI/CD 集成建议

### 1. GitHub Actions 工作流

```yaml
# .github/workflows/quality-check.yml
name: Code Quality Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  quality-check:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest black flake8 mypy
    
    - name: Code formatting check
      run: black --check .
    
    - name: Linting
      run: flake8 .
    
    - name: Type checking
      run: mypy .
    
    - name: Run tests
      run: pytest tests/ -v --cov=.
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
```

### 2. 代码质量工具配置

```ini
# setup.cfg
[flake8]
max-line-length = 88
extend-ignore = E203, W503
exclude = __pycache__, .git, build, dist

[mypy]
python_version = 3.11
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True

[tool:pytest]
testpaths = tests
addopts = --strict-markers --disable-warnings
markers =
    slow: marks tests as slow
    integration: marks tests as integration tests
```

## 📊 监控与日志改进

### 1. 结构化日志

```python
# utils/structured_logging.py
import json
import logging
from datetime import datetime
from typing import Dict, Any

class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry, ensure_ascii=False)

def setup_structured_logging(log_file: str = 'training.jsonl'):
    """设置结构化日志"""
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    
    # 文件处理器（JSON格式）
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(StructuredFormatter())
    
    # 控制台处理器（人类可读格式）
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(
        logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    )
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger
```

## 🚀 部署与运维建议

### 1. Docker 多阶段构建

```dockerfile
# Dockerfile.production
FROM python:3.11-slim as base

# 系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 生产阶段
FROM base as production

WORKDIR /app
COPY . .

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import torch; print('OK')" || exit 1

CMD ["python", "start_yolo11x_training.py"]
```

### 2. 资源限制与监控

```yaml
# docker-compose.production.yml
version: '3.8'

services:
  yolo-training:
    build:
      context: .
      dockerfile: Dockerfile.production
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 16G
        reservations:
          cpus: '2.0'
          memory: 8G
    volumes:
      - ./dataset:/app/dataset:ro
      - ./output:/app/output
      - ./logs:/app/logs
    environment:
      - CUDA_VISIBLE_DEVICES=0
    restart: unless-stopped
    
  monitoring:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
```

## 📈 性能优化建议

### 1. 数据加载优化

```python
# utils/data_loader_optimization.py
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from typing import List, Callable, Any

class OptimizedDataLoader:
    """优化的数据加载器"""
    
    def __init__(self, num_workers: int = None):
        self.num_workers = num_workers or mp.cpu_count()
    
    def parallel_process(self, 
                        items: List[Any], 
                        process_func: Callable,
                        use_threads: bool = True) -> List[Any]:
        """并行处理数据"""
        
        executor_class = ThreadPoolExecutor if use_threads else ProcessPoolExecutor
        
        with executor_class(max_workers=self.num_workers) as executor:
            results = list(executor.map(process_func, items))
        
        return results
    
    def batch_process(self, 
                     items: List[Any], 
                     process_func: Callable,
                     batch_size: int = 100) -> List[Any]:
        """批量处理数据"""
        results = []
        
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            batch_results = self.parallel_process(batch, process_func)
            results.extend(batch_results)
            
            # 内存清理
            if i % (batch_size * 10) == 0:
                import gc
                gc.collect()
        
        return results
```

## 🎯 实施优先级

### 高优先级 (立即实施)
1. **文件操作安全性** - 解决当前的文件锁定问题
2. **错误处理改进** - 添加重试机制和更好的错误信息
3. **配置管理** - 统一配置管理，避免硬编码

### 中优先级 (1-2周内)
1. **单元测试** - 确保代码质量和回归测试
2. **性能监控** - 了解系统资源使用情况
3. **结构化日志** - 便于问题诊断和分析

### 低优先级 (长期规划)
1. **CI/CD集成** - 自动化质量检查和部署
2. **容器化部署** - 标准化部署环境
3. **高级性能优化** - 针对特定瓶颈的优化

## 📝 总结

通过实施这些建议，您的代码将具备：
- 🛡️ **更强的健壮性**: 更好的错误处理和恢复机制
- 🔧 **更高的可维护性**: 清晰的架构和配置管理
- 📊 **更好的可观测性**: 详细的日志和监控
- 🚀 **更优的性能**: 优化的数据处理和资源使用
- 🧪 **更高的质量**: 全面的测试覆盖和自动化检查

建议从解决当前的文件锁定问题开始，然后逐步实施其他改进措施。