#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型性能测试脚本
用于全面评估训练好的YOLO模型在冈上肌撕裂检测任务上的性能
"""

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import cv2
from sklearn.metrics import precision_recall_curve, average_precision_score
from sklearn.metrics import confusion_matrix, classification_report
import pandas as pd
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from services.detection_service import DetectionService
from config import Config

class ModelPerformanceTester:
    """
    模型性能测试器
    """
    
    def __init__(self, model_path: str = None, test_data_dir: str = None):
        """
        初始化性能测试器
        
        Args:
            model_path: 模型文件路径，默认使用配置中的模型
            test_data_dir: 测试数据目录，包含images和labels子目录
        """
        self.model_path = model_path or r"E:\Trae\yolo_ohif\models\weights\best.pt"
        self.test_data_dir = test_data_dir or r"E:\Trae\yolo_ohif\yolo11x_training_output\yolo_dataset\yolo_dataset"  # 默认测试数据目录
        self.results_dir = "test_results"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建结果目录
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 初始化检测服务
        try:
            self.detection_service = DetectionService(
                model_path=self.model_path,
                confidence_threshold=0.25,
                iou_threshold=0.45
            )
        except:
            # 如果配置不可用，使用默认值
            self.detection_service = DetectionService(
                model_path=self.model_path,
                confidence_threshold=0.25,
                iou_threshold=0.45
            )
        
        print(f"模型性能测试器初始化完成")
        print(f"模型路径: {self.model_path}")
        print(f"测试数据目录: {self.test_data_dir}")
        print(f"结果保存目录: {self.results_dir}")
    
    def load_test_data(self) -> Tuple[List[str], List[str]]:
        """
        加载测试数据
        
        Returns:
            (image_paths, label_paths): 图像路径列表和标签路径列表
        """
        images_dir = os.path.join(self.test_data_dir, "images", "test")
        labels_dir = os.path.join(self.test_data_dir, "labels", "test")
        
        if not os.path.exists(images_dir):
            print(f"警告: 测试图像目录不存在: {images_dir}")
            print("将使用训练数据进行测试...")
            images_dir = os.path.join(self.test_data_dir, "images", "train")
            labels_dir = os.path.join(self.test_data_dir, "labels", "train")
        
        image_paths = []
        label_paths = []
        
        # 获取所有图像文件
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
            image_paths.extend(Path(images_dir).glob(ext))
        
        # 为每个图像找到对应的标签文件
        for img_path in image_paths:
            label_path = Path(labels_dir) / f"{img_path.stem}.txt"
            if label_path.exists():
                label_paths.append(str(label_path))
            else:
                label_paths.append(None)  # 没有标签的图像
        
        image_paths = [str(p) for p in image_paths]
        
        print(f"找到 {len(image_paths)} 张测试图像")
        print(f"其中 {len([l for l in label_paths if l is not None])} 张有标签")
        
        return image_paths, label_paths
    
    def parse_yolo_label(self, label_path: str, img_width: int, img_height: int) -> List[Dict]:
        """
        解析YOLO格式的标签文件
        
        Args:
            label_path: 标签文件路径
            img_width: 图像宽度
            img_height: 图像高度
            
        Returns:
            标签列表，每个标签包含class_id, x, y, w, h (绝对坐标)
        """
        if not label_path or not os.path.exists(label_path):
            return []
        
        labels = []
        with open(label_path, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1]) * img_width
                    y_center = float(parts[2]) * img_height
                    width = float(parts[3]) * img_width
                    height = float(parts[4]) * img_height
                    
                    labels.append({
                        'class_id': class_id,
                        'x': x_center - width/2,
                        'y': y_center - height/2,
                        'w': width,
                        'h': height,
                        'x_center': x_center,
                        'y_center': y_center
                    })
        
        return labels
    
    def calculate_iou(self, box1: Dict, box2: Dict) -> float:
        """
        计算两个边界框的IoU
        
        Args:
            box1, box2: 包含x, y, w, h的字典
            
        Returns:
            IoU值
        """
        # 计算交集
        x1 = max(box1['x'], box2['x'])
        y1 = max(box1['y'], box2['y'])
        x2 = min(box1['x'] + box1['w'], box2['x'] + box2['w'])
        y2 = min(box1['y'] + box1['h'], box2['y'] + box2['h'])
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        intersection = (x2 - x1) * (y2 - y1)
        area1 = box1['w'] * box1['h']
        area2 = box2['w'] * box2['h']
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def evaluate_single_image(self, img_path: str, label_path: str, 
                            iou_threshold: float = 0.5) -> Dict:
        """
        评估单张图像的检测性能
        
        Args:
            img_path: 图像路径
            label_path: 标签路径
            iou_threshold: IoU阈值
            
        Returns:
            评估结果字典
        """
        # 读取图像
        image = cv2.imread(img_path)
        if image is None:
            return {'error': f'无法读取图像: {img_path}'}
        
        img_height, img_width = image.shape[:2]
        
        # 获取真实标签
        gt_labels = self.parse_yolo_label(label_path, img_width, img_height)
        
        # 进行检测
        try:
            detections = self.detection_service._detect_image(image)
        except Exception as e:
            return {'error': f'检测失败: {str(e)}'}
        
        # 转换检测结果格式
        pred_boxes = []
        for det in detections:
            pred_boxes.append({
                'class_id': det['class_id'],
                'confidence': det['confidence'],
                'x': det['x'],
                'y': det['y'],
                'w': det['width'],
                'h': det['height']
            })
        
        # 计算匹配
        tp = 0  # True Positives
        fp = 0  # False Positives
        matched_gt = set()  # 已匹配的真实标签索引
        
        for pred in pred_boxes:
            best_iou = 0
            best_gt_idx = -1
            
            for gt_idx, gt in enumerate(gt_labels):
                if gt_idx in matched_gt:
                    continue
                
                iou = self.calculate_iou(pred, gt)
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = gt_idx
            
            if best_iou >= iou_threshold:
                tp += 1
                matched_gt.add(best_gt_idx)
            else:
                fp += 1
        
        fn = len(gt_labels) - len(matched_gt)  # False Negatives
        
        return {
            'image_path': img_path,
            'gt_count': len(gt_labels),
            'pred_count': len(pred_boxes),
            'tp': tp,
            'fp': fp,
            'fn': fn,
            'detections': detections,
            'gt_labels': gt_labels
        }
    
    def run_evaluation(self, confidence_thresholds: List[float] = None, 
                      iou_threshold: float = 0.5) -> Dict:
        """
        运行完整的模型评估
        
        Args:
            confidence_thresholds: 置信度阈值列表，用于绘制PR曲线
            iou_threshold: IoU阈值
            
        Returns:
            评估结果字典
        """
        if confidence_thresholds is None:
            confidence_thresholds = [0.1, 0.15, 0.2, 0.25, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        print("开始模型性能评估...")
        
        # 加载测试数据
        image_paths, label_paths = self.load_test_data()
        
        if not image_paths:
            print("错误: 没有找到测试图像")
            return {}
        
        results = {
            'total_images': len(image_paths),
            'confidence_thresholds': confidence_thresholds,
            'iou_threshold': iou_threshold,
            'per_threshold_results': {},
            'image_results': []
        }
        
        # 对每个置信度阈值进行评估
        for conf_thresh in confidence_thresholds:
            print(f"\n评估置信度阈值: {conf_thresh}")
            
            # 临时设置置信度阈值
            original_conf = self.detection_service.confidence_threshold
            self.detection_service.confidence_threshold = conf_thresh
            
            total_tp = 0
            total_fp = 0
            total_fn = 0
            total_gt = 0
            
            threshold_results = []
            
            for i, (img_path, label_path) in enumerate(zip(image_paths, label_paths)):
                if (i + 1) % 10 == 0:
                    print(f"处理进度: {i+1}/{len(image_paths)}")
                
                result = self.evaluate_single_image(img_path, label_path, iou_threshold)
                
                if 'error' not in result:
                    total_tp += result['tp']
                    total_fp += result['fp']
                    total_fn += result['fn']
                    total_gt += result['gt_count']
                    threshold_results.append(result)
            
            # 计算指标
            precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
            recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            results['per_threshold_results'][conf_thresh] = {
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'total_tp': total_tp,
                'total_fp': total_fp,
                'total_fn': total_fn,
                'total_gt': total_gt
            }
            
            # 保存第一个阈值的详细结果
            if conf_thresh == confidence_thresholds[0]:
                results['image_results'] = threshold_results
            
            # 恢复原始置信度阈值
            self.detection_service.confidence_threshold = original_conf
        
        print("\n评估完成!")
        return results
    
    def plot_pr_curve(self, results: Dict):
        """
        绘制Precision-Recall曲线
        """
        thresholds = results['confidence_thresholds']
        precisions = [results['per_threshold_results'][t]['precision'] for t in thresholds]
        recalls = [results['per_threshold_results'][t]['recall'] for t in thresholds]
        
        plt.figure(figsize=(10, 6))
        plt.plot(recalls, precisions, 'b-', linewidth=2, label='PR Curve')
        plt.scatter(recalls, precisions, c='red', s=50)
        
        # 标注每个点的置信度阈值
        for i, thresh in enumerate(thresholds):
            plt.annotate(f'{thresh}', (recalls[i], precisions[i]), 
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        plt.xlabel('Recall (召回率)')
        plt.ylabel('Precision (精确率)')
        plt.title('Precision-Recall Curve')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.xlim(0, 1)
        plt.ylim(0, 1)
        
        # 保存图像
        plt.savefig(os.path.join(self.results_dir, f'pr_curve_{self.timestamp}.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_metrics_comparison(self, results: Dict):
        """
        绘制不同置信度阈值下的指标对比
        """
        thresholds = results['confidence_thresholds']
        precisions = [results['per_threshold_results'][t]['precision'] for t in thresholds]
        recalls = [results['per_threshold_results'][t]['recall'] for t in thresholds]
        f1_scores = [results['per_threshold_results'][t]['f1_score'] for t in thresholds]
        
        plt.figure(figsize=(12, 8))
        
        plt.subplot(2, 2, 1)
        plt.plot(thresholds, precisions, 'b-o', label='Precision')
        plt.xlabel('Confidence Threshold')
        plt.ylabel('Precision')
        plt.title('Precision vs Confidence Threshold')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.subplot(2, 2, 2)
        plt.plot(thresholds, recalls, 'g-o', label='Recall')
        plt.xlabel('Confidence Threshold')
        plt.ylabel('Recall')
        plt.title('Recall vs Confidence Threshold')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.subplot(2, 2, 3)
        plt.plot(thresholds, f1_scores, 'r-o', label='F1 Score')
        plt.xlabel('Confidence Threshold')
        plt.ylabel('F1 Score')
        plt.title('F1 Score vs Confidence Threshold')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.subplot(2, 2, 4)
        plt.plot(thresholds, precisions, 'b-o', label='Precision')
        plt.plot(thresholds, recalls, 'g-o', label='Recall')
        plt.plot(thresholds, f1_scores, 'r-o', label='F1 Score')
        plt.xlabel('Confidence Threshold')
        plt.ylabel('Score')
        plt.title('All Metrics Comparison')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.results_dir, f'metrics_comparison_{self.timestamp}.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_report(self, results: Dict):
        """
        生成详细的评估报告
        """
        report_path = os.path.join(self.results_dir, f'evaluation_report_{self.timestamp}.txt')
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("YOLO模型性能评估报告\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"模型路径: {self.model_path}\n")
            f.write(f"测试数据: {self.test_data_dir}\n")
            f.write(f"总测试图像数: {results['total_images']}\n")
            f.write(f"IoU阈值: {results['iou_threshold']}\n\n")
            
            f.write("不同置信度阈值下的性能指标:\n")
            f.write("-" * 60 + "\n")
            f.write(f"{'阈值':<8} {'精确率':<10} {'召回率':<10} {'F1分数':<10} {'TP':<6} {'FP':<6} {'FN':<6}\n")
            f.write("-" * 60 + "\n")
            
            best_f1 = 0
            best_threshold = 0
            
            for thresh in results['confidence_thresholds']:
                metrics = results['per_threshold_results'][thresh]
                f.write(f"{thresh:<8.2f} {metrics['precision']:<10.3f} {metrics['recall']:<10.3f} "
                       f"{metrics['f1_score']:<10.3f} {metrics['total_tp']:<6} "
                       f"{metrics['total_fp']:<6} {metrics['total_fn']:<6}\n")
                
                if metrics['f1_score'] > best_f1:
                    best_f1 = metrics['f1_score']
                    best_threshold = thresh
            
            f.write("\n" + "=" * 60 + "\n")
            f.write("推荐配置:\n")
            f.write(f"最佳置信度阈值: {best_threshold} (F1分数: {best_f1:.3f})\n")
            
            best_metrics = results['per_threshold_results'][best_threshold]
            f.write(f"在最佳阈值下:\n")
            f.write(f"  - 精确率: {best_metrics['precision']:.3f}\n")
            f.write(f"  - 召回率: {best_metrics['recall']:.3f}\n")
            f.write(f"  - F1分数: {best_metrics['f1_score']:.3f}\n")
            
        print(f"\n评估报告已保存到: {report_path}")
        return report_path
    
    def save_results(self, results: Dict):
        """
        保存评估结果为JSON文件
        """
        results_path = os.path.join(self.results_dir, f'evaluation_results_{self.timestamp}.json')
        
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"评估结果已保存到: {results_path}")
        return results_path

def main():
    """
    主函数
    """
    print("YOLO模型性能测试工具")
    print("=" * 50)
    
    # 创建测试器
    model_path = r"E:\Trae\yolo_ohif\models\weights\best.pt"
    test_data_dir = r"E:\Trae\yolo_ohif\yolo11x_training_output\yolo_dataset\yolo_dataset"
    tester = ModelPerformanceTester(model_path=model_path, test_data_dir=test_data_dir)
    
    # 运行评估
    results = tester.run_evaluation()
    
    if not results:
        print("评估失败，请检查测试数据和模型配置")
        return
    
    # 生成可视化图表
    print("\n生成可视化图表...")
    tester.plot_pr_curve(results)
    tester.plot_metrics_comparison(results)
    
    # 生成报告
    print("\n生成评估报告...")
    tester.generate_report(results)
    tester.save_results(results)
    
    print("\n性能测试完成!")
    print(f"结果保存在: {tester.results_dir}")

if __name__ == "__main__":
    main()