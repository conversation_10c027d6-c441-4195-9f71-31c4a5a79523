using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using Microsoft.Win32;
using Microsoft.Extensions.Logging;

namespace MedicalImageAnalysis.Wpf.Views
{
    /// <summary>
    /// DirectoryView.xaml 的交互逻辑
    /// </summary>
    public partial class DirectoryView : System.Windows.Controls.UserControl
    {
        private readonly ILogger<DirectoryView> _logger;
        private readonly ObservableCollection<DirectoryItem> _directoryItems;
        private readonly ObservableCollection<FileItem> _fileItems;
        private string _currentPath = "./data";
        private FileItem? _selectedFile;

        public DirectoryView()
        {
            InitializeComponent();
            _logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<DirectoryView>.Instance;
            _directoryItems = new ObservableCollection<DirectoryItem>();
            _fileItems = new ObservableCollection<FileItem>();

            DirectoryTreeView.ItemsSource = _directoryItems;
            FileListView.ItemsSource = _fileItems;

            // 初始化目录结构
            InitializeDirectoryStructure();
            LoadCurrentDirectory();
        }

        /// <summary>
        /// 初始化目录结构
        /// </summary>
        private void InitializeDirectoryStructure()
        {
            try
            {
                // 确保数据目录存在
                if (!Directory.Exists(_currentPath))
                {
                    Directory.CreateDirectory(_currentPath);
                }

                var rootItem = new DirectoryItem
                {
                    Name = "data",
                    Path = _currentPath,
                    Icon = "Folder",
                    IsExpanded = true
                };

                // 添加子目录
                var subDirectories = new[]
                {
                    new DirectoryItem { Name = "dicom", Path = Path.Combine(_currentPath, "dicom"), Icon = "Folder" },
                    new DirectoryItem { Name = "models", Path = Path.Combine(_currentPath, "models"), Icon = "Folder" },
                    new DirectoryItem { Name = "datasets", Path = Path.Combine(_currentPath, "datasets"), Icon = "Folder" },
                    new DirectoryItem { Name = "annotations", Path = Path.Combine(_currentPath, "annotations"), Icon = "Folder" },
                    new DirectoryItem { Name = "exports", Path = Path.Combine(_currentPath, "exports"), Icon = "Folder" },
                    new DirectoryItem { Name = "temp", Path = Path.Combine(_currentPath, "temp"), Icon = "Folder" }
                };

                foreach (var subDir in subDirectories)
                {
                    if (!Directory.Exists(subDir.Path))
                    {
                        Directory.CreateDirectory(subDir.Path);
                    }
                    rootItem.Children.Add(subDir);
                }

                _directoryItems.Add(rootItem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化目录结构时发生错误");
                MessageBox.Show($"初始化目录结构失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 加载当前目录
        /// </summary>
        private void LoadCurrentDirectory()
        {
            try
            {
                _fileItems.Clear();
                CurrentPathText.Text = _currentPath;

                if (!Directory.Exists(_currentPath))
                {
                    return;
                }

                // 加载文件夹
                var directories = Directory.GetDirectories(_currentPath);
                foreach (var dir in directories)
                {
                    var dirInfo = new DirectoryInfo(dir);
                    _fileItems.Add(new FileItem
                    {
                        Name = dirInfo.Name,
                        Type = "文件夹",
                        Size = "",
                        ModifiedTime = dirInfo.LastWriteTime.ToString("yyyy-MM-dd HH:mm"),
                        Icon = "Folder",
                        FullPath = dirInfo.FullName,
                        IsDirectory = true
                    });
                }

                // 加载文件
                var files = Directory.GetFiles(_currentPath);
                long totalSize = 0;

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    totalSize += fileInfo.Length;

                    var fileItem = new FileItem
                    {
                        Name = fileInfo.Name,
                        Type = GetFileType(fileInfo.Extension),
                        Size = FormatFileSize(fileInfo.Length),
                        ModifiedTime = fileInfo.LastWriteTime.ToString("yyyy-MM-dd HH:mm"),
                        Icon = GetFileIcon(fileInfo.Extension),
                        FullPath = fileInfo.FullName,
                        IsDirectory = false
                    };

                    _fileItems.Add(fileItem);
                }

                // 更新状态栏
                FileCountText.Text = $"{_fileItems.Count} 个项目";
                TotalSizeText.Text = $"总大小: {FormatFileSize(totalSize)}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载目录时发生错误: {Path}", _currentPath);
                MessageBox.Show($"加载目录失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 获取文件类型
        /// </summary>
        private string GetFileType(string extension)
        {
            return extension.ToLower() switch
            {
                ".dcm" or ".dicom" => "DICOM",
                ".png" => "PNG图像",
                ".jpg" or ".jpeg" => "JPEG图像",
                ".bmp" => "BMP图像",
                ".txt" => "文本文件",
                ".json" => "JSON文件",
                ".xml" => "XML文件",
                ".pt" or ".pth" => "PyTorch模型",
                ".onnx" => "ONNX模型",
                ".zip" => "压缩文件",
                _ => "文件"
            };
        }

        /// <summary>
        /// 获取文件图标
        /// </summary>
        private string GetFileIcon(string extension)
        {
            return extension.ToLower() switch
            {
                ".dcm" or ".dicom" => "FileImage",
                ".png" or ".jpg" or ".jpeg" or ".bmp" => "Image",
                ".txt" => "FileDocument",
                ".json" or ".xml" => "FileCode",
                ".pt" or ".pth" or ".onnx" => "Brain",
                ".zip" => "Archive",
                _ => "File"
            };
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 目录树选择变化事件
        /// </summary>
        private void DirectoryTreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            if (e.NewValue is DirectoryItem selectedItem)
            {
                _currentPath = selectedItem.Path;
                LoadCurrentDirectory();
            }
        }

        /// <summary>
        /// 文件列表选择变化事件
        /// </summary>
        private void FileListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (FileListView.SelectedItem is FileItem selectedFile)
            {
                _selectedFile = selectedFile;
                ShowFileDetails(selectedFile);
            }
            else
            {
                HideFileDetails();
            }
        }

        /// <summary>
        /// 文件列表双击事件
        /// </summary>
        private void FileListView_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (FileListView.SelectedItem is FileItem selectedFile)
            {
                if (selectedFile.IsDirectory)
                {
                    _currentPath = selectedFile.FullPath;
                    LoadCurrentDirectory();
                }
                else
                {
                    OpenFile_Click(sender, e);
                }
            }
        }

        /// <summary>
        /// 显示文件详情
        /// </summary>
        private void ShowFileDetails(FileItem file)
        {
            try
            {
                FileInfoCard.Visibility = Visibility.Visible;

                FileNameText.Text = file.Name;
                FileTypeText.Text = file.Type;
                FileSizeText.Text = file.Size;
                FilePathText.Text = file.FullPath;

                var fileInfo = new FileInfo(file.FullPath);
                FileCreatedText.Text = fileInfo.CreationTime.ToString("yyyy-MM-dd HH:mm:ss");
                FileModifiedText.Text = fileInfo.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss");

                // 如果是图像文件，显示预览
                if (IsImageFile(file.FullPath))
                {
                    ShowImagePreview(file.FullPath);
                }
                else
                {
                    FilePreviewCard.Visibility = Visibility.Collapsed;
                }

                // 如果是DICOM文件，显示DICOM信息
                if (file.FullPath.ToLower().EndsWith(".dcm") || file.FullPath.ToLower().EndsWith(".dicom"))
                {
                    ShowDicomInfo(file.FullPath);
                }
                else
                {
                    DicomInfoCard.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示文件详情时发生错误: {FilePath}", file.FullPath);
            }
        }

        /// <summary>
        /// 隐藏文件详情
        /// </summary>
        private void HideFileDetails()
        {
            FileInfoCard.Visibility = Visibility.Collapsed;
            FilePreviewCard.Visibility = Visibility.Collapsed;
            DicomInfoCard.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 判断是否为图像文件
        /// </summary>
        private bool IsImageFile(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            return extension == ".png" || extension == ".jpg" || extension == ".jpeg" || extension == ".bmp";
        }

        /// <summary>
        /// 显示图像预览
        /// </summary>
        private void ShowImagePreview(string imagePath)
        {
            try
            {
                var bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri(imagePath);
                bitmap.CacheOption = BitmapCacheOption.OnLoad;
                bitmap.EndInit();
                bitmap.Freeze();

                FilePreviewImage.Source = bitmap;
                FilePreviewCard.Visibility = Visibility.Visible;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示图像预览时发生错误: {ImagePath}", imagePath);
                FilePreviewCard.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 显示DICOM信息
        /// </summary>
        private void ShowDicomInfo(string dicomPath)
        {
            try
            {
                // 模拟DICOM信息（实际应用中需要使用DICOM库解析）
                PatientNameText.Text = "示例患者";
                PatientIdText.Text = "P001";
                StudyDateText.Text = "2025-01-15";
                ModalityText.Text = "CT";
                SeriesDescText.Text = "胸部CT扫描";

                DicomInfoCard.Visibility = Visibility.Visible;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示DICOM信息时发生错误: {DicomPath}", dicomPath);
                DicomInfoCard.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 刷新目录
        /// </summary>
        private void RefreshDirectory_Click(object sender, RoutedEventArgs e)
        {
            LoadCurrentDirectory();
        }

        /// <summary>
        /// 创建文件夹
        /// </summary>
        private void CreateFolder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var folderName = Microsoft.VisualBasic.Interaction.InputBox(
                    "请输入文件夹名称:", "新建文件夹", "新建文件夹");

                if (!string.IsNullOrEmpty(folderName))
                {
                    var newFolderPath = Path.Combine(_currentPath, folderName);
                    if (!Directory.Exists(newFolderPath))
                    {
                        Directory.CreateDirectory(newFolderPath);
                        LoadCurrentDirectory();
                        MessageBox.Show($"文件夹 '{folderName}' 创建成功！", "提示",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("文件夹已存在！", "提示",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建文件夹时发生错误");
                MessageBox.Show($"创建文件夹失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        private void UploadFiles_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择要上传的文件",
                Filter = "所有文件 (*.*)|*.*|" +
                        "DICOM文件 (*.dcm)|*.dcm|" +
                        "图像文件 (*.png;*.jpg;*.jpeg;*.bmp)|*.png;*.jpg;*.jpeg;*.bmp",
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    foreach (var sourceFile in openFileDialog.FileNames)
                    {
                        var fileName = Path.GetFileName(sourceFile);
                        var destFile = Path.Combine(_currentPath, fileName);

                        if (File.Exists(destFile))
                        {
                            var result = MessageBox.Show($"文件 '{fileName}' 已存在，是否覆盖？",
                                                       "确认", MessageBoxButton.YesNo, MessageBoxImage.Question);
                            if (result != MessageBoxResult.Yes)
                                continue;
                        }

                        File.Copy(sourceFile, destFile, true);
                    }

                    LoadCurrentDirectory();
                    MessageBox.Show($"成功上传 {openFileDialog.FileNames.Length} 个文件！", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "上传文件时发生错误");
                    MessageBox.Show($"上传文件失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 删除选中项
        /// </summary>
        private void DeleteSelected_Click(object sender, RoutedEventArgs e)
        {
            if (FileListView.SelectedItems.Count == 0)
            {
                MessageBox.Show("请先选择要删除的文件或文件夹。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var result = MessageBox.Show($"确定要删除选中的 {FileListView.SelectedItems.Count} 个项目吗？",
                                       "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    foreach (FileItem item in FileListView.SelectedItems)
                    {
                        if (item.IsDirectory)
                        {
                            Directory.Delete(item.FullPath, true);
                        }
                        else
                        {
                            File.Delete(item.FullPath);
                        }
                    }

                    LoadCurrentDirectory();
                    MessageBox.Show("删除成功！", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "删除文件时发生错误");
                    MessageBox.Show($"删除失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 打开文件
        /// </summary>
        private void OpenFile_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedFile == null)
            {
                MessageBox.Show("请先选择要打开的文件。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = _selectedFile.FullPath,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开文件时发生错误: {FilePath}", _selectedFile.FullPath);
                MessageBox.Show($"无法打开文件：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 复制路径
        /// </summary>
        private void CopyPath_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedFile != null)
            {
                Clipboard.SetText(_selectedFile.FullPath);
                MessageBox.Show("文件路径已复制到剪贴板。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 重命名文件
        /// </summary>
        private void RenameFile_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedFile == null) return;

            try
            {
                var newName = Microsoft.VisualBasic.Interaction.InputBox(
                    "请输入新的文件名:", "重命名", _selectedFile.Name);

                if (!string.IsNullOrEmpty(newName) && newName != _selectedFile.Name)
                {
                    var newPath = Path.Combine(Path.GetDirectoryName(_selectedFile.FullPath)!, newName);

                    if (_selectedFile.IsDirectory)
                    {
                        Directory.Move(_selectedFile.FullPath, newPath);
                    }
                    else
                    {
                        File.Move(_selectedFile.FullPath, newPath);
                    }

                    LoadCurrentDirectory();
                    MessageBox.Show("重命名成功！", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重命名文件时发生错误");
                MessageBox.Show($"重命名失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 移动文件
        /// </summary>
        private void MoveFile_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedFile == null) return;

            // 使用简单的输入对话框代替文件夹浏览器
            var folderPath = Microsoft.VisualBasic.Interaction.InputBox(
                "请输入目标文件夹路径:",
                "选择目标文件夹",
                @"C:\");

            if (!string.IsNullOrEmpty(folderPath) && Directory.Exists(folderPath))
            {
                try
                {
                    var newPath = Path.Combine(folderPath, _selectedFile.Name);

                    if (_selectedFile.IsDirectory)
                    {
                        Directory.Move(_selectedFile.FullPath, newPath);
                    }
                    else
                    {
                        File.Move(_selectedFile.FullPath, newPath);
                    }

                    LoadCurrentDirectory();
                    MessageBox.Show("移动成功！", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "移动文件时发生错误");
                    MessageBox.Show($"移动失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 删除文件
        /// </summary>
        private void DeleteFile_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedFile == null) return;

            var result = MessageBox.Show($"确定要删除 '{_selectedFile.Name}' 吗？",
                                       "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    if (_selectedFile.IsDirectory)
                    {
                        Directory.Delete(_selectedFile.FullPath, true);
                    }
                    else
                    {
                        File.Delete(_selectedFile.FullPath);
                    }

                    LoadCurrentDirectory();
                    HideFileDetails();
                    MessageBox.Show("删除成功！", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "删除文件时发生错误");
                    MessageBox.Show($"删除失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 显示属性
        /// </summary>
        private void ShowProperties_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedFile != null)
            {
                MessageBox.Show($"文件属性功能正在开发中。\n\n当前选中文件: {_selectedFile.Name}",
                              "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }

    /// <summary>
    /// 目录项数据模型
    /// </summary>
    public class DirectoryItem
    {
        public string Name { get; set; } = "";
        public string Path { get; set; } = "";
        public string Icon { get; set; } = "Folder";
        public bool IsExpanded { get; set; } = false;
        public ObservableCollection<DirectoryItem> Children { get; set; } = new();
    }

    /// <summary>
    /// 文件项数据模型
    /// </summary>
    public class FileItem
    {
        public string Name { get; set; } = "";
        public string Type { get; set; } = "";
        public string Size { get; set; } = "";
        public string ModifiedTime { get; set; } = "";
        public string Icon { get; set; } = "File";
        public string FullPath { get; set; } = "";
        public bool IsDirectory { get; set; } = false;
    }
}
