#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试脚本 - 检测test目录下的所有图像
"""

import os
import sys
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
from typing import List, Dict, Tuple
import time
from collections import defaultdict

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from config import Config
from services.detection_service import DetectionService

# 配置路径
TEST_IMAGE_DIR = r"E:\Trae\yolo_ohif\yolo11x_training_output\yolo_dataset\yolo_dataset\images\test"
MODEL_PATH = r"E:\Trae\yolo_ohif\yolo11x_training_output\training_results\yolo11x_from_scratch_20250719_135134\weights\best.pt"

def get_all_test_images(image_dir: str) -> List[str]:
    """
    获取测试目录下的所有图像文件
    
    Args:
        image_dir: 图像目录路径
        
    Returns:
        List[str]: 图像文件路径列表
    """
    if not os.path.exists(image_dir):
        print(f"错误: 图像目录不存在: {image_dir}")
        return []
    
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
        image_files.extend(Path(image_dir).glob(ext))
    
    # 转换为字符串并排序
    image_files = [str(f) for f in image_files]
    image_files.sort()
    
    return image_files

def test_all_images(image_dir: str = None, 
                   confidence_threshold: float = None) -> Dict:
    """
    测试所有图像的检测效果
    
    Args:
        image_dir: 测试图像目录路径
        confidence_threshold: 置信度阈值
        
    Returns:
        Dict: 测试结果统计
    """
    
    if image_dir is None:
        image_dir = TEST_IMAGE_DIR
    
    print("=" * 80)
    print("全面图像检测测试")
    print("=" * 80)
    print(f"测试目录: {image_dir}")
    print(f"模型路径: {MODEL_PATH}")
    
    # 获取所有测试图像
    image_files = get_all_test_images(image_dir)
    
    if not image_files:
        print("错误: 没有找到测试图像")
        return {}
    
    print(f"找到 {len(image_files)} 张测试图像")
    
    # 初始化检测服务
    try:
        detection_service = DetectionService(
            model_path=MODEL_PATH,
            confidence_threshold=confidence_threshold or Config.YOLO.CONFIDENCE_THRESHOLD,
            iou_threshold=Config.YOLO.IOU_THRESHOLD
        )
        print(f"模型加载成功，置信度阈值: {detection_service.confidence_threshold}")
    except Exception as e:
        print(f"错误: 无法初始化检测服务 - {str(e)}")
        return {}
    
    # 统计变量
    results = {
        'total_images': len(image_files),
        'images_with_detections': 0,
        'images_without_detections': 0,
        'total_detections': 0,
        'detection_details': [],
        'processing_times': [],
        'confidence_distribution': defaultdict(int),
        'error_images': []
    }
    
    print("\n开始检测...")
    print("-" * 80)
    
    start_time = time.time()
    
    for i, image_path in enumerate(image_files, 1):
        image_name = Path(image_path).name
        
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                print(f"[{i:4d}/{len(image_files)}] 错误: 无法读取图像 {image_name}")
                results['error_images'].append(image_path)
                continue
            
            # 检测开始时间
            detection_start = time.time()
            
            # 进行检测
            detections = detection_service._detect_image(image)
            
            # 检测结束时间
            detection_time = time.time() - detection_start
            results['processing_times'].append(detection_time)
            
            # 统计结果
            num_detections = len(detections)
            results['total_detections'] += num_detections
            
            if num_detections > 0:
                results['images_with_detections'] += 1
                status = f"检测到 {num_detections} 个目标"
                
                # 记录检测详情
                detection_info = {
                    'image_path': image_path,
                    'image_name': image_name,
                    'detection_count': num_detections,
                    'detections': detections,
                    'processing_time': detection_time
                }
                results['detection_details'].append(detection_info)
                
                # 统计置信度分布
                for det in detections:
                    conf_range = f"{det['confidence']:.1f}"
                    results['confidence_distribution'][conf_range] += 1
                    
                # 显示检测详情
                print(f"[{i:4d}/{len(image_files)}] {image_name}: {status} ({detection_time:.3f}s)")
                for j, det in enumerate(detections[:3]):  # 只显示前3个
                    print(f"         {j+1}. {det['class']}: {det['confidence']:.3f}")
                if len(detections) > 3:
                    print(f"         ... 还有 {len(detections) - 3} 个目标")
                    
            else:
                results['images_without_detections'] += 1
                status = "无检测"
                print(f"[{i:4d}/{len(image_files)}] {image_name}: {status} ({detection_time:.3f}s)")
            
        except Exception as e:
            print(f"[{i:4d}/{len(image_files)}] 错误: 处理图像 {image_name} 时出错 - {str(e)}")
            results['error_images'].append(image_path)
        
        # 每100张图像显示进度
        if i % 100 == 0:
            elapsed = time.time() - start_time
            avg_time = elapsed / i
            remaining = (len(image_files) - i) * avg_time
            print(f"\n进度: {i}/{len(image_files)} ({i/len(image_files)*100:.1f}%) - 预计剩余时间: {remaining:.1f}秒\n")
    
    total_time = time.time() - start_time
    
    # 打印统计结果
    print("\n" + "=" * 80)
    print("检测结果统计")
    print("=" * 80)
    print(f"总图像数量: {results['total_images']}")
    print(f"有检测结果的图像: {results['images_with_detections']} ({results['images_with_detections']/results['total_images']*100:.1f}%)")
    print(f"无检测结果的图像: {results['images_without_detections']} ({results['images_without_detections']/results['total_images']*100:.1f}%)")
    print(f"总检测目标数量: {results['total_detections']}")
    print(f"错误图像数量: {len(results['error_images'])}")
    print(f"总处理时间: {total_time:.2f}秒")
    
    if results['processing_times']:
        avg_time = np.mean(results['processing_times'])
        print(f"平均检测时间: {avg_time:.3f}秒/图像")
        print(f"检测速度: {1/avg_time:.1f} 图像/秒")
    
    # 置信度分布
    if results['confidence_distribution']:
        print("\n置信度分布:")
        for conf, count in sorted(results['confidence_distribution'].items()):
            print(f"  {conf}: {count} 个检测")
    
    # 显示有检测结果的图像
    if results['detection_details']:
        print("\n有检测结果的图像详情:")
        print("-" * 60)
        for detail in results['detection_details']:
            print(f"{detail['image_name']}: {detail['detection_count']} 个目标")
            for det in detail['detections']:
                print(f"  - {det['class']}: {det['confidence']:.3f}")
    
    return results

def analyze_detection_patterns(results: Dict) -> None:
    """
    分析检测模式和规律
    
    Args:
        results: 检测结果字典
    """
    if not results or not results['detection_details']:
        print("\n没有检测结果可供分析")
        return
    
    print("\n" + "=" * 80)
    print("检测模式分析")
    print("=" * 80)
    
    # 按图像序列分析
    sequence_stats = defaultdict(list)
    
    for detail in results['detection_details']:
        image_name = detail['image_name']
        # 提取序列号（假设格式为 xxx_slice_yyy.jpg）
        parts = image_name.split('_')
        if len(parts) >= 2:
            sequence = parts[0]
            sequence_stats[sequence].append(detail)
    
    if sequence_stats:
        print("按图像序列统计:")
        for sequence, details in sequence_stats.items():
            total_detections = sum(d['detection_count'] for d in details)
            print(f"  序列 {sequence}: {len(details)} 张图像有检测, 共 {total_detections} 个目标")
    
    # 置信度统计
    all_confidences = []
    for detail in results['detection_details']:
        for det in detail['detections']:
            all_confidences.append(det['confidence'])
    
    if all_confidences:
        print(f"\n置信度统计:")
        print(f"  最高置信度: {max(all_confidences):.3f}")
        print(f"  最低置信度: {min(all_confidences):.3f}")
        print(f"  平均置信度: {np.mean(all_confidences):.3f}")
        print(f"  置信度标准差: {np.std(all_confidences):.3f}")

def main():
    """
    主函数
    """
    print("YOLO模型全面测试工具")
    print(f"测试数据目录: {TEST_IMAGE_DIR}")
    print(f"模型路径: {MODEL_PATH}")
    
    # 执行全面测试
    results = test_all_images()
    
    if results:
        # 分析检测模式
        analyze_detection_patterns(results)
        
        # 保存结果到文件
        output_file = "test_all_images_results.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"YOLO模型全面测试结果\n")
            f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"测试目录: {TEST_IMAGE_DIR}\n")
            f.write(f"模型路径: {MODEL_PATH}\n\n")
            
            f.write(f"总图像数量: {results['total_images']}\n")
            f.write(f"有检测结果的图像: {results['images_with_detections']}\n")
            f.write(f"无检测结果的图像: {results['images_without_detections']}\n")
            f.write(f"总检测目标数量: {results['total_detections']}\n")
            
            if results['detection_details']:
                f.write("\n有检测结果的图像详情:\n")
                for detail in results['detection_details']:
                    f.write(f"{detail['image_name']}: {detail['detection_count']} 个目标\n")
                    for det in detail['detections']:
                        f.write(f"  - {det['class']}: {det['confidence']:.3f}\n")
        
        print(f"\n结果已保存到: {output_file}")
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()