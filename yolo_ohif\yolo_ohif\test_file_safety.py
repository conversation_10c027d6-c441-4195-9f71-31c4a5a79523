#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件安全写入功能
验证修复后的create_yolo_dataset.py是否能解决WinError 32文件占用问题
"""

import os
import sys
import logging
import tempfile
import threading
import time
from pathlib import Path
import concurrent.futures

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_safe_file_writing():
    """
    测试安全文件写入功能
    """
    logger.info("🧪 测试安全文件写入功能")
    logger.info("=" * 50)
    
    try:
        from create_yolo_dataset import YOLODatasetCreator
    except ImportError as e:
        logger.error(f"❌ 导入错误: {e}")
        return False
    
    # 创建测试目录
    test_dir = Path('./test_file_safety')
    test_dir.mkdir(exist_ok=True)
    
    try:
        # 创建数据集创建器实例
        creator = YOLODatasetCreator(
            dataset_root='./dataset',
            output_root=str(test_dir),
            img_size=640
        )
        
        logger.info("\n📋 测试1: 基本安全文件写入")
        test_file = test_dir / "test_basic.txt"
        test_content = "这是一个测试文件\n包含多行内容\n用于验证安全写入功能"
        
        try:
            creator.safe_write_file(str(test_file), test_content)
            
            # 验证文件内容
            if test_file.exists():
                with open(test_file, 'r', encoding='utf-8') as f:
                    read_content = f.read()
                if read_content == test_content:
                    logger.info("✅ 基本安全文件写入测试通过")
                else:
                    logger.error("❌ 文件内容不匹配")
                    return False
            else:
                logger.error("❌ 文件未创建")
                return False
        except Exception as e:
            logger.error(f"❌ 基本安全文件写入失败: {e}")
            return False
        
        logger.info("\n📋 测试2: 并发文件写入")
        def write_concurrent_file(file_id):
            """并发写入文件的函数"""
            try:
                test_file = test_dir / f"concurrent_{file_id}.txt"
                content = f"并发测试文件 {file_id}\n时间戳: {time.time()}\n"
                creator.safe_write_file(str(test_file), content)
                return True
            except Exception as e:
                logger.error(f"并发写入文件 {file_id} 失败: {e}")
                return False
        
        # 使用线程池进行并发测试
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(write_concurrent_file, i) for i in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        success_count = sum(results)
        if success_count == 10:
            logger.info(f"✅ 并发文件写入测试通过 ({success_count}/10)")
        else:
            logger.warning(f"⚠️ 并发文件写入部分成功 ({success_count}/10)")
        
        logger.info("\n📋 测试3: YAML配置文件写入")
        yaml_file = test_dir / "test_config.yaml"
        yaml_content = """path: /test/path
train: images/train
val: images/val
test: images/test
nc: 1
names: ['test_class']
"""
        
        try:
            creator.safe_write_file(str(yaml_file), yaml_content)
            
            if yaml_file.exists():
                logger.info("✅ YAML配置文件写入测试通过")
            else:
                logger.error("❌ YAML文件未创建")
                return False
        except Exception as e:
            logger.error(f"❌ YAML文件写入失败: {e}")
            return False
        
        logger.info("\n📋 测试4: 重试机制测试")
        # 模拟文件被占用的情况
        retry_file = test_dir / "retry_test.txt"
        
        def simulate_file_lock():
            """模拟文件被占用"""
            try:
                with open(retry_file, 'w') as f:
                    f.write("占用文件")
                    time.sleep(2)  # 保持文件打开2秒
            except:
                pass
        
        # 启动文件占用线程
        lock_thread = threading.Thread(target=simulate_file_lock)
        lock_thread.start()
        
        time.sleep(0.5)  # 等待文件被占用
        
        try:
            creator.safe_write_file(str(retry_file), "重试测试内容")
            logger.info("✅ 重试机制测试通过")
        except Exception as e:
            logger.warning(f"⚠️ 重试机制测试失败: {e}")
        
        lock_thread.join()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        return False
    
    finally:
        # 清理测试文件
        try:
            import shutil
            if test_dir.exists():
                shutil.rmtree(test_dir)
                logger.info("🧹 测试文件已清理")
        except Exception as e:
            logger.warning(f"清理测试文件失败: {e}")

def test_dataset_creation_with_safety():
    """
    测试带有安全文件写入的数据集创建流程
    """
    logger.info("\n🧪 测试安全数据集创建流程")
    logger.info("=" * 50)
    
    # 检查是否有测试数据
    dataset_dir = Path('./dataset')
    if not dataset_dir.exists():
        logger.warning("⚠️ 未找到测试数据集目录，跳过数据集创建测试")
        return True
    
    try:
        from create_yolo_dataset import YOLODatasetCreator
        
        # 创建测试输出目录
        test_output = Path('./test_safe_dataset_output')
        
        creator = YOLODatasetCreator(
            dataset_root=str(dataset_dir),
            output_root=str(test_output),
            img_size=640
        )
        
        logger.info("开始安全数据集创建测试...")
        config_path = creator.create_dataset()
        
        if Path(config_path).exists():
            logger.info("✅ 安全数据集创建测试通过")
            return True
        else:
            logger.error("❌ 配置文件未创建")
            return False
            
    except Exception as e:
        logger.error(f"❌ 安全数据集创建测试失败: {e}")
        return False
    
    finally:
        # 清理测试输出
        try:
            import shutil
            if test_output.exists():
                shutil.rmtree(test_output)
                logger.info("🧹 测试输出已清理")
        except Exception as e:
            logger.warning(f"清理测试输出失败: {e}")

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("文件安全写入功能测试")
    print("=" * 60)
    
    # 测试1: 安全文件写入功能
    print("\n1. 测试安全文件写入功能...")
    success1 = test_safe_file_writing()
    
    # 测试2: 安全数据集创建
    print("\n2. 测试安全数据集创建...")
    success2 = test_dataset_creation_with_safety()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"安全文件写入测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"安全数据集创建测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
        print("\n📋 修复说明:")
        print("1. 添加了安全文件写入机制，使用原子性操作")
        print("2. 实现了文件操作重试机制，解决临时文件占用问题")
        print("3. 使用临时文件+原子性移动，避免文件损坏")
        print("4. 对标签文件和YAML配置文件都应用了安全写入")
        print("\n🚀 现在可以安全运行数据集创建:")
        print("   python create_yolo_dataset.py")
        return True
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    main()