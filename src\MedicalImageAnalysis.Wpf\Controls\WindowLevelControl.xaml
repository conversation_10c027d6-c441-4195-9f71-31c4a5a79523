<UserControl x:Class="MedicalImageAnalysis.Wpf.Controls.WindowLevelControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="200" d:DesignWidth="300">

    <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp1" Margin="4">
        <StackPanel Margin="16">
            <!-- 标题 -->
            <TextBlock Text="窗宽窗位调整"
                     FontSize="16"
                     FontWeight="Medium"
                     Margin="0,0,0,16"/>

            <!-- 自定义窗宽窗位 -->
            <Grid Margin="0,0,0,16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 窗宽 -->
                <TextBlock Grid.Row="0" Grid.Column="0" 
                         Text="窗宽:" 
                         VerticalAlignment="Center" 
                         Margin="0,0,8,0"/>
                <TextBox x:Name="WindowWidthTextBox"
                       Grid.Row="0" Grid.Column="1"
                       Margin="0,0,16,0"
                       materialDesign:HintAssist.Hint="窗宽值"
                       TextChanged="WindowLevel_TextChanged"/>

                <!-- 窗位 -->
                <TextBlock Grid.Row="0" Grid.Column="2"
                         Text="窗位:"
                         VerticalAlignment="Center"
                         Margin="0,0,8,0"/>
                <TextBox x:Name="WindowCenterTextBox"
                       Grid.Row="0" Grid.Column="3"
                       materialDesign:HintAssist.Hint="窗位值"
                       TextChanged="WindowLevel_TextChanged"/>

                <!-- 重置按钮 -->
                <Button Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="4"
                      x:Name="ResetWindowButton"
                      Content="重置到原始值"
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Margin="0,8,0,0"
                      Click="ResetWindowButton_Click"/>
            </Grid>

            <!-- 预设窗口 -->
            <TextBlock Text="预设窗口"
                     FontSize="14"
                     FontWeight="Medium"
                     Margin="0,0,0,8"/>

            <UniformGrid Columns="2" Rows="2">
                <!-- 肺窗 -->
                <Button x:Name="LungWindowButton"
                      Content="肺窗"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Margin="2"
                      ToolTip="窗宽: 1500, 窗位: -600"
                      Click="LungWindowButton_Click"/>

                <!-- 软组织窗 -->
                <Button x:Name="SoftTissueWindowButton"
                      Content="软组织窗"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Margin="2"
                      ToolTip="窗宽: 400, 窗位: 40"
                      Click="SoftTissueWindowButton_Click"/>

                <!-- 骨窗 -->
                <Button x:Name="BoneWindowButton"
                      Content="骨窗"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Margin="2"
                      ToolTip="窗宽: 2000, 窗位: 400"
                      Click="BoneWindowButton_Click"/>

                <!-- 脑窗 -->
                <Button x:Name="BrainWindowButton"
                      Content="脑窗"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Margin="2"
                      ToolTip="窗宽: 80, 窗位: 40"
                      Click="BrainWindowButton_Click"/>
            </UniformGrid>

            <!-- 当前设置显示 -->
            <Border Background="{DynamicResource MaterialDesignDivider}"
                  Margin="0,16,0,8"
                  Height="1"/>
            
            <TextBlock x:Name="CurrentSettingsText"
                     Text="当前设置: 窗宽 400, 窗位 40"
                     FontSize="12"
                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                     HorizontalAlignment="Center"/>
        </StackPanel>
    </materialDesign:Card>
</UserControl>
