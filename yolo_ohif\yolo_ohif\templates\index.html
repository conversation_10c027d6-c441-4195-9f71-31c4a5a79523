{% extends "base.html" %}

{% block title %}首页 - YOLO-OHIF医学图像疾病检测系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 text-center mb-5">
        <h1 class="display-4">YOLO-OHIF医学图像疾病检测系统</h1>
        <p class="lead">基于人工智能的医学图像疾病检测与诊断辅助平台</p>
        {% if not session.get('user') %}
        <div class="mt-4">
            <a href="{{ url_for('web.register') }}" class="btn btn-primary btn-lg me-2">
                <i class="fas fa-user-plus me-1"></i>立即注册
            </a>
            <a href="{{ url_for('web.login') }}" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-sign-in-alt me-1"></i>登录系统
            </a>
        </div>
        {% else %}
        <div class="mt-4">
            <a href="{{ url_for('web.smart_detect') }}" class="btn btn-primary btn-lg me-2">
                <i class="fas fa-magic me-1"></i>智能检测
            </a>
            <a href="{{ url_for('web.dashboard') }}" class="btn btn-outline-primary btn-lg me-2">
                <i class="fas fa-tachometer-alt me-1"></i>仪表板
            </a>
            <a href="{{ url_for('web.upload') }}" class="btn btn-success btn-lg">
                <i class="fas fa-upload me-1"></i>上传图像
            </a>
        </div>
        {% endif %}
    </div>
</div>

<div class="row mb-5">
    <div class="col-md-3 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-magic fa-4x text-primary mb-3"></i>
                <h3 class="card-title">智能检测</h3>
                <p class="card-text">一键式上传和检测流程，自动完成DICOM处理、AI检测和结果展示，大幅简化操作步骤。</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-brain fa-4x text-primary mb-3"></i>
                <h3 class="card-title">AI辅助诊断</h3>
                <p class="card-text">利用YOLO深度学习模型，对医学图像进行自动疾病检测，提供高精度的病灶定位和分类。</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-eye fa-4x text-primary mb-3"></i>
                <h3 class="card-title">专业可视化</h3>
                <p class="card-text">集成OHIF医学影像查看器，提供专业的DICOM图像浏览、标注和测量工具，支持多模态医学图像。</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-database fa-4x text-primary mb-3"></i>
                <h3 class="card-title">DICOM管理</h3>
                <p class="card-text">基于Orthanc DICOM服务器，提供完整的医学影像存储、检索和共享功能，符合医疗行业标准。</p>
            </div>
        </div>
    </div>
</div>

<div class="row mb-5">
    <div class="col-md-6">
        <h2 class="mb-4">系统特点</h2>
        <ul class="list-group list-group-flush">
            <li class="list-group-item">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong>高精度检测：</strong>采用最新YOLO模型，针对医学图像优化，提供高精度的疾病检测结果
            </li>
            <li class="list-group-item">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong>专业可视化：</strong>集成OHIF专业医学影像查看器，支持多种医学图像格式和交互功能
            </li>
            <li class="list-group-item">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong>标准兼容：</strong>基于Orthanc DICOM服务器，完全兼容DICOM标准，确保医疗数据互操作性
            </li>
            <li class="list-group-item">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong>易于部署：</strong>基于Flask开发的Web应用，简单易用，支持本地部署和云端部署
            </li>
            <li class="list-group-item">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong>安全可靠：</strong>内置用户认证和权限管理，确保医疗数据安全和隐私保护
            </li>
        </ul>
    </div>
    <div class="col-md-6">
        <h2 class="mb-4">应用场景</h2>
        <div class="accordion" id="accordionExample">
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                        <i class="fas fa-hospital me-2"></i>临床辅助诊断
                    </button>
                </h2>
                <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        为临床医生提供AI辅助诊断工具，帮助快速识别潜在病灶，提高诊断效率和准确性，减轻医生工作负担。
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                        <i class="fas fa-graduation-cap me-2"></i>医学教育培训
                    </button>
                </h2>
                <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        为医学院校和培训机构提供教学工具，帮助学生学习医学影像诊断知识，提供实践训练平台。
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree">
                        <i class="fas fa-flask me-2"></i>医学研究与临床试验
                    </button>
                </h2>
                <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        为医学研究人员提供数据分析工具，支持大规模医学图像数据的处理和分析，加速医学研究进展。
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour">
                        <i class="fas fa-clinic-medical me-2"></i>基层医疗机构
                    </button>
                </h2>
                <div id="collapseFour" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        为基层医疗机构提供智能诊断支持，弥补专业医师不足的问题，提高基层医疗服务质量。
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12 text-center">
        <h2 class="mb-4">开始使用</h2>
        <p class="lead mb-4">只需三步，即可体验AI辅助医学影像诊断</p>
        <div class="row justify-content-center">
            <div class="col-md-3 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-primary text-white rounded-circle d-inline-flex justify-content-center align-items-center mb-3" style="width: 60px; height: 60px;">
                            <h3 class="m-0">1</h3>
                        </div>
                        <h4>上传医学图像</h4>
                        <p>上传DICOM格式的医学图像到系统</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-primary text-white rounded-circle d-inline-flex justify-content-center align-items-center mb-3" style="width: 60px; height: 60px;">
                            <h3 class="m-0">2</h3>
                        </div>
                        <h4>AI自动检测</h4>
                        <p>系统自动进行疾病检测和病灶定位</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-primary text-white rounded-circle d-inline-flex justify-content-center align-items-center mb-3" style="width: 60px; height: 60px;">
                            <h3 class="m-0">3</h3>
                        </div>
                        <h4>查看分析结果</h4>
                        <p>在OHIF查看器中查看和分析检测结果</p>
                    </div>
                </div>
            </div>
        </div>
        {% if not session.get('user') %}
        <div class="mt-4">
            <a href="{{ url_for('web.register') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-rocket me-1"></i>立即开始
            </a>
        </div>
        {% else %}
        <div class="mt-4">
            <a href="{{ url_for('web.upload') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-rocket me-1"></i>立即开始
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}