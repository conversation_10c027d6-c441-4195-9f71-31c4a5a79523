using MedicalImageAnalysis.Core.Models;

namespace MedicalImageAnalysis.Core.Interfaces;

/// <summary>
/// nnUNet 服务接口，提供 nnUNet 模型的训练、验证和推理功能
/// </summary>
public interface INnUNetService
{
    /// <summary>
    /// 训练 nnUNet 模型
    /// </summary>
    /// <param name="trainingConfig">训练配置</param>
    /// <param name="progressCallback">进度回调</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>训练结果</returns>
    Task<NnUNetTrainingResult> TrainModelAsync(
        NnUNetTrainingConfig trainingConfig, 
        IProgress<NnUNetTrainingProgress>? progressCallback = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 预处理数据集
    /// </summary>
    /// <param name="preprocessingConfig">预处理配置</param>
    /// <param name="progressCallback">进度回调</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>预处理结果</returns>
    Task<bool> PreprocessDatasetAsync(
        NnUNetPreprocessingConfig preprocessingConfig,
        IProgress<string>? progressCallback = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 使用模型进行推理
    /// </summary>
    /// <param name="inferenceConfig">推理配置</param>
    /// <param name="progressCallback">进度回调</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>推理结果路径</returns>
    Task<string> InferAsync(
        NnUNetInferenceConfig inferenceConfig,
        IProgress<string>? progressCallback = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量推理
    /// </summary>
    /// <param name="modelPath">模型路径</param>
    /// <param name="inputPaths">输入图像路径集合</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="inferenceConfig">推理配置</param>
    /// <param name="progressCallback">进度回调</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量推理结果</returns>
    Task<List<string>> BatchInferAsync(
        string modelPath,
        IEnumerable<string> inputPaths,
        string outputPath,
        NnUNetInferenceConfig inferenceConfig,
        IProgress<int>? progressCallback = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证模型性能
    /// </summary>
    /// <param name="modelPath">模型路径</param>
    /// <param name="validationDataPath">验证数据路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<NnUNetValidationResult> ValidateModelAsync(
        string modelPath,
        string validationDataPath,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 创建数据集配置文件
    /// </summary>
    /// <param name="datasetConfig">数据集配置</param>
    /// <param name="outputPath">输出路径</param>
    /// <returns>配置文件路径</returns>
    Task<string> CreateDatasetConfigAsync(
        NnUNetDatasetConfig datasetConfig,
        string outputPath);

    /// <summary>
    /// 验证数据集格式
    /// </summary>
    /// <param name="datasetPath">数据集路径</param>
    /// <param name="datasetId">数据集ID</param>
    /// <returns>验证结果</returns>
    Task<bool> ValidateDatasetAsync(
        string datasetPath,
        int datasetId);

    /// <summary>
    /// 转换数据集格式为nnUNet格式
    /// </summary>
    /// <param name="sourceDataPath">源数据路径</param>
    /// <param name="targetDataPath">目标数据路径</param>
    /// <param name="datasetConfig">数据集配置</param>
    /// <param name="progressCallback">进度回调</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>转换结果</returns>
    Task<bool> ConvertDatasetAsync(
        string sourceDataPath,
        string targetDataPath,
        NnUNetDatasetConfig datasetConfig,
        IProgress<string>? progressCallback = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取模型信息
    /// </summary>
    /// <param name="modelPath">模型路径</param>
    /// <returns>模型信息</returns>
    Task<Dictionary<string, object>> GetModelInfoAsync(string modelPath);

    /// <summary>
    /// 检查nnUNet环境
    /// </summary>
    /// <returns>环境检查结果</returns>
    Task<bool> CheckEnvironmentAsync();

    /// <summary>
    /// 安装nnUNet依赖
    /// </summary>
    /// <param name="progressCallback">进度回调</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>安装结果</returns>
    Task<bool> InstallDependenciesAsync(
        IProgress<string>? progressCallback = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取可用的预训练模型
    /// </summary>
    /// <returns>预训练模型列表</returns>
    Task<List<string>> GetAvailablePretrainedModelsAsync();

    /// <summary>
    /// 下载预训练模型
    /// </summary>
    /// <param name="modelName">模型名称</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="progressCallback">进度回调</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>下载结果</returns>
    Task<string> DownloadPretrainedModelAsync(
        string modelName,
        string outputPath,
        IProgress<double>? progressCallback = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 计划训练任务
    /// </summary>
    /// <param name="datasetId">数据集ID</param>
    /// <param name="architectures">要训练的架构列表</param>
    /// <param name="folds">折数列表</param>
    /// <param name="outputPath">输出路径</param>
    /// <returns>训练计划</returns>
    Task<List<NnUNetTrainingConfig>> PlanTrainingAsync(
        int datasetId,
        List<NnUNetArchitecture> architectures,
        List<int> folds,
        string outputPath);

    /// <summary>
    /// 集成多个模型的预测结果
    /// </summary>
    /// <param name="modelPaths">模型路径列表</param>
    /// <param name="inputPath">输入路径</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>集成结果路径</returns>
    Task<string> EnsemblePredictionsAsync(
        List<string> modelPaths,
        string inputPath,
        string outputPath,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 导出模型为其他格式
    /// </summary>
    /// <param name="modelPath">源模型路径</param>
    /// <param name="exportFormat">导出格式</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>导出的模型路径</returns>
    Task<string> ExportModelAsync(
        string modelPath,
        string exportFormat,
        string outputPath,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取训练日志
    /// </summary>
    /// <param name="outputPath">训练输出路径</param>
    /// <returns>训练日志内容</returns>
    Task<string> GetTrainingLogsAsync(string outputPath);

    /// <summary>
    /// 清理临时文件
    /// </summary>
    /// <param name="outputPath">输出路径</param>
    /// <returns>清理结果</returns>
    Task<bool> CleanupTempFilesAsync(string outputPath);
}
