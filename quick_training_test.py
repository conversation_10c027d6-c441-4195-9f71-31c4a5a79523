#!/usr/bin/env python3
# 快速训练测试脚本
from ultralytics import YOLO
import torch

print("🚀 开始快速训练测试...")

# 检查设备
device = 'cuda' if torch.cuda.is_available() else 'cpu'
print(f"使用设备: {device}")

# 创建小型测试模型
model = YOLO('yolo11n.pt')  # 使用最小的模型进行测试

# 快速训练测试（仅1个epoch）
try:
    results = model.train(
        data='coco8.yaml',  # 使用内置的小型数据集
        epochs=1,           # 仅训练1个epoch进行测试
        batch=2,            # 小批次
        imgsz=320,          # 小图像尺寸
        device=device,
        project='test_runs',
        name='quick_test',
        verbose=True
    )
    print("✅ 训练测试成功!")
    print(f"训练结果保存在: test_runs/quick_test/")
    
except Exception as e:
    print(f"❌ 训练测试失败: {e}")
