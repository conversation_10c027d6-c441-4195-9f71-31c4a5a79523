#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试类别名称修复是否有效

验证检测结果是否正确显示 'supraspinatus_tear' 而不是 'item'
"""

import os
import sys
import json
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.detection_service import DetectionService

def test_class_names_fix():
    """测试类别名称修复"""
    print("=== 测试类别名称修复 ===")
    
    try:
        # 初始化检测服务
        print("1. 初始化DetectionService...")
        detection_service = DetectionService()
        
        # 加载模型
        print("2. 加载YOLO模型...")
        if not detection_service.load_model():
            print("❌ 模型加载失败")
            return False
        
        print("✅ 模型加载成功")
        
        # 检查类别名称
        print("3. 检查类别名称...")
        print(f"类别名称字典: {detection_service.class_names}")
        print(f"类别数量: {len(detection_service.class_names)}")
        
        # 检查类别0的名称
        if 0 in detection_service.class_names:
            class_0_name = detection_service.class_names[0]
            print(f"类别0名称: '{class_0_name}'")
            
            if class_0_name == 'supraspinatus_tear':
                print("✅ 类别名称正确: supraspinatus_tear")
            else:
                print(f"❌ 类别名称错误，期望: 'supraspinatus_tear'，实际: '{class_0_name}'")
                return False
        else:
            print("❌ 未找到类别0")
            return False
        
        # 创建测试图像
        print("4. 创建测试图像...")
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # 进行检测测试
        print("5. 测试检测功能...")
        detections = detection_service._detect_image(test_image)
        
        print(f"检测结果数量: {len(detections)}")
        
        # 如果有检测结果，检查类别名称
        if detections:
            for i, detection in enumerate(detections):
                print(f"检测结果 {i+1}:")
                print(f"  类别ID: {detection['class_id']}")
                print(f"  类别名称: '{detection['class']}'")
                print(f"  置信度: {detection['confidence']:.3f}")
                
                # 验证类别名称
                if detection['class'] == 'supraspinatus_tear':
                    print(f"  ✅ 类别名称正确")
                else:
                    print(f"  ❌ 类别名称错误，期望: 'supraspinatus_tear'，实际: '{detection['class']}'")
        else:
            print("没有检测到目标（这是正常的，因为使用的是随机图像）")
        
        # 测试模拟检测结果
        print("6. 测试模拟检测结果...")
        mock_detection = {
            'class_id': 0,
            'class': detection_service.class_names.get(0, 'unknown'),
            'confidence': 0.85,
            'x': 320,
            'y': 240,
            'width': 100,
            'height': 80,
            'x1': 270,
            'y1': 200,
            'x2': 370,
            'y2': 280
        }
        
        print(f"模拟检测结果:")
        print(f"  类别ID: {mock_detection['class_id']}")
        print(f"  类别名称: '{mock_detection['class']}'")
        print(f"  置信度: {mock_detection['confidence']:.1%}")
        
        if mock_detection['class'] == 'supraspinatus_tear':
            print("✅ 模拟检测结果类别名称正确")
            return True
        else:
            print(f"❌ 模拟检测结果类别名称错误")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_response_format():
    """测试API响应格式"""
    print("\n=== 测试API响应格式 ===")
    
    try:
        detection_service = DetectionService()
        if not detection_service.load_model():
            print("❌ 模型加载失败")
            return False
        
        # 模拟API返回的检测结果格式
        mock_detections = [
            {
                'class_id': 0,
                'class': detection_service.class_names.get(0, 'unknown'),
                'confidence': 0.169,  # 16.9%
                'x': 320,
                'y': 240,
                'width': 100,
                'height': 80,
                'x1': 270,
                'y1': 200,
                'x2': 370,
                'y2': 280
            }
        ]
        
        # 模拟完整的API响应
        api_response = {
            'file': 'test.dcm',
            'image_index': 0,
            'detections': mock_detections,
            'result_image': None
        }
        
        print("模拟API响应:")
        print(json.dumps(api_response, indent=2, ensure_ascii=False))
        
        # 检查前端会接收到的数据
        for detection in api_response['detections']:
            class_name = detection['class']
            confidence = detection['confidence']
            
            print(f"\n前端显示格式: {class_name} ({confidence:.1%})")
            
            if class_name == 'supraspinatus_tear':
                print("✅ 前端应该显示: supraspinatus_tear (16.9%)")
                return True
            else:
                print(f"❌ 前端会显示错误的类别名称: {class_name}")
                return False
        
    except Exception as e:
        print(f"❌ API响应格式测试失败: {e}")
        return False

def check_frontend_integration():
    """检查前端集成"""
    print("\n=== 前端集成检查 ===")
    
    print("前端JavaScript代码应该从detection.class获取类别名称")
    print("检查要点:")
    print("1. 后端API返回的detection对象包含正确的'class'字段")
    print("2. 前端JavaScript正确读取detection.class")
    print("3. 前端显示格式: `${detection.class} (${(detection.confidence * 100).toFixed(1)}%)`")
    
    # 检查前端代码文件
    frontend_file = "E:/Trae/yolo_ohif/templates/dicom_viewer.html"
    if os.path.exists(frontend_file):
        print(f"\n检查前端文件: {frontend_file}")
        try:
            with open(frontend_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'detection.class' in content:
                print("✅ 前端代码包含detection.class引用")
            else:
                print("❌ 前端代码可能没有正确引用detection.class")
            
            if 'detection.confidence' in content:
                print("✅ 前端代码包含detection.confidence引用")
            else:
                print("❌ 前端代码可能没有正确引用detection.confidence")
                
        except Exception as e:
            print(f"❌ 读取前端文件失败: {e}")
    else:
        print(f"❌ 前端文件不存在: {frontend_file}")

def main():
    """主函数"""
    print("YOLO类别名称修复验证工具")
    print("=" * 50)
    
    # 1. 测试类别名称修复
    class_names_ok = test_class_names_fix()
    
    # 2. 测试API响应格式
    api_format_ok = test_api_response_format()
    
    # 3. 检查前端集成
    check_frontend_integration()
    
    print("\n=== 总结 ===")
    print(f"类别名称修复: {'✅ 成功' if class_names_ok else '❌ 失败'}")
    print(f"API响应格式: {'✅ 正确' if api_format_ok else '❌ 错误'}")
    
    if class_names_ok and api_format_ok:
        print("\n✅ 修复成功！")
        print("现在检测结果应该显示: supraspinatus_tear (16.9%) 而不是 item (16.9%)")
        print("\n建议:")
        print("1. 重启应用服务以确保更改生效")
        print("2. 清除浏览器缓存")
        print("3. 重新进行DICOM检测测试")
    else:
        print("\n❌ 仍有问题需要解决")
        print("请检查上述错误信息并进行相应修复")

if __name__ == "__main__":
    main()