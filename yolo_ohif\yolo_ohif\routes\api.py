import os
import json
import logging
import tempfile
import shutil
from flask import Blueprint, request, jsonify, g, current_app, send_file
from werkzeug.utils import secure_filename
from datetime import datetime
from pathlib import Path

logger = logging.getLogger(__name__)

# 创建API蓝图
api_bp = Blueprint('api', __name__, url_prefix='/api/v1')

def verify_token_manually():
    """手动验证JWT令牌的辅助函数"""
    auth_service = current_app.auth_service
    
    token = None
    auth_header = request.headers.get('Authorization')
    if auth_header and auth_header.startswith('Bearer '):
        token = auth_header.split(' ')[1]
    
    if not token:
        return jsonify({'message': '缺少认证令牌'}), 401
    
    try:
        user = auth_service.verify_token(token)
        g.user = user
        return None  # 验证成功，返回None
    except ValueError as e:
        return jsonify({'message': str(e)}), 401
    except Exception as e:
        logger.error(f"令牌验证时出错: {str(e)}")
        return jsonify({'message': '令牌验证失败'}), 401

# 健康检查端点
@api_bp.route('/health', methods=['GET'])
def health_check():
    """系统健康检查"""
    try:
        # 检查Orthanc服务器状态
        orthanc_status = current_app.orthanc_service.check_health()
        
        # 检查OHIF查看器状态
        ohif_status = current_app.ohif_service.check_health()
        
        # 获取YOLO模型信息
        model_info = current_app.detection_service.get_model_info()
        
        return jsonify({
            'status': 'ok',
            'timestamp': datetime.now().isoformat(),
            'services': {
                'orthanc': orthanc_status,
                'ohif': ohif_status,
                'yolo': model_info
            }
        })
    except Exception as e:
        logger.error(f"健康检查时出错: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@api_bp.route('/models', methods=['GET'])
def get_available_models():
    """获取可用的YOLO模型列表"""
    try:
        from config import Config
        model_dir = Config.YOLO.MODEL_PATH
        
        if not os.path.exists(model_dir):
            return jsonify({
                'models': [],
                'current_model': None,
                'message': '模型目录不存在'
            })
        
        # 扫描模型目录
        models = []
        for file_path in Path(model_dir).glob('*.pt'):
            model_info = {
                'name': file_path.name,
                'path': str(file_path),
                'size': file_path.stat().st_size,
                'modified': file_path.stat().st_mtime
            }
            models.append(model_info)
        
        # 获取当前使用的模型
        current_model = getattr(current_app.detection_service, 'model_path', None)
        if current_model:
            current_model = os.path.basename(current_model)
        
        return jsonify({
            'models': models,
            'current_model': current_model,
            'model_dir': model_dir
        })
        
    except Exception as e:
        logger.error(f"获取模型列表时出错: {str(e)}")
        return jsonify({
            'message': '获取模型列表失败',
            'error': str(e)
        }), 500


@api_bp.route('/models/switch', methods=['POST'])
def switch_model():
    """切换YOLO模型"""
    try:
        # 记录请求信息
        logger.info(f"收到模型切换请求，Content-Type: {request.content_type}")
        logger.info(f"请求数据: {request.get_data(as_text=True)}")
        
        data = request.get_json()
        logger.info(f"解析后的JSON数据: {data}")
        
        if not data:
            logger.warning("请求数据为空或无法解析JSON")
            return jsonify({'message': '请求数据为空或格式错误'}), 400
            
        if 'model_name' not in data:
            logger.warning(f"请求数据中缺少model_name字段: {data}")
            return jsonify({'message': '请提供模型名称'}), 400
        
        model_name = data['model_name']
        logger.info(f"请求切换到模型: {model_name}")
        
        from config import Config
        model_path = os.path.join(Config.YOLO.MODEL_PATH, model_name)
        
        if not os.path.exists(model_path):
            return jsonify({'message': f'模型文件不存在: {model_name}'}), 404
        
        # 重新初始化detection_service
        from services.detection_service import DetectionService
        
        new_detection_service = DetectionService(
            model_path=model_path,
            confidence_threshold=Config.YOLO.CONFIDENCE_THRESHOLD,
            iou_threshold=Config.YOLO.IOU_THRESHOLD,
            device=Config.YOLO.DEVICE
        )
        
        # 替换当前的detection_service
        current_app.detection_service = new_detection_service
        
        logger.info(f"成功切换到模型: {model_name}")
        
        # 获取类别数量
        class_count = 0
        if hasattr(new_detection_service, 'class_names'):
            if isinstance(new_detection_service.class_names, dict):
                class_count = len(new_detection_service.class_names)
            elif isinstance(new_detection_service.class_names, list):
                class_count = len(new_detection_service.class_names)
        
        return jsonify({
            'message': f'成功切换到模型: {model_name}',
            'current_model': model_name,
            'model_info': {
                'path': model_path,
                'classes': class_count
            }
        })
        
    except Exception as e:
        logger.error(f"切换模型时出错: {str(e)}")
        return jsonify({
            'message': '切换模型失败',
            'error': str(e)
        }), 500

# 用户认证路由
@api_bp.route('/auth/register', methods=['POST'])
def register():
    """注册新用户"""
    try:
        data = request.get_json()
        
        # 验证必要字段
        if not all(k in data for k in ['username', 'password']):
            return jsonify({'message': '缺少必要字段'}), 400
        
        # 注册用户
        user_id = current_app.auth_service.register_user(
            username=data['username'],
            password=data['password'],
            email=data.get('email'),
            role=data.get('role', 'user')
        )
        
        return jsonify({
            'message': '用户注册成功',
            'user_id': user_id
        }), 201
    except ValueError as e:
        return jsonify({'message': str(e)}), 400
    except Exception as e:
        logger.error(f"注册用户时出错: {str(e)}")
        return jsonify({'message': '注册失败'}), 500

@api_bp.route('/auth/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        
        # 验证必要字段
        if not all(k in data for k in ['username', 'password']):
            return jsonify({'message': '缺少必要字段'}), 400
        
        # 用户登录
        token, user = current_app.auth_service.login(
            username=data['username'],
            password=data['password']
        )
        
        return jsonify({
            'message': '登录成功',
            'token': token,
            'user': user
        })
    except ValueError as e:
        return jsonify({'message': str(e)}), 401
    except Exception as e:
        logger.error(f"用户登录时出错: {str(e)}")
        return jsonify({'message': '登录失败'}), 500

# 受保护的路由示例
@api_bp.route('/profile', methods=['GET'])
def get_profile():
    """获取用户个人资料"""
    # 验证令牌
    auth_error = verify_token_manually()
    if auth_error:
        return auth_error
    
    return jsonify({
        'user': g.user
    })

# DICOM上传路由
@api_bp.route('/upload', methods=['POST'])
def upload_dicom():
    """上传DICOM文件"""
    try:
        # 检查是否有文件（支持单文件和多文件上传）
        files_to_upload = []
        
        if 'file' in request.files:
            # 单文件上传
            file = request.files['file']
            if file.filename != '':
                files_to_upload.append(file)
        
        if 'files[]' in request.files:
            # 多文件上传（文件夹）
            files = request.files.getlist('files[]')
            for file in files:
                if file.filename != '':
                    files_to_upload.append(file)
        
        if not files_to_upload:
            return jsonify({'message': '没有选择文件'}), 400
        
        # 处理所有文件
        temp_dir = tempfile.mkdtemp()
        all_results = []
        
        try:
            for file in files_to_upload:
                # 保存文件到临时目录
                filename = secure_filename(file.filename)
                file_path = os.path.join(temp_dir, filename)
                file.save(file_path)
                
                # 上传到Orthanc
                result = current_app.orthanc_service.upload_dicom(file_path)
                all_results.append(result)
                
                # 清理单个文件
                try:
                    os.remove(file_path)
                except:
                    pass
            
            # 合并结果
            if len(all_results) == 1:
                result = all_results[0]
            else:
                # 多文件上传结果合并
                result = {
                    'status': 'success',
                    'message': f'成功上传 {len([r for r in all_results if r.get("status") == "success"])} 个文件',
                    'files_processed': len(all_results),
                    'results': all_results
                }
            
            # 处理数据库保存和审计日志
            user_id = g.user['id'] if hasattr(g, 'user') else None
            
            if len(all_results) == 1:
                # 单文件上传
                if result.get('status') == 'success' and 'studyID' in result:
                    study_id = current_app.database_service.add_study(
                        orthanc_id=result['studyID'],
                        patient_name=result.get('patientName', 'Unknown'),
                        patient_id=result.get('patientID', 'Unknown'),
                        study_date=result.get('studyDate', ''),
                        study_description=result.get('studyDescription', 'No description'),
                        modality=result.get('modality', 'Unknown'),
                        series_count=result.get('seriesCount', 0),
                        instance_count=result.get('instanceCount', 0)
                    )
                    
                    # 添加审计日志
                    current_app.database_service.add_audit_log(
                        user_id=user_id,
                        action='upload',
                        entity_type='study',
                        entity_id=result['studyID'],
                        details={
                            'patient_name': result.get('patientName', 'Unknown'),
                            'study_description': result.get('studyDescription', 'No description')
                        },
                        ip_address=request.remote_addr
                    )
            else:
                # 多文件上传
                for single_result in all_results:
                    if single_result.get('status') == 'success' and 'studyID' in single_result:
                        try:
                            study_id = current_app.database_service.add_study(
                                orthanc_id=single_result['studyID'],
                                patient_name=single_result.get('patientName', 'Unknown'),
                                patient_id=single_result.get('patientID', 'Unknown'),
                                study_date=single_result.get('studyDate', ''),
                                study_description=single_result.get('studyDescription', 'No description'),
                                modality=single_result.get('modality', 'Unknown'),
                                series_count=single_result.get('seriesCount', 0),
                                instance_count=single_result.get('instanceCount', 0)
                            )
                            
                            # 添加审计日志
                            current_app.database_service.add_audit_log(
                                user_id=user_id,
                                action='upload',
                                entity_type='study',
                                entity_id=single_result['studyID'],
                                details={
                                    'patient_name': single_result.get('patientName', 'Unknown'),
                                    'study_description': single_result.get('studyDescription', 'No description')
                                },
                                ip_address=request.remote_addr
                            )
                        except Exception as e:
                            logger.warning(f"保存研究信息时出错: {str(e)}")
            
            return jsonify(result)
        finally:
            # 清理临时目录
            try:
                os.rmdir(temp_dir)
            except:
                pass
    except Exception as e:
        logger.error(f"上传DICOM文件时出错: {str(e)}")
        logger.error(f"请求文件字段: {list(request.files.keys())}")
        logger.error(f"文件数量: {len(files_to_upload) if 'files_to_upload' in locals() else 0}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# 研究列表路由
@api_bp.route('/studies', methods=['GET'])
def get_studies():
    """获取研究列表"""
    try:
        # 从Orthanc获取研究列表
        studies = current_app.orthanc_service.get_studies()
        return jsonify(studies)
    except Exception as e:
        logger.error(f"获取研究列表时出错: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# YOLO检测路由
@api_bp.route('/detect/<study_id>', methods=['POST'])
def detect_study(study_id):
    """对研究进行YOLO检测"""
    try:
        # 获取参数
        data = request.get_json() or {}
        confidence = float(data.get('confidence', current_app.config.get('YOLO_CONFIDENCE_THRESHOLD', 0.25)))
        save_images = data.get('save_images', True)
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        output_dir = os.path.join(temp_dir, 'detection_results')
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            # 下载DICOM文件
            dicom_files = current_app.orthanc_service.download_study(study_id, temp_dir)
            
            # 执行YOLO检测
            detection_results = current_app.detection_service.detect_dicom(
                dicom_files, output_dir, save_images)
            
            # 保存检测结果到数据库
            user_id = g.user['id'] if hasattr(g, 'user') else None
            db_study = current_app.database_service.get_study(study_id)
            if db_study:
                result_id = current_app.database_service.add_detection_result(
                    study_id=db_study['id'],
                    user_id=user_id,
                    model_name=os.path.basename(current_app.config.get('YOLO_MODEL_PATH', '')),
                    confidence_threshold=confidence,
                    result_data=detection_results
                )
            
            # 保存检测结果为JSON文件
            result_json_path = os.path.join(output_dir, f"{study_id}_results.json")
            current_app.detection_service.export_results(detection_results, result_json_path)
            
            # 生成OHIF查看器URL
            viewer_url = current_app.ohif_service.get_viewer_url(study_id)
            
            # 添加审计日志
            current_app.database_service.add_audit_log(
                user_id=user_id,
                action='detect',
                entity_type='study',
                entity_id=study_id,
                details={
                    'confidence': confidence,
                    'detection_count': sum(len(r.get('detections', [])) for r in detection_results)
                },
                ip_address=request.remote_addr
            )
            
            return jsonify({
                'status': 'success',
                'study_id': study_id,
                'detection_results': detection_results,
                'viewer_url': viewer_url
            })
        finally:
            # 清理临时文件（可选，取决于是否需要保留结果）
            if not save_images:
                try:
                    import shutil
                    shutil.rmtree(temp_dir)
                except:
                    pass
    except Exception as e:
        logger.error(f"执行YOLO检测时出错: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# 获取检测结果路由
@api_bp.route('/results/<study_id>', methods=['GET'])
def get_detection_results(study_id):
    """获取研究的检测结果"""
    try:
        # 获取数据库中的研究信息
        study = current_app.database_service.get_study(study_id)
        if not study:
            return jsonify({'message': '研究不存在'}), 404
        
        # 获取检测结果
        results = current_app.database_service.get_detection_results(study['id'])
        
        return jsonify({
            'study_id': study_id,
            'results': results
        })
    except Exception as e:
        logger.error(f"获取检测结果时出错: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# 获取检测结果图像路由
@api_bp.route('/results/<study_id>/images/<instance_id>', methods=['GET'])
def get_result_image(study_id, instance_id):
    """获取检测结果图像"""
    try:
        # 获取数据库中的研究信息
        study = current_app.database_service.get_study(study_id)
        if not study:
            return jsonify({'message': '研究不存在'}), 404
        
        # 获取检测结果
        results = current_app.database_service.get_detection_results(study['id'])
        
        # 查找对应实例的结果图像
        image_path = None
        for result in results:
            result_data = result.get('result_data', [])
            if isinstance(result_data, str):
                try:
                    result_data = json.loads(result_data)
                except:
                    result_data = []
            
            for item in result_data:
                if os.path.basename(item.get('file', '')).startswith(instance_id) and item.get('result_image'):
                    image_path = item['result_image']
                    break
            
            if image_path:
                break
        
        if not image_path or not os.path.exists(image_path):
            return jsonify({'message': '结果图像不存在'}), 404
        
        # 返回图像文件
        return send_file(image_path, mimetype='image/jpeg')
    except Exception as e:
        logger.error(f"获取结果图像时出错: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# OHIF查看器URL路由
@api_bp.route('/viewer/<study_id>', methods=['GET'])
def get_viewer_url(study_id):
    """获取OHIF查看器URL"""
    try:
        # 获取OHIF查看器URL
        viewer_url = current_app.ohif_service.get_viewer_url(study_id)
        
        return jsonify({
            'study_id': study_id,
            'viewer_url': viewer_url
        })
    except Exception as e:
        logger.error(f"获取查看器URL时出错: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# 保存标注路由
@api_bp.route('/annotations/<study_id>', methods=['POST'])
def save_annotation(study_id):
    """保存标注"""
    # 验证令牌
    auth_error = verify_token_manually()
    if auth_error:
        return auth_error
    
    try:
        data = request.get_json()
        
        # 验证必要字段
        if not all(k in data for k in ['instance_id', 'annotation_data']):
            return jsonify({'message': '缺少必要字段'}), 400
        
        # 获取数据库中的研究信息
        study = current_app.database_service.get_study(study_id)
        if not study:
            return jsonify({'message': '研究不存在'}), 404
        
        # 保存标注
        annotation_id = current_app.database_service.add_annotation(
            study_id=study['id'],
            user_id=g.user['id'],
            instance_id=data['instance_id'],
            annotation_data=data['annotation_data']
        )
        
        # 添加审计日志
        current_app.database_service.add_audit_log(
            user_id=g.user['id'],
            action='save_annotation',
            entity_type='instance',
            entity_id=data['instance_id'],
            details={'study_id': study_id},
            ip_address=request.remote_addr
        )
        
        return jsonify({
            'status': 'success',
            'annotation_id': annotation_id
        })
    except Exception as e:
        logger.error(f"保存标注时出错: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# 获取标注路由
@api_bp.route('/annotations/<study_id>', methods=['GET'])
def get_annotations(study_id):
    """获取研究的标注"""
    try:
        # 获取数据库中的研究信息
        study = current_app.database_service.get_study(study_id)
        if not study:
            return jsonify({'message': '研究不存在'}), 404
        
        # 获取用户ID（如果已认证）
        user_id = g.user['id'] if hasattr(g, 'user') else None
        
        # 获取标注
        annotations = current_app.database_service.get_annotations(study['id'], user_id)
        
        return jsonify({
            'study_id': study_id,
            'annotations': annotations
        })
    except Exception as e:
        logger.error(f"获取标注时出错: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@api_bp.route('/upload-and-detect', methods=['POST'])
def upload_and_detect():
    """一体化上传和检测流程"""
    try:
        # 检查是否有文件
        files_to_upload = []
        
        if 'file' in request.files:
            # 单文件上传
            file = request.files['file']
            if file.filename != '':
                files_to_upload.append(file)
        
        if 'files[]' in request.files:
            # 多文件上传
            files = request.files.getlist('files[]')
            for file in files:
                if file.filename != '':
                    files_to_upload.append(file)
        
        if not files_to_upload:
            return jsonify({'message': '没有选择文件'}), 400
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        output_dir = os.path.join(temp_dir, 'detection_results')
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            # 第一步：上传文件到Orthanc
            upload_results = []
            study_ids = []
            
            for file in files_to_upload:
                # 保存文件到临时目录
                filename = secure_filename(file.filename)
                file_path = os.path.join(temp_dir, filename)
                file.save(file_path)
                
                # 上传到Orthanc
                result = current_app.orthanc_service.upload_dicom(file_path)
                upload_results.append(result)
                
                if result.get('status') == 'success' and 'studyID' in result:
                    study_ids.append(result['studyID'])
                    
                    # 保存到数据库
                    user_id = g.user['id'] if hasattr(g, 'user') else None
                    db_study_id = current_app.database_service.add_study(
                        orthanc_id=result['studyID'],
                        patient_name=result.get('patientName', 'Unknown'),
                        patient_id=result.get('patientID', 'Unknown'),
                        study_date=result.get('studyDate', ''),
                        study_description=result.get('studyDescription', 'No description'),
                        modality=result.get('modality', 'Unknown'),
                        series_count=result.get('seriesCount', 0),
                        instance_count=result.get('instanceCount', 0)
                    )
                
                # 清理单个文件
                try:
                    os.remove(file_path)
                except:
                    pass
            
            if not study_ids:
                return jsonify({
                    'status': 'error',
                    'message': '文件上传失败，请检查DICOM文件格式'
                }), 400
            
            # 第二步：对所有上传的研究进行检测
            all_detection_results = []
            main_study_id = study_ids[0]  # 使用第一个研究作为主要研究
            
            for study_id in study_ids:
                try:
                    # 下载DICOM文件
                    dicom_files = current_app.orthanc_service.download_study(study_id, temp_dir)
                    
                    # 执行YOLO检测（使用默认参数）
                    confidence = current_app.config.get('YOLO_CONFIDENCE_THRESHOLD', 0.25)
                    detection_results = current_app.detection_service.detect_dicom(
                        dicom_files, output_dir, save_images=True)
                    
                    all_detection_results.extend(detection_results)
                    
                    # 保存检测结果到数据库
                    user_id = g.user['id'] if hasattr(g, 'user') else None
                    db_study = current_app.database_service.get_study(study_id)
                    if db_study:
                        result_id = current_app.database_service.add_detection_result(
                            study_id=db_study['id'],
                            user_id=user_id,
                            model_name=os.path.basename(current_app.config.get('YOLO_MODEL_PATH', '')),
                            confidence_threshold=confidence,
                            result_data=detection_results
                        )
                    
                    # 保存检测结果为JSON文件
                    result_json_path = os.path.join(output_dir, f"{study_id}_results.json")
                    current_app.detection_service.export_results(detection_results, result_json_path)
                    
                except Exception as e:
                    logger.error(f"检测研究 {study_id} 时出错: {str(e)}")
                    continue
            
            # 第三步：生成OHIF查看器URL（使用主要研究）
            viewer_url = current_app.ohif_service.get_viewer_url(main_study_id)
            
            # 添加审计日志
            user_id = g.user['id'] if hasattr(g, 'user') else None
            current_app.database_service.add_audit_log(
                user_id=user_id,
                action='upload_and_detect',
                entity_type='study',
                entity_id=main_study_id,
                details={
                    'files_uploaded': len(files_to_upload),
                    'studies_processed': len(study_ids),
                    'total_detections': sum(len(r.get('detections', [])) for r in all_detection_results)
                },
                ip_address=request.remote_addr
            )
            
            return jsonify({
                'status': 'success',
                'message': f'成功处理 {len(files_to_upload)} 个文件，检测到 {sum(len(r.get("detections", [])) for r in all_detection_results)} 个病灶',
                'study_id': main_study_id,
                'study_ids': study_ids,
                'detection_results': all_detection_results,
                'viewer_url': viewer_url,
                'upload_results': upload_results
            })
            
        finally:
            # 清理临时文件（保留检测结果）
            try:
                import shutil
                # 只清理DICOM文件，保留检测结果
                for file in os.listdir(temp_dir):
                    file_path = os.path.join(temp_dir, file)
                    if os.path.isfile(file_path) and file.endswith(('.dcm', '.dicom')):
                        os.remove(file_path)
            except:
                pass
                
    except Exception as e:
        logger.error(f"一体化上传检测时出错: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'处理失败: {str(e)}'
        }), 500

# DICOM查看器支持的API端点
@api_bp.route('/detection-results/<result_id>', methods=['GET'])
def get_detection_result(result_id):
    """获取检测结果详情"""
    try:
        result = current_app.database_service.get_detection_result(result_id)
        
        if result:
            return jsonify({
                'success': True,
                'result': result
            })
        else:
            return jsonify({
                'success': False,
                'message': '检测结果不存在'
            }), 404
            
    except Exception as e:
        logger.error(f"获取检测结果失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取检测结果失败: {str(e)}'
        }), 500

@api_bp.route('/detection-results/<study_id>', methods=['GET'])
def get_study_detection_results(study_id):
    """获取研究的所有检测结果（兼容前端调用）"""
    try:
        # 获取数据库中的研究信息
        study = current_app.database_service.get_study(study_id)
        if not study:
            return jsonify({
                'success': False,
                'message': '研究不存在'
            }), 404
        
        # 获取检测结果
        detections = current_app.database_service.get_detection_results(study['id'])
        
        return jsonify({
            'success': True,
            'results': detections
        })
        
    except Exception as e:
        logger.error(f"获取研究检测结果失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取研究检测结果失败: {str(e)}'
        }), 500

@api_bp.route('/studies/<study_id>', methods=['GET'])
def get_study_info(study_id):
    """获取研究信息和序列列表"""
    try:
        study_info = current_app.orthanc_service.get_study_info(study_id)
        
        if study_info:
            # 获取序列列表
            series_list = current_app.orthanc_service.get_study_series(study_id)
            
            return jsonify({
                'success': True,
                'study': study_info,
                'series': series_list
            })
        else:
            return jsonify({
                'success': False,
                'message': '研究不存在'
            }), 404
            
    except Exception as e:
        logger.error(f"获取研究信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取研究信息失败: {str(e)}'
        }), 500

@api_bp.route('/series/<series_id>/images', methods=['GET'])
def get_series_images(series_id):
    """获取序列的图像列表"""
    try:
        images = current_app.orthanc_service.get_series_images(series_id)
        
        return jsonify({
            'success': True,
            'images': images
        })
        
    except Exception as e:
        logger.error(f"获取序列图像失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取序列图像失败: {str(e)}'
        }), 500

@api_bp.route('/images/<image_id>/dicom', methods=['GET'])
def get_dicom_image(image_id):
    """获取DICOM图像数据"""
    try:
        dicom_data = current_app.orthanc_service.get_dicom_file(image_id)
        
        if dicom_data:
            from flask import make_response
            response = make_response(dicom_data)
            response.headers['Content-Type'] = 'application/dicom'
            response.headers['Content-Disposition'] = f'inline; filename="{image_id}.dcm"'
            return response
        else:
            return jsonify({
                'success': False,
                'message': 'DICOM图像不存在'
            }), 404
            
    except Exception as e:
        logger.error(f"获取DICOM图像失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取DICOM图像失败: {str(e)}'
        }), 500

@api_bp.route('/studies/<study_id>/detections', methods=['GET'])
def get_study_detections(study_id):
    """获取研究的检测结果"""
    try:
        # 获取数据库中的研究信息
        study = current_app.database_service.get_study(study_id)
        if not study:
            return jsonify({
                'success': False,
                'message': '研究不存在'
            }), 404
        
        # 获取检测结果
        detections = current_app.database_service.get_detection_results(study['id'])
        
        return jsonify({
            'success': True,
            'detections': detections
        })
        
    except Exception as e:
        logger.error(f"获取研究检测结果失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取研究检测结果失败: {str(e)}'
        }), 500


@api_bp.route('/studies/<study_id>/detections', methods=['DELETE'])
def delete_study_detections(study_id):
    """删除研究的检测结果"""
    try:
        # 获取数据库中的研究信息
        study = current_app.database_service.get_study(study_id)
        if not study:
            return jsonify({
                'success': False,
                'message': '研究不存在'
            }), 404
        
        # 删除检测结果
        current_app.database_service.delete_detection_results(study['id'])
        
        return jsonify({
            'success': True,
            'message': '检测结果已删除'
        })
        
    except Exception as e:
        logger.error(f"删除研究检测结果失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除研究检测结果失败: {str(e)}'
        }), 500


# AI扩展API端点
@api_bp.route('/ai/models', methods=['GET'])
def get_ai_models():
    """获取可用的AI模型列表"""
    try:
        from config import Config
        model_dir = Config.YOLO.MODEL_PATH
        current_model = Config.YOLO.DEFAULT_MODEL
        
        models = []
        
        if os.path.exists(model_dir):
            # 扫描模型目录
            for filename in os.listdir(model_dir):
                if filename.lower().endswith('.pt'):
                    model_path = os.path.join(model_dir, filename)
                    file_size = os.path.getsize(model_path)
                    
                    # 根据文件名推断模型类型和描述
                    model_name = filename.replace('.pt', '')
                    if 'yolo11' in filename.lower() or 'yolov11' in filename.lower():
                        description = f'YOLOv11 {model_name.upper()} 模型'
                    elif 'best' in filename.lower():
                        description = '自定义训练的最佳模型'
                    else:
                        description = f'YOLO {model_name.upper()} 模型'
                    
                    # 判断模型类型
                    if 'cls' in filename.lower():
                        model_type = '分类模型'
                    elif 'seg' in filename.lower():
                        model_type = '分割模型'
                    else:
                        model_type = '检测模型'
                    
                    models.append({
                        'id': model_name,
                        'name': f'{model_name.upper()} ({model_type})',
                        'description': description,
                        'filename': filename,
                        'modality': 'CR',
                        'is_active': filename == current_model,
                        'confidence_threshold': current_app.detection_service.confidence_threshold,
                        'file_size': file_size,
                        'file_size_mb': round(file_size / (1024 * 1024), 2)
                    })
        
        # 如果没有找到模型，添加默认模型信息
        if not models:
            models.append({
                'id': 'yolo_chest_xray',
                'name': 'YOLO胸部X光检测 (未找到模型文件)',
                'description': '默认YOLO模型配置，但模型文件不存在',
                'modality': 'CR',
                'is_active': True,
                'confidence_threshold': current_app.detection_service.confidence_threshold,
                'status': 'missing'
            })
        
        # 按文件名排序
        models.sort(key=lambda x: x['filename'] if 'filename' in x else x['id'])
        
        return jsonify({
            'success': True,
            'models': models,
            'total_count': len(models),
            'model_directory': model_dir
        })
        
    except Exception as e:
        logger.error(f"获取AI模型列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取AI模型列表失败: {str(e)}'
        }), 500


@api_bp.route('/ai/predict', methods=['POST'])
def ai_predict():
    """AI预测端点"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据为空'
            }), 400
        
        # 获取预测参数
        model_id = data.get('model_id', 'yolo_chest_xray')
        study_id = data.get('study_id')
        image_id = data.get('image_id')  # 前端传递的是image_id，对应后端的instance_id
        options = data.get('options', {})
        
        if not study_id:
            return jsonify({
                'success': False,
                'message': '缺少研究ID'
            }), 400
        
        # 获取置信度阈值
        confidence_threshold = options.get('minConfidence', current_app.detection_service.confidence_threshold)
        
        # 获取研究信息
        study = current_app.database_service.get_study(study_id)
        if not study:
            logger.warning(f"未在数据库中找到研究: {study_id}，尝试从Orthanc获取")
            # 尝试从Orthanc获取研究信息
            orthanc_study = current_app.orthanc_service.get_study_info(study_id)
            if not orthanc_study:
                return jsonify({
                    'success': False,
                    'message': f'未找到研究: {study_id}'
                }), 404
            
            # 使用Orthanc研究信息
            study = orthanc_study
        
        # 获取DICOM文件路径或直接从Orthanc获取图像数据
        instance_id = None
        if image_id:
            # 从image_id中提取instance_id
            # 假设image_id格式为: "instances/{instance_id}"
            parts = image_id.split('/')
            if len(parts) > 1:
                instance_id = parts[-1]
            else:
                instance_id = image_id
            
            logger.info(f"准备预测单个实例: {instance_id}")
        
        # 从Orthanc获取DICOM数据
        try:
            if instance_id:
                # 获取单个实例的DICOM数据
                dicom_data = current_app.orthanc_service.get_instance_dicom(instance_id)
                if not dicom_data:
                    return jsonify({
                        'success': False,
                        'message': f'未找到DICOM实例: {instance_id}'
                    }), 404
                
                # 保存到临时文件
                with tempfile.NamedTemporaryFile(suffix='.dcm', delete=False) as temp_file:
                    temp_file.write(dicom_data)
                    temp_path = temp_file.name
                
                # 执行YOLO检测
                detection_results = current_app.detection_service.detect_single_dicom(
                    temp_path,
                    confidence_threshold=confidence_threshold
                )
                
                # 删除临时文件
                try:
                    os.unlink(temp_path)
                except Exception as e:
                    logger.warning(f"删除临时文件失败: {str(e)}")
            else:
                # 获取研究的所有实例
                instances = current_app.orthanc_service.get_study_instances(study_id)
                if not instances:
                    return jsonify({
                        'success': False,
                        'message': f'研究没有DICOM实例: {study_id}'
                    }), 404
                
                # 创建临时目录
                temp_dir = tempfile.mkdtemp()
                temp_files = []
                
                # 下载所有实例
                for instance in instances:
                    # 处理不同的键名格式
                    instance_id = instance.get('id') or instance.get('ID')
                    if not instance_id:
                        logger.warning(f"实例缺少ID字段: {instance}")
                        continue
                        
                    dicom_data = current_app.orthanc_service.get_instance_dicom(instance_id)
                    if dicom_data:
                        temp_path = os.path.join(temp_dir, f"{instance_id}.dcm")
                        with open(temp_path, 'wb') as temp_file:
                            temp_file.write(dicom_data)
                        temp_files.append(temp_path)
                
                # 临时设置置信度阈值
                original_threshold = current_app.detection_service.confidence_threshold
                current_app.detection_service.confidence_threshold = confidence_threshold
                
                try:
                    # 执行YOLO检测
                    detection_results = current_app.detection_service.detect_dicom(temp_files)
                finally:
                    # 恢复原始置信度阈值
                    current_app.detection_service.confidence_threshold = original_threshold
                
                # 清理临时文件
                try:
                    shutil.rmtree(temp_dir, ignore_errors=True)
                except Exception as e:
                    logger.warning(f"删除临时目录失败: {str(e)}")
        except Exception as e:
            logger.error(f"从Orthanc获取DICOM数据失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'获取DICOM数据失败: {str(e)}'
            }), 500
        
        # 格式化检测结果
        formatted_detections = []
        if isinstance(detection_results, dict):
            # 单个文件的检测结果
            if detection_results.get('detections'):
                for detection in detection_results['detections']:
                    formatted_detections.append({
                        'class_name': detection.get('class', 'unknown'),
                        'confidence': detection.get('confidence', 0),
                        'bbox': [
                            detection.get('x1', 0),
                            detection.get('y1', 0),
                            detection.get('x2', 0),
                            detection.get('y2', 0)
                        ],
                        'instance_id': instance_id or 'unknown'
                    })
        else:
            # 多个文件的检测结果
            for result in detection_results:
                if result.get('detections'):
                    for detection in result['detections']:
                        formatted_detections.append({
                             'class_name': detection.get('class', 'unknown'),
                             'confidence': detection.get('confidence', 0),
                             'bbox': [
                                 detection.get('x1', 0),
                                 detection.get('y1', 0),
                                 detection.get('x2', 0),
                                 detection.get('y2', 0)
                             ],
                             'instance_id': result.get('instance_id', 'unknown')
                         })
        
        # 保存检测结果到数据库
        if formatted_detections:
            try:
                result_id = current_app.database_service.add_detection_result(
                    study_id=study_id,
                    user_id=1,  # 默认用户ID，实际应该从认证中获取
                    model_name='YOLO胸部X光检测',
                    confidence_threshold=confidence_threshold,
                    result_data=detection_results if isinstance(detection_results, list) else [detection_results]
                )
                logger.info(f"AI预测结果已保存到数据库，ID: {result_id}")
            except Exception as save_error:
                logger.warning(f"保存检测结果失败: {str(save_error)}")
        
        result = {
            'success': True,
            'model_id': model_id,
            'model_name': 'YOLO胸部X光检测',
            'detections': formatted_detections,
            'detection_count': len(formatted_detections),
            'timestamp': datetime.now().isoformat(),
            'confidence_threshold': confidence_threshold,
            'message': f'检测完成，发现 {len(formatted_detections)} 个目标'
        }
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"AI预测失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'AI预测失败: {str(e)}'
        }), 500


@api_bp.route('/ai/models/<model_id>/switch', methods=['POST'])
def switch_ai_model(model_id):
    """切换AI模型"""
    try:
        from config import Config
        model_dir = Config.YOLO.MODEL_PATH
        
        # 查找对应的模型文件
        model_filename = None
        for filename in os.listdir(model_dir):
            if filename.lower().endswith('.pt'):
                model_name = filename.replace('.pt', '')
                if model_name == model_id:
                    model_filename = filename
                    break
        
        if not model_filename:
            return jsonify({
                'success': False,
                'message': f'未找到模型: {model_id}'
            }), 404
        
        # 构建新的模型路径
        new_model_path = os.path.join(model_dir, model_filename)
        
        if not os.path.exists(new_model_path):
            return jsonify({
                'success': False,
                'message': f'模型文件不存在: {model_filename}'
            }), 404
        
        # 切换检测服务的模型
        try:
            current_app.detection_service.switch_model(new_model_path)
            logger.info(f"已切换到模型: {model_filename}")
        except Exception as switch_error:
            logger.error(f"模型切换失败: {str(switch_error)}")
            return jsonify({
                'success': False,
                'message': f'模型切换失败: {str(switch_error)}'
            }), 500
        
        # 获取新模型信息
        try:
            model_info = current_app.detection_service.get_current_model_info()
        except:
            model_info = {}
        
        return jsonify({
            'success': True,
            'message': f'已切换到模型: {model_filename}',
            'active_model': {
                'id': model_id,
                'name': f'{model_id.upper()} 模型',
                'filename': model_filename,
                'description': f'当前使用的YOLO模型: {model_filename}',
                'model_info': model_info
            }
        })
        
    except Exception as e:
        logger.error(f"切换AI模型失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'切换AI模型失败: {str(e)}'
        }), 500


@api_bp.route('/ai/models/<model_id>/info', methods=['GET'])
def get_ai_model_info(model_id):
    """获取AI模型信息"""
    try:
        if model_id != 'yolo_chest_xray':
            return jsonify({
                'success': False,
                'message': f'不支持的模型ID: {model_id}'
            }), 400
        
        model_info = current_app.detection_service.get_model_info()
        
        return jsonify({
            'success': True,
            'model_id': model_id,
            'model_name': 'YOLO胸部X光检测',
            'description': '基于YOLOv11的胸部X光疾病检测模型',
            'modality': 'CR',
            'confidence_threshold': current_app.detection_service.confidence_threshold,
            'model_info': model_info
        })
        
    except Exception as e:
        logger.error(f"获取AI模型信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取AI模型信息失败: {str(e)}'
        }), 500


@api_bp.route('/ai/extension/info', methods=['GET'])
def get_ai_extension_info():
    """获取AI扩展信息"""
    try:
        extension_info = {
            'name': 'AI预测扩展',
            'version': '2.0.0',
            'description': '基于YOLO的医学图像AI预测扩展',
            'supported_modalities': ['CR', 'DX'],
            'available_models': ['yolo_chest_xray'],
            'features': [
                '实时AI预测',
                '边界框显示',
                '置信度评分',
                '多模型支持'
            ]
        }
        
        return jsonify({
            'success': True,
            'extension_info': extension_info
        })
        
    except Exception as e:
        logger.error(f"获取AI扩展信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取AI扩展信息失败: {str(e)}'
        }), 500