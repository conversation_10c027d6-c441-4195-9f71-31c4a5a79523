import sqlite3
import threading
import logging
import time
from contextlib import contextmanager
from typing import Optional, Dict, Any, List
from queue import Queue, Empty
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class ConnectionInfo:
    """连接信息"""
    connection: sqlite3.Connection
    created_at: datetime
    last_used: datetime
    in_use: bool = False

class DatabaseConnectionPool:
    """数据库连接池"""
    
    def __init__(self, db_path: str, min_connections: int = 2, max_connections: int = 10, 
                 max_idle_time: int = 300):
        """
        初始化连接池
        
        Args:
            db_path: 数据库路径
            min_connections: 最小连接数
            max_connections: 最大连接数
            max_idle_time: 最大空闲时间（秒）
        """
        self.db_path = db_path
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.max_idle_time = max_idle_time
        
        self._connections: Dict[int, ConnectionInfo] = {}
        self._available_connections = Queue(maxsize=max_connections)
        self._lock = threading.Lock()
        self._connection_counter = 0
        self._cleanup_thread = None
        self._running = False
        
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池"""
        with self._lock:
            for _ in range(self.min_connections):
                conn_info = self._create_connection()
                if conn_info:
                    self._available_connections.put(conn_info)
        
        # 启动清理线程
        self._running = True
        self._cleanup_thread = threading.Thread(target=self._cleanup_idle_connections, daemon=True)
        self._cleanup_thread.start()
        
        logger.info(f"数据库连接池初始化完成，最小连接数: {self.min_connections}")
    
    def _create_connection(self) -> Optional[ConnectionInfo]:
        """创建新连接"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            
            # 设置连接参数
            conn.execute('PRAGMA journal_mode=WAL')
            conn.execute('PRAGMA synchronous=NORMAL')
            conn.execute('PRAGMA cache_size=10000')
            conn.execute('PRAGMA temp_store=MEMORY')
            conn.execute('PRAGMA mmap_size=268435456')  # 256MB
            
            conn_id = self._connection_counter
            self._connection_counter += 1
            
            conn_info = ConnectionInfo(
                connection=conn,
                created_at=datetime.now(),
                last_used=datetime.now()
            )
            
            self._connections[conn_id] = conn_info
            logger.debug(f"创建新数据库连接: {conn_id}")
            
            return conn_info
        except Exception as e:
            logger.error(f"创建数据库连接失败: {str(e)}")
            return None
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        conn_info = None
        try:
            # 尝试从池中获取连接
            try:
                conn_info = self._available_connections.get(timeout=5.0)
            except Empty:
                # 如果没有可用连接，尝试创建新连接
                with self._lock:
                    if len(self._connections) < self.max_connections:
                        conn_info = self._create_connection()
                
                if not conn_info:
                    raise Exception("无法获取数据库连接")
            
            # 标记连接为使用中
            conn_info.in_use = True
            conn_info.last_used = datetime.now()
            
            yield conn_info.connection
            
        finally:
            # 归还连接到池中
            if conn_info:
                conn_info.in_use = False
                conn_info.last_used = datetime.now()
                self._available_connections.put(conn_info)
    
    def _cleanup_idle_connections(self):
        """清理空闲连接"""
        while self._running:
            try:
                time.sleep(60)  # 每分钟检查一次
                
                with self._lock:
                    current_time = datetime.now()
                    connections_to_remove = []
                    
                    for conn_id, conn_info in self._connections.items():
                        if (not conn_info.in_use and 
                            (current_time - conn_info.last_used).seconds > self.max_idle_time and
                            len(self._connections) > self.min_connections):
                            connections_to_remove.append(conn_id)
                    
                    for conn_id in connections_to_remove:
                        conn_info = self._connections.pop(conn_id)
                        try:
                            conn_info.connection.close()
                            logger.debug(f"关闭空闲连接: {conn_id}")
                        except Exception as e:
                            logger.error(f"关闭连接时出错: {str(e)}")
            
            except Exception as e:
                logger.error(f"清理空闲连接时出错: {str(e)}")
    
    def close_all(self):
        """关闭所有连接"""
        self._running = False
        
        if self._cleanup_thread:
            self._cleanup_thread.join(timeout=5)
        
        with self._lock:
            for conn_info in self._connections.values():
                try:
                    conn_info.connection.close()
                except Exception as e:
                    logger.error(f"关闭连接时出错: {str(e)}")
            
            self._connections.clear()
            
            # 清空队列
            while not self._available_connections.empty():
                try:
                    self._available_connections.get_nowait()
                except Empty:
                    break
        
        logger.info("数据库连接池已关闭")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        with self._lock:
            active_connections = sum(1 for conn in self._connections.values() if conn.in_use)
            
            return {
                'total_connections': len(self._connections),
                'active_connections': active_connections,
                'available_connections': self._available_connections.qsize(),
                'max_connections': self.max_connections,
                'min_connections': self.min_connections
            }

class DatabaseOptimizer:
    """数据库优化器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
    
    def optimize_database(self) -> Dict[str, Any]:
        """优化数据库"""
        results = {
            'vacuum_completed': False,
            'analyze_completed': False,
            'reindex_completed': False,
            'size_before': 0,
            'size_after': 0,
            'optimization_time': 0
        }
        
        start_time = time.time()
        
        try:
            # 获取优化前的数据库大小
            results['size_before'] = self._get_database_size()
            
            conn = sqlite3.connect(self.db_path)
            
            # 执行VACUUM
            logger.info("开始执行VACUUM...")
            conn.execute('VACUUM')
            results['vacuum_completed'] = True
            
            # 执行ANALYZE
            logger.info("开始执行ANALYZE...")
            conn.execute('ANALYZE')
            results['analyze_completed'] = True
            
            # 重建索引
            logger.info("开始重建索引...")
            conn.execute('REINDEX')
            results['reindex_completed'] = True
            
            conn.close()
            
            # 获取优化后的数据库大小
            results['size_after'] = self._get_database_size()
            results['optimization_time'] = time.time() - start_time
            
            logger.info(f"数据库优化完成，耗时: {results['optimization_time']:.2f}秒")
            
        except Exception as e:
            logger.error(f"数据库优化失败: {str(e)}")
            results['error'] = str(e)
        
        return results
    
    def _get_database_size(self) -> int:
        """获取数据库文件大小"""
        try:
            import os
            return os.path.getsize(self.db_path)
        except Exception:
            return 0
    
    def create_indexes(self) -> List[str]:
        """创建推荐的索引"""
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_studies_orthanc_id ON studies(orthanc_id)',
            'CREATE INDEX IF NOT EXISTS idx_studies_patient_id ON studies(patient_id)',
            'CREATE INDEX IF NOT EXISTS idx_studies_study_date ON studies(study_date)',
            'CREATE INDEX IF NOT EXISTS idx_detection_results_study_id ON detection_results(study_id)',
            'CREATE INDEX IF NOT EXISTS idx_detection_results_user_id ON detection_results(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_detection_results_detection_time ON detection_results(detection_time)',
            'CREATE INDEX IF NOT EXISTS idx_annotations_study_id ON annotations(study_id)',
            'CREATE INDEX IF NOT EXISTS idx_annotations_user_id ON annotations(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp)',
            'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)'
        ]
        
        created_indexes = []
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            for index_sql in indexes:
                try:
                    conn.execute(index_sql)
                    created_indexes.append(index_sql)
                    logger.debug(f"创建索引: {index_sql}")
                except Exception as e:
                    logger.warning(f"创建索引失败: {index_sql}, 错误: {str(e)}")
            
            conn.commit()
            conn.close()
            
            logger.info(f"成功创建 {len(created_indexes)} 个索引")
            
        except Exception as e:
            logger.error(f"创建索引时出错: {str(e)}")
        
        return created_indexes
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        stats = {}
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            stats['tables'] = {}
            
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                stats['tables'][table] = {'row_count': count}
            
            # 获取数据库大小
            stats['database_size'] = self._get_database_size()
            
            # 获取页面信息
            cursor.execute("PRAGMA page_count")
            page_count = cursor.fetchone()[0]
            
            cursor.execute("PRAGMA page_size")
            page_size = cursor.fetchone()[0]
            
            stats['page_info'] = {
                'page_count': page_count,
                'page_size': page_size,
                'total_size': page_count * page_size
            }
            
            conn.close()
            
        except Exception as e:
            logger.error(f"获取数据库统计信息失败: {str(e)}")
            stats['error'] = str(e)
        
        return stats

# 全局连接池实例
_connection_pool: Optional[DatabaseConnectionPool] = None

def initialize_connection_pool(db_path: str, **kwargs):
    """初始化全局连接池"""
    global _connection_pool
    if _connection_pool:
        _connection_pool.close_all()
    
    _connection_pool = DatabaseConnectionPool(db_path, **kwargs)
    logger.info("全局数据库连接池已初始化")

def get_connection_pool() -> Optional[DatabaseConnectionPool]:
    """获取全局连接池"""
    return _connection_pool

def close_connection_pool():
    """关闭全局连接池"""
    global _connection_pool
    if _connection_pool:
        _connection_pool.close_all()
        _connection_pool = None