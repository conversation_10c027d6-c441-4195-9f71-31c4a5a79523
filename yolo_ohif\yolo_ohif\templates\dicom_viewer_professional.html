<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业DICOM阅读器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            overflow: hidden;
        }
        
        .main-container {
            display: flex;
            height: 100vh;
            width: 100vw;
        }
        
        /* 顶部标题栏 */
        .title-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 40px;
            background: #2c3e50;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            z-index: 1000;
            border-bottom: 1px solid #34495e;
        }
        
        .title-bar .logo {
            color: #3498db;
            font-weight: bold;
            font-size: 14px;
        }
        
        .title-bar .patient-info {
            color: #ecf0f1;
            font-size: 12px;
        }
        
        .title-bar .controls {
            display: flex;
            gap: 10px;
        }
        
        .title-bar .btn {
            padding: 4px 8px;
            font-size: 11px;
            background: #34495e;
            border: 1px solid #4a5f7a;
            color: #ecf0f1;
        }
        
        .title-bar .btn:hover {
            background: #4a5f7a;
        }
        
        /* 左侧工具栏 */
        .left-toolbar {
            width: 60px;
            background: #2c3e50;
            border-right: 1px solid #34495e;
            display: flex;
            flex-direction: column;
            padding: 50px 5px 10px 5px;
            gap: 5px;
        }
        
        .tool-btn {
            width: 50px;
            height: 50px;
            background: #34495e;
            border: 1px solid #4a5f7a;
            color: #bdc3c7;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            border-radius: 4px;
        }
        
        .tool-btn:hover {
            background: #4a5f7a;
            color: #ecf0f1;
        }
        
        .tool-btn.active {
            background: #3498db;
            color: #ffffff;
            border-color: #2980b9;
        }
        
        .tool-btn i {
            font-size: 18px;
        }
        
        /* 中央图像显示区域 */
        .image-area {
            flex: 1;
            background: #000000;
            position: relative;
            margin-top: 40px;
        }
        
        .viewport-container {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .dicom-viewport {
            width: 100%;
            height: 100%;
            background: #000000;
            position: relative;
            cursor: crosshair;
        }
        
        .dicom-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        /* 图像信息覆盖层 */
        .image-overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: #ffffff;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-family: 'Courier New', monospace;
            z-index: 10;
        }
        
        .image-overlay div {
            margin-bottom: 2px;
        }
        
        /* 右侧信息面板 */
        .right-panel {
            width: 300px;
            background: #2c3e50;
            border-left: 1px solid #34495e;
            display: flex;
            flex-direction: column;
            margin-top: 40px;
        }
        
        .panel-header {
            height: 40px;
            background: #34495e;
            display: flex;
            align-items: center;
            padding: 0 15px;
            border-bottom: 1px solid #4a5f7a;
            font-size: 13px;
            font-weight: bold;
            color: #ecf0f1;
        }
        
        .panel-content {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }
        
        /* 序列列表 */
        .series-section {
            margin-bottom: 20px;
        }
        
        .section-title {
            font-size: 12px;
            color: #3498db;
            margin-bottom: 8px;
            padding-bottom: 4px;
            border-bottom: 1px solid #34495e;
        }
        
        .series-item {
            background: #34495e;
            border: 1px solid #4a5f7a;
            margin-bottom: 5px;
            padding: 8px;
            cursor: pointer;
            border-radius: 3px;
            font-size: 11px;
            transition: all 0.2s;
        }
        
        .series-item:hover {
            background: #4a5f7a;
        }
        
        .series-item.active {
            background: #3498db;
            border-color: #2980b9;
        }
        
        .series-item .series-desc {
            color: #ecf0f1;
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .series-item .series-info {
            color: #bdc3c7;
            font-size: 10px;
        }
        
        /* 测量结果 */
        .measurement-section {
            margin-bottom: 20px;
        }
        
        .measurement-item {
            background: #34495e;
            border: 1px solid #4a5f7a;
            margin-bottom: 5px;
            padding: 6px 8px;
            border-radius: 3px;
            font-size: 11px;
        }
        
        .measurement-type {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .measurement-value {
            color: #ecf0f1;
            margin-top: 2px;
        }
        
        /* 图像导航 */
        .navigation-section {
            background: #34495e;
            padding: 10px;
            border-top: 1px solid #4a5f7a;
        }
        
        .nav-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .nav-btn {
            width: 30px;
            height: 30px;
            background: #2c3e50;
            border: 1px solid #4a5f7a;
            color: #bdc3c7;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 3px;
        }
        
        .nav-btn:hover {
            background: #4a5f7a;
            color: #ecf0f1;
        }
        
        .image-slider {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .slider-container {
            margin-bottom: 8px;
        }
        
        .slider-label {
            font-size: 11px;
            color: #bdc3c7;
            margin-bottom: 3px;
            display: flex;
            justify-content: space-between;
        }
        
        .slider {
            width: 100%;
            height: 4px;
            background: #4a5f7a;
            outline: none;
            border-radius: 2px;
        }
        
        .slider::-webkit-slider-thumb {
            appearance: none;
            width: 12px;
            height: 12px;
            background: #3498db;
            cursor: pointer;
            border-radius: 50%;
        }
        
        /* 检测结果 */
        .detection-section {
            margin-bottom: 20px;
        }
        
        .detection-item {
            background: #34495e;
            border: 1px solid #4a5f7a;
            margin-bottom: 5px;
            padding: 6px 8px;
            border-radius: 3px;
            font-size: 11px;
            cursor: pointer;
        }
        
        .detection-item:hover {
            background: #4a5f7a;
        }
        
        .detection-class {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .detection-confidence {
            color: #f39c12;
            margin-top: 2px;
        }
        
        .detection-coords {
            color: #bdc3c7;
            font-size: 10px;
            margin-top: 2px;
        }
        
        /* 检测框覆盖层 */
        .detection-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 5;
        }
        
        .detection-box {
            position: absolute;
            border: 2px solid #e74c3c;
            background: rgba(231, 76, 60, 0.1);
            pointer-events: none;
        }
        
        .detection-label {
            position: absolute;
            top: -20px;
            left: 0;
            background: #e74c3c;
            color: #ffffff;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 2px;
            white-space: nowrap;
        }
        
        /* 加载指示器 */
        .loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #3498db;
            font-size: 16px;
            z-index: 20;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .right-panel {
                width: 250px;
            }
        }
        
        @media (max-width: 768px) {
            .left-toolbar {
                width: 50px;
            }
            
            .tool-btn {
                width: 40px;
                height: 40px;
            }
            
            .tool-btn i {
                font-size: 14px;
            }
            
            .right-panel {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部标题栏 -->
    <div class="title-bar">
        <div class="logo">
            <i class="fas fa-eye me-2"></i>专业DICOM阅读器
        </div>
        <div class="patient-info" id="patientInfo">
            <span id="patientName">患者姓名</span> | 
            <span id="patientId">患者ID</span> | 
            <span id="studyDate">检查日期</span>
        </div>
        <div class="controls">
            <button class="btn" onclick="toggleFullscreen()">
                <i class="fas fa-expand"></i>
            </button>
            <button class="btn" onclick="window.close()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
    
    <div class="main-container">
        <!-- 左侧工具栏 -->
        <div class="left-toolbar">
            <div class="tool-btn active" id="zoomTool" title="缩放工具" onclick="setTool('zoom')">
                <i class="fas fa-search-plus"></i>
            </div>
            <div class="tool-btn" id="panTool" title="平移工具" onclick="setTool('pan')">
                <i class="fas fa-hand-paper"></i>
            </div>
            <div class="tool-btn" id="windowTool" title="窗宽窗位" onclick="setTool('window')">
                <i class="fas fa-adjust"></i>
            </div>
            <div class="tool-btn" id="lengthTool" title="长度测量" onclick="setTool('length')">
                <i class="fas fa-ruler"></i>
            </div>
            <div class="tool-btn" id="areaTool" title="面积测量" onclick="setTool('area')">
                <i class="fas fa-draw-polygon"></i>
            </div>
            <div class="tool-btn" id="probeTool" title="CT值测量" onclick="setTool('probe')">
                <i class="fas fa-crosshairs"></i>
            </div>
            <div class="tool-btn" id="resetTool" title="重置视图" onclick="resetView()">
                <i class="fas fa-undo"></i>
            </div>
            <div class="tool-btn" id="toggleDetection" title="切换检测结果" onclick="toggleDetection()">
                <i class="fas fa-search"></i>
            </div>
        </div>
        
        <!-- 中央图像显示区域 -->
        <div class="image-area">
            <div class="viewport-container">
                <div class="dicom-viewport" id="dicomViewport">
                    <canvas class="dicom-canvas" id="dicomCanvas"></canvas>
                    
                    <!-- 图像信息覆盖层 -->
                    <div class="image-overlay" id="imageOverlay">
                        <div>患者: <span id="overlayPatientName">-</span></div>
                        <div>序列: <span id="overlaySeriesDesc">-</span></div>
                        <div>图像: <span id="overlayImageNum">-</span>/<span id="overlayTotalImages">-</span></div>
                        <div>窗宽: <span id="overlayWindowWidth">-</span> 窗位: <span id="overlayWindowCenter">-</span></div>
                        <div>缩放: <span id="overlayZoom">100%</span></div>
                        <div>位置: <span id="overlayPosition">-</span></div>
                    </div>
                    
                    <!-- 检测结果覆盖层 -->
                    <div class="detection-overlay" id="detectionOverlay"></div>
                    
                    <!-- 加载指示器 -->
                    <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧信息面板 -->
        <div class="right-panel">
            <div class="panel-header">
                <i class="fas fa-info-circle me-2"></i>信息面板
            </div>
            <div class="panel-content">
                <!-- 序列列表 -->
                <div class="series-section">
                    <div class="section-title">
                        <i class="fas fa-layer-group me-1"></i>序列列表
                    </div>
                    <div id="seriesList">
                        <div class="series-item active">
                            <div class="series-desc">CT胸部平扫</div>
                            <div class="series-info">176张图像 | 1.25mm</div>
                        </div>
                        <div class="series-item">
                            <div class="series-desc">CT胸部增强</div>
                            <div class="series-info">176张图像 | 1.25mm</div>
                        </div>
                    </div>
                </div>
                
                <!-- 检测结果 -->
                <div class="detection-section">
                    <div class="section-title">
                        <i class="fas fa-search me-1"></i>检测结果 <span class="badge bg-danger ms-2" id="detectionCount">0</span>
                    </div>
                    <div id="detectionResults">
                        <div class="text-center text-muted small">暂无检测结果</div>
                    </div>
                </div>
                
                <!-- 测量结果 -->
                <div class="measurement-section">
                    <div class="section-title">
                        <i class="fas fa-ruler me-1"></i>测量结果
                        <button class="btn btn-sm btn-outline-danger ms-auto" onclick="clearMeasurements()" style="font-size: 10px; padding: 2px 6px;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div id="measurementResults">
                        <div class="text-center text-muted small">暂无测量数据</div>
                    </div>
                </div>
            </div>
            
            <!-- 底部导航控制 -->
            <div class="navigation-section">
                <div class="nav-controls">
                    <div class="nav-btn" onclick="previousImage()" title="上一张">
                        <i class="fas fa-chevron-left"></i>
                    </div>
                    <div class="nav-btn" onclick="nextImage()" title="下一张">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                    <div class="nav-btn" onclick="playImages()" title="播放" id="playBtn">
                        <i class="fas fa-play"></i>
                    </div>
                    <span style="color: #bdc3c7; font-size: 11px; margin-left: auto;">
                        <span id="currentImageIndex">1</span>/<span id="totalImageCount">1</span>
                    </span>
                </div>
                
                <div class="slider-container">
                    <div class="slider-label">
                        <span>图像</span>
                        <span id="imageSliderValue">1/1</span>
                    </div>
                    <input type="range" class="slider" id="imageSlider" min="0" max="0" value="0">
                </div>
                
                <div class="slider-container">
                    <div class="slider-label">
                        <span>窗宽</span>
                        <span id="windowWidthValue">400</span>
                    </div>
                    <input type="range" class="slider" id="windowWidthSlider" min="1" max="4000" value="400">
                </div>
                
                <div class="slider-container">
                    <div class="slider-label">
                        <span>窗位</span>
                        <span id="windowCenterValue">40</span>
                    </div>
                    <input type="range" class="slider" id="windowCenterSlider" min="-1000" max="1000" value="40">
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/cornerstone-core@2.6.1/dist/cornerstone.min.js"></script>
    <script src="https://unpkg.com/dicom-parser@1.8.21/dist/dicomParser.min.js"></script>
    <script src="https://unpkg.com/cornerstone-wado-image-loader@4.1.3/dist/cornerstoneWADOImageLoader.bundle.min.js"></script>
    <script src="https://unpkg.com/cornerstone-web-image-loader@2.1.1/dist/cornerstoneWebImageLoader.min.js"></script>
    <script src="https://unpkg.com/cornerstone-tools@4.21.1/dist/cornerstoneTools.min.js"></script>
    
    <script>
        // 全局变量
        let viewer = null;
        let currentTool = 'zoom';
        let isPlaying = false;
        let playInterval = null;
        
        // 专业DICOM查看器类
        class ProfessionalDICOMViewer {
            constructor() {
                this.element = document.getElementById('dicomViewport');
                this.canvas = document.getElementById('dicomCanvas');
                this.currentStudyId = null;
                this.currentSeriesId = null;
                this.currentImageIndex = 0;
                this.images = [];
                this.detections = [];
                this.detectionVisible = true;
                this.measurements = [];
                
                this.init();
            }
            
            init() {
                try {
                    // 初始化Cornerstone
                    cornerstone.enable(this.element);
                    
                    // 配置WADO图像加载器
                    cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
                    cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
                    
                    // 配置Web图像加载器
                    cornerstoneWebImageLoader.external.cornerstone = cornerstone;
                    
                    // 初始化工具
                    cornerstoneTools.external.cornerstone = cornerstone;
                    cornerstoneTools.external.Hammer = window.Hammer;
                    cornerstoneTools.init();
                    
                    // 添加工具
                    this.addTools();
                    
                    // 绑定事件
                    this.bindEvents();
                    
                    // 从URL参数加载研究
                    this.loadFromUrlParams();
                    
                    console.log('专业DICOM查看器初始化成功');
                } catch (error) {
                    console.error('初始化失败:', error);
                    this.showError('初始化失败: ' + error.message);
                }
            }
            
            addTools() {
                // 添加各种工具
                cornerstoneTools.addTool(cornerstoneTools.ZoomTool);
                cornerstoneTools.addTool(cornerstoneTools.PanTool);
                cornerstoneTools.addTool(cornerstoneTools.WwwcTool);
                cornerstoneTools.addTool(cornerstoneTools.LengthTool);
                cornerstoneTools.addTool(cornerstoneTools.RectangleRoiTool);
                cornerstoneTools.addTool(cornerstoneTools.ProbeTool);
                
                // 设置默认工具
                cornerstoneTools.setToolActive('Zoom', { mouseButtonMask: 1 });
            }
            
            bindEvents() {
                // 绑定滑块事件
                document.getElementById('imageSlider').addEventListener('input', (e) => {
                    this.displayImage(parseInt(e.target.value));
                });
                
                document.getElementById('windowWidthSlider').addEventListener('input', (e) => {
                    this.updateWindowLevel();
                });
                
                document.getElementById('windowCenterSlider').addEventListener('input', (e) => {
                    this.updateWindowLevel();
                });
                
                // 绑定Cornerstone事件
                this.element.addEventListener('cornerstoneimagerendered', (e) => {
                    this.updateImageInfo(e.detail);
                });
                
                this.element.addEventListener('cornerstonenewimage', (e) => {
                    this.updateImageInfo(e.detail);
                });
            }
            
            loadFromUrlParams() {
                const urlParams = new URLSearchParams(window.location.search);
                const studyId = urlParams.get('study_id');
                
                if (studyId) {
                    this.loadStudy(studyId);
                }
            }
            
            async loadStudy(studyId) {
                try {
                    this.showLoading(true);
                    this.currentStudyId = studyId;
                    
                    // 获取研究信息
                    const response = await fetch(`/api/studies/${studyId}`);
                    const study = await response.json();
                    
                    if (study.success) {
                        this.updatePatientInfo(study.data);
                        await this.loadSeries(studyId);
                        await this.loadDetectionResults(studyId);
                    } else {
                        throw new Error(study.message || '加载研究失败');
                    }
                } catch (error) {
                    console.error('加载研究失败:', error);
                    this.showError('加载研究失败: ' + error.message);
                } finally {
                    this.showLoading(false);
                }
            }
            
            async loadSeries(studyId) {
                try {
                    const response = await fetch(`/api/studies/${studyId}/series`);
                    const result = await response.json();
                    
                    if (result.success && result.data.length > 0) {
                        this.updateSeriesList(result.data);
                        // 加载第一个序列
                        await this.loadSeriesImages(result.data[0].id);
                    }
                } catch (error) {
                    console.error('加载序列失败:', error);
                }
            }
            
            async loadSeriesImages(seriesId) {
                try {
                    this.showLoading(true);
                    this.currentSeriesId = seriesId;
                    
                    const response = await fetch(`/api/series/${seriesId}/images`);
                    const result = await response.json();
                    
                    if (result.success) {
                        this.images = result.data;
                        this.updateImageSlider();
                        
                        if (this.images.length > 0) {
                            await this.displayImage(0);
                        }
                    }
                } catch (error) {
                    console.error('加载图像失败:', error);
                    this.showError('加载图像失败: ' + error.message);
                } finally {
                    this.showLoading(false);
                }
            }
            
            async displayImage(index) {
                if (index < 0 || index >= this.images.length) return;
                
                try {
                    this.currentImageIndex = index;
                    const imageId = this.images[index].wado_url;
                    
                    const image = await cornerstone.loadImage(imageId);
                    cornerstone.displayImage(this.element, image);
                    
                    this.updateImageCounter();
                    this.updateDetectionOverlay();
                    
                } catch (error) {
                    console.error('显示图像失败:', error);
                    this.showError('显示图像失败: ' + error.message);
                }
            }
            
            updatePatientInfo(study) {
                document.getElementById('patientName').textContent = study.patient_name || '未知患者';
                document.getElementById('patientId').textContent = study.patient_id || '未知ID';
                document.getElementById('studyDate').textContent = study.study_date || '未知日期';
                
                document.getElementById('overlayPatientName').textContent = study.patient_name || '-';
            }
            
            updateSeriesList(series) {
                const seriesList = document.getElementById('seriesList');
                seriesList.innerHTML = '';
                
                series.forEach((s, index) => {
                    const item = document.createElement('div');
                    item.className = `series-item ${index === 0 ? 'active' : ''}`;
                    item.onclick = () => this.selectSeries(s.id, item);
                    
                    item.innerHTML = `
                        <div class="series-desc">${s.series_description || '未知序列'}</div>
                        <div class="series-info">${s.images_count || 0}张图像 | ${s.slice_thickness || '-'}mm</div>
                    `;
                    
                    seriesList.appendChild(item);
                });
            }
            
            selectSeries(seriesId, element) {
                // 更新选中状态
                document.querySelectorAll('.series-item').forEach(item => {
                    item.classList.remove('active');
                });
                element.classList.add('active');
                
                // 加载序列图像
                this.loadSeriesImages(seriesId);
            }
            
            updateImageSlider() {
                const slider = document.getElementById('imageSlider');
                slider.max = this.images.length - 1;
                slider.value = 0;
                
                document.getElementById('totalImageCount').textContent = this.images.length;
                document.getElementById('overlayTotalImages').textContent = this.images.length;
            }
            
            updateImageCounter() {
                const current = this.currentImageIndex + 1;
                document.getElementById('currentImageIndex').textContent = current;
                document.getElementById('overlayImageNum').textContent = current;
                document.getElementById('imageSliderValue').textContent = `${current}/${this.images.length}`;
                document.getElementById('imageSlider').value = this.currentImageIndex;
            }
            
            updateImageInfo(eventData) {
                const viewport = eventData.viewport;
                const image = eventData.image;
                
                // 更新窗宽窗位
                document.getElementById('overlayWindowWidth').textContent = Math.round(viewport.voi.windowWidth);
                document.getElementById('overlayWindowCenter').textContent = Math.round(viewport.voi.windowCenter);
                document.getElementById('windowWidthValue').textContent = Math.round(viewport.voi.windowWidth);
                document.getElementById('windowCenterValue').textContent = Math.round(viewport.voi.windowCenter);
                
                // 更新缩放
                const zoom = Math.round(viewport.scale * 100);
                document.getElementById('overlayZoom').textContent = zoom + '%';
                
                // 更新滑块值
                document.getElementById('windowWidthSlider').value = viewport.voi.windowWidth;
                document.getElementById('windowCenterSlider').value = viewport.voi.windowCenter;
            }
            
            updateWindowLevel() {
                const windowWidth = parseInt(document.getElementById('windowWidthSlider').value);
                const windowCenter = parseInt(document.getElementById('windowCenterSlider').value);
                
                const viewport = cornerstone.getViewport(this.element);
                viewport.voi.windowWidth = windowWidth;
                viewport.voi.windowCenter = windowCenter;
                cornerstone.setViewport(this.element, viewport);
            }
            
            async loadDetectionResults(studyId) {
                try {
                    const response = await fetch(`/api/studies/${studyId}/detections`);
                    const result = await response.json();
                    
                    if (result.success && result.detections) {
                        this.detections = result.detections;
                        this.updateDetectionResults();
                        this.updateDetectionOverlay();
                    }
                } catch (error) {
                    console.error('加载检测结果失败:', error);
                }
            }
            
            updateDetectionResults() {
                const container = document.getElementById('detectionResults');
                const count = document.getElementById('detectionCount');
                
                if (this.detections.length === 0) {
                    container.innerHTML = '<div class="text-center text-muted small">暂无检测结果</div>';
                    count.textContent = '0';
                    return;
                }
                
                count.textContent = this.detections.length;
                container.innerHTML = '';
                
                this.detections.forEach((detection, index) => {
                    const item = document.createElement('div');
                    item.className = 'detection-item';
                    item.onclick = () => this.highlightDetection(index);
                    
                    item.innerHTML = `
                        <div class="detection-class">${detection.class}</div>
                        <div class="detection-confidence">置信度: ${(detection.confidence * 100).toFixed(1)}%</div>
                        <div class="detection-coords">位置: (${Math.round(detection.x)}, ${Math.round(detection.y)})</div>
                    `;
                    
                    container.appendChild(item);
                });
            }
            
            updateDetectionOverlay() {
                const overlay = document.getElementById('detectionOverlay');
                overlay.innerHTML = '';
                
                if (!this.detectionVisible || this.detections.length === 0) {
                    return;
                }
                
                const viewport = cornerstone.getViewport(this.element);
                if (!viewport) return;
                
                this.detections.forEach((detection, index) => {
                    const box = document.createElement('div');
                    box.className = 'detection-box';
                    
                    // 转换坐标
                    const x = (detection.x - detection.width / 2) * viewport.scale + viewport.translation.x;
                    const y = (detection.y - detection.height / 2) * viewport.scale + viewport.translation.y;
                    const width = detection.width * viewport.scale;
                    const height = detection.height * viewport.scale;
                    
                    box.style.left = x + 'px';
                    box.style.top = y + 'px';
                    box.style.width = width + 'px';
                    box.style.height = height + 'px';
                    
                    // 添加标签
                    const label = document.createElement('div');
                    label.className = 'detection-label';
                    label.textContent = `${detection.class} ${(detection.confidence * 100).toFixed(0)}%`;
                    box.appendChild(label);
                    
                    overlay.appendChild(box);
                });
            }
            
            highlightDetection(index) {
                // 高亮显示特定检测结果
                const detection = this.detections[index];
                if (!detection) return;
                
                // 可以添加特殊的高亮效果
                console.log('高亮检测结果:', detection);
            }
            
            showLoading(show) {
                const indicator = document.getElementById('loadingIndicator');
                indicator.style.display = show ? 'block' : 'none';
            }
            
            showError(message) {
                console.error(message);
                // 可以添加错误提示UI
            }
        }
        
        // 工具函数
        function setTool(toolName) {
            // 更新工具按钮状态
            document.querySelectorAll('.tool-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(toolName + 'Tool').classList.add('active');
            
            // 设置Cornerstone工具
            cornerstoneTools.setToolPassive('Zoom');
            cornerstoneTools.setToolPassive('Pan');
            cornerstoneTools.setToolPassive('Wwwc');
            cornerstoneTools.setToolPassive('Length');
            cornerstoneTools.setToolPassive('RectangleRoi');
            cornerstoneTools.setToolPassive('Probe');
            
            switch(toolName) {
                case 'zoom':
                    cornerstoneTools.setToolActive('Zoom', { mouseButtonMask: 1 });
                    break;
                case 'pan':
                    cornerstoneTools.setToolActive('Pan', { mouseButtonMask: 1 });
                    break;
                case 'window':
                    cornerstoneTools.setToolActive('Wwwc', { mouseButtonMask: 1 });
                    break;
                case 'length':
                    cornerstoneTools.setToolActive('Length', { mouseButtonMask: 1 });
                    break;
                case 'area':
                    cornerstoneTools.setToolActive('RectangleRoi', { mouseButtonMask: 1 });
                    break;
                case 'probe':
                    cornerstoneTools.setToolActive('Probe', { mouseButtonMask: 1 });
                    break;
            }
            
            currentTool = toolName;
        }
        
        function resetView() {
            if (viewer && viewer.element) {
                cornerstone.reset(viewer.element);
            }
        }
        
        function toggleDetection() {
            if (viewer) {
                viewer.detectionVisible = !viewer.detectionVisible;
                viewer.updateDetectionOverlay();
                
                const btn = document.getElementById('toggleDetection');
                if (viewer.detectionVisible) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            }
        }
        
        function previousImage() {
            if (viewer && viewer.currentImageIndex > 0) {
                viewer.displayImage(viewer.currentImageIndex - 1);
            }
        }
        
        function nextImage() {
            if (viewer && viewer.currentImageIndex < viewer.images.length - 1) {
                viewer.displayImage(viewer.currentImageIndex + 1);
            }
        }
        
        function playImages() {
            const btn = document.getElementById('playBtn');
            const icon = btn.querySelector('i');
            
            if (isPlaying) {
                // 停止播放
                clearInterval(playInterval);
                isPlaying = false;
                icon.className = 'fas fa-play';
            } else {
                // 开始播放
                isPlaying = true;
                icon.className = 'fas fa-pause';
                
                playInterval = setInterval(() => {
                    if (viewer.currentImageIndex < viewer.images.length - 1) {
                        nextImage();
                    } else {
                        // 播放完毕，重新开始
                        viewer.displayImage(0);
                    }
                }, 200); // 每200ms切换一张图像
            }
        }
        
        function clearMeasurements() {
            if (viewer && viewer.element) {
                cornerstoneTools.clearToolState(viewer.element, 'Length');
                cornerstoneTools.clearToolState(viewer.element, 'RectangleRoi');
                cornerstoneTools.clearToolState(viewer.element, 'Probe');
                cornerstone.updateImage(viewer.element);
                
                // 清除测量结果显示
                document.getElementById('measurementResults').innerHTML = 
                    '<div class="text-center text-muted small">暂无测量数据</div>';
            }
        }
        
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
        
        // 初始化查看器
        document.addEventListener('DOMContentLoaded', function() {
            viewer = new ProfessionalDICOMViewer();
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowUp':
                case 'ArrowLeft':
                    e.preventDefault();
                    previousImage();
                    break;
                case 'ArrowDown':
                case 'ArrowRight':
                    e.preventDefault();
                    nextImage();
                    break;
                case ' ':
                    e.preventDefault();
                    playImages();
                    break;
                case 'r':
                case 'R':
                    e.preventDefault();
                    resetView();
                    break;
                case 'd':
                case 'D':
                    e.preventDefault();
                    toggleDetection();
                    break;
            }
        });
    </script>
</body>
</html>