#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预训练权重训练工作流程测试脚本

此脚本用于验证预训练权重训练工作流程的完整性和正确性。
包括数据集检查、训练脚本验证、配置文件测试等。

作者: AI Assistant
创建时间: 2025
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from datetime import datetime

class PretrainedWorkflowTester:
    """预训练权重训练工作流程测试器"""
    
    def __init__(self):
        self.test_results = []
        self.current_dir = Path.cwd()
        
    def log_test(self, test_name, status, message=""):
        """记录测试结果"""
        result = {
            "test": test_name,
            "status": status,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {message}")
    
    def test_pretrained_training_files(self):
        """测试预训练权重训练相关文件是否存在"""
        print("\n🔍 测试预训练权重训练文件...")
        
        required_files = [
            "start_yolo11x_pretrained_training.py",
            "train_yolo11x_pretrained.py",
            "YOLO11X_PRETRAINED_TRAINING_GUIDE.md"
        ]
        
        for file_path in required_files:
            if os.path.exists(file_path):
                self.log_test(f"文件存在检查: {file_path}", "PASS", "文件存在")
            else:
                self.log_test(f"文件存在检查: {file_path}", "FAIL", "文件不存在")
    
    def test_pretrained_script_syntax(self):
        """测试预训练权重训练脚本语法"""
        print("\n🔍 测试预训练权重训练脚本语法...")
        
        scripts = [
            "start_yolo11x_pretrained_training.py",
            "train_yolo11x_pretrained.py"
        ]
        
        for script in scripts:
            if os.path.exists(script):
                try:
                    result = subprocess.run(
                        [sys.executable, "-m", "py_compile", script],
                        capture_output=True,
                        text=True,
                        timeout=30
                    )
                    
                    if result.returncode == 0:
                        self.log_test(f"语法检查: {script}", "PASS", "语法正确")
                    else:
                        self.log_test(f"语法检查: {script}", "FAIL", f"语法错误: {result.stderr}")
                        
                except subprocess.TimeoutExpired:
                    self.log_test(f"语法检查: {script}", "FAIL", "检查超时")
                except Exception as e:
                    self.log_test(f"语法检查: {script}", "FAIL", f"检查异常: {str(e)}")
            else:
                self.log_test(f"语法检查: {script}", "SKIP", "文件不存在")
    
    def test_dataset_detection(self):
        """测试数据集检测功能"""
        print("\n🔍 测试数据集检测功能...")
        
        # 检查是否存在YOLO数据集
        possible_paths = [
            "yolo_dataset_output/yolo_dataset",
            "dataset/yolo_dataset",
            "./yolo_dataset"
        ]
        
        dataset_found = False
        for path in possible_paths:
            if os.path.exists(path) and os.path.exists(os.path.join(path, "dataset.yaml")):
                dataset_found = True
                self.log_test("数据集检测", "PASS", f"找到数据集: {path}")
                break
        
        if not dataset_found:
            self.log_test("数据集检测", "WARN", "未找到YOLO数据集，需要先运行 create_yolo_dataset.py")
    
    def test_pretrained_import(self):
        """测试预训练权重训练模块导入"""
        print("\n🔍 测试预训练权重训练模块导入...")
        
        try:
            # 测试导入train_yolo11x_pretrained模块
            if os.path.exists("train_yolo11x_pretrained.py"):
                import importlib.util
                spec = importlib.util.spec_from_file_location(
                    "train_yolo11x_pretrained", 
                    "train_yolo11x_pretrained.py"
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # 检查关键类是否存在
                if hasattr(module, 'YOLO11xPretrainedTrainer'):
                    self.log_test("模块导入: YOLO11xPretrainedTrainer", "PASS", "类导入成功")
                else:
                    self.log_test("模块导入: YOLO11xPretrainedTrainer", "FAIL", "类不存在")
            else:
                self.log_test("模块导入", "SKIP", "train_yolo11x_pretrained.py 不存在")
                
        except Exception as e:
            self.log_test("模块导入", "FAIL", f"导入失败: {str(e)}")
    
    def test_workflow_integration(self):
        """测试工作流程集成"""
        print("\n🔍 测试工作流程集成...")
        
        # 检查README.md是否包含预训练权重训练信息
        if os.path.exists("README.md"):
            with open("README.md", "r", encoding="utf-8") as f:
                content = f.read()
                
            if "start_yolo11x_pretrained_training.py" in content:
                self.log_test("README集成", "PASS", "README包含预训练权重训练信息")
            else:
                self.log_test("README集成", "FAIL", "README缺少预训练权重训练信息")
        else:
            self.log_test("README集成", "SKIP", "README.md 不存在")
        
        # 检查训练工作流程指南
        if os.path.exists("TRAINING_WORKFLOW_GUIDE.md"):
            with open("TRAINING_WORKFLOW_GUIDE.md", "r", encoding="utf-8") as f:
                content = f.read()
                
            if "预训练权重训练" in content and "start_yolo11x_pretrained_training.py" in content:
                self.log_test("工作流程指南集成", "PASS", "工作流程指南包含预训练权重训练信息")
            else:
                self.log_test("工作流程指南集成", "FAIL", "工作流程指南缺少预训练权重训练信息")
        else:
            self.log_test("工作流程指南集成", "SKIP", "TRAINING_WORKFLOW_GUIDE.md 不存在")
    
    def test_dependencies(self):
        """测试依赖项"""
        print("\n🔍 测试依赖项...")
        
        required_packages = [
            "ultralytics",
            "torch",
            "torchvision",
            "opencv-python",
            "pillow",
            "pyyaml"
        ]
        
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
                self.log_test(f"依赖检查: {package}", "PASS", "已安装")
            except ImportError:
                self.log_test(f"依赖检查: {package}", "WARN", "未安装或版本不兼容")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始预训练权重训练工作流程测试...")
        print(f"📁 当前目录: {self.current_dir}")
        print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 运行各项测试
        self.test_pretrained_training_files()
        self.test_pretrained_script_syntax()
        self.test_dataset_detection()
        self.test_pretrained_import()
        self.test_workflow_integration()
        self.test_dependencies()
        
        # 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n📊 测试报告生成中...")
        
        # 统计测试结果
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r["status"] == "PASS"])
        failed_tests = len([r for r in self.test_results if r["status"] == "FAIL"])
        warned_tests = len([r for r in self.test_results if r["status"] == "WARN"])
        skipped_tests = len([r for r in self.test_results if r["status"] == "SKIP"])
        
        # 打印摘要
        print("\n" + "="*60)
        print("📋 预训练权重训练工作流程测试摘要")
        print("="*60)
        print(f"📊 总测试数: {total_tests}")
        print(f"✅ 通过: {passed_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"⚠️  警告: {warned_tests}")
        print(f"⏭️  跳过: {skipped_tests}")
        
        # 计算成功率
        if total_tests > 0:
            success_rate = (passed_tests / total_tests) * 100
            print(f"📈 成功率: {success_rate:.1f}%")
        
        # 保存详细报告
        report_file = f"pretrained_workflow_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump({
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": failed_tests,
                    "warned": warned_tests,
                    "skipped": skipped_tests,
                    "success_rate": success_rate if total_tests > 0 else 0
                },
                "details": self.test_results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细报告已保存: {report_file}")
        
        # 给出建议
        print("\n💡 建议:")
        if failed_tests > 0:
            print("❌ 存在失败的测试，请检查相关文件和配置")
        if warned_tests > 0:
            print("⚠️  存在警告，建议安装缺失的依赖或准备数据集")
        if failed_tests == 0 and warned_tests == 0:
            print("🎉 所有测试通过！预训练权重训练工作流程已准备就绪")
        
        print("\n🚀 使用方法:")
        print("1. 准备数据集: python create_yolo_dataset.py")
        print("2. 开始预训练权重训练: python start_yolo11x_pretrained_training.py")
        print("3. 查看详细指南: YOLO11X_PRETRAINED_TRAINING_GUIDE.md")

def main():
    """主函数"""
    tester = PretrainedWorkflowTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()