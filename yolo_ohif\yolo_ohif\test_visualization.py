#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化工具测试脚本
用于快速验证可视化功能是否正常工作
"""

import os
import sys
from pathlib import Path
import traceback

def test_imports():
    """
    测试必要的库导入
    """
    print("🔍 测试库导入...")
    
    try:
        import cv2
        print("✅ OpenCV 导入成功")
    except ImportError as e:
        print(f"❌ OpenCV 导入失败: {e}")
        return False
    
    try:
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
        print("✅ Matplotlib 导入成功")
    except ImportError as e:
        print(f"❌ Matplotlib 导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print("✅ NumPy 导入成功")
    except ImportError as e:
        print(f"❌ NumPy 导入失败: {e}")
        return False
    
    return True

def test_visualizer_import():
    """
    测试可视化器导入
    """
    print("\n🔍 测试可视化器导入...")
    
    try:
        from visualize_dataset import YOLODatasetVisualizer
        print("✅ YOLODatasetVisualizer 导入成功")
        return True
    except ImportError as e:
        print(f"❌ YOLODatasetVisualizer 导入失败: {e}")
        print("请确保 visualize_dataset.py 文件存在")
        return False
    except Exception as e:
        print(f"❌ 导入时发生错误: {e}")
        traceback.print_exc()
        return False

def test_dataset_path():
    """
    测试数据集路径
    """
    print("\n🔍 测试数据集路径...")
    
    dataset_root = "./yolo_training_output/supraspinatus_detection"
    dataset_path = Path(dataset_root)
    
    if not dataset_path.exists():
        print(f"❌ 数据集路径不存在: {dataset_path.absolute()}")
        print("请先运行数据处理脚本生成数据集")
        return False
    
    print(f"✅ 数据集路径存在: {dataset_path.absolute()}")
    
    # 检查子目录
    required_dirs = ['images', 'labels']
    for dir_name in required_dirs:
        dir_path = dataset_path / dir_name
        if dir_path.exists():
            print(f"✅ {dir_name} 目录存在")
        else:
            print(f"❌ {dir_name} 目录不存在")
            return False
    
    return True

def test_visualizer_creation():
    """
    测试可视化器创建
    """
    print("\n🔍 测试可视化器创建...")
    
    try:
        from visualize_dataset import YOLODatasetVisualizer
        
        dataset_root = "./yolo_training_output/supraspinatus_detection"
        visualizer = YOLODatasetVisualizer(dataset_root)
        
        print("✅ 可视化器创建成功")
        print(f"   数据集路径: {visualizer.dataset_root}")
        print(f"   类别定义: {visualizer.classes}")
        print(f"   颜色配置: {visualizer.colors}")
        
        return visualizer
    except Exception as e:
        print(f"❌ 可视化器创建失败: {e}")
        traceback.print_exc()
        return None

def test_dataset_check(visualizer):
    """
    测试数据集检查功能
    """
    print("\n🔍 测试数据集检查功能...")
    
    try:
        visualizer.check_dataset_integrity()
        print("✅ 数据集检查功能正常")
        return True
    except Exception as e:
        print(f"❌ 数据集检查失败: {e}")
        traceback.print_exc()
        return False

def test_annotation_loading(visualizer):
    """
    测试标注加载功能
    """
    print("\n🔍 测试标注加载功能...")
    
    try:
        # 查找第一个标签文件
        dataset_path = Path(visualizer.dataset_root)
        
        for split in ['train', 'val', 'test']:
            labels_dir = dataset_path / 'labels' / split
            if labels_dir.exists():
                label_files = list(labels_dir.glob('*.txt'))
                if label_files:
                    # 测试加载第一个标签文件
                    label_path = label_files[0]
                    annotations = visualizer.load_yolo_annotations(label_path)
                    
                    print(f"✅ 成功加载标注文件: {label_path.name}")
                    print(f"   标注数量: {len(annotations)}")
                    
                    if annotations:
                        print(f"   第一个标注: {annotations[0]}")
                    
                    return True
        
        print("⚠️  没有找到标签文件进行测试")
        return True
        
    except Exception as e:
        print(f"❌ 标注加载测试失败: {e}")
        traceback.print_exc()
        return False

def test_coordinate_conversion(visualizer):
    """
    测试坐标转换功能
    """
    print("\n🔍 测试坐标转换功能...")
    
    try:
        # 测试YOLO坐标转换
        x_center, y_center, width, height = 0.5, 0.5, 0.3, 0.4
        img_width, img_height = 640, 640
        
        x_min, y_min, x_max, y_max = visualizer.yolo_to_bbox(
            x_center, y_center, width, height, img_width, img_height
        )
        
        print(f"✅ 坐标转换成功")
        print(f"   输入 (YOLO): 中心({x_center}, {y_center}), 尺寸({width}, {height})")
        print(f"   输出 (像素): ({x_min}, {y_min}) -> ({x_max}, {y_max})")
        
        # 验证转换结果
        expected_x_min = int((0.5 - 0.3/2) * 640)  # 224
        expected_y_min = int((0.5 - 0.4/2) * 640)  # 192
        expected_x_max = int((0.5 + 0.3/2) * 640)  # 416
        expected_y_max = int((0.5 + 0.4/2) * 640)  # 448
        
        if (x_min == expected_x_min and y_min == expected_y_min and 
            x_max == expected_x_max and y_max == expected_y_max):
            print("✅ 坐标转换结果正确")
        else:
            print(f"⚠️  坐标转换结果可能有误")
            print(f"   期望: ({expected_x_min}, {expected_y_min}) -> ({expected_x_max}, {expected_y_max})")
        
        return True
        
    except Exception as e:
        print(f"❌ 坐标转换测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """
    主测试函数
    """
    print("🧪 可视化工具测试")
    print("=" * 50)
    
    # 测试步骤
    tests = [
        ("库导入", test_imports),
        ("可视化器导入", test_visualizer_import),
        ("数据集路径", test_dataset_path),
    ]
    
    # 执行基础测试
    for test_name, test_func in tests:
        if not test_func():
            print(f"\n❌ {test_name} 测试失败，停止后续测试")
            return False
    
    # 创建可视化器并执行高级测试
    visualizer = test_visualizer_creation()
    if visualizer is None:
        print("\n❌ 无法创建可视化器，停止测试")
        return False
    
    advanced_tests = [
        ("数据集检查", lambda: test_dataset_check(visualizer)),
        ("标注加载", lambda: test_annotation_loading(visualizer)),
        ("坐标转换", lambda: test_coordinate_conversion(visualizer)),
    ]
    
    for test_name, test_func in advanced_tests:
        if not test_func():
            print(f"\n⚠️  {test_name} 测试失败，但可以继续")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    print("\n📋 下一步操作建议:")
    print("1. 运行 'python visualize_examples.py' 进行交互式可视化")
    print("2. 运行 'python visualize_dataset.py --check_only' 检查数据集")
    print("3. 打开 'dataset_visualization.ipynb' 进行详细分析")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生未预期错误: {e}")
        traceback.print_exc()