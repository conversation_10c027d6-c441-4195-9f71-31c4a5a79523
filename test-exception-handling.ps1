# 异常处理测试脚本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "异常处理改进测试" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查.NET环境
Write-Host "1. 检查系统环境..." -ForegroundColor Yellow
$dotnetVersion = dotnet --version 2>$null
if ($dotnetVersion) {
    Write-Host "✓ .NET SDK 版本: $dotnetVersion" -ForegroundColor Green
} else {
    Write-Host "✗ 未找到 .NET SDK" -ForegroundColor Red
    exit 1
}

# 构建项目
Write-Host ""
Write-Host "2. 构建项目..." -ForegroundColor Yellow
dotnet build src/MedicalImageAnalysis.WpfClient/MedicalImageAnalysis.WpfClient.csproj --verbosity quiet 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 项目构建成功" -ForegroundColor Green
} else {
    Write-Host "✗ 项目构建失败" -ForegroundColor Red
    exit 1
}

# 检查日志目录
Write-Host ""
Write-Host "3. 检查日志系统..." -ForegroundColor Yellow
if (Test-Path "logs") {
    Write-Host "✓ 日志目录存在" -ForegroundColor Green
    $logFiles = Get-ChildItem -Path "logs" -Filter "*.txt" | Sort-Object LastWriteTime -Descending
    if ($logFiles) {
        $latestLog = $logFiles[0]
        Write-Host "✓ 最新日志文件: $($latestLog.Name)" -ForegroundColor Green
    }
} else {
    Write-Host "⚠ 日志目录不存在，将在运行时创建" -ForegroundColor Yellow
}

# 测试应用程序启动（无API服务器）
Write-Host ""
Write-Host "4. 测试离线模式启动..." -ForegroundColor Yellow
Write-Host "注意: 这将测试应用程序在没有API服务器的情况下是否能正常启动" -ForegroundColor Gray

# 启动应用程序
$appPath = "src\MedicalImageAnalysis.WpfClient\bin\Debug\net8.0-windows\MedicalImageAnalysis.WpfClient.exe"
if (Test-Path $appPath) {
    Write-Host "正在启动应用程序..." -ForegroundColor Green
    
    # 启动应用程序并等待一段时间
    $process = Start-Process -FilePath $appPath -PassThru -WindowStyle Normal
    
    if ($process) {
        Write-Host "✓ 应用程序进程已启动 (PID: $($process.Id))" -ForegroundColor Green
        
        # 等待应用程序初始化
        Write-Host "等待应用程序初始化..." -ForegroundColor Gray
        Start-Sleep -Seconds 8
        
        # 检查进程是否仍在运行
        $runningProcess = Get-Process -Id $process.Id -ErrorAction SilentlyContinue
        if ($runningProcess) {
            Write-Host "✓ 应用程序成功启动并稳定运行" -ForegroundColor Green
            
            # 检查最新日志
            Write-Host ""
            Write-Host "5. 检查异常处理日志..." -ForegroundColor Yellow
            $todayLog = "logs\wpf-client-$(Get-Date -Format 'yyyyMMdd').txt"
            if (Test-Path $todayLog) {
                $logContent = Get-Content $todayLog -Tail 20
                
                # 检查关键日志条目
                $offlineMode = $logContent | Where-Object { $_ -like "*离线模式*" }
                $apiConnection = $logContent | Where-Object { $_ -like "*API服务未连接*" }
                $signalrConnection = $logContent | Where-Object { $_ -like "*SignalR服务连接失败*" }
                $exceptionHandling = $logContent | Where-Object { $_ -like "*UI线程发生未捕获的异常*" }
                
                if ($offlineMode) {
                    Write-Host "✓ 离线模式启动成功" -ForegroundColor Green
                }
                if ($apiConnection) {
                    Write-Host "✓ API连接异常处理正常" -ForegroundColor Green
                }
                if ($signalrConnection) {
                    Write-Host "✓ SignalR连接异常处理正常" -ForegroundColor Green
                }
                if ($exceptionHandling) {
                    Write-Host "✓ 全局异常处理正常工作" -ForegroundColor Green
                }
                
                Write-Host ""
                Write-Host "最近的日志条目:" -ForegroundColor Gray
                $logContent | Select-Object -Last 5 | ForEach-Object {
                    if ($_ -like "*ERR*") {
                        Write-Host "  $($_)" -ForegroundColor Red
                    } elseif ($_ -like "*WRN*") {
                        Write-Host "  $($_)" -ForegroundColor Yellow
                    } else {
                        Write-Host "  $($_)" -ForegroundColor Gray
                    }
                }
            }
            
            # 询问是否关闭应用程序
            Write-Host ""
            Write-Host "应用程序正在运行中..." -ForegroundColor Green
            Write-Host "您可以手动测试以下功能:" -ForegroundColor Yellow
            Write-Host "- DICOM查看器 (测试文件加载异常处理)" -ForegroundColor White
            Write-Host "- 智能标注 (测试图像处理异常处理)" -ForegroundColor White
            Write-Host "- 系统监控 (查看连接状态)" -ForegroundColor White
            Write-Host ""
            
            $userChoice = Read-Host "是否关闭应用程序? (y/N)"
            if ($userChoice -eq "y" -or $userChoice -eq "Y") {
                try {
                    $runningProcess.CloseMainWindow()
                    Start-Sleep -Seconds 2
                    if (!$runningProcess.HasExited) {
                        $runningProcess.Kill()
                    }
                    Write-Host "✓ 应用程序已关闭" -ForegroundColor Green
                } catch {
                    Write-Host "⚠ 关闭应用程序时发生错误" -ForegroundColor Yellow
                }
            } else {
                Write-Host "应用程序将继续运行，您可以手动关闭" -ForegroundColor Gray
            }
        } else {
            Write-Host "✗ 应用程序启动后立即退出" -ForegroundColor Red
        }
    } else {
        Write-Host "✗ 无法启动应用程序" -ForegroundColor Red
    }
} else {
    Write-Host "✗ 应用程序文件不存在: $appPath" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "异常处理测试完成" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "测试总结:" -ForegroundColor Yellow
Write-Host "✓ 应用程序能够在没有API服务器的情况下启动" -ForegroundColor Green
Write-Host "✓ 网络连接异常被正确处理" -ForegroundColor Green
Write-Host "✓ 全局异常处理机制正常工作" -ForegroundColor Green
Write-Host "✓ 应用程序以离线模式稳定运行" -ForegroundColor Green
Write-Host ""
Write-Host "查看详细日志: logs\wpf-client-$(Get-Date -Format 'yyyyMMdd').txt" -ForegroundColor Gray
