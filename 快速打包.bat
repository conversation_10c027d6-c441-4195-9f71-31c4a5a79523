@echo off
chcp 65001 > nul
title 医学影像智能标注与模型训练系统 - 快速打包

echo.
echo ========================================
echo   医学影像智能标注与模型训练系统
echo           快速打包工具
echo ========================================
echo.

:: 设置变量
set OUTPUT_DIR=Release
set APP_NAME=医学影像智能标注与模型训练系统

:: 检查环境
echo 🔍 检查构建环境...
dotnet --version > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ 错误: 未找到.NET SDK，请安装.NET 8或更高版本
    pause
    exit /b 1
)

python --version > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ⚠️  警告: 未找到Python环境，将跳过Python环境打包
    set INCLUDE_PYTHON=false
) else (
    set INCLUDE_PYTHON=true
)

echo ✅ 环境检查完成

:: 清理旧的发布目录
echo.
echo 🧹 清理旧的发布目录...
if exist %OUTPUT_DIR% (
    rmdir /s /q %OUTPUT_DIR%
)

:: 创建目录结构
echo.
echo 📁 创建目录结构...
mkdir %OUTPUT_DIR%\App
mkdir %OUTPUT_DIR%\Data\DICOM
mkdir %OUTPUT_DIR%\Data\Models
mkdir %OUTPUT_DIR%\Data\Datasets\Train\images
mkdir %OUTPUT_DIR%\Data\Datasets\Train\labels
mkdir %OUTPUT_DIR%\Data\Datasets\Val\images
mkdir %OUTPUT_DIR%\Data\Datasets\Val\labels
mkdir %OUTPUT_DIR%\Data\Datasets\Test\images
mkdir %OUTPUT_DIR%\Data\Datasets\Test\labels
mkdir %OUTPUT_DIR%\Data\Output
mkdir %OUTPUT_DIR%\Data\Temp
mkdir %OUTPUT_DIR%\Data\Logs
mkdir %OUTPUT_DIR%\Python
mkdir %OUTPUT_DIR%\Scripts
mkdir %OUTPUT_DIR%\Docs
mkdir %OUTPUT_DIR%\Examples
mkdir %OUTPUT_DIR%\Assets

echo ✅ 目录结构创建完成

:: 发布.NET应用程序
echo.
echo 🔨 发布.NET应用程序...
dotnet publish src\MedicalImageAnalysis.Wpf\MedicalImageAnalysis.Wpf.csproj ^
    --configuration Release ^
    --runtime win-x64 ^
    --self-contained true ^
    --output %OUTPUT_DIR%\App ^
    --verbosity minimal

if %ERRORLEVEL% neq 0 (
    echo ❌ 错误: .NET应用程序发布失败
    pause
    exit /b 1
)

echo ✅ .NET应用程序发布完成

:: 复制Python环境和脚本
if "%INCLUDE_PYTHON%"=="true" (
    echo.
    echo 🐍 配置Python环境...
    
    :: 创建虚拟环境
    python -m venv %OUTPUT_DIR%\Python\venv
    
    :: 激活虚拟环境并安装依赖
    call %OUTPUT_DIR%\Python\venv\Scripts\activate.bat
    
    :: 安装基础依赖
    pip install ultralytics torch torchvision opencv-python pillow numpy pyyaml tqdm matplotlib --no-cache-dir
    
    :: 复制训练脚本
    if exist yolo_ohif\yolo_ohif\*.py (
        copy yolo_ohif\yolo_ohif\*.py %OUTPUT_DIR%\Python\
    )
    
    :: 停用虚拟环境
    call deactivate
    
    echo ✅ Python环境配置完成
)

:: 复制数据文件
echo.
echo 📄 复制数据文件...

:: 复制示例DICOM文件
if exist Brain (
    copy Brain\*.dcm %OUTPUT_DIR%\Data\DICOM\ > nul 2>&1
    echo   ✓ 复制示例DICOM文件
)

:: 复制模型文件
if exist yolo_ohif\yolo_ohif\models (
    xcopy yolo_ohif\yolo_ohif\models %OUTPUT_DIR%\Data\Models\ /E /I /Q > nul 2>&1
    echo   ✓ 复制模型文件
)

:: 复制配置文件
copy src\MedicalImageAnalysis.Wpf\appsettings.json %OUTPUT_DIR%\App\ > nul 2>&1

:: 复制文档
copy *.md %OUTPUT_DIR%\Docs\ > nul 2>&1
echo   ✓ 复制文档文件

:: 复制脚本
copy 验证训练环境.py %OUTPUT_DIR%\Scripts\ > nul 2>&1
echo   ✓ 复制脚本文件

:: 复制图标和图片
if exist Assets (
    xcopy Assets %OUTPUT_DIR%\Assets\ /E /I /Q > nul 2>&1
    echo   ✓ 复制图标和图片
)

echo ✅ 数据文件复制完成

:: 创建启动脚本
echo.
echo 📝 创建启动脚本...

:: 主启动脚本
(
echo @echo off
echo chcp 65001 ^> nul
echo title %APP_NAME%
echo.
echo echo.
echo echo ========================================
echo echo   %APP_NAME%
echo echo ========================================
echo echo.
echo.
echo cd /d "%%~dp0"
echo.
echo echo 正在启动应用程序...
echo start "" "App\MedicalImageAnalysis.Wpf.exe"
echo.
echo echo.
echo echo 应用程序已启动！
echo echo.
echo echo 如需帮助，请查看 Docs 目录中的教程文档
echo echo.
echo pause
) > %OUTPUT_DIR%\启动应用程序.bat

:: Python训练环境启动脚本
if "%INCLUDE_PYTHON%"=="true" (
    (
    echo @echo off
    echo chcp 65001 ^> nul
    echo title YOLO模型训练环境
    echo.
    echo echo.
    echo echo ========================================
    echo echo        YOLO模型训练环境
    echo echo ========================================
    echo echo.
    echo.
    echo cd /d "%%~dp0"
    echo.
    echo echo 激活Python环境...
    echo call Python\venv\Scripts\activate.bat
    echo.
    echo echo.
    echo echo Python环境已激活，可以运行以下训练脚本：
    echo echo.
    echo echo 1. python Python\start_yolo11x_training.py
    echo echo 2. python Python\train_yolo11x_from_scratch.py
    echo echo 3. python Scripts\验证训练环境.py
    echo echo.
    echo.
    echo cmd /k
    ) > %OUTPUT_DIR%\启动训练环境.bat
)

echo ✅ 启动脚本创建完成

:: 创建用户指南
echo.
echo 📖 创建用户指南...

(
echo # %APP_NAME% - 用户指南
echo.
echo ## 🚀 快速开始
echo.
echo ### 1. 启动应用程序
echo 双击 `启动应用程序.bat` 文件启动主应用程序。
echo.
echo ### 2. 数据文件放置位置
echo.
echo #### DICOM影像文件
echo 📁 **位置**: `Data\DICOM\`
echo 📝 **说明**: 将您的DICOM文件（.dcm格式）放置在此目录下
echo 📋 **示例**: 系统已包含示例脑部CT文件（DJ01.dcm - DJ10.dcm）
echo.
echo #### YOLO模型文件  
echo 📁 **位置**: `Data\Models\`
echo 📝 **说明**: 将训练好的YOLO模型文件（.pt格式）放置在此目录下
echo 📋 **推荐**: 
echo - `yolo11n.pt` - 轻量级模型（快速）
echo - `yolo11x.pt` - 高精度模型（推荐）
echo.
echo #### 训练数据集
echo 📁 **训练集**: `Data\Datasets\Train\`
echo   - `images\` - 训练图像文件
echo   - `labels\` - 训练标签文件（YOLO格式）
echo.
echo 📁 **验证集**: `Data\Datasets\Val\`
echo   - `images\` - 验证图像文件  
echo   - `labels\` - 验证标签文件
echo.
echo 📁 **测试集**: `Data\Datasets\Test\`
echo   - `images\` - 测试图像文件
echo   - `labels\` - 测试标签文件
echo.
echo ## 📖 详细教程
echo.
echo 查看 `Docs\` 目录中的详细教程：
echo - `智能打标教程.md` - 智能标注功能使用指南
echo - `模型训练教程.md` - AI模型训练指南
echo - `快速入门指南.md` - 5分钟快速上手
echo.
echo ## 🔧 环境配置
echo.
echo ### Python训练环境
echo 1. 双击 `启动训练环境.bat` 激活Python环境
echo 2. 运行 `python Scripts\验证训练环境.py` 验证环境配置
echo.
echo ## 📞 技术支持
echo.
echo ### 日志文件位置
echo - **应用程序日志**: `Data\Logs\`
echo - **训练日志**: `Python\runs\train\`
echo.
echo ### 获取帮助
echo 1. 查看 `Docs\` 目录中的详细教程
echo 2. 检查日志文件获取错误信息
echo 3. 运行环境验证脚本诊断问题
echo.
echo ---
echo.
echo **开始您的医学影像AI之旅！** 🚀
) > %OUTPUT_DIR%\用户指南.md

echo ✅ 用户指南创建完成

:: 创建README文件
(
echo # %APP_NAME%
echo.
echo 这是一个完整的医学影像智能标注与AI模型训练系统发布包。
echo.
echo ## 快速开始
echo.
echo 1. 双击 `启动应用程序.bat` 启动主应用程序
echo 2. 查看 `用户指南.md` 了解详细使用方法
echo 3. 查看 `Docs\` 目录中的完整教程
echo.
echo ## 系统要求
echo.
echo - Windows 10/11 (x64)
echo - 至少 8GB RAM
echo - 至少 10GB 可用空间
echo - NVIDIA GPU (推荐，用于训练)
echo.
echo ## 技术支持
echo.
echo 如遇问题，请查看用户指南或联系技术支持。
) > %OUTPUT_DIR%\README.md

:: 完成打包
echo.
echo 🎉 打包完成！
echo.
echo 📁 发布包位置: %OUTPUT_DIR%\
echo 📦 发布包大小: 
for /f %%i in ('dir %OUTPUT_DIR% /s /-c ^| find "个文件"') do echo    %%i

echo.
echo 📋 发布包内容:
echo   ✓ .NET应用程序 (自包含)
if "%INCLUDE_PYTHON%"=="true" (
    echo   ✓ Python训练环境
)
echo   ✓ 示例数据文件
echo   ✓ 完整文档教程
echo   ✓ 启动脚本
echo   ✓ 用户指南
echo.
echo 🚀 可以将整个 %OUTPUT_DIR% 文件夹分发给用户使用！
echo.
echo 📖 用户使用方法:
echo   1. 解压发布包到任意目录
echo   2. 双击"启动应用程序.bat"即可使用
echo   3. 查看"用户指南.md"了解详细说明
echo.

pause
