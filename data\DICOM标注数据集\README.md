# DICOM标注数据集

## 数据集结构

```
DICOM标注数据集/
├── images/
│   ├── train/          # 训练图像
│   ├── val/            # 验证图像
│   └── test/           # 测试图像
├── labels/
│   ├── train/          # 训练标注
│   └── val/            # 验证标注
└── README.md           # 本文件

```

## 使用说明

1. 将训练图像放入 `images/train/` 目录
2. 将对应的标注文件放入 `labels/train/` 目录
3. 将验证图像放入 `images/val/` 目录
4. 将对应的标注文件放入 `labels/val/` 目录
5. 测试图像放入 `images/test/` 目录（可选）

## 支持的格式

- 图像格式: .jpg, .jpeg, .png, .bmp, .tiff, .dcm, .nii.gz
- 标注格式: .txt (YOLO), .xml (Pascal VOC), .json (COCO)

创建时间: 2025-07-27 19:46:52
