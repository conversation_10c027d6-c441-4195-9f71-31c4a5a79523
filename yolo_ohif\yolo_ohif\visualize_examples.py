#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集可视化示例脚本
快速查看和验证YOLO数据集的图像和标签匹配情况
"""

import os
import sys
from pathlib import Path
from visualize_dataset import YOLODatasetVisualizer

def quick_check():
    """
    快速检查数据集完整性
    """
    dataset_root = "./yolo_training_output/supraspinatus_detection"
    
    print("🔍 快速数据集检查")
    print(f"数据集路径: {dataset_root}")
    print("=" * 50)
    
    visualizer = YOLODatasetVisualizer(dataset_root)
    visualizer.check_dataset_integrity()

def visualize_samples():
    """
    可视化数据集样本
    """
    dataset_root = "./yolo_training_output/supraspinatus_detection"
    save_dir = "./visualization_results"
    
    print("\n📊 开始可视化数据集样本")
    print(f"数据集路径: {dataset_root}")
    print(f"保存路径: {save_dir}")
    print("=" * 50)
    
    visualizer = YOLODatasetVisualizer(dataset_root)
    
    # 可视化训练集样本
    print("\n🎯 可视化训练集样本...")
    visualizer.visualize_dataset_samples(
        split='train',
        num_samples=5,
        save_dir=save_dir
    )
    
    # 可视化验证集样本
    print("\n🎯 可视化验证集样本...")
    visualizer.visualize_dataset_samples(
        split='val',
        num_samples=3,
        save_dir=save_dir
    )
    
    print(f"\n✅ 可视化完成！结果保存在: {save_dir}")

def visualize_specific_image():
    """
    可视化特定图像
    """
    dataset_root = "./yolo_training_output/supraspinatus_detection"
    
    # 示例：可视化第一个训练图像
    images_dir = Path(dataset_root) / "images" / "train"
    labels_dir = Path(dataset_root) / "labels" / "train"
    
    if not images_dir.exists():
        print(f"❌ 图像目录不存在: {images_dir}")
        return
    
    # 获取第一个图像文件
    image_files = list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.png"))
    
    if not image_files:
        print(f"❌ 在 {images_dir} 中没有找到图像文件")
        return
    
    img_path = image_files[0]
    label_path = labels_dir / (img_path.stem + '.txt')
    
    print(f"\n🖼️  可视化特定图像")
    print(f"图像: {img_path}")
    print(f"标签: {label_path}")
    print("=" * 50)
    
    visualizer = YOLODatasetVisualizer(dataset_root)
    visualizer.visualize_single_image(img_path, label_path)

def main():
    """
    主函数 - 提供交互式菜单
    """
    print("🎯 YOLO数据集可视化工具")
    print("=" * 50)
    print("请选择操作:")
    print("1. 快速检查数据集完整性")
    print("2. 可视化数据集样本（保存到文件）")
    print("3. 可视化特定图像（显示窗口）")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请输入选项 (1-4): ").strip()
            
            if choice == '1':
                quick_check()
            elif choice == '2':
                visualize_samples()
            elif choice == '3':
                visualize_specific_image()
            elif choice == '4':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选项，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()