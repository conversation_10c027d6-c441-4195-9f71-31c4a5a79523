#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单类别配置的训练代码
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from train_supraspinatus_yolo import SupraspinatusYOLOTrainer

def test_single_class_config():
    """
    测试单类别配置
    """
    print("测试单类别配置...")
    
    # 创建训练器实例
    trainer = SupraspinatusYOLOTrainer(
        dataset_root='./dataset',
        output_root='./test_output',
        img_size=640
    )
    
    # 检查类别配置
    print(f"类别配置: {trainer.classes}")
    print(f"类别数量: {len(trainer.classes)}")
    
    # 验证只有一个类别
    assert len(trainer.classes) == 1, f"期望1个类别，实际{len(trainer.classes)}个"
    assert 'supraspinatus_tear' in trainer.classes, "缺少supraspinatus_tear类别"
    assert trainer.classes['supraspinatus_tear'] == 0, "supraspinatus_tear类别ID应该为0"
    
    # 测试YAML配置生成
    trainer.create_yaml_config()
    
    # 检查生成的YAML文件
    yaml_path = trainer.yolo_dataset_root / "dataset.yaml"
    if yaml_path.exists():
        import yaml
        with open(yaml_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print(f"YAML配置: {config}")
        assert config['nc'] == 1, f"期望nc=1，实际nc={config['nc']}"
        assert len(config['names']) == 1, f"期望1个类别名，实际{len(config['names'])}个"
        assert config['names'][0] == 'supraspinatus_tear', f"期望类别名为supraspinatus_tear，实际为{config['names'][0]}"
        
        print("✅ YAML配置正确")
    else:
        print("❌ YAML文件未生成")
        return False
    
    print("✅ 所有测试通过！")
    print("配置摘要:")
    print(f"  - 类别数量: {len(trainer.classes)}")
    print(f"  - 类别名称: {list(trainer.classes.keys())}")
    print(f"  - 类别ID: {list(trainer.classes.values())}")
    print("  - 正常图像将作为负样本（无标注文件）")
    print("  - 符合单类别目标检测要求")
    
    return True

if __name__ == "__main__":
    try:
        test_single_class_config()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        sys.exit(1)