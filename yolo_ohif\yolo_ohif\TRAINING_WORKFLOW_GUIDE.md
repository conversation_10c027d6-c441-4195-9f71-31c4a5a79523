# YOLO11x 训练工作流程指南

## 🎯 新的工作流程

为了提高代码的模块化和可维护性，我们将数据准备和模型训练分离为两个独立的步骤：

### 步骤 1: 数据准备 📁

使用 `create_yolo_dataset.py` 准备训练数据：

```bash
# 基本用法
python create_yolo_dataset.py

# 自定义参数
python create_yolo_dataset.py \
    --dataset_root ./dataset \
    --output_root ./yolo_dataset_output \
    --img_size 640
```

**功能**:
- 将 nii.gz 文件转换为 JPG 图像
- 生成 YOLO 格式的标注文件
- 创建训练/验证/测试数据集划分
- 生成 YOLO 配置文件 (dataset.yaml)

**输出结构**:
```
yolo_dataset_output/yolo_dataset/
├── dataset.yaml          # YOLO配置文件
├── images/
│   ├── train/            # 训练图像
│   ├── val/              # 验证图像
│   └── test/             # 测试图像
└── labels/
    ├── train/            # 训练标签
    ├── val/              # 验证标签
    └── test/             # 测试标签
```

### 步骤 2: 模型训练 🚀

我们提供两种训练方式，您可以根据需求选择：

#### 选项 A: 预训练权重训练 (推荐) ⚡

**标准预训练权重训练 (640×640)**:
```bash
python start_yolo11x_pretrained_training.py
```

**医学图像专用 (512×512) - 强烈推荐医学影像项目**:
```bash
python start_yolo11x_pretrained_training_512.py
```

**功能**:
- 🚀 使用 YOLO11x 预训练权重作为起点
- ⚡ 训练时间短 (2-4小时)
- 📊 数据需求少，效果通常更好
- 🎯 适合大多数应用场景

**训练参数**:
- 训练轮数: 100
- 学习率: 0.001 (较小，适合微调)
- 冻结层数: 10 (前10层参数不更新)
- 批次大小: 16

#### 选项 B: 从头开始训练 🔥

**标准从头训练 (640×640)**:
```bash
python start_yolo11x_training.py
```

**医学图像专用 (512×512) - 推荐医学影像项目**:
```bash
python start_yolo11x_training_512.py
```

**功能**:
- 🔥 完全从头开始训练 YOLO11x 模型
- 🕐 训练时间长 (8-12小时)
- 📈 需要大量数据，完全定制化
- 🎲 适合特殊领域或大数据集

**训练参数**:
- 训练轮数: 200
- 学习率: 0.01 (较大，从头学习)
- 预训练权重: 禁用
- 批次大小: 16

#### 🤔 如何选择训练方式？

| 场景 | 推荐方式 | 图像尺寸 | 原因 |
|------|----------|----------|------|
| 医学影像检测 | 预训练权重 | **512×512** | 匹配原生分辨率，快速验证 |
| 数据集 < 5000张 | 预训练权重 | 512×512 | 避免过拟合，利用预训练知识 |
| 数据集 > 10000张 | 可选择任一 | 512×512或640×640 | 数据充足，两种方式都可行 |
| 特殊领域检测 | 从头训练 | 640×640 | 完全定制化，避免预训练偏见 |
| 快速原型验证 | 预训练权重 | **512×512** | 快速获得结果，节省资源 |
| 生产环境部署 | 预训练权重 | **512×512** | 稳定性好，推理速度快 |
| GPU内存有限 | 预训练权重 | **512×512** | 节省38%内存，提升30%速度 |

## 📋 完整使用示例

### 1. 准备原始数据

确保数据目录结构正确：

```
dataset/
├── image_T2/           # 撕裂图像 (130-395.nii.gz)
├── label_T2/           # 撕裂标签 (130-395.nii.gz)
└── image_T2_normal/    # 正常图像 (1-129.nii.gz)
```

### 2. 运行数据准备

```bash
# 创建 YOLO 数据集
python create_yolo_dataset.py

# 验证数据集创建结果
ls yolo_dataset_output/yolo_dataset/
```

### 3. 开始训练

#### 选项 A: 预训练权重训练 (推荐)

```bash
# 启动预训练权重微调
python start_yolo11x_pretrained_training.py
```

预训练权重训练脚本会：
- ✅ 自动检测数据集位置
- ✅ 下载 YOLO11x 预训练权重
- ✅ 显示训练配置 (微调模式)
- ✅ 冻结前10层进行微调训练
- ⚡ 预计训练时间: 2-4小时

#### 选项 B: 从头开始训练

```bash
# 启动从头训练
python start_yolo11x_training.py
```

从头训练脚本会：
- ✅ 自动检测数据集位置
- ✅ 显示数据集统计信息
- ✅ 确认训练配置 (从头模式)
- ✅ 开始完全从头训练模型
- 🕐 预计训练时间: 8-12小时

### 4. 查看结果

```bash
# 训练输出目录
ls yolo11x_training_output/

# 查看训练日志
tail -f yolo11x_training_output/logs/training.log
```

## 🔧 优势

### 模块化设计
- **数据准备** 和 **模型训练** 完全分离
- 可以独立运行和调试每个步骤
- 便于代码维护和扩展

### 灵活性
- 可以多次使用同一个数据集训练不同的模型
- 可以在不同的机器上分别进行数据准备和训练
- 支持自定义数据集路径和参数

### 可靠性
- 数据准备包含文件安全写入机制
- 训练前会验证数据集完整性
- 清晰的错误提示和使用指导

## 🛠️ 故障排除

### 问题 1: 找不到数据集

**错误信息**: "❌ 未找到有效的YOLO数据集"

**解决方案**:
1. 确认已运行 `python create_yolo_dataset.py`
2. 检查输出目录是否存在：`ls yolo_dataset_output/yolo_dataset/`
3. 验证配置文件：`cat yolo_dataset_output/yolo_dataset/dataset.yaml`

### 问题 2: 数据集不完整

**症状**: 训练集或验证集图像数量为 0

**解决方案**:
1. 重新运行数据准备：`python create_yolo_dataset.py`
2. 检查原始数据：`ls dataset/`
3. 查看数据准备日志中的错误信息

### 问题 3: CUDA 设备问题

**解决方案**:
1. 运行 CUDA 检测：`python test_cuda_detection.py`
2. 查看训练日志中的设备信息
3. 如果 CUDA 不可用，会自动降级到 CPU 训练

## 📊 性能建议

### 数据准备阶段
- 使用 SSD 存储可以加速文件 I/O
- 确保有足够的磁盘空间（约为原始数据的 3-5 倍）

### 训练阶段
- GPU 内存不足时可以减小 batch_size
- 使用 `--img_size 512` 可以减少内存使用
- 监控 GPU 使用率和温度

## 🔗 相关文件

### 数据准备
- `create_yolo_dataset.py` - 数据准备脚本

### 预训练权重训练 (推荐)
- `start_yolo11x_pretrained_training.py` - 预训练权重训练启动脚本 (640×640) (支持模型选择)
- `start_yolo11x_pretrained_training_512.py` - 医学图像专用启动脚本 (512×512) (支持模型选择)
- `train_yolo11x_pretrained.py` - 预训练权重训练核心逻辑
- `YOLO11X_PRETRAINED_TRAINING_GUIDE.md` - 预训练权重训练详细指南

### 从头开始训练
- `start_yolo11x_training.py` - 从头训练启动脚本 (640×640)
- `start_yolo11x_training_512.py` - 医学图像专用启动脚本 (512×512)
- `train_yolo11x_from_scratch.py` - 从头训练核心逻辑

### 配置指南
- `YOLO_IMAGE_SIZE_CONFIGURATION_GUIDE.md` - 图像尺寸配置详细指南
- `YOLO_MODEL_SELECTION_GUIDE.md` - 🆕 YOLO模型选择指南 (智能选择最适合的模型)

### 测试和工具
- `test_file_safety.py` - 文件安全性测试
- `test_cuda_detection.py` - CUDA 检测测试
- `test_separated_workflow.py` - 分离式工作流程测试

## 📝 配置文件

### dataset.yaml 示例

```yaml
path: /absolute/path/to/yolo_dataset
train: images/train
val: images/val
test: images/test
nc: 1
names: ['supraspinatus_tear']
```

### 训练参数

- **epochs**: 200 (可根据需要调整)
- **batch_size**: 16 (根据 GPU 内存调整)
- **img_size**: 640 (标准 YOLO 输入尺寸)
- **learning_rate**: 0.01 (自适应调整)

---

**更新时间**: 2025年
**工作流程版本**: v2.0 (分离式)
**兼容性**: Windows/Linux/macOS