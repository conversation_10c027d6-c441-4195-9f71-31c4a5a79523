{% extends "base.html" %}

{% block title %}OHIF查看器 - {{ study.patient_name if study else study_id }}{% endblock %}

{% block extra_css %}
<style>
    .viewer-container {
        height: calc(100vh - 120px);
        width: 100%;
        border: none;
        background-color: #000;
    }
    
    .viewer-header {
        background-color: #f8f9fa;
        padding: 15px;
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 0;
    }
    
    .study-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
    }
    
    .study-details {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
    }
    
    .study-detail {
        display: flex;
        flex-direction: column;
    }
    
    .study-detail-label {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 2px;
    }
    
    .study-detail-value {
        font-weight: 500;
        color: #212529;
    }
    
    .viewer-actions {
        display: flex;
        gap: 10px;
    }
    
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        color: white;
        z-index: 1000;
    }
    
    .viewer-wrapper {
        position: relative;
        height: calc(100vh - 180px);
    }
    
    .error-message {
        text-align: center;
        padding: 50px;
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <!-- 研究信息头部 -->
    <div class="viewer-header">
        <div class="study-info">
            <div class="study-details">
                {% if study %}
                <div class="study-detail">
                    <span class="study-detail-label">患者姓名</span>
                    <span class="study-detail-value">{{ study.patient_name or '未知' }}</span>
                </div>
                <div class="study-detail">
                    <span class="study-detail-label">患者ID</span>
                    <span class="study-detail-value">{{ study.patient_id or '未知' }}</span>
                </div>
                <div class="study-detail">
                    <span class="study-detail-label">研究日期</span>
                    <span class="study-detail-value">{{ study.study_date or '未知' }}</span>
                </div>
                <div class="study-detail">
                    <span class="study-detail-label">研究描述</span>
                    <span class="study-detail-value">{{ study.study_description or '未知' }}</span>
                </div>
                {% endif %}
                <div class="study-detail">
                    <span class="study-detail-label">研究ID</span>
                    <span class="study-detail-value">{{ study_id }}</span>
                </div>
            </div>
            
            {% if auto_detect %}
            <div class="alert alert-info mb-3">
                <i class="fas fa-magic me-2"></i>
                <strong>自动检测已启用：</strong>系统已自动进行YOLO检测，检测结果将直接显示在图像上。
            </div>
            {% endif %}
            
            <div class="viewer-actions">
                <a href="{{ url_for('web.results', study_id=study_id) }}" class="btn btn-info btn-sm">
                    <i class="fas fa-chart-bar me-1"></i>检测结果
                </a>
                <a href="{{ url_for('web.dashboard') }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-left me-1"></i>返回仪表板
                </a>
            </div>
        </div>
    </div>
    
    <!-- OHIF查看器 -->
    <div class="viewer-wrapper">
        {% if viewer_url %}
        <div class="loading-overlay" id="loadingOverlay">
            <div class="text-center">
                <div class="spinner-border text-light mb-3" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div>正在加载OHIF查看器...</div>
                <div class="mt-2">
                    <small class="text-muted">正在连接到 {{ viewer_url }}</small>
                </div>
            </div>
        </div>
        <iframe 
            id="ohifViewer" 
            src="{{ viewer_url }}" 
            class="viewer-container"
            frameborder="0"
            allowfullscreen
            onload="hideLoading()"
            onerror="showError()">
        </iframe>
        {% else %}
        <div class="error-message">
            <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
            <h4>无法加载查看器</h4>
            <p>无法获取OHIF查看器URL，请检查服务配置。</p>
            <div class="mt-3">
                <a href="{{ url_for('web.system_status') }}" class="btn btn-warning me-2">
                    <i class="fas fa-heartbeat me-1"></i>检查系统状态
                </a>
                <a href="{{ url_for('web.dashboard') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-1"></i>返回仪表板
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<script>
function hideLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

function showError() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    const iframe = document.getElementById('ohifViewer');
    
    if (loadingOverlay) {
        loadingOverlay.innerHTML = `
            <div class="text-center">
                <i class="fas fa-exclamation-triangle fa-3x mb-3 text-warning"></i>
                <h4>OHIF查看器连接失败</h4>
                <p class="mb-2">无法连接到OHIF查看器服务 ({{ viewer_url }})。</p>
                <div class="alert alert-info text-start mt-3" style="max-width: 500px; margin: 0 auto;">
                    <h6><i class="fas fa-info-circle me-1"></i>可能的原因：</h6>
                    <ul class="mb-2">
                        <li>OHIF查看器服务未启动</li>
                        <li>服务运行在不同的端口</li>
                        <li>网络连接问题</li>
                        <li>防火墙阻止连接</li>
                    </ul>
                    <small><strong>建议：</strong>请检查系统状态页面确认服务状态</small>
                </div>
                <div class="mt-3">
                    <button class="btn btn-primary me-2" onclick="location.reload()">
                        <i class="fas fa-redo me-1"></i>重新加载
                    </button>
                    <a href="{{ url_for('web.system_status') }}" class="btn btn-warning me-2">
                        <i class="fas fa-heartbeat me-1"></i>检查系统状态
                    </a>
                    <a href="{{ url_for('web.dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回仪表板
                    </a>
                </div>
            </div>
        `;
    }
}

// 设置超时，如果5秒后还在加载则显示错误
setTimeout(function() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay && loadingOverlay.style.display !== 'none') {
        showError();
    }
}, 5000);
</script>
{% endblock %}