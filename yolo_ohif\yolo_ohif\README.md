# 基于YOLO、Flask、Orthanc和OHIF的医学图像疾病检测系统

## 项目概述

本项目是一个集成了YOLO（You Only Look Once）目标检测算法、Flask Web框架、Orthanc DICOM服务器和OHIF（Open Health Imaging Foundation）查看器的医学图像疾病检测系统。该系统允许用户上传医学图像（如CT、MRI等），使用YOLO模型进行疾病检测和标注，并通过OHIF查看器展示检测结果。

## 系统架构

```
+------------------+    +------------------+    +------------------+
|                  |    |                  |    |                  |
|  医学图像数据源   |    |  Orthanc DICOM   |    |   Flask 服务器    |
|  (DICOM文件)     |    |     服务器       |    |                  |
|                  |    |                  |    |                  |
+--------+---------+    +--------+---------+    +--------+---------+
         |                       |                       |
         v                       v                       v
+------------------+    +------------------+    +------------------+
|                  |    |                  |    |                  |
|   OHIF 查看器    |<-->|   YOLO 模型     |<-->|   结果数据库     |
|                  |    |                  |    |                  |
+------------------+    +------------------+    +------------------+
```

## 功能特点

1. **DICOM图像管理**：通过Orthanc服务器管理和存储DICOM格式的医学图像
2. **疾病自动检测**：使用YOLO模型对医学图像进行疾病检测和标注
3. **交互式查看**：通过OHIF查看器提供专业的医学图像查看和交互功能
4. **结果标注与存储**：将检测结果转换为DICOM标注并存储
5. **用户友好界面**：提供简洁直观的Web界面进行操作

## 技术栈

- **前端**：OHIF Viewer (基于React)、Bootstrap
- **后端**：Flask (Python Web框架)
- **DICOM服务器**：Orthanc
- **AI模型**：YOLOv5/YOLOv8 (PyTorch)
- **数据库**：SQLite/PostgreSQL

## 🚀 快速开始

### 新的工作流程 (推荐)

我们将数据准备和模型训练分离为两个独立步骤，提高了代码的模块化和可维护性：

#### 步骤 1: 数据准备
```bash
# 准备 YOLO 训练数据集
python create_yolo_dataset.py
```

#### 步骤 2: 模型训练

**选项 A: 预训练权重训练 (推荐)**
**新功能**: 🎯 智能模型选择 - 支持选择不同大小的YOLO模型，避免重复下载
```bash
# 使用预训练权重进行微调训练 (更快，通常效果更好，支持模型选择)
python start_yolo11x_pretrained_training.py

# 医学图像512×512专用 (推荐医学影像，支持模型选择)
python start_yolo11x_pretrained_training_512.py
```

**模型选择功能**:
- 🤖 交互式选择：YOLO11n/s/m/l/x
- 📥 智能检测：自动检查模型是否已下载
- 💡 个性化推荐：根据场景推荐最适合的模型
- ⚡ 避免重复下载：节省时间和带宽

**选项 B: 从头开始训练**
```bash
# 从头开始训练 YOLO11x 模型 (需要更多时间和数据)
python start_yolo11x_training.py

# 医学图像512×512专用 (推荐医学影像)
python start_yolo11x_training_512.py
```

**训练方式对比:**
- **预训练权重训练**: 训练时间短(2-4小时)，数据需求少，通常效果更好
- **从头开始训练**: 训练时间长(8-12小时)，需要大量数据，完全定制化
- **512×512医学图像**: 节省38%内存，提升30%速度，匹配医学图像原生分辨率

详细说明请参考：[训练工作流程指南](TRAINING_WORKFLOW_GUIDE.md)

## 安装与配置

请参考各目录下的README文件进行详细安装和配置。

## 目录结构

```
/yolo_ohif
├── README.md                 # 项目说明文档
├── requirements.txt          # Python依赖包列表
├── config.py                 # 配置文件
├── app.py                    # Flask应用主入口
├── run.py                    # 启动脚本
├── static/                   # 静态资源文件
├── templates/                # HTML模板
├── models/                   # YOLO模型文件
│   ├── weights/              # 预训练权重
│   └── config/               # 模型配置
├── utils/                    # 工具函数
│   ├── dicom_utils.py        # DICOM处理工具
│   ├── detection_utils.py    # 检测相关工具
│   └── visualization.py      # 可视化工具
├── services/                 # 服务模块
│   ├── orthanc_service.py    # Orthanc服务接口
│   ├── detection_service.py  # 检测服务
│   └── ohif_service.py       # OHIF集成服务
└── docs/                     # 文档
```

## 使用流程

1. 启动Orthanc DICOM服务器
2. 启动Flask Web服务器
3. 上传DICOM图像到Orthanc服务器
4. 通过Web界面选择要分析的图像
5. 运行YOLO模型进行疾病检测
6. 在OHIF查看器中查看带有检测结果的图像

## 许可证

本项目采用MIT许可证。请参阅LICENSE文件了解详情。

## 贡献

欢迎提交问题和拉取请求以改进本项目。