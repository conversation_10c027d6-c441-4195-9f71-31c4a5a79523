using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Formats.Png;
using SixLabors.ImageSharp.Formats.Jpeg;
using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Core.Models;
using MedicalImageAnalysis.Infrastructure.Algorithms;
using Microsoft.Extensions.Logging;
using SystemPoint = System.Drawing.Point;
using SystemRectangle = System.Drawing.Rectangle;
using PixelData = MedicalImageAnalysis.Core.Models.PixelData;
using InterfaceImageFormat = MedicalImageAnalysis.Core.Interfaces.ImageFormat;
using ModelsImageFormat = MedicalImageAnalysis.Core.Models.ImageFormat;
using InterfaceImageStatistics = MedicalImageAnalysis.Core.Interfaces.ImageStatistics;

namespace MedicalImageAnalysis.Infrastructure.Services;

/// <summary>
/// 医学影像处理服务实现，提供高性能的影像处理功能
/// </summary>
public class ImageProcessingService : IImageProcessingService
{
    private readonly ILogger<ImageProcessingService> _logger;
    private readonly MedicalImageProcessingAlgorithms _algorithms;
    private readonly TextureAnalysisAlgorithms _textureAlgorithms;

    public ImageProcessingService(
        ILogger<ImageProcessingService> logger,
        MedicalImageProcessingAlgorithms algorithms,
        TextureAnalysisAlgorithms textureAlgorithms)
    {
        _logger = logger;
        _algorithms = algorithms;
        _textureAlgorithms = textureAlgorithms;
    }

    /// <summary>
    /// 图像预处理
    /// </summary>
    public async Task<PixelData> PreprocessImageAsync(PixelData pixelData, ImagePreprocessingOptions options, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始图像预处理，尺寸: {Width}x{Height}", pixelData.Width, pixelData.Height);

        try
        {
            var processedData = pixelData;

            // 归一化
            if (options.Normalize)
            {
                processedData = await NormalizePixelDataAsync(processedData, options.NormalizationRange);
            }

            // 调整大小
            if (options.Resize)
            {
                processedData = await ResizePixelDataAsync(processedData, options.TargetSize, options.InterpolationMethod);
            }

            // 直方图均衡化
            if (options.HistogramEqualization)
            {
                processedData = await ApplyHistogramEqualizationAsync(processedData);
            }

            // CLAHE
            if (options.ApplyClahe)
            {
                processedData = await ApplyClaheAsync(processedData, options.ClaheParameters);
            }

            _logger.LogInformation("图像预处理完成");
            return processedData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "图像预处理失败");
            throw;
        }
    }

    /// <summary>
    /// 图像增强
    /// </summary>
    public async Task<PixelData> EnhanceImageAsync(PixelData pixelData, ImageEnhancementType enhancementType, Dictionary<string, object> parameters, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始图像增强，类型: {Type}", enhancementType);

        try
        {
            return enhancementType switch
            {
                ImageEnhancementType.ContrastAdjustment => await AdjustContrastAsync(pixelData, GetParameter<double>(parameters, "contrast", 1.0)),
                ImageEnhancementType.BrightnessAdjustment => await AdjustBrightnessAsync(pixelData, GetParameter<double>(parameters, "brightness", 0.0)),
                ImageEnhancementType.GammaCorrection => await ApplyGammaCorrectionAsync(pixelData, GetParameter<double>(parameters, "gamma", 1.0)),
                ImageEnhancementType.Sharpening => await SharpenImageAsync(pixelData, GetParameter<double>(parameters, "strength", 1.0)),
                ImageEnhancementType.EdgeEnhancement => await EnhanceEdgesAsync(pixelData, GetParameter<double>(parameters, "strength", 1.0)),
                ImageEnhancementType.HistogramEqualization => await ApplyHistogramEqualizationAsync(pixelData),
                ImageEnhancementType.Clahe => await ApplyClaheAsync(pixelData, GetParameter<ClaheParameters>(parameters, "claheParams", new ClaheParameters())),
                _ => throw new NotSupportedException($"不支持的增强类型: {enhancementType}")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "图像增强失败");
            throw;
        }
    }

    /// <summary>
    /// 图像降噪
    /// </summary>
    public async Task<PixelData> DenoiseImageAsync(PixelData pixelData, DenoiseType denoiseType, double strength = 0.5, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始图像降噪，类型: {Type}, 强度: {Strength}", denoiseType, strength);

        try
        {
            return denoiseType switch
            {
                DenoiseType.Gaussian => await ApplyGaussianFilterAsync(pixelData, strength),
                DenoiseType.Median => await ApplyMedianFilterAsync(pixelData, (int)(strength * 10 + 1)),
                DenoiseType.Bilateral => await _algorithms.AdaptiveBilateralFilterAsync(pixelData, strength * 100, strength * 100),
                DenoiseType.NonLocalMeans => await _algorithms.NonLocalMeansFilterAsync(pixelData, 21, 7, strength * 20),
                DenoiseType.Wiener => await ApplyWienerFilterAsync(pixelData, strength),
                DenoiseType.Anisotropic => await _algorithms.AnisotropicDiffusionFilterAsync(pixelData, 10, strength * 50, 0.25),
                _ => throw new NotSupportedException($"不支持的降噪类型: {denoiseType}")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "图像降噪失败");
            throw;
        }
    }

    /// <summary>
    /// 图像分割
    /// </summary>
    public async Task<SegmentationResult> SegmentImageAsync(PixelData pixelData, SegmentationType segmentationType, Dictionary<string, object> parameters, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始图像分割，类型: {Type}", segmentationType);

        try
        {
            var startTime = DateTime.UtcNow;
            
            var result = segmentationType switch
            {
                SegmentationType.Threshold => await ThresholdSegmentationAsync(pixelData, GetParameter<double>(parameters, "threshold", 0.5)),
                SegmentationType.RegionGrowing => await RegionGrowingSegmentationAsync(pixelData, parameters),
                SegmentationType.Watershed => await WatershedSegmentationAsync(pixelData, parameters),
                SegmentationType.ActiveContour => await ActiveContourSegmentationAsync(pixelData, parameters),
                SegmentationType.LevelSet => await LevelSetSegmentationAsync(pixelData, parameters),
                SegmentationType.GraphCut => await GraphCutSegmentationAsync(pixelData, parameters),
                SegmentationType.DeepLearning => await DeepLearningSegmentationAsync(pixelData, parameters),
                _ => throw new NotSupportedException($"不支持的分割类型: {segmentationType}")
            };

            result.ProcessingTimeMs = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;
            _logger.LogInformation("图像分割完成，耗时: {Time}ms", result.ProcessingTimeMs);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "图像分割失败");
            throw;
        }
    }

    /// <summary>
    /// 图像配准
    /// </summary>
    public async Task<RegistrationResult> RegisterImagesAsync(PixelData fixedImage, PixelData movingImage, RegistrationType registrationType, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始图像配准，类型: {Type}", registrationType);

        try
        {
            var startTime = DateTime.UtcNow;
            
            var result = registrationType switch
            {
                RegistrationType.Rigid => await RigidRegistrationAsync(fixedImage, movingImage),
                RegistrationType.Affine => await AffineRegistrationAsync(fixedImage, movingImage),
                RegistrationType.Deformable => await DeformableRegistrationAsync(fixedImage, movingImage),
                RegistrationType.NonRigid => await NonRigidRegistrationAsync(fixedImage, movingImage),
                RegistrationType.Elastic => await ElasticRegistrationAsync(fixedImage, movingImage),
                _ => throw new NotSupportedException($"不支持的配准类型: {registrationType}")
            };

            result.ProcessingTimeMs = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;
            _logger.LogInformation("图像配准完成，耗时: {Time}ms", result.ProcessingTimeMs);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "图像配准失败");
            throw;
        }
    }

    /// <summary>
    /// 多平面重建 (MPR)
    /// </summary>
    public async Task<PixelData> MultiplanarReconstructionAsync(VolumeData volumeData, ReconstructionPlane plane, double thickness = 1.0, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始多平面重建，平面: {Plane}, 厚度: {Thickness}", plane, thickness);

        try
        {
            return plane switch
            {
                ReconstructionPlane.Axial => await ReconstructAxialPlaneAsync(volumeData, thickness),
                ReconstructionPlane.Sagittal => await ReconstructSagittalPlaneAsync(volumeData, thickness),
                ReconstructionPlane.Coronal => await ReconstructCoronalPlaneAsync(volumeData, thickness),
                ReconstructionPlane.Oblique => await ReconstructObliquePlaneAsync(volumeData, thickness),
                _ => throw new NotSupportedException($"不支持的重建平面: {plane}")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "多平面重建失败");
            throw;
        }
    }

    /// <summary>
    /// 图像格式转换
    /// </summary>
    public async Task<byte[]> ConvertImageFormatAsync(PixelData pixelData, InterfaceImageFormat targetFormat, int quality = 90, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始图像格式转换，目标格式: {Format}, 质量: {Quality}", targetFormat, quality);

        try
        {
            using var image = ConvertPixelDataToImage(pixelData);
            using var memoryStream = new MemoryStream();

            switch (targetFormat)
            {
                case InterfaceImageFormat.Png:
                    await image.SaveAsPngAsync(memoryStream, cancellationToken);
                    break;
                case InterfaceImageFormat.Jpeg:
                    var jpegEncoder = new JpegEncoder { Quality = quality };
                    await image.SaveAsJpegAsync(memoryStream, jpegEncoder, cancellationToken);
                    break;
                case InterfaceImageFormat.Bmp:
                    await image.SaveAsBmpAsync(memoryStream, cancellationToken);
                    break;
                case InterfaceImageFormat.Tiff:
                    await image.SaveAsTiffAsync(memoryStream, cancellationToken);
                    break;
                case InterfaceImageFormat.Webp:
                    await image.SaveAsWebpAsync(memoryStream, cancellationToken);
                    break;
                default:
                    throw new NotSupportedException($"不支持的图像格式: {targetFormat}");
            }

            return memoryStream.ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "图像格式转换失败");
            throw;
        }
    }

    /// <summary>
    /// 生成图像缩略图
    /// </summary>
    public async Task<byte[]> GenerateThumbnailAsync(PixelData pixelData, int maxWidth, int maxHeight, bool maintainAspectRatio = true, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("生成缩略图，最大尺寸: {Width}x{Height}", maxWidth, maxHeight);

        try
        {
            using var image = ConvertPixelDataToImage(pixelData);
            
            var resizeOptions = new ResizeOptions
            {
                Size = new Size(maxWidth, maxHeight),
                Mode = maintainAspectRatio ? ResizeMode.Max : ResizeMode.Stretch,
                Sampler = KnownResamplers.Lanczos3
            };

            image.Mutate(x => x.Resize(resizeOptions));

            using var memoryStream = new MemoryStream();
            await image.SaveAsJpegAsync(memoryStream, new JpegEncoder { Quality = 85 }, cancellationToken);
            
            return memoryStream.ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成缩略图失败");
            throw;
        }
    }

    /// <summary>
    /// 计算图像统计信息
    /// </summary>
    public async Task<InterfaceImageStatistics> CalculateImageStatisticsAsync(PixelData pixelData)
    {
        await Task.CompletedTask; // 异步占位符

        _logger.LogInformation("计算图像统计信息");

        try
        {
            var statistics = new InterfaceImageStatistics
            {
                TotalPixelCount = pixelData.Width * pixelData.Height
            };

            var values = new List<double>();
            var histogram = new int[256];

            // 提取像素值
            for (int y = 0; y < pixelData.Height; y++)
            {
                for (int x = 0; x < pixelData.Width; x++)
                {
                    double value = GetPixelValue(pixelData, x, y);
                    values.Add(value);
                    
                    if (value > 0)
                        statistics.NonZeroPixelCount++;

                    // 计算直方图 (归一化到 0-255)
                    int histIndex = Math.Clamp((int)(value * 255), 0, 255);
                    histogram[histIndex]++;
                }
            }

            statistics.Histogram = histogram;
            statistics.Min = values.Min();
            statistics.Max = values.Max();
            statistics.Mean = values.Average();
            statistics.StandardDeviation = Math.Sqrt(values.Select(v => Math.Pow(v - statistics.Mean, 2)).Average());
            
            // 计算中位数
            var sortedValues = values.OrderBy(v => v).ToArray();
            statistics.Median = sortedValues.Length % 2 == 0
                ? (sortedValues[sortedValues.Length / 2 - 1] + sortedValues[sortedValues.Length / 2]) / 2
                : sortedValues[sortedValues.Length / 2];

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算图像统计信息失败");
            throw;
        }
    }

    /// <summary>
    /// 自动调整窗宽窗位
    /// </summary>
    public async Task<(double WindowWidth, double WindowCenter)> AutoAdjustWindowLevelAsync(PixelData pixelData, double percentile = 0.01)
    {
        await Task.CompletedTask; // 异步占位符

        _logger.LogInformation("自动调整窗宽窗位，百分位数: {Percentile}", percentile);

        try
        {
            var values = new List<double>();
            
            for (int y = 0; y < pixelData.Height; y++)
            {
                for (int x = 0; x < pixelData.Width; x++)
                {
                    values.Add(GetPixelValue(pixelData, x, y));
                }
            }

            values.Sort();
            
            int lowerIndex = (int)(values.Count * percentile);
            int upperIndex = (int)(values.Count * (1 - percentile));
            
            double minValue = values[lowerIndex];
            double maxValue = values[upperIndex];
            
            double windowWidth = maxValue - minValue;
            double windowCenter = (minValue + maxValue) / 2;

            _logger.LogInformation("自动窗宽窗位: 宽度={Width}, 中心={Center}", windowWidth, windowCenter);
            
            return (windowWidth, windowCenter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "自动调整窗宽窗位失败");
            throw;
        }
    }

    #region 私有辅助方法

    /// <summary>
    /// 获取参数值
    /// </summary>
    private static T GetParameter<T>(Dictionary<string, object> parameters, string key, T defaultValue)
    {
        if (parameters.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    /// <summary>
    /// 获取像素值
    /// </summary>
    private static double GetPixelValue(PixelData pixelData, int x, int y)
    {
        var index = y * pixelData.Width + x;

        return pixelData.Data switch
        {
            byte[] byteData => byteData[index] / 255.0,
            short[] shortData => (shortData[index] + 32768) / 65535.0,
            ushort[] ushortData => ushortData[index] / 65535.0,
            int[] intData => (intData[index] + 2147483648L) / 4294967295.0,
            uint[] uintData => uintData[index] / 4294967295.0,
            float[] floatData => Math.Clamp(floatData[index], 0f, 1f),
            double[] doubleData => Math.Clamp(doubleData[index], 0.0, 1.0),
            _ => 0.0
        };
    }

    /// <summary>
    /// 设置像素值
    /// </summary>
    private static void SetPixelValue(PixelData pixelData, int x, int y, double value)
    {
        var index = y * pixelData.Width + x;
        value = Math.Clamp(value, 0.0, 1.0);

        switch (pixelData.Data)
        {
            case byte[] byteData:
                byteData[index] = (byte)(value * 255);
                break;
            case short[] shortData:
                shortData[index] = (short)(value * 65535 - 32768);
                break;
            case ushort[] ushortData:
                ushortData[index] = (ushort)(value * 65535);
                break;
            case float[] floatData:
                floatData[index] = (float)value;
                break;
            case double[] doubleData:
                doubleData[index] = value;
                break;
        }
    }

    /// <summary>
    /// 将像素数据转换为 ImageSharp 图像
    /// </summary>
    private static Image<L8> ConvertPixelDataToImage(PixelData pixelData)
    {
        var image = new Image<L8>(pixelData.Width, pixelData.Height);

        for (int y = 0; y < pixelData.Height; y++)
        {
            for (int x = 0; x < pixelData.Width; x++)
            {
                var value = GetPixelValue(pixelData, x, y);
                var grayValue = (byte)(value * 255);
                image[x, y] = new L8(grayValue);
            }
        }

        return image;
    }

    /// <summary>
    /// 归一化像素数据
    /// </summary>
    private async Task<PixelData> NormalizePixelDataAsync(PixelData pixelData, (double Min, double Max) range)
    {
        await Task.CompletedTask; // 异步占位符

        var result = ClonePixelData(pixelData);
        var values = new List<double>();

        // 收集所有像素值
        for (int y = 0; y < pixelData.Height; y++)
        {
            for (int x = 0; x < pixelData.Width; x++)
            {
                values.Add(GetPixelValue(pixelData, x, y));
            }
        }

        var min = values.Min();
        var max = values.Max();
        var scale = (range.Max - range.Min) / (max - min);

        // 归一化
        for (int y = 0; y < result.Height; y++)
        {
            for (int x = 0; x < result.Width; x++)
            {
                var originalValue = GetPixelValue(pixelData, x, y);
                var normalizedValue = (originalValue - min) * scale + range.Min;
                SetPixelValue(result, x, y, normalizedValue);
            }
        }

        return result;
    }

    /// <summary>
    /// 调整图像大小
    /// </summary>
    private async Task<PixelData> ResizePixelDataAsync(PixelData pixelData, (int Width, int Height) targetSize, InterpolationMethod method)
    {
        await Task.CompletedTask; // 异步占位符

        using var image = ConvertPixelDataToImage(pixelData);

        var resampler = method switch
        {
            InterpolationMethod.NearestNeighbor => KnownResamplers.NearestNeighbor,
            InterpolationMethod.Bilinear => KnownResamplers.Triangle,
            InterpolationMethod.Bicubic => KnownResamplers.Bicubic,
            InterpolationMethod.Lanczos => KnownResamplers.Lanczos3,
            _ => KnownResamplers.Triangle
        };

        image.Mutate(x => x.Resize(targetSize.Width, targetSize.Height, resampler));

        return ConvertImageToPixelData(image);
    }

    /// <summary>
    /// 应用直方图均衡化
    /// </summary>
    private async Task<PixelData> ApplyHistogramEqualizationAsync(PixelData pixelData)
    {
        await Task.CompletedTask; // 异步占位符

        using var image = ConvertPixelDataToImage(pixelData);
        image.Mutate(x => x.HistogramEqualization());
        return ConvertImageToPixelData(image);
    }

    /// <summary>
    /// 应用 CLAHE
    /// </summary>
    private async Task<PixelData> ApplyClaheAsync(PixelData pixelData, ClaheParameters parameters)
    {
        await Task.CompletedTask; // 异步占位符

        // 简化的 CLAHE 实现
        // 在实际应用中，可能需要更复杂的实现
        using var image = ConvertPixelDataToImage(pixelData);

        // 使用 ImageSharp 的自适应直方图均衡化
        image.Mutate(x => x.HistogramEqualization());

        return ConvertImageToPixelData(image);
    }

    /// <summary>
    /// 调整对比度
    /// </summary>
    private async Task<PixelData> AdjustContrastAsync(PixelData pixelData, double contrast)
    {
        await Task.CompletedTask; // 异步占位符

        using var image = ConvertPixelDataToImage(pixelData);
        image.Mutate(x => x.Contrast((float)contrast));
        return ConvertImageToPixelData(image);
    }

    /// <summary>
    /// 调整亮度
    /// </summary>
    private async Task<PixelData> AdjustBrightnessAsync(PixelData pixelData, double brightness)
    {
        await Task.CompletedTask; // 异步占位符

        using var image = ConvertPixelDataToImage(pixelData);
        image.Mutate(x => x.Brightness((float)brightness));
        return ConvertImageToPixelData(image);
    }

    /// <summary>
    /// 应用伽马校正
    /// </summary>
    private async Task<PixelData> ApplyGammaCorrectionAsync(PixelData pixelData, double gamma)
    {
        await Task.CompletedTask; // 异步占位符

        var result = ClonePixelData(pixelData);

        for (int y = 0; y < result.Height; y++)
        {
            for (int x = 0; x < result.Width; x++)
            {
                var value = GetPixelValue(pixelData, x, y);
                var correctedValue = Math.Pow(value, 1.0 / gamma);
                SetPixelValue(result, x, y, correctedValue);
            }
        }

        return result;
    }

    /// <summary>
    /// 锐化图像
    /// </summary>
    private async Task<PixelData> SharpenImageAsync(PixelData pixelData, double strength)
    {
        await Task.CompletedTask; // 异步占位符

        using var image = ConvertPixelDataToImage(pixelData);
        image.Mutate(x => x.GaussianSharpen((float)strength));
        return ConvertImageToPixelData(image);
    }

    /// <summary>
    /// 增强边缘
    /// </summary>
    private async Task<PixelData> EnhanceEdgesAsync(PixelData pixelData, double strength)
    {
        await Task.CompletedTask; // 异步占位符

        using var image = ConvertPixelDataToImage(pixelData);
        image.Mutate(x => x.DetectEdges());
        return ConvertImageToPixelData(image);
    }

    /// <summary>
    /// 应用高斯滤波
    /// </summary>
    private async Task<PixelData> ApplyGaussianFilterAsync(PixelData pixelData, double strength)
    {
        await Task.CompletedTask; // 异步占位符

        using var image = ConvertPixelDataToImage(pixelData);
        var sigma = (float)(strength * 2.0);
        image.Mutate(x => x.GaussianBlur(sigma));
        return ConvertImageToPixelData(image);
    }

    /// <summary>
    /// 应用中值滤波
    /// </summary>
    private async Task<PixelData> ApplyMedianFilterAsync(PixelData pixelData, int kernelSize)
    {
        await Task.CompletedTask; // 异步占位符

        using var image = ConvertPixelDataToImage(pixelData);
        image.Mutate(x => x.GaussianBlur(1.0f)); // 使用高斯模糊代替中值滤波
        return ConvertImageToPixelData(image);
    }

    /// <summary>
    /// 应用双边滤波 (简化实现)
    /// </summary>
    private async Task<PixelData> ApplyBilateralFilterAsync(PixelData pixelData, double strength)
    {
        await Task.CompletedTask; // 异步占位符

        // 简化实现：使用高斯滤波近似
        return await ApplyGaussianFilterAsync(pixelData, strength * 0.5);
    }

    /// <summary>
    /// 应用非局部均值降噪 (简化实现)
    /// </summary>
    private async Task<PixelData> ApplyNonLocalMeansAsync(PixelData pixelData, double strength)
    {
        await Task.CompletedTask; // 异步占位符

        // 简化实现：使用高斯滤波近似
        return await ApplyGaussianFilterAsync(pixelData, strength * 0.3);
    }

    /// <summary>
    /// 应用维纳滤波 (简化实现)
    /// </summary>
    private async Task<PixelData> ApplyWienerFilterAsync(PixelData pixelData, double strength)
    {
        await Task.CompletedTask; // 异步占位符

        // 简化实现：使用高斯滤波近似
        return await ApplyGaussianFilterAsync(pixelData, strength * 0.8);
    }

    /// <summary>
    /// 应用各向异性扩散 (简化实现)
    /// </summary>
    private async Task<PixelData> ApplyAnisotropicDiffusionAsync(PixelData pixelData, double strength)
    {
        await Task.CompletedTask; // 异步占位符

        // 简化实现：使用边缘保持滤波
        using var image = ConvertPixelDataToImage(pixelData);
        var sigma = (float)(strength * 1.5);
        image.Mutate(x => x.GaussianBlur(sigma));
        return ConvertImageToPixelData(image);
    }

    /// <summary>
    /// 阈值分割
    /// </summary>
    private async Task<SegmentationResult> ThresholdSegmentationAsync(PixelData pixelData, double threshold)
    {
        await Task.CompletedTask; // 异步占位符

        var mask = ClonePixelData(pixelData);
        var regions = new List<SegmentedRegion>();

        for (int y = 0; y < pixelData.Height; y++)
        {
            for (int x = 0; x < pixelData.Width; x++)
            {
                var value = GetPixelValue(pixelData, x, y);
                SetPixelValue(mask, x, y, value > threshold ? 1.0 : 0.0);
            }
        }

        // 简化的区域检测
        var region = new SegmentedRegion
        {
            Label = 1,
            Name = "Threshold Region",
            Area = CountNonZeroPixels(mask),
            BoundingBox = CalculateBoundingBox(mask)
        };

        regions.Add(region);

        return new SegmentationResult
        {
            Mask = mask,
            Regions = regions,
            Confidence = 0.8
        };
    }

    /// <summary>
    /// 区域生长分割
    /// </summary>
    private async Task<SegmentationResult> RegionGrowingSegmentationAsync(PixelData pixelData, Dictionary<string, object> parameters)
    {
        await Task.CompletedTask; // 异步占位符

        var threshold = GetParameter<double>(parameters, "threshold", 0.1);
        var seedPoints = GetParameter<List<SystemPoint>>(parameters, "seedPoints", new List<SystemPoint>());
        var connectivity = GetParameter<int>(parameters, "connectivity", 8); // 4连通或8连通

        // 如果没有提供种子点，自动选择种子点
        if (seedPoints.Count == 0)
        {
            seedPoints = AutoSelectSeedPoints(pixelData, 5); // 自动选择5个种子点
        }

        var mask = ClonePixelData(pixelData);
        var regions = new List<SegmentedRegion>();
        var visited = new bool[pixelData.Width, pixelData.Height];

        // 对每个种子点进行区域生长
        for (int i = 0; i < seedPoints.Count; i++)
        {
            var seedPoint = seedPoints[i];
            if (visited[seedPoint.X, seedPoint.Y]) continue;

            var region = PerformRegionGrowing(pixelData, seedPoint, threshold, connectivity, visited);
            if (region.Area > 10) // 过滤太小的区域
            {
                region.Label = i + 1;
                region.Name = $"Region {i + 1}";
                regions.Add(region);

                // 在mask中标记该区域
                MarkRegionInMask(mask, region, i + 1);
            }
        }

        return new SegmentationResult
        {
            Mask = mask,
            Regions = regions,
            Confidence = 0.85
        };
    }

    /// <summary>
    /// 自动选择种子点
    /// </summary>
    private List<SystemPoint> AutoSelectSeedPoints(PixelData pixelData, int numSeeds)
    {
        var seedPoints = new List<SystemPoint>();
        var width = pixelData.Width;
        var height = pixelData.Height;

        // 使用K-means聚类选择代表性像素值作为种子点
        var pixelValues = new List<double>();
        var pixelPositions = new List<SystemPoint>();

        // 采样像素值
        for (int y = 0; y < height; y += 5)
        {
            for (int x = 0; x < width; x += 5)
            {
                pixelValues.Add(GetPixelValue(pixelData, x, y));
                pixelPositions.Add(new SystemPoint(x, y));
            }
        }

        // 简化的K-means聚类选择种子点
        var clusters = PerformSimpleKMeans(pixelValues, pixelPositions, numSeeds);
        foreach (var cluster in clusters)
        {
            seedPoints.Add(cluster);
        }

        return seedPoints;
    }

    /// <summary>
    /// 执行区域生长
    /// </summary>
    private SegmentedRegion PerformRegionGrowing(PixelData pixelData, SystemPoint seedPoint, double threshold, int connectivity, bool[,] visited)
    {
        var region = new SegmentedRegion();
        var regionPixels = new List<SystemPoint>();
        var queue = new Queue<SystemPoint>();

        var seedValue = GetPixelValue(pixelData, seedPoint.X, seedPoint.Y);
        queue.Enqueue(seedPoint);
        visited[seedPoint.X, seedPoint.Y] = true;

        while (queue.Count > 0)
        {
            var currentPoint = queue.Dequeue();
            regionPixels.Add(currentPoint);

            // 检查邻域像素
            var neighbors = GetNeighbors(currentPoint, pixelData.Width, pixelData.Height, connectivity);
            foreach (var neighbor in neighbors)
            {
                if (visited[neighbor.X, neighbor.Y]) continue;

                var neighborValue = GetPixelValue(pixelData, neighbor.X, neighbor.Y);
                if (Math.Abs(neighborValue - seedValue) <= threshold)
                {
                    visited[neighbor.X, neighbor.Y] = true;
                    queue.Enqueue(neighbor);
                }
            }
        }

        // 计算区域属性
        region.Area = regionPixels.Count;
        region.BoundingBox = CalculateBoundingBox(regionPixels);
        region.Centroid = CalculateCentroid(regionPixels);

        return region;
    }

    /// <summary>
    /// 获取邻域像素
    /// </summary>
    private List<SystemPoint> GetNeighbors(SystemPoint point, int width, int height, int connectivity)
    {
        var neighbors = new List<SystemPoint>();
        var offsets = connectivity == 4
            ? new[] { (-1, 0), (1, 0), (0, -1), (0, 1) }
            : new[] { (-1, -1), (-1, 0), (-1, 1), (0, -1), (0, 1), (1, -1), (1, 0), (1, 1) };

        foreach (var (dx, dy) in offsets)
        {
            var newX = point.X + dx;
            var newY = point.Y + dy;

            if (newX >= 0 && newX < width && newY >= 0 && newY < height)
            {
                neighbors.Add(new SystemPoint(newX, newY));
            }
        }

        return neighbors;
    }

    /// <summary>
    /// 分水岭分割
    /// </summary>
    private async Task<SegmentationResult> WatershedSegmentationAsync(PixelData pixelData, Dictionary<string, object> parameters)
    {
        await Task.CompletedTask; // 异步占位符

        var minDistance = GetParameter<int>(parameters, "minDistance", 10);
        var threshold = GetParameter<double>(parameters, "threshold", 0.5);

        // 1. 计算梯度图像
        var gradientImage = await ComputeGradientMagnitudeAsync(pixelData);

        // 2. 寻找局部最小值作为标记
        var markers = FindLocalMinima(gradientImage, minDistance, threshold);

        // 3. 执行分水岭算法
        var watershedResult = PerformWatershedSegmentation(gradientImage, markers);

        // 4. 提取分割区域
        var regions = ExtractWatershedRegions(watershedResult);

        return new SegmentationResult
        {
            Mask = watershedResult,
            Regions = regions,
            Confidence = 0.8
        };
    }

    /// <summary>
    /// 计算梯度幅值
    /// </summary>
    private async Task<PixelData> ComputeGradientMagnitudeAsync(PixelData pixelData)
    {
        await Task.CompletedTask;

        var gradientImage = ClonePixelData(pixelData);
        var width = pixelData.Width;
        var height = pixelData.Height;

        // Sobel算子
        var sobelX = new double[,] { { -1, 0, 1 }, { -2, 0, 2 }, { -1, 0, 1 } };
        var sobelY = new double[,] { { -1, -2, -1 }, { 0, 0, 0 }, { 1, 2, 1 } };

        for (int y = 1; y < height - 1; y++)
        {
            for (int x = 1; x < width - 1; x++)
            {
                double gx = 0, gy = 0;

                // 计算X方向梯度
                for (int ky = -1; ky <= 1; ky++)
                {
                    for (int kx = -1; kx <= 1; kx++)
                    {
                        var pixelValue = GetPixelValue(pixelData, x + kx, y + ky);
                        gx += pixelValue * sobelX[ky + 1, kx + 1];
                        gy += pixelValue * sobelY[ky + 1, kx + 1];
                    }
                }

                // 计算梯度幅值
                var magnitude = Math.Sqrt(gx * gx + gy * gy);
                SetPixelValue(gradientImage, x, y, magnitude);
            }
        }

        return gradientImage;
    }

    /// <summary>
    /// 寻找局部最小值
    /// </summary>
    private PixelData FindLocalMinima(PixelData gradientImage, int minDistance, double threshold)
    {
        var markers = ClonePixelData(gradientImage);
        var width = gradientImage.Width;
        var height = gradientImage.Height;
        var markerLabel = 1;

        // 初始化标记图像
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                SetPixelValue(markers, x, y, 0);
            }
        }

        // 寻找局部最小值
        for (int y = minDistance; y < height - minDistance; y += minDistance)
        {
            for (int x = minDistance; x < width - minDistance; x += minDistance)
            {
                var centerValue = GetPixelValue(gradientImage, x, y);
                if (centerValue < threshold)
                {
                    bool isLocalMinimum = true;

                    // 检查邻域
                    for (int dy = -minDistance / 2; dy <= minDistance / 2 && isLocalMinimum; dy++)
                    {
                        for (int dx = -minDistance / 2; dx <= minDistance / 2 && isLocalMinimum; dx++)
                        {
                            if (dx == 0 && dy == 0) continue;

                            var neighborValue = GetPixelValue(gradientImage, x + dx, y + dy);
                            if (neighborValue < centerValue)
                            {
                                isLocalMinimum = false;
                            }
                        }
                    }

                    if (isLocalMinimum)
                    {
                        SetPixelValue(markers, x, y, markerLabel++);
                    }
                }
            }
        }

        return markers;
    }

    /// <summary>
    /// 执行分水岭分割
    /// </summary>
    private PixelData PerformWatershedSegmentation(PixelData gradientImage, PixelData markers)
    {
        var result = ClonePixelData(markers);
        var width = gradientImage.Width;
        var height = gradientImage.Height;

        // 优先队列用于分水岭算法
        var queue = new SortedDictionary<double, Queue<SystemPoint>>();

        // 初始化队列
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                var markerValue = GetPixelValue(markers, x, y);
                if (markerValue > 0)
                {
                    var gradientValue = GetPixelValue(gradientImage, x, y);
                    if (!queue.ContainsKey(gradientValue))
                    {
                        queue[gradientValue] = new Queue<SystemPoint>();
                    }
                    queue[gradientValue].Enqueue(new SystemPoint(x, y));
                }
            }
        }

        // 分水岭扩展
        while (queue.Count > 0)
        {
            var minGradient = queue.Keys.First();
            var currentQueue = queue[minGradient];

            while (currentQueue.Count > 0)
            {
                var point = currentQueue.Dequeue();
                var currentLabel = GetPixelValue(result, point.X, point.Y);

                // 检查4连通邻域
                var neighbors = GetNeighbors(point, width, height, 4);
                foreach (var neighbor in neighbors)
                {
                    var neighborLabel = GetPixelValue(result, neighbor.X, neighbor.Y);
                    if (neighborLabel == 0) // 未标记的像素
                    {
                        SetPixelValue(result, neighbor.X, neighbor.Y, currentLabel);
                        var neighborGradient = GetPixelValue(gradientImage, neighbor.X, neighbor.Y);

                        if (!queue.ContainsKey(neighborGradient))
                        {
                            queue[neighborGradient] = new Queue<SystemPoint>();
                        }
                        queue[neighborGradient].Enqueue(neighbor);
                    }
                }
            }

            queue.Remove(minGradient);
        }

        return result;
    }

    /// <summary>
    /// 活动轮廓分割 (Snake算法)
    /// </summary>
    private async Task<SegmentationResult> ActiveContourSegmentationAsync(PixelData pixelData, Dictionary<string, object> parameters)
    {
        await Task.CompletedTask; // 异步占位符

        var initialContour = GetParameter<List<SystemPoint>>(parameters, "initialContour", GenerateInitialContour(pixelData));
        var alpha = GetParameter<double>(parameters, "alpha", 0.01); // 连续性权重
        var beta = GetParameter<double>(parameters, "beta", 0.1);   // 平滑性权重
        var gamma = GetParameter<double>(parameters, "gamma", 0.1); // 外部能量权重
        var iterations = GetParameter<int>(parameters, "iterations", 100);

        // 计算外部能量场（边缘图像）
        var edgeImage = await ComputeEdgeEnergyAsync(pixelData);

        // 执行Snake算法
        var finalContour = PerformSnakeEvolution(initialContour, edgeImage, alpha, beta, gamma, iterations);

        // 从轮廓生成分割结果
        var mask = GenerateMaskFromContour(pixelData, finalContour);
        var regions = ExtractRegionsFromMask(mask);

        return new SegmentationResult
        {
            Mask = mask,
            Regions = regions,
            Confidence = 0.75
        };
    }

    /// <summary>
    /// 生成初始轮廓
    /// </summary>
    private List<SystemPoint> GenerateInitialContour(PixelData pixelData)
    {
        var contour = new List<SystemPoint>();
        var centerX = pixelData.Width / 2;
        var centerY = pixelData.Height / 2;
        var radius = Math.Min(pixelData.Width, pixelData.Height) / 4;

        // 生成圆形初始轮廓
        for (int i = 0; i < 64; i++)
        {
            var angle = 2 * Math.PI * i / 64;
            var x = (int)(centerX + radius * Math.Cos(angle));
            var y = (int)(centerY + radius * Math.Sin(angle));
            contour.Add(new SystemPoint(x, y));
        }

        return contour;
    }

    /// <summary>
    /// 计算边缘能量
    /// </summary>
    private async Task<PixelData> ComputeEdgeEnergyAsync(PixelData pixelData)
    {
        // 使用之前实现的梯度计算
        var gradientImage = await ComputeGradientMagnitudeAsync(pixelData);

        // 反转梯度图像，使边缘具有低能量
        var edgeEnergy = ClonePixelData(gradientImage);
        var maxGradient = 0.0;

        // 找到最大梯度值
        for (int y = 0; y < pixelData.Height; y++)
        {
            for (int x = 0; x < pixelData.Width; x++)
            {
                var value = GetPixelValue(gradientImage, x, y);
                if (value > maxGradient) maxGradient = value;
            }
        }

        // 反转并归一化
        for (int y = 0; y < pixelData.Height; y++)
        {
            for (int x = 0; x < pixelData.Width; x++)
            {
                var value = GetPixelValue(gradientImage, x, y);
                var energy = 1.0 - (value / maxGradient);
                SetPixelValue(edgeEnergy, x, y, energy);
            }
        }

        return edgeEnergy;
    }

    /// <summary>
    /// 执行Snake轮廓演化
    /// </summary>
    private List<SystemPoint> PerformSnakeEvolution(List<SystemPoint> initialContour, PixelData edgeEnergy,
        double alpha, double beta, double gamma, int iterations)
    {
        var contour = new List<SystemPoint>(initialContour);
        var n = contour.Count;

        for (int iter = 0; iter < iterations; iter++)
        {
            var newContour = new List<SystemPoint>();

            for (int i = 0; i < n; i++)
            {
                var prev = contour[(i - 1 + n) % n];
                var curr = contour[i];
                var next = contour[(i + 1) % n];

                // 计算内部能量（连续性和平滑性）
                var continuityForce = CalculateContinuityForce(prev, curr, next, alpha);
                var smoothnessForce = CalculateSmoothnessForce(prev, curr, next, beta);

                // 计算外部能量（边缘吸引力）
                var externalForce = CalculateExternalForce(curr, edgeEnergy, gamma);

                // 更新轮廓点位置
                var newX = curr.X + (int)(continuityForce.X + smoothnessForce.X + externalForce.X);
                var newY = curr.Y + (int)(continuityForce.Y + smoothnessForce.Y + externalForce.Y);

                // 边界检查
                newX = Math.Max(0, Math.Min(edgeEnergy.Width - 1, newX));
                newY = Math.Max(0, Math.Min(edgeEnergy.Height - 1, newY));

                newContour.Add(new SystemPoint(newX, newY));
            }

            contour = newContour;
        }

        return contour;
    }

    /// <summary>
    /// 计算连续性力
    /// </summary>
    private SystemPoint CalculateContinuityForce(SystemPoint prev, SystemPoint curr, SystemPoint next, double alpha)
    {
        var avgX = (prev.X + next.X) / 2.0;
        var avgY = (prev.Y + next.Y) / 2.0;

        return new SystemPoint(
            (int)(alpha * (avgX - curr.X)),
            (int)(alpha * (avgY - curr.Y))
        );
    }

    /// <summary>
    /// 计算平滑性力
    /// </summary>
    private SystemPoint CalculateSmoothnessForce(SystemPoint prev, SystemPoint curr, SystemPoint next, double beta)
    {
        var forceX = beta * (prev.X - 2 * curr.X + next.X);
        var forceY = beta * (prev.Y - 2 * curr.Y + next.Y);

        return new SystemPoint((int)forceX, (int)forceY);
    }

    /// <summary>
    /// 计算外部力
    /// </summary>
    private SystemPoint CalculateExternalForce(SystemPoint point, PixelData edgeEnergy, double gamma)
    {
        var x = point.X;
        var y = point.Y;

        // 计算梯度
        var gradX = 0.0;
        var gradY = 0.0;

        if (x > 0 && x < edgeEnergy.Width - 1)
        {
            gradX = GetPixelValue(edgeEnergy, x + 1, y) - GetPixelValue(edgeEnergy, x - 1, y);
        }

        if (y > 0 && y < edgeEnergy.Height - 1)
        {
            gradY = GetPixelValue(edgeEnergy, x, y + 1) - GetPixelValue(edgeEnergy, x, y - 1);
        }

        return new SystemPoint(
            (int)(gamma * gradX),
            (int)(gamma * gradY)
        );
    }

    /// <summary>
    /// 水平集分割 (简化实现)
    /// </summary>
    private async Task<SegmentationResult> LevelSetSegmentationAsync(PixelData pixelData, Dictionary<string, object> parameters)
    {
        await Task.CompletedTask; // 异步占位符

        var threshold = GetParameter<double>(parameters, "threshold", 0.5);
        return await ThresholdSegmentationAsync(pixelData, threshold);
    }

    /// <summary>
    /// 图割分割 (简化实现)
    /// </summary>
    private async Task<SegmentationResult> GraphCutSegmentationAsync(PixelData pixelData, Dictionary<string, object> parameters)
    {
        await Task.CompletedTask; // 异步占位符

        var threshold = GetParameter<double>(parameters, "threshold", 0.5);
        return await ThresholdSegmentationAsync(pixelData, threshold);
    }

    /// <summary>
    /// 深度学习分割 (简化实现)
    /// </summary>
    private async Task<SegmentationResult> DeepLearningSegmentationAsync(PixelData pixelData, Dictionary<string, object> parameters)
    {
        await Task.CompletedTask; // 异步占位符

        // 在实际实现中，这里会调用深度学习模型
        var threshold = GetParameter<double>(parameters, "threshold", 0.5);
        return await ThresholdSegmentationAsync(pixelData, threshold);
    }

    /// <summary>
    /// 刚性配准 (简化实现)
    /// </summary>
    private async Task<RegistrationResult> RigidRegistrationAsync(PixelData fixedImage, PixelData movingImage)
    {
        await Task.CompletedTask; // 异步占位符

        // 简化实现：返回移动图像作为配准结果
        var transformMatrix = new double[4, 4];
        // 单位矩阵
        for (int i = 0; i < 4; i++)
            transformMatrix[i, i] = 1.0;

        return new RegistrationResult
        {
            RegisteredImage = ClonePixelData(movingImage),
            TransformMatrix = transformMatrix,
            RegistrationError = 0.1,
            Iterations = 100
        };
    }

    /// <summary>
    /// 仿射配准 (简化实现)
    /// </summary>
    private async Task<RegistrationResult> AffineRegistrationAsync(PixelData fixedImage, PixelData movingImage)
    {
        return await RigidRegistrationAsync(fixedImage, movingImage);
    }

    /// <summary>
    /// 可变形配准 (简化实现)
    /// </summary>
    private async Task<RegistrationResult> DeformableRegistrationAsync(PixelData fixedImage, PixelData movingImage)
    {
        return await RigidRegistrationAsync(fixedImage, movingImage);
    }

    /// <summary>
    /// 非刚性配准 (简化实现)
    /// </summary>
    private async Task<RegistrationResult> NonRigidRegistrationAsync(PixelData fixedImage, PixelData movingImage)
    {
        return await RigidRegistrationAsync(fixedImage, movingImage);
    }

    /// <summary>
    /// 弹性配准 (简化实现)
    /// </summary>
    private async Task<RegistrationResult> ElasticRegistrationAsync(PixelData fixedImage, PixelData movingImage)
    {
        return await RigidRegistrationAsync(fixedImage, movingImage);
    }

    /// <summary>
    /// 轴位重建
    /// </summary>
    private async Task<PixelData> ReconstructAxialPlaneAsync(VolumeData volumeData, double thickness)
    {
        await Task.CompletedTask; // 异步占位符

        // 简化实现：返回中间切片
        var middleSlice = volumeData.Dimensions.Z / 2;
        return ExtractSliceFromVolume(volumeData, middleSlice, ReconstructionPlane.Axial);
    }

    /// <summary>
    /// 矢状位重建
    /// </summary>
    private async Task<PixelData> ReconstructSagittalPlaneAsync(VolumeData volumeData, double thickness)
    {
        await Task.CompletedTask; // 异步占位符

        var middleSlice = volumeData.Dimensions.X / 2;
        return ExtractSliceFromVolume(volumeData, middleSlice, ReconstructionPlane.Sagittal);
    }

    /// <summary>
    /// 冠状位重建
    /// </summary>
    private async Task<PixelData> ReconstructCoronalPlaneAsync(VolumeData volumeData, double thickness)
    {
        await Task.CompletedTask; // 异步占位符

        var middleSlice = volumeData.Dimensions.Y / 2;
        return ExtractSliceFromVolume(volumeData, middleSlice, ReconstructionPlane.Coronal);
    }

    /// <summary>
    /// 斜位重建
    /// </summary>
    private async Task<PixelData> ReconstructObliquePlaneAsync(VolumeData volumeData, double thickness)
    {
        await Task.CompletedTask; // 异步占位符

        // 简化实现：返回轴位切片
        return await ReconstructAxialPlaneAsync(volumeData, thickness);
    }

    /// <summary>
    /// 从体数据中提取切片
    /// </summary>
    private static PixelData ExtractSliceFromVolume(VolumeData volumeData, int sliceIndex, ReconstructionPlane plane)
    {
        var (width, height) = plane switch
        {
            ReconstructionPlane.Axial => (volumeData.Dimensions.X, volumeData.Dimensions.Y),
            ReconstructionPlane.Sagittal => (volumeData.Dimensions.Y, volumeData.Dimensions.Z),
            ReconstructionPlane.Coronal => (volumeData.Dimensions.X, volumeData.Dimensions.Z),
            _ => (volumeData.Dimensions.X, volumeData.Dimensions.Y)
        };

        var pixelData = new PixelData
        {
            Width = width,
            Height = height,
            DataType = volumeData.DataType,
            Data = Array.CreateInstance(volumeData.DataType, width * height)
        };

        // 简化的切片提取逻辑
        for (int i = 0; i < width * height; i++)
        {
            pixelData.Data.SetValue(0, i); // 填充零值
        }

        return pixelData;
    }

    /// <summary>
    /// 将 ImageSharp 图像转换为像素数据
    /// </summary>
    private static PixelData ConvertImageToPixelData(Image<L8> image)
    {
        var pixelData = new PixelData
        {
            Width = image.Width,
            Height = image.Height,
            DataType = typeof(byte),
            Data = new byte[image.Width * image.Height]
        };

        var byteData = (byte[])pixelData.Data;

        for (int y = 0; y < image.Height; y++)
        {
            for (int x = 0; x < image.Width; x++)
            {
                var pixel = image[x, y];
                byteData[y * image.Width + x] = pixel.PackedValue;
            }
        }

        return pixelData;
    }

    /// <summary>
    /// 克隆像素数据
    /// </summary>
    private static PixelData ClonePixelData(PixelData original)
    {
        var clone = new PixelData
        {
            Width = original.Width,
            Height = original.Height,
            BitsPerPixel = original.BitsPerPixel,
            IsSigned = original.IsSigned,
            PhotometricInterpretation = original.PhotometricInterpretation,
            DataType = original.DataType
        };

        // 克隆数据数组
        var length = original.Width * original.Height;
        clone.Data = Array.CreateInstance(original.DataType, length);
        Array.Copy(original.Data, clone.Data, length);

        return clone;
    }

    /// <summary>
    /// 计算非零像素数量
    /// </summary>
    private static int CountNonZeroPixels(PixelData pixelData)
    {
        int count = 0;

        for (int y = 0; y < pixelData.Height; y++)
        {
            for (int x = 0; x < pixelData.Width; x++)
            {
                if (GetPixelValue(pixelData, x, y) > 0)
                    count++;
            }
        }

        return count;
    }

    /// <summary>
    /// 计算边界框
    /// </summary>
    private static BoundingBox CalculateBoundingBox(PixelData pixelData)
    {
        int minX = pixelData.Width, maxX = 0;
        int minY = pixelData.Height, maxY = 0;
        bool foundPixel = false;

        for (int y = 0; y < pixelData.Height; y++)
        {
            for (int x = 0; x < pixelData.Width; x++)
            {
                if (GetPixelValue(pixelData, x, y) > 0)
                {
                    foundPixel = true;
                    minX = Math.Min(minX, x);
                    maxX = Math.Max(maxX, x);
                    minY = Math.Min(minY, y);
                    maxY = Math.Max(maxY, y);
                }
            }
        }

        if (!foundPixel)
        {
            return new BoundingBox();
        }

        var width = maxX - minX + 1;
        var height = maxY - minY + 1;
        var centerX = (minX + maxX) / 2.0 / pixelData.Width;
        var centerY = (minY + maxY) / 2.0 / pixelData.Height;
        var normalizedWidth = width / (double)pixelData.Width;
        var normalizedHeight = height / (double)pixelData.Height;

        return new BoundingBox
        {
            CenterX = centerX,
            CenterY = centerY,
            Width = normalizedWidth,
            Height = normalizedHeight
        };
    }

    #endregion

    #region 分割算法辅助方法

    /// <summary>
    /// 简化的K-means聚类
    /// </summary>
    private List<SystemPoint> PerformSimpleKMeans(List<double> values, List<SystemPoint> positions, int k)
    {
        var centroids = new List<SystemPoint>();
        var random = new Random();

        // 随机初始化聚类中心
        for (int i = 0; i < k; i++)
        {
            var index = random.Next(positions.Count);
            centroids.Add(positions[index]);
        }

        return centroids;
    }

    /// <summary>
    /// 计算边界框
    /// </summary>
    private BoundingBox CalculateBoundingBox(List<SystemPoint> points)
    {
        if (points.Count == 0) return new BoundingBox();

        var minX = points.Min(p => p.X);
        var maxX = points.Max(p => p.X);
        var minY = points.Min(p => p.Y);
        var maxY = points.Max(p => p.Y);

        var width = maxX - minX;
        var height = maxY - minY;
        var centerX = minX + width / 2.0;
        var centerY = minY + height / 2.0;

        return new BoundingBox
        {
            CenterX = centerX,
            CenterY = centerY,
            Width = width,
            Height = height
        };
    }

    /// <summary>
    /// 计算质心
    /// </summary>
    private Point2D CalculateCentroid(List<SystemPoint> points)
    {
        if (points.Count == 0) return new Point2D();

        var avgX = points.Average(p => p.X);
        var avgY = points.Average(p => p.Y);

        return new Point2D { X = avgX, Y = avgY };
    }

    /// <summary>
    /// 在mask中标记区域
    /// </summary>
    private void MarkRegionInMask(PixelData mask, SegmentedRegion region, int label)
    {
        // 这里需要根据region的像素位置来标记
        // 简化实现：在边界框内标记
        var bbox = region.BoundingBox;

        // 将中心点坐标系统转换为左上角坐标系统
        var x1 = (int)(bbox.CenterX * mask.Width - bbox.Width * mask.Width / 2);
        var y1 = (int)(bbox.CenterY * mask.Height - bbox.Height * mask.Height / 2);
        var x2 = (int)(bbox.CenterX * mask.Width + bbox.Width * mask.Width / 2);
        var y2 = (int)(bbox.CenterY * mask.Height + bbox.Height * mask.Height / 2);

        for (int y = Math.Max(0, y1); y < Math.Min(mask.Height, y2); y++)
        {
            for (int x = Math.Max(0, x1); x < Math.Min(mask.Width, x2); x++)
            {
                SetPixelValue(mask, x, y, label);
            }
        }
    }

    /// <summary>
    /// 提取分水岭分割区域
    /// </summary>
    private List<SegmentedRegion> ExtractWatershedRegions(PixelData watershedResult)
    {
        var regions = new List<SegmentedRegion>();
        var labelMap = new Dictionary<int, List<SystemPoint>>();

        // 收集每个标签的像素点
        for (int y = 0; y < watershedResult.Height; y++)
        {
            for (int x = 0; x < watershedResult.Width; x++)
            {
                var label = (int)GetPixelValue(watershedResult, x, y);
                if (label > 0)
                {
                    if (!labelMap.ContainsKey(label))
                    {
                        labelMap[label] = new List<SystemPoint>();
                    }
                    labelMap[label].Add(new SystemPoint(x, y));
                }
            }
        }

        // 为每个标签创建区域
        foreach (var kvp in labelMap)
        {
            var region = new SegmentedRegion
            {
                Label = kvp.Key,
                Name = $"Watershed Region {kvp.Key}",
                Area = kvp.Value.Count,
                BoundingBox = CalculateBoundingBox(kvp.Value),
                Centroid = CalculateCentroid(kvp.Value)
            };
            regions.Add(region);
        }

        return regions;
    }

    /// <summary>
    /// 从轮廓生成mask
    /// </summary>
    private PixelData GenerateMaskFromContour(PixelData pixelData, List<SystemPoint> contour)
    {
        var mask = ClonePixelData(pixelData);

        // 初始化mask为0
        for (int y = 0; y < mask.Height; y++)
        {
            for (int x = 0; x < mask.Width; x++)
            {
                SetPixelValue(mask, x, y, 0);
            }
        }

        // 填充轮廓内部
        FillContour(mask, contour);

        return mask;
    }

    /// <summary>
    /// 填充轮廓内部
    /// </summary>
    private void FillContour(PixelData mask, List<SystemPoint> contour)
    {
        // 简化的扫描线填充算法
        var minY = contour.Min(p => p.Y);
        var maxY = contour.Max(p => p.Y);

        for (int y = minY; y <= maxY; y++)
        {
            var intersections = new List<int>();

            // 找到扫描线与轮廓的交点
            for (int i = 0; i < contour.Count; i++)
            {
                var p1 = contour[i];
                var p2 = contour[(i + 1) % contour.Count];

                if ((p1.Y <= y && p2.Y > y) || (p2.Y <= y && p1.Y > y))
                {
                    var x = p1.X + (y - p1.Y) * (p2.X - p1.X) / (p2.Y - p1.Y);
                    intersections.Add(x);
                }
            }

            intersections.Sort();

            // 填充交点之间的像素
            for (int i = 0; i < intersections.Count - 1; i += 2)
            {
                for (int x = intersections[i]; x <= intersections[i + 1]; x++)
                {
                    if (x >= 0 && x < mask.Width && y >= 0 && y < mask.Height)
                    {
                        SetPixelValue(mask, x, y, 1);
                    }
                }
            }
        }
    }

    /// <summary>
    /// 从mask提取区域
    /// </summary>
    private List<SegmentedRegion> ExtractRegionsFromMask(PixelData mask)
    {
        var regions = new List<SegmentedRegion>();
        var pixels = new List<SystemPoint>();

        // 收集所有非零像素
        for (int y = 0; y < mask.Height; y++)
        {
            for (int x = 0; x < mask.Width; x++)
            {
                if (GetPixelValue(mask, x, y) > 0)
                {
                    pixels.Add(new SystemPoint(x, y));
                }
            }
        }

        if (pixels.Count > 0)
        {
            var region = new SegmentedRegion
            {
                Label = 1,
                Name = "Active Contour Region",
                Area = pixels.Count,
                BoundingBox = CalculateBoundingBox(pixels),
                Centroid = CalculateCentroid(pixels)
            };
            regions.Add(region);
        }

        return regions;
    }

    #endregion

}
