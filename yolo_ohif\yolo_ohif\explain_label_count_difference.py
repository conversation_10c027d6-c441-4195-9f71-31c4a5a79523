#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解释标签数量差异的原因
"""

import os
from pathlib import Path

def explain_difference():
    """
    解释为什么convert_label_to_bbox.py生成2076个标签，
    但训练数据集只有410个标签的原因
    """
    print("=== 标签数量差异分析 ===")
    print()
    
    # 检查各个目录的文件数量
    bbox_labels_dir = Path("sliced_output/bbox_labels")
    train_labels_dir = Path("yolo_training_output/yolo_dataset/labels/train")
    val_labels_dir = Path("yolo_training_output/yolo_dataset/labels/val")
    test_labels_dir = Path("yolo_training_output/yolo_dataset/labels/test")
    
    # 统计文件数量
    bbox_count = len(list(bbox_labels_dir.glob("*.txt"))) if bbox_labels_dir.exists() else 0
    train_count = len(list(train_labels_dir.glob("*.txt"))) if train_labels_dir.exists() else 0
    val_count = len(list(val_labels_dir.glob("*.txt"))) if val_labels_dir.exists() else 0
    test_count = len(list(test_labels_dir.glob("*.txt"))) if test_labels_dir.exists() else 0
    
    total_training_labels = train_count + val_count + test_count
    
    print(f"📊 文件数量统计:")
    print(f"  convert_label_to_bbox.py 生成的标签: {bbox_count} 个")
    print(f"  训练数据集中的标签总数: {total_training_labels} 个")
    print(f"    - 训练集: {train_count} 个")
    print(f"    - 验证集: {val_count} 个")
    print(f"    - 测试集: {test_count} 个")
    print(f"  差异: {bbox_count - total_training_labels} 个标签未被使用")
    print()
    
    print("🔍 差异原因分析:")
    print()
    
    print("1. **数据处理流程不同**")
    print("   - convert_label_to_bbox.py: 直接处理已切片的图像文件")
    print("   - train_supraspinatus_yolo.py: 从原始nii.gz文件重新处理")
    print()
    
    print("2. **过滤条件差异**")
    print("   虽然两个脚本使用相同的参数，但处理流程不同:")
    print("   - convert脚本: 处理预切片的图像")
    print("   - 训练代码: 重新从nii.gz切片并应用mask_to_bbox过滤")
    print()
    
    print("3. **图像预处理差异**")
    print("   - convert脚本: 使用已有的切片图像")
    print("   - 训练代码: 重新加载nii.gz，重新归一化和调整尺寸")
    print("   - 这可能导致像素值和mask质量的细微差异")
    print()
    
    print("4. **边界框生成逻辑**")
    print("   训练代码中的mask_to_bbox函数可能更严格:")
    print("   - 使用连通组件分析")
    print("   - 自动合并多个边界框")
    print("   - 更严格的尺寸和面积检查")
    print()
    
    print("5. **数据源一致性**")
    print("   - convert脚本处理的是slice_nii_to_jpg.py生成的切片")
    print("   - 训练代码直接从dataset/目录的原始nii.gz文件处理")
    print("   - 两个数据源可能不完全一致")
    print()
    
    print("💡 **结论**:")
    print(f"   实际上有 {total_training_labels} 个有效的撕裂样本被用于训练，")
    print("   这是训练代码经过严格过滤后的结果。")
    print("   剩余的图像作为负样本（无标注文件）参与训练。")
    print()
    
    print("✅ **这是正常现象**:")
    print("   - 单类别目标检测中，负样本不需要标注文件")
    print("   - 训练代码使用了更严格的质量控制")
    print("   - 确保只有高质量的撕裂样本被用于训练")
    print()
    
    # 检查图像数量
    train_images_dir = Path("yolo_training_output/yolo_dataset/images/train")
    val_images_dir = Path("yolo_training_output/yolo_dataset/images/val")
    test_images_dir = Path("yolo_training_output/yolo_dataset/images/test")
    
    train_img_count = len(list(train_images_dir.glob("*.jpg"))) if train_images_dir.exists() else 0
    val_img_count = len(list(val_images_dir.glob("*.jpg"))) if val_images_dir.exists() else 0
    test_img_count = len(list(test_images_dir.glob("*.jpg"))) if test_images_dir.exists() else 0
    
    total_images = train_img_count + val_img_count + test_img_count
    
    print(f"📈 **训练数据集统计**:")
    print(f"   总图像数: {total_images} 个")
    print(f"   有标注的图像: {total_training_labels} 个 ({total_training_labels/total_images*100:.1f}%)")
    print(f"   负样本图像: {total_images - total_training_labels} 个 ({(total_images - total_training_labels)/total_images*100:.1f}%)")
    print()
    
    print("🎯 **建议**:")
    print("   当前的410个标签是经过严格质量控制的结果，")
    print("   如果需要更多训练样本，可以考虑:")
    print("   1. 调整mask_to_bbox函数的过滤参数")
    print("   2. 检查被过滤掉的样本是否确实质量较低")
    print("   3. 确认数据处理流程的一致性")

if __name__ == "__main__":
    explain_difference()