"""事件服务

提供事件发布订阅机制
"""

import logging
import threading
import weakref
from typing import Any, Callable, Dict, List, Optional, Set
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from ..core.interfaces import EventManagerInterface
from ..core.exceptions import EventError

logger = logging.getLogger(__name__)


class EventPriority(Enum):
    """事件优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class EventData:
    """事件数据"""
    event_type: str
    data: Any
    timestamp: datetime
    source: Optional[str] = None
    priority: EventPriority = EventPriority.NORMAL
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class EventSubscription:
    """事件订阅"""
    callback: Callable[[EventData], None]
    priority: EventPriority
    once: bool
    filter_func: Optional[Callable[[EventData], bool]]
    created_at: datetime
    call_count: int = 0
    last_called: Optional[datetime] = None


class EventService(EventManagerInterface):
    """事件服务
    
    提供线程安全的事件发布订阅机制
    支持优先级、过滤器、一次性订阅等功能
    """
    
    def __init__(self, max_history: int = 1000):
        """初始化事件服务
        
        Args:
            max_history: 最大事件历史记录数
        """
        self._subscribers: Dict[str, List[EventSubscription]] = {}
        self._event_history: List[EventData] = []
        self._max_history = max_history
        self._lock = threading.RLock()
        self._stats = {
            'events_published': 0,
            'events_delivered': 0,
            'subscription_count': 0,
            'errors': 0
        }
        
        logger.info(f"事件服务初始化完成 (最大历史记录: {max_history})")
    
    def subscribe(self, 
                  event_type: str, 
                  callback: Callable[[EventData], None],
                  priority: EventPriority = EventPriority.NORMAL,
                  once: bool = False,
                  filter_func: Optional[Callable[[EventData], bool]] = None) -> str:
        """订阅事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
            priority: 优先级
            once: 是否只触发一次
            filter_func: 过滤函数
            
        Returns:
            订阅ID
        """
        with self._lock:
            subscription = EventSubscription(
                callback=callback,
                priority=priority,
                once=once,
                filter_func=filter_func,
                created_at=datetime.now()
            )
            
            if event_type not in self._subscribers:
                self._subscribers[event_type] = []
            
            self._subscribers[event_type].append(subscription)
            
            # 按优先级排序
            self._subscribers[event_type].sort(
                key=lambda s: s.priority.value, 
                reverse=True
            )
            
            self._stats['subscription_count'] += 1
            subscription_id = f"{event_type}_{id(subscription)}"
            
            logger.debug(f"事件订阅: {event_type} (优先级: {priority.name}, 一次性: {once})")
            return subscription_id
    
    def unsubscribe(self, event_type: str, callback: Callable[[EventData], None]) -> bool:
        """取消订阅
        
        Args:
            event_type: 事件类型
            callback: 回调函数
            
        Returns:
            是否成功取消订阅
        """
        with self._lock:
            if event_type not in self._subscribers:
                return False
            
            original_count = len(self._subscribers[event_type])
            self._subscribers[event_type] = [
                sub for sub in self._subscribers[event_type]
                if sub.callback != callback
            ]
            
            removed_count = original_count - len(self._subscribers[event_type])
            if removed_count > 0:
                self._stats['subscription_count'] -= removed_count
                logger.debug(f"取消事件订阅: {event_type} (移除 {removed_count} 个订阅)")
                return True
            
            return False
    
    def unsubscribe_all(self, event_type: str) -> int:
        """取消所有订阅
        
        Args:
            event_type: 事件类型
            
        Returns:
            取消的订阅数量
        """
        with self._lock:
            if event_type not in self._subscribers:
                return 0
            
            count = len(self._subscribers[event_type])
            self._subscribers[event_type] = []
            self._stats['subscription_count'] -= count
            
            logger.debug(f"取消所有事件订阅: {event_type} (移除 {count} 个订阅)")
            return count
    
    def publish(self, 
                event_type: str, 
                data: Any = None,
                source: Optional[str] = None,
                priority: EventPriority = EventPriority.NORMAL,
                metadata: Optional[Dict[str, Any]] = None) -> int:
        """发布事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            source: 事件源
            priority: 事件优先级
            metadata: 元数据
            
        Returns:
            成功投递的订阅者数量
        """
        event_data = EventData(
            event_type=event_type,
            data=data,
            timestamp=datetime.now(),
            source=source,
            priority=priority,
            metadata=metadata or {}
        )
        
        # 添加到历史记录
        with self._lock:
            self._event_history.append(event_data)
            if len(self._event_history) > self._max_history:
                self._event_history.pop(0)
            
            self._stats['events_published'] += 1
        
        # 投递事件
        delivered_count = self._deliver_event(event_data)
        
        logger.debug(f"事件发布: {event_type} (投递给 {delivered_count} 个订阅者)")
        return delivered_count
    
    def publish_async(self, 
                      event_type: str, 
                      data: Any = None,
                      source: Optional[str] = None,
                      priority: EventPriority = EventPriority.NORMAL,
                      metadata: Optional[Dict[str, Any]] = None) -> None:
        """异步发布事件"""
        import threading
        
        def _async_publish():
            try:
                self.publish(event_type, data, source, priority, metadata)
            except Exception as e:
                logger.error(f"异步事件发布失败: {e}")
                self._stats['errors'] += 1
        
        thread = threading.Thread(target=_async_publish, daemon=True)
        thread.start()
    
    def get_subscribers(self, event_type: str) -> List[Dict[str, Any]]:
        """获取订阅者信息"""
        with self._lock:
            if event_type not in self._subscribers:
                return []
            
            return [
                {
                    'priority': sub.priority.name,
                    'once': sub.once,
                    'has_filter': sub.filter_func is not None,
                    'created_at': sub.created_at.isoformat(),
                    'call_count': sub.call_count,
                    'last_called': sub.last_called.isoformat() if sub.last_called else None
                }
                for sub in self._subscribers[event_type]
            ]
    
    def get_event_types(self) -> Set[str]:
        """获取所有事件类型"""
        with self._lock:
            return set(self._subscribers.keys())
    
    def get_event_history(self, 
                          event_type: Optional[str] = None,
                          limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取事件历史
        
        Args:
            event_type: 过滤事件类型
            limit: 限制数量
            
        Returns:
            事件历史列表
        """
        with self._lock:
            history = self._event_history
            
            if event_type:
                history = [e for e in history if e.event_type == event_type]
            
            if limit:
                history = history[-limit:]
            
            return [
                {
                    'event_type': event.event_type,
                    'timestamp': event.timestamp.isoformat(),
                    'source': event.source,
                    'priority': event.priority.name,
                    'data_type': type(event.data).__name__,
                    'metadata': event.metadata
                }
                for event in history
            ]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            return {
                'total_event_types': len(self._subscribers),
                'total_subscribers': sum(len(subs) for subs in self._subscribers.values()),
                'history_size': len(self._event_history),
                'max_history': self._max_history,
                **self._stats
            }
    
    def clear_history(self) -> None:
        """清空事件历史"""
        with self._lock:
            cleared_count = len(self._event_history)
            self._event_history.clear()
            logger.info(f"事件历史已清空，删除了 {cleared_count} 条记录")
    
    def _deliver_event(self, event_data: EventData) -> int:
        """投递事件到订阅者"""
        with self._lock:
            if event_data.event_type not in self._subscribers:
                return 0
            
            subscribers = self._subscribers[event_data.event_type][:]
            delivered_count = 0
            to_remove = []
            
            for i, subscription in enumerate(subscribers):
                try:
                    # 应用过滤器
                    if subscription.filter_func and not subscription.filter_func(event_data):
                        continue
                    
                    # 调用回调
                    subscription.callback(event_data)
                    subscription.call_count += 1
                    subscription.last_called = datetime.now()
                    delivered_count += 1
                    self._stats['events_delivered'] += 1
                    
                    # 检查是否为一次性订阅
                    if subscription.once:
                        to_remove.append(i)
                        
                except Exception as e:
                    logger.error(f"事件回调执行失败: {e}")
                    self._stats['errors'] += 1
            
            # 移除一次性订阅
            for i in reversed(to_remove):
                self._subscribers[event_data.event_type].pop(i)
                self._stats['subscription_count'] -= 1
            
            return delivered_count
    
    def cleanup(self) -> None:
        """清理事件服务"""
        with self._lock:
            self._subscribers.clear()
            self._event_history.clear()
            self._stats = {
                'events_published': 0,
                'events_delivered': 0,
                'subscription_count': 0,
                'errors': 0
            }
            logger.info("事件服务已清理")


class WeakEventService(EventService):
    """弱引用事件服务
    
    使用弱引用避免内存泄漏
    """
    
    def subscribe(self, 
                  event_type: str, 
                  callback: Callable[[EventData], None],
                  priority: EventPriority = EventPriority.NORMAL,
                  once: bool = False,
                  filter_func: Optional[Callable[[EventData], bool]] = None) -> str:
        """订阅事件（使用弱引用）"""
        # 创建弱引用回调
        weak_callback = weakref.ref(callback)
        
        def safe_callback(event_data: EventData):
            cb = weak_callback()
            if cb is not None:
                cb(event_data)
            else:
                # 回调对象已被垃圾回收，自动取消订阅
                self.unsubscribe(event_type, safe_callback)
        
        return super().subscribe(event_type, safe_callback, priority, once, filter_func)


class FilteredEventService(EventService):
    """过滤事件服务
    
    支持全局事件过滤
    """
    
    def __init__(self, 
                 global_filter: Optional[Callable[[EventData], bool]] = None,
                 **kwargs):
        """初始化过滤事件服务
        
        Args:
            global_filter: 全局过滤函数
            **kwargs: 其他参数传递给父类
        """
        super().__init__(**kwargs)
        self._global_filter = global_filter
    
    def publish(self, 
                event_type: str, 
                data: Any = None,
                source: Optional[str] = None,
                priority: EventPriority = EventPriority.NORMAL,
                metadata: Optional[Dict[str, Any]] = None) -> int:
        """发布事件（应用全局过滤）"""
        event_data = EventData(
            event_type=event_type,
            data=data,
            timestamp=datetime.now(),
            source=source,
            priority=priority,
            metadata=metadata or {}
        )
        
        # 应用全局过滤器
        if self._global_filter and not self._global_filter(event_data):
            logger.debug(f"事件被全局过滤器拒绝: {event_type}")
            return 0
        
        return super().publish(event_type, data, source, priority, metadata)