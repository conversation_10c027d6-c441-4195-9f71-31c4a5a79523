version: '3.8'

services:
  # Flask应用服务
  flask-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: yolo-ohif-app
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      - ./uploads:/app/uploads
      - ./models:/app/models
      - ./results:/app/results
      - ./logs:/app/logs
      - ./instance:/app/instance
    environment:
      - FLASK_APP=app.py
      - FLASK_ENV=production
      - SECRET_KEY=your_secret_key_here
      - DATABASE_URL=sqlite:///instance/yolo_ohif.db
      - ORTHANC_URL=http://orthanc:8042
      - OHIF_URL=http://ohif:3000
    depends_on:
      - orthanc
      - ohif

  # Orthanc DICOM服务器
  orthanc:
    image: jodogne/orthanc-plugins:1.9.7
    container_name: orthanc-server
    restart: unless-stopped
    ports:
      - "8042:8042"
      - "4242:4242"
    volumes:
      - ./orthanc.json:/etc/orthanc/orthanc.json:ro
      - orthanc-storage:/var/lib/orthanc/db
    environment:
      - ORTHANC__NAME=YOLO-OHIF Orthanc Server
      - ORTHANC__DICOM_SERVER_ENABLED=true
      - ORTHANC__DICOM_AET=ORTHANC
      - ORTHANC__DICOM_PORT=4242
      - ORTHANC__REMOTE_ACCESS_ALLOWED=true
      - ORTHANC__AUTHENTICATION_ENABLED=false

  # OHIF查看器
  ohif:
    image: ohif/viewer:latest
    container_name: ohif-viewer
    restart: unless-stopped
    ports:
      - "3000:80"
    environment:
      - APP_CONFIG=config/docker_nginx-orthanc.js
    depends_on:
      - orthanc

volumes:
  orthanc-storage:
    driver: local