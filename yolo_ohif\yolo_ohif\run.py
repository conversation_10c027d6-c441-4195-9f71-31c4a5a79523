#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import subprocess
import time
import webbrowser
import signal
import logging
from config import Config, BASE_DIR

# 设置日志
logging.basicConfig(
    level=getattr(logging, Config.LOG.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(Config.LOG.LOG_DIR, 'run.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 进程列表
processes = []

def signal_handler(sig, frame):
    """处理终止信号，确保所有子进程都被正确关闭"""
    logger.info("接收到终止信号，正在关闭所有服务...")
    for process in processes:
        if process and process.poll() is None:  # 如果进程存在且仍在运行
            process.terminate()
            logger.info(f"已终止进程: {process.args}")
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def start_orthanc(orthanc_path=None, config_path=None):
    """启动Orthanc DICOM服务器"""
    if orthanc_path and os.path.exists(orthanc_path):
        cmd = [orthanc_path]
        if config_path and os.path.exists(config_path):
            cmd.extend(['--config', config_path])
        
        logger.info(f"正在启动Orthanc服务器: {' '.join(cmd)}")
        orthanc_process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        processes.append(orthanc_process)
        return orthanc_process
    else:
        logger.warning("未提供Orthanc路径或路径不存在，假设Orthanc已在外部启动")
        return None

def start_flask_app():
    """启动Flask应用服务器"""
    flask_env = os.environ.copy()
    flask_env['FLASK_APP'] = 'app.py'
    
    if Config.DEBUG:
        flask_env['FLASK_ENV'] = 'development'
        cmd = [sys.executable, '-m', 'flask', 'run', '--host=0.0.0.0', '--port=5000']
    else:
        workers = os.cpu_count() or 1
        cmd = [
            sys.executable, '-m', 'gunicorn', 'app:app',
            '--bind', '0.0.0.0:5000',
            '--workers', str(workers),
            '--timeout', '120'
        ]
    
    logger.info(f"正在启动Flask应用: {' '.join(cmd)}")
    flask_process = subprocess.Popen(cmd, env=flask_env, cwd=BASE_DIR)
    processes.append(flask_process)
    return flask_process

def check_service_health(url, max_retries=30, retry_interval=1):
    """检查服务是否健康并可访问"""
    import requests
    from requests.exceptions import RequestException
    
    for i in range(max_retries):
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                logger.info(f"服务 {url} 已启动并可访问")
                return True
        except RequestException:
            pass
        
        if i < max_retries - 1:  # 不是最后一次尝试
            time.sleep(retry_interval)
    
    logger.error(f"服务 {url} 无法访问，请检查是否正确启动")
    return False

def main():
    parser = argparse.ArgumentParser(description='启动医学图像疾病检测系统')
    parser.add_argument('--orthanc-path', help='Orthanc可执行文件路径')
    parser.add_argument('--orthanc-config', help='Orthanc配置文件路径')
    parser.add_argument('--no-browser', action='store_true', help='不自动打开浏览器')
    parser.add_argument('--flask-only', action='store_true', help='仅启动Flask应用')
    
    args = parser.parse_args()
    
    try:
        # 启动Orthanc（如果提供了路径）
        if not args.flask_only and args.orthanc_path:
            orthanc_process = start_orthanc(args.orthanc_path, args.orthanc_config)
            if orthanc_process:
                # 等待Orthanc启动
                if not check_service_health(Config.ORTHANC.ORTHANC_URL):
                    logger.error("Orthanc服务器启动失败，请检查配置")
                    return
        
        # 启动Flask应用
        flask_process = start_flask_app()
        
        # 等待Flask应用启动
        flask_url = "http://localhost:5000/health"
        if check_service_health(flask_url):
            app_url = "http://localhost:5000"
            logger.info(f"医学图像疾病检测系统已启动，访问地址: {app_url}")
            
            # 自动打开浏览器
            if not args.no_browser:
                webbrowser.open(app_url)
        
        # 保持脚本运行
        while all(p.poll() is None for p in processes if p):
            time.sleep(1)
        
        # 如果有进程退出，终止所有进程
        for process in processes:
            if process and process.poll() is None:
                process.terminate()
    
    except KeyboardInterrupt:
        logger.info("接收到用户中断，正在关闭服务...")
        for process in processes:
            if process and process.poll() is None:
                process.terminate()
    except Exception as e:
        logger.error(f"启动服务时发生错误: {str(e)}")
        for process in processes:
            if process and process.poll() is None:
                process.terminate()

if __name__ == "__main__":
    main()