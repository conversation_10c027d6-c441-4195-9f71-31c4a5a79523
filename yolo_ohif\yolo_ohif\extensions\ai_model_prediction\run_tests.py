#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立测试运行器

这个脚本可以独立运行，不依赖于包结构
使用方法：
    python run_tests.py
    或者
    python e:\Trae\yolo_ohif\extensions\ai_model_prediction\run_tests.py
"""

import sys
import os
import unittest
import logging
from pathlib import Path

# 设置当前目录
current_dir = Path(__file__).parent.absolute()
project_root = current_dir.parent.parent
os.chdir(current_dir)

# 添加路径
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(current_dir))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('test_results.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖是否可用"""
    missing_deps = []
    
    try:
        import asyncio
    except ImportError:
        missing_deps.append('asyncio')
    
    try:
        from unittest.mock import Mock, patch, MagicMock
    except ImportError:
        missing_deps.append('unittest.mock')
    
    if missing_deps:
        print(f"缺少依赖: {', '.join(missing_deps)}")
        return False
    
    return True

def check_modules():
    """检查模块是否可以导入"""
    modules_to_check = [
        'extensions.ai_model_prediction.core.container',
        'extensions.ai_model_prediction.core.interfaces',
        'extensions.ai_model_prediction.core.types',
        'extensions.ai_model_prediction.core.exceptions',
        'extensions.ai_model_prediction.services.null_services',
        'extensions.ai_model_prediction.factory',
        'extensions.ai_model_prediction.refactored_extension'
    ]
    
    missing_modules = []
    
    for module in modules_to_check:
        try:
            __import__(module)
            print(f"✓ {module} 导入成功")
        except ImportError as e:
            missing_modules.append(module)
            print(f"✗ {module} 导入失败: {e}")
    
    if missing_modules:
        print(f"\n缺少模块: {', '.join(missing_modules)}")
        print("请确保所有必要的文件都已创建")
        return False
    
    return True

def run_basic_tests():
    """运行基本功能测试"""
    print("\n=== 运行基本功能测试 ===")
    
    try:
        # 测试服务容器
        from extensions.ai_model_prediction.core.container import ServiceContainer
        container = ServiceContainer()
        print("✓ 服务容器创建成功")
        
        # 测试工厂
        from extensions.ai_model_prediction.factory import ExtensionFactory
        extension = ExtensionFactory.create_minimal()
        print("✓ 扩展工厂创建成功")
        
        # 测试扩展信息
        info = extension.get_extension_info()
        print(f"✓ 扩展信息获取成功: {info.get('name', 'Unknown')}")
        
        # 清理
        extension.cleanup()
        print("✓ 扩展清理成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        logger.error(f"基本功能测试失败: {e}")
        return False

def run_advanced_tests():
    """运行高级功能测试"""
    print("\n=== 运行高级功能测试 ===")
    
    try:
        from extensions.ai_model_prediction.factory import create_builder
        
        # 测试构建器
        extension = (create_builder()
                    .with_name('测试扩展')
                    .with_version('1.0.0')
                    .enable_cache(ttl=300)
                    .enable_debug()
                    .build())
        
        print("✓ 构建器模式测试成功")
        
        # 测试事件系统
        from extensions.ai_model_prediction.core.interfaces import EventManagerInterface
        event_service = extension.container.get_service(EventManagerInterface)
        
        def test_handler(event_data):
            print(f"事件处理: {event_data.event_type}")
        
        sub_id = event_service.subscribe('test_event', test_handler)
        event_service.publish('test_event', {'test': 'data'})
        event_service.unsubscribe(sub_id)
        
        print("✓ 事件系统测试成功")
        
        # 测试缓存系统
        from extensions.ai_model_prediction.core.interfaces import CacheManagerInterface
        cache_service = extension.container.get_service(CacheManagerInterface)
        
        cache_service.set('test_key', 'test_value')
        value = cache_service.get('test_key')
        assert value == 'test_value'
        
        print("✓ 缓存系统测试成功")
        
        # 清理
        extension.cleanup()
        
        return True
        
    except Exception as e:
        print(f"✗ 高级功能测试失败: {e}")
        logger.error(f"高级功能测试失败: {e}")
        return False

def run_async_tests():
    """运行异步功能测试"""
    print("\n=== 运行异步功能测试 ===")
    
    async def async_test():
        try:
            from extensions.ai_model_prediction.factory import create_extension
            
            extension = create_extension()
            
            # 模拟图像数据
            mock_image_data = {
                'width': 512,
                'height': 512,
                'data': 'mock_data',
                'format': 'DICOM'
            }
            
            # 异步预测（预期会失败，因为没有真实模型）
            result = await extension.run_prediction_async(mock_image_data)
            
            # 这里预期result为None或抛出异常
            print("✓ 异步预测接口调用成功（返回None是预期行为）")
            
            extension.cleanup()
            return True
            
        except Exception as e:
            print(f"✓ 异步预测测试完成（异常是预期的）: {e}")
            return True
    
    try:
        import asyncio
        result = asyncio.run(async_test())
        return result
    except Exception as e:
        print(f"✗ 异步功能测试失败: {e}")
        return False

def run_unittest_suite():
    """运行完整的unittest套件"""
    print("\n=== 运行完整测试套件 ===")
    
    try:
        # 导入测试模块
        from test_refactored_extension import (
            TestServiceContainer,
            TestNullServices,
            TestExtensionFactory,
            TestExtensionBuilder,
            TestRefactoredExtension,
            TestAsyncFunctionality,
            TestIntegration,
            TestErrorHandling
        )
        
        # 创建测试套件
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()
        
        # 添加测试类
        test_classes = [
            TestServiceContainer,
            TestNullServices,
            TestExtensionFactory,
            TestExtensionBuilder,
            TestRefactoredExtension,
            TestAsyncFunctionality,
            TestIntegration,
            TestErrorHandling
        ]
        
        for test_class in test_classes:
            tests = loader.loadTestsFromTestCase(test_class)
            suite.addTests(tests)
        
        # 运行测试
        runner = unittest.TextTestRunner(
            verbosity=2,
            stream=sys.stdout,
            buffer=True
        )
        
        result = runner.run(suite)
        
        # 输出结果
        print(f"\n测试结果:")
        print(f"运行测试: {result.testsRun}")
        print(f"失败: {len(result.failures)}")
        print(f"错误: {len(result.errors)}")
        print(f"跳过: {len(result.skipped)}")
        
        if result.failures:
            print("\n失败的测试:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")
        
        if result.errors:
            print("\n错误的测试:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")
        
        return result.wasSuccessful()
        
    except Exception as e:
        print(f"✗ 完整测试套件运行失败: {e}")
        logger.error(f"完整测试套件运行失败: {e}")
        return False

def main():
    """主函数"""
    print("重构后AI预测扩展测试运行器")
    print("=" * 50)
    
    # 检查依赖
    print("检查Python依赖...")
    if not check_dependencies():
        print("依赖检查失败，退出")
        return 1
    
    # 检查模块
    print("\n检查模块导入...")
    if not check_modules():
        print("模块检查失败，退出")
        return 1
    
    # 运行测试
    all_passed = True
    
    # 基本功能测试
    if not run_basic_tests():
        all_passed = False
    
    # 高级功能测试
    if not run_advanced_tests():
        all_passed = False
    
    # 异步功能测试
    if not run_async_tests():
        all_passed = False
    
    # 完整测试套件
    if not run_unittest_suite():
        all_passed = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！")
        print("重构后的AI预测扩展工作正常")
        return 0
    else:
        print("❌ 部分测试失败")
        print("请检查错误信息并修复问题")
        return 1

if __name__ == '__main__':
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试运行器发生未预期错误: {e}")
        logger.error(f"测试运行器发生未预期错误: {e}")
        sys.exit(1)