"""AI预测扩展工厂

提供扩展创建和配置的工厂方法
"""

import logging
from typing import Any, Dict, List, Optional, Type
from pathlib import Path

from .core.container import ServiceContainer, get_container, set_container
from .core.interfaces import (
    ModelManagerInterface,
    PredictionServiceInterface,
    UIComponentsInterface,
    CacheManagerInterface,
    EventManagerInterface,
    ConfigValidatorInterface
)
from .core.exceptions import ExtensionError, ConfigurationError
from .core.types import ExtensionConfig, ModelConfig

from .services import (
    ModelManagerService,
    PredictionService,
    UIService,
    CacheService,
    EventService,
    ConfigService,
    ValidationService
)

from .refactored_extension import RefactoredAIPredictionExtension

logger = logging.getLogger(__name__)


class ExtensionFactory:
    """AI预测扩展工厂
    
    提供多种创建扩展实例的方法
    """
    
    @staticmethod
    def create_default() -> RefactoredAIPredictionExtension:
        """创建默认配置的扩展实例
        
        Returns:
            扩展实例
        """
        try:
            # 创建新的容器
            container = ServiceContainer()
            
            # 注册默认服务
            ExtensionFactory._register_default_services(container)
            
            # 创建扩展实例
            extension = RefactoredAIPredictionExtension(container=container)
            
            # 初始化扩展
            extension.initialize()
            
            logger.info("默认AI预测扩展创建成功")
            return extension
            
        except Exception as e:
            logger.error(f"创建默认扩展失败: {e}")
            raise ExtensionError(f"创建默认扩展失败: {str(e)}")
    
    @staticmethod
    def create_with_config(config: Dict[str, Any]) -> RefactoredAIPredictionExtension:
        """使用指定配置创建扩展实例
        
        Args:
            config: 扩展配置
            
        Returns:
            扩展实例
        """
        try:
            # 创建新的容器
            container = ServiceContainer()
            
            # 注册默认服务
            ExtensionFactory._register_default_services(container)
            
            # 创建扩展实例
            extension = RefactoredAIPredictionExtension(
                config=config,
                container=container
            )
            
            # 初始化扩展
            extension.initialize()
            
            logger.info("配置AI预测扩展创建成功")
            return extension
            
        except Exception as e:
            logger.error(f"创建配置扩展失败: {e}")
            raise ExtensionError(f"创建配置扩展失败: {str(e)}")
    
    @staticmethod
    def create_from_file(config_file: str) -> RefactoredAIPredictionExtension:
        """从配置文件创建扩展实例
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            扩展实例
        """
        try:
            import json
            import yaml
            
            config_path = Path(config_file)
            
            if not config_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {config_file}")
            
            # 根据文件扩展名选择解析器
            if config_path.suffix.lower() == '.json':
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            elif config_path.suffix.lower() in ['.yml', '.yaml']:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
            else:
                raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
            
            return ExtensionFactory.create_with_config(config)
            
        except Exception as e:
            logger.error(f"从文件创建扩展失败: {e}")
            raise ExtensionError(f"从文件创建扩展失败: {str(e)}")
    
    @staticmethod
    def create_with_custom_services(
        config: Optional[Dict[str, Any]] = None,
        custom_services: Optional[Dict[Type, Type]] = None
    ) -> RefactoredAIPredictionExtension:
        """使用自定义服务创建扩展实例
        
        Args:
            config: 扩展配置
            custom_services: 自定义服务映射 {接口: 实现类}
            
        Returns:
            扩展实例
        """
        try:
            # 创建新的容器
            container = ServiceContainer()
            
            # 注册默认服务
            ExtensionFactory._register_default_services(container)
            
            # 注册自定义服务
            if custom_services:
                for interface, implementation in custom_services.items():
                    container.register_singleton(interface, implementation)
                    logger.debug(f"注册自定义服务: {interface.__name__} -> {implementation.__name__}")
            
            # 创建扩展实例
            extension = RefactoredAIPredictionExtension(
                config=config,
                container=container
            )
            
            # 初始化扩展
            extension.initialize()
            
            logger.info("自定义服务AI预测扩展创建成功")
            return extension
            
        except Exception as e:
            logger.error(f"创建自定义服务扩展失败: {e}")
            raise ExtensionError(f"创建自定义服务扩展失败: {str(e)}")
    
    @staticmethod
    def create_test_instance(
        mock_services: Optional[Dict[Type, Any]] = None
    ) -> RefactoredAIPredictionExtension:
        """创建测试用扩展实例
        
        Args:
            mock_services: Mock服务映射 {接口: Mock对象}
            
        Returns:
            扩展实例
        """
        try:
            # 创建新的容器
            container = ServiceContainer()
            
            # 注册Mock服务或默认服务
            if mock_services:
                for interface, mock_service in mock_services.items():
                    container.register_instance(interface, mock_service)
                    logger.debug(f"注册Mock服务: {interface.__name__}")
                
                # 注册未Mock的默认服务
                default_services = ExtensionFactory._get_default_service_mappings()
                for interface, implementation in default_services.items():
                    if interface not in mock_services:
                        container.register_singleton(interface, implementation)
            else:
                ExtensionFactory._register_default_services(container)
            
            # 创建测试配置
            test_config = ExtensionFactory._create_test_config()
            
            # 创建扩展实例
            extension = RefactoredAIPredictionExtension(
                config=test_config,
                container=container
            )
            
            logger.info("测试AI预测扩展创建成功")
            return extension
            
        except Exception as e:
            logger.error(f"创建测试扩展失败: {e}")
            raise ExtensionError(f"创建测试扩展失败: {str(e)}")
    
    @staticmethod
    def create_minimal() -> RefactoredAIPredictionExtension:
        """创建最小化配置的扩展实例
        
        Returns:
            扩展实例
        """
        try:
            # 创建新的容器
            container = ServiceContainer()
            
            # 只注册核心服务
            container.register_singleton(ConfigValidatorInterface, ConfigService)
            container.register_singleton(EventManagerInterface, EventService)
            container.register_singleton(ModelManagerInterface, ModelManagerService)
            container.register_singleton(PredictionServiceInterface, PredictionService)
            
            # 注册空实现的可选服务
            from .services.null_services import (
                NullCacheService,
                NullUIService
            )
            container.register_singleton(CacheManagerInterface, NullCacheService)
            container.register_singleton(UIComponentsInterface, NullUIService)
            
            # 创建最小配置
            minimal_config = {
                'name': 'AI预测扩展（最小版）',
                'version': '2.0.0',
                'models': [],
                'options': {
                    'auto_detection': False,
                    'cache_enabled': False,
                    'ui_enabled': False
                }
            }
            
            # 创建扩展实例
            extension = RefactoredAIPredictionExtension(
                config=minimal_config,
                container=container
            )
            
            logger.info("最小化AI预测扩展创建成功")
            return extension
            
        except Exception as e:
            logger.error(f"创建最小化扩展失败: {e}")
            raise ExtensionError(f"创建最小化扩展失败: {str(e)}")
    
    @staticmethod
    def _register_default_services(container: ServiceContainer) -> None:
        """注册默认服务
        
        Args:
            container: 服务容器
        """
        service_mappings = ExtensionFactory._get_default_service_mappings()
        
        for interface, implementation in service_mappings.items():
            container.register_singleton(interface, implementation)
            logger.debug(f"注册服务: {interface.__name__} -> {implementation.__name__}")
        
        # 注册验证服务
        container.register_singleton(ValidationService)
    
    @staticmethod
    def _get_default_service_mappings() -> Dict[Type, Type]:
        """获取默认服务映射
        
        Returns:
            服务映射字典
        """
        return {
            ConfigValidatorInterface: ConfigService,
            CacheManagerInterface: CacheService,
            EventManagerInterface: EventService,
            UIComponentsInterface: UIService,
            ModelManagerInterface: ModelManagerService,
            PredictionServiceInterface: PredictionService
        }
    
    @staticmethod
    def _create_test_config() -> Dict[str, Any]:
        """创建测试配置
        
        Returns:
            测试配置
        """
        return {
            'name': 'AI预测扩展（测试版）',
            'version': '2.0.0-test',
            'models': [
                {
                    'id': 'test_model_1',
                    'name': '测试模型1',
                    'type': 'detection',
                    'modality': 'CT',
                    'endpoint': 'http://localhost:8000/predict',
                    'description': '用于测试的模型',
                    'classes': ['test_class_1', 'test_class_2'],
                    'confidence_threshold': 0.5,
                    'enabled': True
                },
                {
                    'id': 'test_model_2',
                    'name': '测试模型2',
                    'type': 'segmentation',
                    'modality': 'MRI',
                    'endpoint': 'http://localhost:8001/predict',
                    'description': '另一个测试模型',
                    'classes': ['test_class_3'],
                    'confidence_threshold': 0.7,
                    'enabled': False
                }
            ],
            'options': {
                'auto_detection': False,
                'cache_enabled': True,
                'cache_ttl': 300,
                'ui_enabled': True,
                'debug_mode': True,
                'max_concurrent_predictions': 2
            }
        }


class ExtensionBuilder:
    """AI预测扩展构建器
    
    提供流式API来构建扩展实例
    """
    
    def __init__(self):
        self._config: Dict[str, Any] = {
            'name': 'AI预测扩展',
            'version': '2.0.0',
            'models': [],
            'options': {}
        }
        self._custom_services: Dict[Type, Type] = {}
        self._container: Optional[ServiceContainer] = None
    
    def with_name(self, name: str) -> 'ExtensionBuilder':
        """设置扩展名称
        
        Args:
            name: 扩展名称
            
        Returns:
            构建器实例
        """
        self._config['name'] = name
        return self
    
    def with_version(self, version: str) -> 'ExtensionBuilder':
        """设置扩展版本
        
        Args:
            version: 扩展版本
            
        Returns:
            构建器实例
        """
        self._config['version'] = version
        return self
    
    def add_model(self, model_config: Dict[str, Any]) -> 'ExtensionBuilder':
        """添加模型配置
        
        Args:
            model_config: 模型配置
            
        Returns:
            构建器实例
        """
        self._config['models'].append(model_config)
        return self
    
    def with_option(self, key: str, value: Any) -> 'ExtensionBuilder':
        """设置选项
        
        Args:
            key: 选项键
            value: 选项值
            
        Returns:
            构建器实例
        """
        self._config['options'][key] = value
        return self
    
    def with_options(self, options: Dict[str, Any]) -> 'ExtensionBuilder':
        """设置多个选项
        
        Args:
            options: 选项字典
            
        Returns:
            构建器实例
        """
        self._config['options'].update(options)
        return self
    
    def with_custom_service(self, interface: Type, implementation: Type) -> 'ExtensionBuilder':
        """添加自定义服务
        
        Args:
            interface: 服务接口
            implementation: 服务实现
            
        Returns:
            构建器实例
        """
        self._custom_services[interface] = implementation
        return self
    
    def with_container(self, container: ServiceContainer) -> 'ExtensionBuilder':
        """使用指定的服务容器
        
        Args:
            container: 服务容器
            
        Returns:
            构建器实例
        """
        self._container = container
        return self
    
    def enable_auto_detection(self) -> 'ExtensionBuilder':
        """启用自动检测
        
        Returns:
            构建器实例
        """
        self._config['options']['auto_detection'] = True
        return self
    
    def enable_cache(self, ttl: int = 300) -> 'ExtensionBuilder':
        """启用缓存
        
        Args:
            ttl: 缓存TTL（秒）
            
        Returns:
            构建器实例
        """
        self._config['options']['cache_enabled'] = True
        self._config['options']['cache_ttl'] = ttl
        return self
    
    def enable_debug(self) -> 'ExtensionBuilder':
        """启用调试模式
        
        Returns:
            构建器实例
        """
        self._config['options']['debug_mode'] = True
        return self
    
    def build(self) -> RefactoredAIPredictionExtension:
        """构建扩展实例
        
        Returns:
            扩展实例
        """
        try:
            if self._container:
                # 使用指定容器
                extension = RefactoredAIPredictionExtension(
                    config=self._config,
                    container=self._container
                )
            elif self._custom_services:
                # 使用自定义服务
                extension = ExtensionFactory.create_with_custom_services(
                    config=self._config,
                    custom_services=self._custom_services
                )
            else:
                # 使用默认配置
                extension = ExtensionFactory.create_with_config(self._config)
            
            logger.info(f"扩展构建成功: {self._config['name']}")
            return extension
            
        except Exception as e:
            logger.error(f"构建扩展失败: {e}")
            raise ExtensionError(f"构建扩展失败: {str(e)}")


# 便捷函数
def create_extension(
    config: Optional[Dict[str, Any]] = None,
    config_file: Optional[str] = None
) -> RefactoredAIPredictionExtension:
    """创建AI预测扩展实例
    
    Args:
        config: 扩展配置
        config_file: 配置文件路径
        
    Returns:
        扩展实例
    """
    if config_file:
        return ExtensionFactory.create_from_file(config_file)
    elif config:
        return ExtensionFactory.create_with_config(config)
    else:
        return ExtensionFactory.create_default()


def create_builder() -> ExtensionBuilder:
    """创建扩展构建器
    
    Returns:
        构建器实例
    """
    return ExtensionBuilder()