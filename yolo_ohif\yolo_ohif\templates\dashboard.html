{% extends "base.html" %}

{% block title %}仪表板 - YOLO-OHIF医学图像疾病检测系统{% endblock %}

{% block extra_css %}
<style>
    .study-card {
        transition: transform 0.3s;
    }
    .study-card:hover {
        transform: translateY(-5px);
    }
    .detection-badge {
        position: absolute;
        top: 10px;
        right: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-0">欢迎，{{ session.get('user', {}).get('username', '用户') }}</h2>
                        <p class="text-muted">这是您的个人仪表板，您可以在这里管理您的医学影像研究和检测结果。</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ url_for('web.upload') }}" class="btn btn-primary me-2">
                            <i class="fas fa-upload me-1"></i>上传新研究
                        </a>
                        <a href="{{ url_for('web.dicom_viewer') }}" class="btn btn-info me-2">
                            <i class="fas fa-eye me-1"></i>DICOM查看器
                        </a>
                        <a href="{{ url_for('web.system_status') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-heartbeat me-1"></i>系统状态
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3 mb-4">
        <div class="card bg-primary text-white shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase">研究总数</h6>
                        <h2 class="mb-0">{{ studies|length if studies else 0 }}</h2>
                    </div>
                    <i class="fas fa-folder-open fa-3x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card bg-success text-white shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase">已检测研究</h6>
                        <h2 class="mb-0">{{ detected_count if detected_count else 0 }}</h2>
                    </div>
                    <i class="fas fa-check-circle fa-3x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card bg-warning text-white shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase">检测到异常</h6>
                        <h2 class="mb-0">{{ abnormal_count if abnormal_count else 0 }}</h2>
                    </div>
                    <i class="fas fa-exclamation-triangle fa-3x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card bg-info text-white shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase">最近上传</h6>
                        <h2 class="mb-0">{{ recent_count if recent_count else 0 }}</h2>
                    </div>
                    <i class="fas fa-clock fa-3x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-search me-2"></i>研究过滤</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('web.dashboard') }}" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">搜索</label>
                        <input type="text" class="form-control" id="search" name="search" placeholder="患者ID、研究描述..." value="{{ request.args.get('search', '') }}">
                    </div>
                    <div class="col-md-3">
                        <label for="modality" class="form-label">模态</label>
                        <select class="form-select" id="modality" name="modality">
                            <option value="" {% if not request.args.get('modality') %}selected{% endif %}>全部</option>
                            <option value="CT" {% if request.args.get('modality') == 'CT' %}selected{% endif %}>CT</option>
                            <option value="MR" {% if request.args.get('modality') == 'MR' %}selected{% endif %}>MR</option>
                            <option value="XR" {% if request.args.get('modality') == 'XR' %}selected{% endif %}>XR</option>
                            <option value="US" {% if request.args.get('modality') == 'US' %}selected{% endif %}>US</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">检测状态</label>
                        <select class="form-select" id="status" name="status">
                            <option value="" {% if not request.args.get('status') %}selected{% endif %}>全部</option>
                            <option value="detected" {% if request.args.get('status') == 'detected' %}selected{% endif %}>已检测</option>
                            <option value="undetected" {% if request.args.get('status') == 'undetected' %}selected{% endif %}>未检测</option>
                            <option value="abnormal" {% if request.args.get('status') == 'abnormal' %}selected{% endif %}>检测到异常</option>
                            <option value="normal" {% if request.args.get('status') == 'normal' %}selected{% endif %}>正常</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="sort" class="form-label">排序</label>
                        <select class="form-select" id="sort" name="sort">
                            <option value="date_desc" {% if request.args.get('sort') == 'date_desc' or not request.args.get('sort') %}selected{% endif %}>日期（最新优先）</option>
                            <option value="date_asc" {% if request.args.get('sort') == 'date_asc' %}selected{% endif %}>日期（最早优先）</option>
                            <option value="patient_asc" {% if request.args.get('sort') == 'patient_asc' %}selected{% endif %}>患者ID（升序）</option>
                            <option value="patient_desc" {% if request.args.get('sort') == 'patient_desc' %}selected{% endif %}>患者ID（降序）</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-1"></i>应用过滤
                        </button>
                        <a href="{{ url_for('web.dashboard') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-undo me-1"></i>重置
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <h3 class="mb-3">我的研究</h3>
        {% if studies %}
        <div class="row">
            {% for study in studies %}
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm study-card h-100">
                    {% if study.detection_status == 'detected' %}
                        {% if study.has_abnormal %}
                        <div class="detection-badge badge bg-warning text-dark">
                            <i class="fas fa-exclamation-triangle me-1"></i>检测到异常
                        </div>
                        {% else %}
                        <div class="detection-badge badge bg-success">
                            <i class="fas fa-check-circle me-1"></i>正常
                        </div>
                        {% endif %}
                    {% endif %}
                    <div class="card-body">
                        <h5 class="card-title">{{ study.patient_name or '未知患者' }}</h5>
                        <h6 class="card-subtitle mb-2 text-muted">ID: {{ study.patient_id or '未知ID' }}</h6>
                        <p class="card-text">
                            <small class="text-muted">
                                <i class="fas fa-calendar-alt me-1"></i>{{ study.study_date or '未知日期' }}
                            </small>
                            <br>
                            <small class="text-muted">
                                <i class="fas fa-x-ray me-1"></i>{{ study.modality or '未知模态' }}
                            </small>
                            <br>
                            <small class="text-muted">
                                <i class="fas fa-images me-1"></i>{{ study.images_count or 0 }} 张图像
                            </small>
                        </p>
                        <p class="card-text">{{ study.study_description or '无描述' }}</p>
                    </div>
                    <div class="card-footer bg-white">
                        <div class="btn-group w-100">
                            <a href="{{ url_for('web.dicom_viewer', study_id=study.id) }}" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>DICOM查看
                            </a>
                            {% if study.detection_status != 'detected' %}
                            <a href="{{ url_for('web.detect', study_id=study.id) }}" class="btn btn-outline-success">
                                <i class="fas fa-brain me-1"></i>YOLO检测
                            </a>
                            {% else %}
                            <a href="{{ url_for('web.results', study_id=study.id) }}" class="btn btn-outline-info">
                                <i class="fas fa-chart-bar me-1"></i>检测结果
                            </a>
                            {% endif %}
                            <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ study.id }}">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 删除确认模态框 -->
            <div class="modal fade" id="deleteModal{{ study.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ study.id }}" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="deleteModalLabel{{ study.id }}">确认删除</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>您确定要删除这个研究吗？此操作无法撤销。</p>
                            <p><strong>患者：</strong> {{ study.patient_name or '未知患者' }}</p>
                            <p><strong>研究日期：</strong> {{ study.study_date or '未知日期' }}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <form action="{{ url_for('web.delete_study', study_id=study.id) }}" method="POST" style="display: inline;">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash-alt me-1"></i>确认删除
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 分页 -->
        {% if pagination and pagination.pages > 1 %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                <li class="page-item {% if pagination.page == 1 %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('web.dashboard', page=pagination.page-1, **request.args) if pagination.page > 1 else '#' }}">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                {% for p in range(1, pagination.pages + 1) %}
                <li class="page-item {% if p == pagination.page %}active{% endif %}">
                    <a class="page-link" href="{{ url_for('web.dashboard', page=p, **request.args) }}">{{ p }}</a>
                </li>
                {% endfor %}
                <li class="page-item {% if pagination.page == pagination.pages %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('web.dashboard', page=pagination.page+1, **request.args) if pagination.page < pagination.pages else '#' }}">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>您还没有上传任何研究。
            <a href="{{ url_for('web.upload') }}" class="alert-link">点击这里上传您的第一个研究</a>。
        </div>
        {% endif %}
        
        {% if get_flashed_messages(category_filter=['warning']) %}
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>系统提示：</strong>部分服务可能不可用，这可能影响系统功能。
            <a href="{{ url_for('web.system_status') }}" class="alert-link">点击这里查看详细的系统状态</a>。
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 自动提交表单当选择改变时
    document.querySelectorAll('#modality, #status, #sort').forEach(function(element) {
        element.addEventListener('change', function() {
            this.form.submit();
        });
    });
</script>
{% endblock %}