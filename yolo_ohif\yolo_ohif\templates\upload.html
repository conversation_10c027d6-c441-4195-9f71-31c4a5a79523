{% extends "base.html" %}

{% block title %}上传 - YOLO-OHIF医学图像疾病检测系统{% endblock %}

{% block extra_css %}
<style>
    .upload-area {
        border: 2px dashed #ccc;
        border-radius: 5px;
        padding: 30px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
    }
    .upload-area:hover, .upload-area.dragover {
        border-color: #0d6efd;
        background-color: rgba(13, 110, 253, 0.05);
    }
    .upload-icon {
        font-size: 48px;
        color: #0d6efd;
        margin-bottom: 15px;
    }
    #file-list {
        max-height: 300px;
        overflow-y: auto;
    }
    .progress {
        height: 5px;
    }
    .file-item {
        border-left: 3px solid #0d6efd;
        background-color: #f8f9fa;
        margin-bottom: 5px;
        padding: 10px;
        border-radius: 0 5px 5px 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-upload me-2"></i>上传医学图像</h4>
            </div>
            <div class="card-body">
                <!-- 模型选择器 -->
                <div class="alert alert-info mb-4" role="alert">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <h6 class="mb-0"><i class="fas fa-brain me-2"></i>YOLO检测模型</h6>
                        </div>
                        <div class="col-md-6">
                            <select class="form-select" id="modelSelector">
                                <option value="">正在加载模型列表...</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-primary btn-sm" id="refreshModelsBtn">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <span class="badge bg-success ms-2" id="currentModelBadge" style="display: none;">当前</span>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-12">
                            <small class="text-muted" id="modelInfo">选择适合您数据的YOLO模型进行疾病检测</small>
                        </div>
                    </div>
                </div>
                
                <ul class="nav nav-tabs mb-4" id="uploadTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="dicom-tab" data-bs-toggle="tab" data-bs-target="#dicom" type="button" role="tab" aria-controls="dicom" aria-selected="true">
                            <i class="fas fa-file-medical me-1"></i>DICOM文件
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="zip-tab" data-bs-toggle="tab" data-bs-target="#zip" type="button" role="tab" aria-controls="zip" aria-selected="false">
                            <i class="fas fa-file-archive me-1"></i>ZIP压缩包
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="folder-tab" data-bs-toggle="tab" data-bs-target="#folder" type="button" role="tab" aria-controls="folder" aria-selected="false">
                            <i class="fas fa-folder-open me-1"></i>文件夹
                        </button>
                    </li>
                </ul>
                
                <div class="tab-content" id="uploadTabsContent">
                    <!-- DICOM文件上传 -->
                    <div class="tab-pane fade show active" id="dicom" role="tabpanel" aria-labelledby="dicom-tab">
                        <form id="dicomUploadForm" action="{{ url_for('api.upload_dicom') }}" method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <div class="upload-area" id="dicomDropArea">
                                <i class="fas fa-file-medical upload-icon"></i>
                                <h4>拖放DICOM文件到这里</h4>
                                <p>或者</p>
                                <input type="file" id="dicomFiles" name="files[]" multiple accept=".dcm,.dicom" style="display: none;">
                                <button type="button" class="btn btn-primary" id="dicomBrowseBtn">
                                    <i class="fas fa-folder-open me-1"></i>浏览文件
                                </button>
                                <p class="mt-2 text-muted">支持多个DICOM文件同时上传</p>
                            </div>
                            
                            <div class="mt-4" id="dicomFileList" style="display: none;">
                                <h5><i class="fas fa-list me-2"></i>已选择的文件</h5>
                                <div id="dicom-file-list" class="mt-3"></div>
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-success" id="dicomUploadBtn">
                                        <i class="fas fa-cloud-upload-alt me-1"></i>开始上传
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" id="dicomClearBtn">
                                        <i class="fas fa-times me-1"></i>清除选择
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mt-4" id="dicomUploadProgress" style="display: none;">
                                <h5><i class="fas fa-spinner fa-spin me-2"></i>上传进度</h5>
                                <div class="progress mt-3">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" id="dicomProgressBar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <p class="mt-2 text-center" id="dicomProgressText">准备上传...</p>
                            </div>
                        </form>
                    </div>
                    
                    <!-- ZIP压缩包上传 -->
                    <div class="tab-pane fade" id="zip" role="tabpanel" aria-labelledby="zip-tab">
                        <form id="zipUploadForm" action="{{ url_for('api.upload_dicom') }}" method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <div class="upload-area" id="zipDropArea">
                                <i class="fas fa-file-archive upload-icon"></i>
                                <h4>拖放ZIP压缩包到这里</h4>
                                <p>或者</p>
                                <input type="file" id="zipFiles" name="file" accept=".zip" style="display: none;">
                                <button type="button" class="btn btn-primary" id="zipBrowseBtn">
                                    <i class="fas fa-folder-open me-1"></i>浏览文件
                                </button>
                                <p class="mt-2 text-muted">ZIP压缩包应包含DICOM文件</p>
                            </div>
                            
                            <div class="mt-4" id="zipFileList" style="display: none;">
                                <h5><i class="fas fa-info-circle me-2"></i>已选择的ZIP文件</h5>
                                <div id="zip-file-list" class="mt-3"></div>
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-success" id="zipUploadBtn">
                                        <i class="fas fa-cloud-upload-alt me-1"></i>开始上传
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" id="zipClearBtn">
                                        <i class="fas fa-times me-1"></i>清除选择
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mt-4" id="zipUploadProgress" style="display: none;">
                                <h5><i class="fas fa-spinner fa-spin me-2"></i>上传进度</h5>
                                <div class="progress mt-3">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" id="zipProgressBar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <p class="mt-2 text-center" id="zipProgressText">准备上传...</p>
                            </div>
                        </form>
                    </div>
                    
                    <!-- 文件夹上传 -->
                    <div class="tab-pane fade" id="folder" role="tabpanel" aria-labelledby="folder-tab">
                        <form id="folderUploadForm" action="{{ url_for('api.upload_dicom') }}" method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <div class="upload-area" id="folderDropArea">
                                <i class="fas fa-folder-open upload-icon"></i>
                                <h4>选择包含DICOM文件的文件夹</h4>
                                <p>或者</p>
                                <input type="file" id="folderFiles" name="files[]" webkitdirectory directory multiple style="display: none;">
                                <button type="button" class="btn btn-primary" id="folderBrowseBtn">
                                    <i class="fas fa-folder-open me-1"></i>浏览文件夹
                                </button>
                                <p class="mt-2 text-muted">将保留文件夹结构</p>
                            </div>
                            
                            <div class="mt-4" id="folderFileList" style="display: none;">
                                <h5><i class="fas fa-list me-2"></i>已选择的文件</h5>
                                <p><strong>文件夹：</strong> <span id="selectedFolderName"></span></p>
                                <p><strong>文件数量：</strong> <span id="selectedFileCount">0</span> 个文件</p>
                                <div id="folder-file-list" class="mt-3"></div>
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-success" id="folderUploadBtn">
                                        <i class="fas fa-cloud-upload-alt me-1"></i>开始上传
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" id="folderClearBtn">
                                        <i class="fas fa-times me-1"></i>清除选择
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mt-4" id="folderUploadProgress" style="display: none;">
                                <h5><i class="fas fa-spinner fa-spin me-2"></i>上传进度</h5>
                                <div class="progress mt-3">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" id="folderProgressBar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <p class="mt-2 text-center" id="folderProgressText">准备上传...</p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-white">
                <h5 class="mb-3">上传说明</h5>
                <ul class="list-group list-group-flush">
                    <li class="list-group-item">
                        <i class="fas fa-info-circle text-primary me-2"></i>
                        支持上传单个或多个DICOM文件、包含DICOM文件的ZIP压缩包或文件夹
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-info-circle text-primary me-2"></i>
                        上传完成后，系统会自动将文件存储到Orthanc DICOM服务器
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-info-circle text-primary me-2"></i>
                        上传完成后，您可以在仪表板中查看和管理您的研究
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        请确保上传的DICOM文件不包含患者的个人身份信息，除非您已获得授权
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // DICOM文件上传处理
    document.addEventListener('DOMContentLoaded', function() {
        // 模型选择器相关元素
        const modelSelector = document.getElementById('modelSelector');
        const refreshModelsBtn = document.getElementById('refreshModelsBtn');
        const currentModelBadge = document.getElementById('currentModelBadge');
        const modelInfo = document.getElementById('modelInfo');
        
        // 加载可用模型列表
        function loadModels() {
            fetch('/api/v1/models')
                .then(response => response.json())
                .then(data => {
                    modelSelector.innerHTML = '';
                    
                    if (data.models && data.models.length > 0) {
                        data.models.forEach(model => {
                            const option = document.createElement('option');
                            option.value = model.name;
                            option.textContent = `${model.name} (${formatFileSize(model.size)})`;
                            
                            if (model.name === data.current_model) {
                                option.selected = true;
                                currentModelBadge.style.display = 'inline';
                            }
                            
                            modelSelector.appendChild(option);
                        });
                        
                        modelInfo.textContent = `共找到 ${data.models.length} 个可用模型，当前使用: ${data.current_model || '未知'}`;
                    } else {
                        const option = document.createElement('option');
                        option.value = '';
                        option.textContent = '没有找到可用的模型';
                        modelSelector.appendChild(option);
                        modelInfo.textContent = '请将YOLO模型文件(.pt)放置到models/weights目录中';
                    }
                })
                .catch(error => {
                    console.error('加载模型列表失败:', error);
                    modelSelector.innerHTML = '<option value="">加载失败</option>';
                    modelInfo.textContent = '加载模型列表时出错';
                });
        }
        
        // 切换模型
        function switchModel(modelName) {
            if (!modelName) {
                alert('请选择一个模型');
                return;
            }
            
            console.log('正在切换到模型:', modelName);
            
            fetch('/api/v1/models/switch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ model_name: modelName })
            })
            .then(response => {
                console.log('API响应状态:', response.status, response.statusText);
                if (!response.ok) {
                    return response.json().then(errorData => {
                        throw new Error(errorData.message || errorData.error || `HTTP ${response.status}: ${response.statusText}`);
                    }).catch(() => {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                console.log('API响应数据:', data);
                // 成功切换模型
                alert(data.message || '模型切换成功');
                if (data.current_model) {
                    currentModelBadge.style.display = 'inline';
                    modelInfo.textContent = `当前模型: ${data.current_model}，类别数: ${data.model_info?.classes || 0}`;
                }
                // 重新加载模型列表以更新当前选中状态
                loadModels();
            })
            .catch(error => {
                console.error('切换模型失败:', error);
                alert('切换模型时出错: ' + error.message);
            });
        }
        
        // 获取CSRF令牌
        function getCsrfToken() {
            var csrfInput = document.querySelector('input[name="csrf_token"]');
            if (csrfInput) {
                return csrfInput.value;
            }
            return '';
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 事件监听器
        modelSelector.addEventListener('change', function() {
            if (this.value) {
                switchModel(this.value);
            }
        });
        
        refreshModelsBtn.addEventListener('click', function() {
            loadModels();
        });
        
        // 初始加载模型列表
        loadModels();
        
        // DICOM文件上传
        const dicomDropArea = document.getElementById('dicomDropArea');
        const dicomFiles = document.getElementById('dicomFiles');
        const dicomBrowseBtn = document.getElementById('dicomBrowseBtn');
        const dicomFileList = document.getElementById('dicomFileList');
        const dicomFileListContainer = document.getElementById('dicom-file-list');
        const dicomClearBtn = document.getElementById('dicomClearBtn');
        const dicomUploadForm = document.getElementById('dicomUploadForm');
        const dicomUploadProgress = document.getElementById('dicomUploadProgress');
        const dicomProgressBar = document.getElementById('dicomProgressBar');
        const dicomProgressText = document.getElementById('dicomProgressText');
        
        // 点击浏览按钮触发文件选择
        dicomBrowseBtn.addEventListener('click', function() {
            dicomFiles.click();
        });
        
        // 拖放处理
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dicomDropArea.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        ['dragenter', 'dragover'].forEach(eventName => {
            dicomDropArea.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dicomDropArea.addEventListener(eventName, unhighlight, false);
        });
        
        function highlight() {
            dicomDropArea.classList.add('dragover');
        }
        
        function unhighlight() {
            dicomDropArea.classList.remove('dragover');
        }
        
        // 处理拖放的文件
        dicomDropArea.addEventListener('drop', function(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleDicomFiles(files);
        });
        
        // 处理选择的文件
        dicomFiles.addEventListener('change', function() {
            handleDicomFiles(this.files);
        });
        
        // 处理DICOM文件
        function handleDicomFiles(files) {
            if (files.length === 0) return;
            
            dicomFileList.style.display = 'block';
            dicomFileListContainer.innerHTML = '';
            
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const fileSize = formatFileSize(file.size);
                
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-file-medical me-2"></i>
                            <strong>${file.name}</strong>
                        </div>
                        <span class="badge bg-primary">${fileSize}</span>
                    </div>
                `;
                
                dicomFileListContainer.appendChild(fileItem);
            }
        }
        
        // 清除选择的文件
        dicomClearBtn.addEventListener('click', function() {
            dicomFiles.value = '';
            dicomFileList.style.display = 'none';
            dicomFileListContainer.innerHTML = '';
        });
        
        // 处理DICOM文件上传
        dicomUploadForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (dicomFiles.files.length === 0) {
                alert('请选择至少一个DICOM文件');
                return;
            }
            
            const formData = new FormData(this);
            
            dicomUploadProgress.style.display = 'block';
            dicomProgressBar.style.width = '0%';
            dicomProgressText.textContent = '上传中...';
            
            const xhr = new XMLHttpRequest();
            
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    dicomProgressBar.style.width = percentComplete + '%';
                    dicomProgressText.textContent = `上传中... ${percentComplete}%`;
                }
            });
            
            xhr.addEventListener('load', function() {
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    dicomProgressBar.style.width = '100%';
                    dicomProgressText.textContent = '上传成功！正在重定向到仪表板...';
                    setTimeout(function() {
                        window.location.href = '/dashboard';
                    }, 1500);
                } else {
                    dicomProgressText.textContent = '上传失败：' + xhr.statusText;
                }
            });
            
            xhr.addEventListener('error', function() {
                dicomProgressText.textContent = '上传失败：网络错误';
            });
            
            xhr.open('POST', dicomUploadForm.action);
            xhr.send(formData);
        });
        
        // ZIP文件上传处理
        const zipDropArea = document.getElementById('zipDropArea');
        const zipFiles = document.getElementById('zipFiles');
        const zipBrowseBtn = document.getElementById('zipBrowseBtn');
        const zipFileList = document.getElementById('zipFileList');
        const zipFileListContainer = document.getElementById('zip-file-list');
        const zipClearBtn = document.getElementById('zipClearBtn');
        const zipUploadForm = document.getElementById('zipUploadForm');
        const zipUploadProgress = document.getElementById('zipUploadProgress');
        const zipProgressBar = document.getElementById('zipProgressBar');
        const zipProgressText = document.getElementById('zipProgressText');
        
        // 点击浏览按钮触发文件选择
        zipBrowseBtn.addEventListener('click', function() {
            zipFiles.click();
        });
        
        // ZIP拖放处理
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            zipDropArea.addEventListener(eventName, preventDefaults, false);
        });
        
        ['dragenter', 'dragover'].forEach(eventName => {
            zipDropArea.addEventListener(eventName, function() {
                zipDropArea.classList.add('dragover');
            }, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            zipDropArea.addEventListener(eventName, function() {
                zipDropArea.classList.remove('dragover');
            }, false);
        });
        
        // 处理ZIP拖放的文件
        zipDropArea.addEventListener('drop', function(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleZipFiles(files);
        });
        
        // 处理ZIP选择的文件
        zipFiles.addEventListener('change', function() {
            handleZipFiles(this.files);
        });
        
        // 处理ZIP文件
        function handleZipFiles(files) {
            if (files.length === 0) return;
            
            zipFileList.style.display = 'block';
            zipFileListContainer.innerHTML = '';
            
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const fileSize = formatFileSize(file.size);
                
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-file-archive me-2"></i>
                            <strong>${file.name}</strong>
                        </div>
                        <span class="badge bg-primary">${fileSize}</span>
                    </div>
                `;
                
                zipFileListContainer.appendChild(fileItem);
            }
        }
        
        // 清除ZIP选择的文件
        zipClearBtn.addEventListener('click', function() {
            zipFiles.value = '';
            zipFileList.style.display = 'none';
            zipFileListContainer.innerHTML = '';
        });
        
        // 处理ZIP文件上传
        zipUploadForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (zipFiles.files.length === 0) {
                alert('请选择至少一个ZIP文件');
                return;
            }
            
            const formData = new FormData(this);
            
            zipUploadProgress.style.display = 'block';
            zipProgressBar.style.width = '0%';
            zipProgressText.textContent = '上传中...';
            
            const xhr = new XMLHttpRequest();
            
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    zipProgressBar.style.width = percentComplete + '%';
                    zipProgressText.textContent = `上传中... ${percentComplete}%`;
                }
            });
            
            xhr.addEventListener('load', function() {
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    zipProgressBar.style.width = '100%';
                    zipProgressText.textContent = '上传成功！正在重定向到仪表板...';
                    setTimeout(function() {
                        window.location.href = '/dashboard';
                    }, 1500);
                } else {
                    zipProgressText.textContent = '上传失败：' + xhr.statusText;
                }
            });
            
            xhr.addEventListener('error', function() {
                zipProgressText.textContent = '上传失败：网络错误';
            });
            
            xhr.open('POST', zipUploadForm.action);
            xhr.send(formData);
        });
        
        // 文件夹上传处理
        const folderDropArea = document.getElementById('folderDropArea');
        const folderFiles = document.getElementById('folderFiles');
        const folderBrowseBtn = document.getElementById('folderBrowseBtn');
        const folderFileList = document.getElementById('folderFileList');
        const folderFileListContainer = document.getElementById('folder-file-list');
        const folderClearBtn = document.getElementById('folderClearBtn');
        const folderUploadForm = document.getElementById('folderUploadForm');
        const folderUploadProgress = document.getElementById('folderUploadProgress');
        const folderProgressBar = document.getElementById('folderProgressBar');
        const folderProgressText = document.getElementById('folderProgressText');
        
        // 点击浏览按钮触发文件夹选择
        folderBrowseBtn.addEventListener('click', function() {
            folderFiles.click();
        });
        
        // 文件夹拖放处理
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            folderDropArea.addEventListener(eventName, preventDefaults, false);
        });
        
        ['dragenter', 'dragover'].forEach(eventName => {
            folderDropArea.addEventListener(eventName, function() {
                folderDropArea.classList.add('dragover');
            }, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            folderDropArea.addEventListener(eventName, function() {
                folderDropArea.classList.remove('dragover');
            }, false);
        });
        
        // 处理文件夹拖放的文件
        folderDropArea.addEventListener('drop', function(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFolderFiles(files);
        });
        
        // 处理文件夹选择的文件
        folderFiles.addEventListener('change', function() {
            handleFolderFiles(this.files);
        });
        
        // 处理文件夹文件
        function handleFolderFiles(files) {
            if (files.length === 0) return;
            
            folderFileList.style.display = 'block';
            folderFileListContainer.innerHTML = '';
            
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const fileSize = formatFileSize(file.size);
                
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-folder me-2"></i>
                            <strong>${file.webkitRelativePath || file.name}</strong>
                        </div>
                        <span class="badge bg-primary">${fileSize}</span>
                    </div>
                `;
                
                folderFileListContainer.appendChild(fileItem);
            }
        }
        
        // 清除文件夹选择的文件
        folderClearBtn.addEventListener('click', function() {
            folderFiles.value = '';
            folderFileList.style.display = 'none';
            folderFileListContainer.innerHTML = '';
        });
        
        // 处理文件夹上传
        folderUploadForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (folderFiles.files.length === 0) {
                alert('请选择至少一个文件夹');
                return;
            }
            
            const formData = new FormData(this);
            
            folderUploadProgress.style.display = 'block';
            folderProgressBar.style.width = '0%';
            folderProgressText.textContent = '上传中...';
            
            const xhr = new XMLHttpRequest();
            
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    folderProgressBar.style.width = percentComplete + '%';
                    folderProgressText.textContent = `上传中... ${percentComplete}%`;
                }
            });
            
            xhr.addEventListener('load', function() {
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    folderProgressBar.style.width = '100%';
                    folderProgressText.textContent = '上传成功！正在重定向到仪表板...';
                    setTimeout(function() {
                        window.location.href = '/dashboard';
                    }, 1500);
                } else {
                    folderProgressText.textContent = '上传失败：' + xhr.statusText;
                }
            });
            
            xhr.addEventListener('error', function() {
                folderProgressText.textContent = '上传失败：网络错误';
            });
            
            xhr.open('POST', folderUploadForm.action);
            xhr.send(formData);
        });
        
        // formatFileSize函数已在上方定义
    });
</script>
{% endblock %}