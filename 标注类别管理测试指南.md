# 标注类别管理测试指南

## 测试目标
验证智能标注系统的标注类别管理功能，包括添加自定义类别、删除类别、系统类别保护等功能。

## 测试环境
- ✅ 应用程序已启动：`dotnet run --project src/MedicalImageAnalysis.Wpf`
- ✅ 编译成功，无错误和警告
- ✅ 新功能已集成到智能标注界面

## 快速测试步骤

### 🔧 测试1：查看类别管理界面

#### 1.1 打开智能标注界面
1. 启动应用程序
2. 点击左侧导航栏的"智能标注"选项卡
3. 验证界面正常加载

#### 1.2 检查标注类别区域
1. 在右侧控制面板找到"标注类别"区域
2. ✅ 验证：下拉框显示系统预设类别
   - 病灶区域
   - 正常组织
   - 骨折
   - 肿瘤
   - 血管
   - 其他

3. ✅ 验证：显示自定义类别输入框
4. ✅ 验证：显示"添加类别"和"删除类别"按钮（并排显示）
5. ✅ 验证：显示操作提示文字

#### 1.3 检查类别管理面板
1. 找到"类别管理"展开面板
2. 展开该面板
3. ✅ 验证：显示类别列表
4. ✅ 验证：每个类别显示颜色指示器
5. ✅ 验证：显示类型标识（系统/自定义）
6. ✅ 验证：显示"刷新类别列表"按钮

### ➕ 测试2：添加自定义类别

#### 2.1 添加有效类别
1. **输入新类别名称**：
   - 在"自定义类别"文本框中输入"血管瘤"
   - 点击"添加类别"按钮

2. **验证添加结果**：
   - ✅ 验证：下拉框中出现"血管瘤"选项
   - ✅ 验证：新类别自动被选中
   - ✅ 验证：输入框自动清空
   - ✅ 验证：状态栏显示"已添加类别：血管瘤"

3. **检查类别管理面板**：
   - ✅ 验证：类别列表自动更新
   - ✅ 验证："血管瘤"显示为"自定义"类型
   - ✅ 验证：显示对应的颜色指示器

#### 2.2 测试输入验证
1. **空值测试**：
   - 不输入任何内容，直接点击"添加类别"
   - ✅ 验证：显示"请输入类别名称"提示

2. **重复类别测试**：
   - 再次输入"血管瘤"，点击"添加类别"
   - ✅ 验证：显示"类别 '血管瘤' 已存在"提示

3. **系统类别重复测试**：
   - 输入"病灶区域"，点击"添加类别"
   - ✅ 验证：显示重复提示

#### 2.3 添加多个自定义类别
1. 依次添加以下类别：
   - "淋巴结"
   - "囊肿"
   - "钙化"

2. ✅ 验证：所有类别都成功添加到下拉框
3. ✅ 验证：类别管理面板正确显示所有类别

### ❌ 测试3：删除类别功能

#### 3.1 尝试删除系统预设类别
1. **选择系统类别**：
   - 在下拉框中选择"病灶区域"
   - 点击"删除类别"按钮

2. **验证保护机制**：
   - ✅ 验证：显示"无法删除系统预设类别 '病灶区域'"提示
   - ✅ 验证：类别没有被删除
   - ✅ 验证：下拉框中仍然存在该类别

3. **测试其他系统类别**：
   - 依次尝试删除"肿瘤"、"骨折"等系统类别
   - ✅ 验证：都无法删除，显示保护提示

#### 3.2 删除自定义类别（无关联标注）
1. **选择自定义类别**：
   - 在下拉框中选择"淋巴结"
   - 点击"删除类别"按钮

2. **验证删除结果**：
   - ✅ 验证：类别成功删除
   - ✅ 验证：下拉框中不再显示"淋巴结"
   - ✅ 验证：状态栏显示"已删除类别：淋巴结"
   - ✅ 验证：类别管理面板自动更新

#### 3.3 删除有关联标注的类别
1. **创建使用自定义类别的标注**：
   - 加载一个图像文件
   - 选择"血管瘤"类别
   - 使用矩形工具绘制一个标注

2. **尝试删除该类别**：
   - 选择"血管瘤"类别
   - 点击"删除类别"按钮

3. **验证关联检查**：
   - ✅ 验证：显示确认对话框
   - ✅ 验证：提示"类别 '血管瘤' 正在被 1 个标注使用"
   - ✅ 验证：询问是否继续删除

4. **测试删除选择**：
   - **选择"是"**：
     - ✅ 验证：类别和相关标注都被删除
     - ✅ 验证：画布上的标注消失
   - **选择"否"**：
     - ✅ 验证：类别和标注都保留

#### 3.4 测试删除验证
1. **未选择类别时删除**：
   - 不选择任何类别，直接点击"删除类别"
   - ✅ 验证：显示"请先选择要删除的类别"提示

### 🔄 测试4：类别管理面板交互

#### 4.1 类别列表交互
1. **点击类别列表项**：
   - 在类别管理面板中点击"囊肿"
   - ✅ 验证：主下拉框自动选择"囊肿"

2. **颜色指示器验证**：
   - ✅ 验证：每个类别显示正确的颜色
   - ✅ 验证：颜色与实际标注颜色一致

3. **类型标识验证**：
   - ✅ 验证：系统类别显示"系统"标识
   - ✅ 验证：自定义类别显示"自定义"标识

#### 4.2 刷新功能测试
1. **手动刷新**：
   - 点击"刷新类别列表"按钮
   - ✅ 验证：列表正确更新
   - ✅ 验证：显示最新的类别信息

### 🎨 测试5：颜色系统集成

#### 5.1 自定义类别颜色测试
1. **添加特殊名称类别**：
   - 添加"lesion"（英文病灶）
   - ✅ 验证：使用绿色（与"病灶区域"相同）

2. **添加普通自定义类别**：
   - 添加"测试类别"
   - ✅ 验证：使用默认黄色

#### 5.2 标注颜色一致性测试
1. **使用自定义类别标注**：
   - 选择自定义类别进行标注
   - ✅ 验证：标注颜色与类别管理面板显示一致

### 📊 测试6：综合功能测试

#### 6.1 完整工作流程测试
1. **添加专业类别**：
   - 添加"动脉瘤"、"静脉曲张"、"血栓"

2. **使用类别进行标注**：
   - 为每个类别创建标注
   - ✅ 验证：不同类别使用不同颜色

3. **管理类别**：
   - 删除不需要的类别
   - ✅ 验证：相关标注处理正确

#### 6.2 数据一致性测试
1. **类别数量验证**：
   - 统计下拉框中的类别数量
   - 统计类别管理面板中的类别数量
   - ✅ 验证：两者数量一致

2. **类别名称一致性**：
   - ✅ 验证：两个位置显示的类别名称完全一致

## 预期结果总结

### ✅ 添加类别功能
- [x] 可以添加自定义类别
- [x] 输入验证正常工作
- [x] 重复检查有效
- [x] 自动选择新类别
- [x] 界面实时更新

### ✅ 删除类别功能
- [x] 系统类别受到保护
- [x] 自定义类别可以删除
- [x] 关联标注检查正常
- [x] 批量删除功能正常
- [x] 确认对话框正常

### ✅ 类别管理界面
- [x] 类别列表正确显示
- [x] 颜色指示器正确
- [x] 类型标识准确
- [x] 交互功能正常
- [x] 刷新功能有效

### ✅ 系统集成
- [x] 与标注功能无缝集成
- [x] 颜色系统一致
- [x] 数据同步正确
- [x] 错误处理完善

## 问题排查

### ❌ 如果添加类别失败
1. **检查输入**：确认输入了有效的类别名称
2. **检查重复**：确认类别名称不重复
3. **重启应用**：如果问题持续，重启应用程序

### ❌ 如果删除类别失败
1. **检查类别类型**：确认不是系统预设类别
2. **检查选择**：确认已选择要删除的类别
3. **处理关联标注**：确认处理了相关标注

### ❌ 如果界面显示异常
1. **刷新列表**：点击"刷新类别列表"按钮
2. **重新加载**：重新打开智能标注界面
3. **检查日志**：查看控制台是否有错误信息

## 测试报告模板

```
测试日期：[日期]
测试人员：[姓名]

添加类别功能：
□ 有效类别添加：通过/失败
□ 输入验证：通过/失败
□ 重复检查：通过/失败
□ 界面更新：通过/失败

删除类别功能：
□ 系统类别保护：通过/失败
□ 自定义类别删除：通过/失败
□ 关联标注处理：通过/失败
□ 确认对话框：通过/失败

类别管理界面：
□ 列表显示：通过/失败
□ 颜色指示器：通过/失败
□ 类型标识：通过/失败
□ 交互功能：通过/失败

系统集成：
□ 标注功能集成：通过/失败
□ 颜色系统一致：通过/失败
□ 数据同步：通过/失败

总体评价：
□ 功能完全正常
□ 存在小问题但不影响使用
□ 存在严重问题需要修复

备注：[详细说明]
```

按照这个测试指南，您可以全面验证标注类别管理功能的正确性和完整性！
