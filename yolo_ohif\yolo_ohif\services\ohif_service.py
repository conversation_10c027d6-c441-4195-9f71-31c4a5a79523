import os
import json
import logging
import requests
import shutil
import tempfile
from pathlib import Path
import subprocess
import time
import socket
import platform
import webbrowser

logger = logging.getLogger(__name__)

class OHIFService:
    """OHIF医学影像查看器服务"""
    
    def __init__(self, ohif_viewer_url, orthanc_url, ohif_config_dir=None):
        """初始化OHIF服务
        
        Args:
            ohif_viewer_url: OHIF查看器URL
            orthanc_url: Orthanc服务器URL
            ohif_config_dir: OHIF配置目录
        """
        self.ohif_viewer_url = ohif_viewer_url.rstrip('/')
        self.orthanc_url = orthanc_url.rstrip('/')
        self.ohif_config_dir = ohif_config_dir
        
        # 确保配置目录存在
        if self.ohif_config_dir and not os.path.exists(self.ohif_config_dir):
            os.makedirs(self.ohif_config_dir, exist_ok=True)
    
    def get_viewer_url(self, study_id=None):
        """获取DICOM查看器URL
        
        Args:
            study_id: 研究ID，如果提供则直接打开该研究
            
        Returns:
            DICOM查看器URL
        """
        if study_id:
            # 使用自定义DICOM查看器
            return f"/dicom-viewer?study_id={study_id}"
        return "/dicom-viewer"
    
    def get_viewer_url_with_detection(self, study_id, detection_results):
        """获取带有检测结果的DICOM查看器URL
        
        Args:
            study_id: 研究ID
            detection_results: 检测结果
            
        Returns:
            带有检测结果的DICOM查看器URL
        """
        # 使用自定义DICOM查看器，支持检测结果显示
        url = f"/dicom-viewer?study_id={study_id}"
        
        # 检测结果通过API动态加载，无需在URL中传递
        return url
    
    def configure_orthanc_datasource(self):
        """配置OHIF使用Orthanc作为数据源
        
        Returns:
            配置是否成功
        """
        if not self.ohif_config_dir:
            logger.warning("未指定OHIF配置目录，无法配置Orthanc数据源")
            return False
        
        try:
            # 创建Orthanc数据源配置
            config = {
                "name": "Orthanc",
                "wadoUriRoot": f"{self.orthanc_url}/wado",
                "qidoRoot": f"{self.orthanc_url}/dicom-web",
                "wadoRoot": f"{self.orthanc_url}/dicom-web",
                "qidoSupportsIncludeField": True,
                "imageRendering": "wadors",
                "thumbnailRendering": "wadors",
                "enableStudyLazyLoad": True,
                "supportsFuzzyMatching": True
            }
            
            # 保存配置文件
            config_path = os.path.join(self.ohif_config_dir, 'orthanc-datasource.json')
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)
            
            logger.info(f"已创建Orthanc数据源配置: {config_path}")
            return True
        except Exception as e:
            logger.error(f"配置Orthanc数据源时出错: {str(e)}")
            return False
    
    def configure_yolo_plugin(self, plugin_config):
        """配置OHIF的YOLO检测插件
        
        Args:
            plugin_config: 插件配置字典
            
        Returns:
            配置是否成功
        """
        if not self.ohif_config_dir:
            logger.warning("未指定OHIF配置目录，无法配置YOLO插件")
            return False
        
        try:
            # 保存插件配置文件
            config_path = os.path.join(self.ohif_config_dir, 'yolo-plugin-config.json')
            with open(config_path, 'w') as f:
                json.dump(plugin_config, f, indent=2)
            
            logger.info(f"已创建YOLO插件配置: {config_path}")
            return True
        except Exception as e:
            logger.error(f"配置YOLO插件时出错: {str(e)}")
            return False
    
    def check_health(self):
        """检查OHIF查看器健康状态
        
        Returns:
            健康状态字典
        """
        try:
            # 检查Orthanc集成的OHIF查看器
            # 首先检查Orthanc服务是否可用
            orthanc_response = requests.get(f"{self.orthanc_url}/system", timeout=5)
            if orthanc_response.status_code != 200:
                return {'status': 'error', 'message': 'Orthanc服务不可用'}
            
            # 检查OHIF插件是否可用
            ohif_test_url = f"{self.orthanc_url}/ohif/viewer"
            response = requests.get(ohif_test_url, timeout=5)
            if response.status_code == 200:
                return {'status': 'ok', 'message': 'Orthanc集成的OHIF查看器可用'}
            else:
                return {'status': 'error', 'message': f"OHIF插件不可用: HTTP {response.status_code}"}
        except Exception as e:
            logger.error(f"检查OHIF健康状态时出错: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    def open_study_in_browser(self, study_id):
        """在浏览器中打开研究
        
        Args:
            study_id: 研究ID
            
        Returns:
            是否成功打开
        """
        try:
            url = self.get_viewer_url(study_id)
            webbrowser.open(url)
            return True
        except Exception as e:
            logger.error(f"在浏览器中打开研究时出错: {str(e)}")
            return False
    
    def generate_detection_overlay(self, detection_results):
        """生成用于OHIF的检测结果覆盖层配置
        
        Args:
            detection_results: 检测结果列表
            
        Returns:
            覆盖层配置字典
        """
        try:
            overlays = []
            for result in detection_results:
                for detection in result.get('detections', []):
                    # 创建覆盖层配置
                    overlay = {
                        'toolType': 'RectangleROI',
                        'handles': {
                            'start': {
                                'x': detection['x1'],
                                'y': detection['y1']
                            },
                            'end': {
                                'x': detection['x2'],
                                'y': detection['y2']
                            }
                        },
                        'invalidated': False,
                        'active': False,
                        'data': {
                            'label': f"{detection['class']}: {detection['confidence']:.2f}",
                            'text': f"{detection['class']}: {detection['confidence']:.2f}",
                            'findingSites': detection['class'],
                            'confidence': detection['confidence']
                        }
                    }
                    overlays.append(overlay)
            
            return {
                'overlays': overlays,
                'metadata': {
                    'source': 'YOLO-OHIF Detection',
                    'timestamp': time.time()
                }
            }
        except Exception as e:
            logger.error(f"生成检测覆盖层时出错: {str(e)}")
            raise
    
    def save_detection_overlay(self, study_id, detection_results, output_dir=None):
        """保存检测结果覆盖层配置
        
        Args:
            study_id: 研究ID
            detection_results: 检测结果列表
            output_dir: 输出目录
            
        Returns:
            保存的配置文件路径
        """
        try:
            # 生成覆盖层配置
            overlay_config = self.generate_detection_overlay(detection_results)
            
            # 确定输出目录
            if output_dir is None:
                if self.ohif_config_dir:
                    output_dir = os.path.join(self.ohif_config_dir, 'overlays')
                else:
                    output_dir = os.path.join(tempfile.gettempdir(), 'ohif-overlays')
            
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存配置文件
            output_path = os.path.join(output_dir, f"{study_id}_overlay.json")
            with open(output_path, 'w') as f:
                json.dump(overlay_config, f, indent=2)
            
            return output_path
        except Exception as e:
            logger.error(f"保存检测覆盖层时出错: {str(e)}")
            raise
    
    @staticmethod
    def is_port_in_use(port):
        """检查端口是否被占用
        
        Args:
            port: 端口号
            
        Returns:
            端口是否被占用
        """
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(('localhost', port)) == 0
    
    @staticmethod
    def find_available_port(start_port=3000, max_attempts=100):
        """查找可用端口
        
        Args:
            start_port: 起始端口号
            max_attempts: 最大尝试次数
            
        Returns:
            可用端口号
        """
        port = start_port
        for _ in range(max_attempts):
            if not OHIFService.is_port_in_use(port):
                return port
            port += 1
        raise RuntimeError(f"无法找到可用端口（尝试范围: {start_port}-{start_port+max_attempts-1}）")
    
    @staticmethod
    def start_ohif_viewer(ohif_dir, port=None, orthanc_url=None):
        """启动OHIF查看器
        
        Args:
            ohif_dir: OHIF目录
            port: 端口号
            orthanc_url: Orthanc服务器URL
            
        Returns:
            进程对象和URL
        """
        if not os.path.isdir(ohif_dir):
            raise ValueError(f"OHIF目录不存在: {ohif_dir}")
        
        # 查找可用端口
        if port is None:
            port = OHIFService.find_available_port()
        elif OHIFService.is_port_in_use(port):
            raise RuntimeError(f"端口 {port} 已被占用")
        
        # 设置环境变量
        env = os.environ.copy()
        env['PORT'] = str(port)
        
        # 如果提供了Orthanc URL，设置相关环境变量
        if orthanc_url:
            env['REACT_APP_ORTHANC_URL'] = orthanc_url
        
        # 确定启动命令
        if os.path.exists(os.path.join(ohif_dir, 'package.json')):
            # 源码目录，使用npm启动
            cmd = ['npm', 'start']
            cwd = ohif_dir
        elif os.path.exists(os.path.join(ohif_dir, 'Viewers-v3.7.0', 'index.html')):
            # 预构建版本，使用HTTP服务器
            cmd = ['npx', 'http-server', '.', '-p', str(port)]
            cwd = os.path.join(ohif_dir, 'Viewers-v3.7.0')
        else:
            # 尝试查找可能的启动方式
            for root, dirs, files in os.walk(ohif_dir):
                if 'index.html' in files:
                    cmd = ['npx', 'http-server', '.', '-p', str(port)]
                    cwd = root
                    break
            else:
                raise ValueError(f"无法确定OHIF启动方式: {ohif_dir}")
        
        # 启动进程
        logger.info(f"启动OHIF查看器: {' '.join(cmd)} (在 {cwd})")
        if platform.system() == 'Windows':
            process = subprocess.Popen(
                cmd,
                cwd=cwd,
                env=env,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
        else:
            process = subprocess.Popen(
                cmd,
                cwd=cwd,
                env=env,
                preexec_fn=os.setsid
            )
        
        # 构建URL
        url = f"http://localhost:{port}"
        
        # 等待服务启动
        max_attempts = 30
        for i in range(max_attempts):
            try:
                response = requests.get(url, timeout=1)
                if response.status_code == 200:
                    logger.info(f"OHIF查看器已启动: {url}")
                    break
            except requests.exceptions.RequestException:
                pass
            
            # 检查进程是否仍在运行
            if process.poll() is not None:
                raise RuntimeError(f"OHIF进程已退出，退出码: {process.returncode}")
            
            time.sleep(1)
            if i == max_attempts - 1:
                logger.warning(f"等待OHIF启动超时，但进程仍在运行")
        
        return process, url
    
    @staticmethod
    def stop_ohif_viewer(process):
        """停止OHIF查看器
        
        Args:
            process: 进程对象
            
        Returns:
            是否成功停止
        """
        if process is None or process.poll() is not None:
            return True
        
        try:
            if platform.system() == 'Windows':
                process.send_signal(signal.CTRL_BREAK_EVENT)
            else:
                os.killpg(os.getpgid(process.pid), signal.SIGTERM)
            
            # 等待进程退出
            for _ in range(10):
                if process.poll() is not None:
                    return True
                time.sleep(0.5)
            
            # 如果进程仍未退出，强制终止
            process.kill()
            process.wait()
            return True
        except Exception as e:
            logger.error(f"停止OHIF查看器时出错: {str(e)}")
            return False