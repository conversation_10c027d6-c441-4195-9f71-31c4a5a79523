#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO11x训练启动脚本
专门负责模型训练，数据准备请使用 create_yolo_dataset.py
"""

import os
import sys
from pathlib import Path

def main():
    print("🚀 YOLO11x 模型训练启动器")
    print("=" * 50)
    
    # 检查当前目录
    current_dir = Path.cwd()
    print(f"当前工作目录: {current_dir}")
    
    # 检查YOLO数据集是否存在
    print("\n📁 检查YOLO数据集...")
    
    # 检查多个可能的数据集位置
    possible_dataset_paths = [
        current_dir / "yolo_dataset_output" / "yolo_dataset",  # create_yolo_dataset.py 默认输出
        current_dir / "yolo11x_training_output" / "yolo_dataset",  # 训练输出目录
        current_dir / "yolo_dataset"  # 直接在根目录
    ]
    
    dataset_path = None
    config_file = None
    
    for path in possible_dataset_paths:
        if path.exists():
            # 检查是否有有效的配置文件
            for config_name in ["dataset.yaml", "data.yaml"]:
                potential_config = path / config_name
                if potential_config.exists():
                    # 检查基本目录结构
                    train_dir = path / "images" / "train"
                    val_dir = path / "images" / "val"
                    if train_dir.exists() and val_dir.exists():
                        dataset_path = path
                        config_file = potential_config
                        break
            if dataset_path:
                break
    
    if not dataset_path:
        print("❌ 未找到有效的YOLO数据集")
        print("\n📋 请先运行数据准备脚本:")
        print("   python create_yolo_dataset.py")
        print("\n💡 数据准备完成后，再运行此训练脚本")
        print("\n📁 期望的数据集结构:")
        print("   yolo_dataset_output/yolo_dataset/")
        print("   ├── dataset.yaml")
        print("   ├── images/")
        print("   │   ├── train/")
        print("   │   ├── val/")
        print("   │   └── test/")
        print("   └── labels/")
        print("       ├── train/")
        print("       ├── val/")
        print("       └── test/")
        return
    
    print(f"✅ 找到YOLO数据集: {dataset_path}")
    print(f"✅ 配置文件: {config_file}")
    
    # 统计数据集信息
    train_images = len(list((dataset_path / "images" / "train").glob("*.jpg")))
    val_images = len(list((dataset_path / "images" / "val").glob("*.jpg")))
    test_images = len(list((dataset_path / "images" / "test").glob("*.jpg")))
    
    print(f"📊 数据集统计:")
    print(f"   训练集: {train_images} 张图像")
    print(f"   验证集: {val_images} 张图像")
    print(f"   测试集: {test_images} 张图像")
    print(f"   总计: {train_images + val_images + test_images} 张图像")
    
    print("\n📋 训练配置:")
    print("- 模型: YOLO11x (从头开始训练)")
    print("- 图像尺寸: 640x640")
    print("- 训练轮数: 200")
    print("- 批次大小: 16")
    print("- 学习率: 0.01")
    print("- 输出目录: ./yolo11x_training_output")
    print(f"- 数据集: {dataset_path}")
    print(f"- 配置文件: {config_file}")
    print("- 数据准备: ✅ 已完成 (跳过数据集创建)")
    
    # 确认开始训练
    response = input("\n是否开始训练? (y/n): ").lower().strip()
    if response != 'y':
        print("训练已取消")
        return
    
    print("\n🔥 开始训练...")
    
    # 导入并运行训练脚本
    try:
        from train_yolo11x_from_scratch import YOLO11xTrainer
        
        # 创建训练器（只用于训练，不创建数据集）
        trainer = YOLO11xTrainer(
            dataset_root='./dataset',  # 原始数据路径（用于兼容性）
            output_root='./yolo11x_training_output',
            img_size=640
        )
        
        # 直接使用现有数据集进行训练
        print("\n🔥 开始从头训练YOLO11x模型...")
        trainer.train_from_scratch(
            config_path=str(config_file),
            epochs=200,
            batch_size=16,
            learning_rate=0.01
        )
        
        print("\n🎉 训练完成!")
        print("📁 查看结果: ./yolo11x_training_output")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保train_yolo11x_from_scratch.py文件存在")
    except Exception as e:
        print(f"❌ 训练错误: {e}")

if __name__ == "__main__":
    main()