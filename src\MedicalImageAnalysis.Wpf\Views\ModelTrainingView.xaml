<UserControl x:Class="MedicalImageAnalysis.Wpf.Views.ModelTrainingView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="24">
            <!-- 标题 -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                <materialDesign:PackIcon Kind="Brain"
                                       Width="32" Height="32"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                <TextBlock Text="AI模型训练中心"
                         FontSize="28"
                         FontWeight="Medium"
                         VerticalAlignment="Center"/>
            </StackPanel>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧：数据集管理 -->
                <materialDesign:Card Grid.Column="0"
                                   Margin="0,0,12,0"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel Margin="24">
                        <TextBlock Text="数据集管理"
                                 FontSize="20"
                                 FontWeight="Medium"
                                 Margin="0,0,0,16"/>

                        <!-- 数据集选择 -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="选择数据集" FontWeight="Medium" Margin="0,0,0,8"/>
                            <ComboBox x:Name="DatasetComboBox"
                                    materialDesign:HintAssist.Hint="选择训练数据集"
                                    SelectionChanged="DatasetComboBox_SelectionChanged">
                                <ComboBoxItem Content="医学影像数据集 v1.0"/>
                                <ComboBoxItem Content="DICOM标注数据集"/>
                                <ComboBoxItem Content="自定义数据集"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- 数据集信息 -->
                        <materialDesign:Card Background="{DynamicResource MaterialDesignCardBackground}"
                                           Margin="0,0,0,16">
                            <StackPanel Margin="16">
                                <TextBlock Text="数据集信息" FontWeight="Medium" Margin="0,0,0,8"/>
                                <StackPanel x:Name="DatasetInfoPanel">
                                    <TextBlock x:Name="DatasetImageCountText" Text="图像数量: 未选择" Margin="0,2"/>
                                    <TextBlock x:Name="DatasetAnnotationCountText" Text="标注数量: 未选择" Margin="0,2"/>
                                    <TextBlock x:Name="DatasetSizeText" Text="数据集大小: 未选择" Margin="0,2"/>
                                    <TextBlock x:Name="DatasetClassesText" Text="类别数量: 未选择" Margin="0,2"/>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 数据集操作 -->
                        <StackPanel>
                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,4"
                                  Click="BrowseDataset_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FolderOpen"
                                                               Width="16" Height="16"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="浏览数据集"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,4"
                                  Click="PreviewDataset_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Eye"
                                                               Width="16" Height="16"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="预览数据集"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,4"
                                  Click="ValidateDataset_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="CheckCircle"
                                                               Width="16" Height="16"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="验证数据集"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,4"
                                  Click="DataAugmentation_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="AutoFix"
                                                               Width="16" Height="16"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="数据增强"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,4"
                                  Click="DownloadDatasets_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Download"
                                                               Width="16" Height="16"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="下载开源数据集"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 右侧：训练配置 -->
                <materialDesign:Card Grid.Column="1"
                                   Margin="12,0,0,0"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel Margin="24">
                        <TextBlock Text="训练配置"
                                 FontSize="20"
                                 FontWeight="Medium"
                                 Margin="0,0,0,16"/>

                        <!-- 训练方法选择 -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="训练方法" FontWeight="Medium" Margin="0,0,0,8"/>
                            <ComboBox x:Name="TrainingMethodComboBox"
                                    materialDesign:HintAssist.Hint="选择训练方法"
                                    SelectionChanged="TrainingMethodComboBox_SelectionChanged">
                                <ComboBoxItem Content="YOLO (目标检测)" Tag="YOLO"/>
                                <ComboBoxItem Content="nnUNet (医学图像分割)" Tag="nnUNet"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- YOLO模型选择 -->
                        <StackPanel x:Name="YoloModelPanel" Margin="0,0,0,16">
                            <TextBlock Text="YOLO模型架构" FontWeight="Medium" Margin="0,0,0,8"/>
                            <ComboBox x:Name="ModelArchitectureComboBox"
                                    materialDesign:HintAssist.Hint="选择模型架构"
                                    SelectedIndex="0">
                                <ComboBoxItem Content="YOLOv11n (轻量级)"/>
                                <ComboBoxItem Content="YOLOv11s (小型)"/>
                                <ComboBoxItem Content="YOLOv11m (中型)"/>
                                <ComboBoxItem Content="YOLOv11l (大型)"/>
                                <ComboBoxItem Content="YOLOv11x (超大型)"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- nnUNet模型选择 -->
                        <StackPanel x:Name="NnUNetModelPanel" Margin="0,0,0,16" Visibility="Collapsed">
                            <TextBlock Text="nnUNet网络架构" FontWeight="Medium" Margin="0,0,0,8"/>
                            <ComboBox x:Name="NnUNetArchitectureComboBox"
                                    materialDesign:HintAssist.Hint="选择网络架构"
                                    SelectedIndex="2">
                                <ComboBoxItem Content="2D U-Net" Tag="TwoD"/>
                                <ComboBoxItem Content="3D U-Net (低分辨率)" Tag="ThreeD_LowRes"/>
                                <ComboBoxItem Content="3D U-Net (全分辨率)" Tag="ThreeD_FullRes"/>
                                <ComboBoxItem Content="3D U-Net (级联)" Tag="ThreeD_Cascade"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- YOLO训练参数 -->
                        <Expander x:Name="YoloTrainingParams" Header="YOLO训练参数" IsExpanded="True" Margin="0,0,0,16">
                            <StackPanel Margin="16,8,0,8">
                                <!-- 训练轮数 -->
                                <StackPanel Margin="0,0,0,12">
                                    <TextBlock Text="训练轮数 (Epochs)" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <Slider x:Name="EpochsSlider"
                                          Minimum="10"
                                          Maximum="500"
                                          Value="100"
                                          TickFrequency="10"
                                          IsSnapToTickEnabled="True"/>
                                    <TextBlock Text="{Binding ElementName=EpochsSlider, Path=Value, StringFormat=F0}"
                                             HorizontalAlignment="Center"
                                             FontSize="12"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                </StackPanel>

                                <!-- 批次大小 -->
                                <StackPanel Margin="0,0,0,12">
                                    <TextBlock Text="批次大小 (Batch Size)" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <Slider x:Name="BatchSizeSlider"
                                          Minimum="1"
                                          Maximum="32"
                                          Value="8"
                                          TickFrequency="1"
                                          IsSnapToTickEnabled="True"/>
                                    <TextBlock Text="{Binding ElementName=BatchSizeSlider, Path=Value, StringFormat=F0}"
                                             HorizontalAlignment="Center"
                                             FontSize="12"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                </StackPanel>

                                <!-- 学习率 -->
                                <StackPanel Margin="0,0,0,12">
                                    <TextBlock Text="学习率 (Learning Rate)" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <Slider x:Name="LearningRateSlider"
                                          Minimum="0.0001"
                                          Maximum="0.1"
                                          Value="0.001"
                                          TickFrequency="0.0001"
                                          IsSnapToTickEnabled="True"/>
                                    <TextBlock Text="{Binding ElementName=LearningRateSlider, Path=Value, StringFormat=F4}"
                                             HorizontalAlignment="Center"
                                             FontSize="12"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                </StackPanel>
                            </StackPanel>
                        </Expander>

                        <!-- nnUNet训练参数 -->
                        <Expander x:Name="NnUNetTrainingParams" Header="nnUNet训练参数" IsExpanded="True" Margin="0,0,0,16" Visibility="Collapsed">
                            <StackPanel Margin="16,8,0,8">
                                <!-- 数据集ID -->
                                <StackPanel Margin="0,0,0,12">
                                    <TextBlock Text="数据集ID" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <TextBox x:Name="DatasetIdTextBox"
                                           materialDesign:HintAssist.Hint="输入数据集ID (例如: 1)"
                                           Text="1"/>
                                </StackPanel>

                                <!-- 数据集名称 -->
                                <StackPanel Margin="0,0,0,12">
                                    <TextBlock Text="数据集名称" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <TextBox x:Name="DatasetNameTextBox"
                                           materialDesign:HintAssist.Hint="输入数据集名称 (例如: MedicalDemo)"
                                           Text="MedicalDemo"/>
                                </StackPanel>

                                <!-- 最大训练轮数 -->
                                <StackPanel Margin="0,0,0,12">
                                    <TextBlock Text="最大训练轮数" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <Slider x:Name="NnUNetEpochsSlider"
                                          Minimum="100"
                                          Maximum="2000"
                                          Value="1000"
                                          TickFrequency="100"
                                          IsSnapToTickEnabled="True"/>
                                    <TextBlock Text="{Binding ElementName=NnUNetEpochsSlider, Path=Value, StringFormat=F0}"
                                             HorizontalAlignment="Center"
                                             FontSize="12"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                </StackPanel>

                                <!-- 批次大小 -->
                                <StackPanel Margin="0,0,0,12">
                                    <TextBlock Text="批次大小" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <Slider x:Name="NnUNetBatchSizeSlider"
                                          Minimum="1"
                                          Maximum="8"
                                          Value="2"
                                          TickFrequency="1"
                                          IsSnapToTickEnabled="True"/>
                                    <TextBlock Text="{Binding ElementName=NnUNetBatchSizeSlider, Path=Value, StringFormat=F0}"
                                             HorizontalAlignment="Center"
                                             FontSize="12"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                </StackPanel>

                                <!-- 学习率 -->
                                <StackPanel Margin="0,0,0,12">
                                    <TextBlock Text="学习率" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <Slider x:Name="NnUNetLearningRateSlider"
                                          Minimum="0.001"
                                          Maximum="0.1"
                                          Value="0.01"
                                          TickFrequency="0.001"
                                          IsSnapToTickEnabled="False"/>
                                    <TextBlock Text="{Binding ElementName=NnUNetLearningRateSlider, Path=Value, StringFormat=F3}"
                                             HorizontalAlignment="Center"
                                             FontSize="12"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                </StackPanel>

                                <!-- 交叉验证折数 -->
                                <StackPanel Margin="0,0,0,12">
                                    <TextBlock Text="交叉验证折数" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <Slider x:Name="NnUNetFoldSlider"
                                          Minimum="0"
                                          Maximum="4"
                                          Value="0"
                                          TickFrequency="1"
                                          IsSnapToTickEnabled="True"/>
                                    <TextBlock Text="{Binding ElementName=NnUNetFoldSlider, Path=Value, StringFormat=F0}"
                                             HorizontalAlignment="Center"
                                             FontSize="12"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                </StackPanel>
                            </StackPanel>
                        </Expander>

                        <!-- YOLO高级设置 -->
                        <Expander x:Name="YoloAdvancedSettings" Header="YOLO高级设置" Margin="0,0,0,16">
                            <StackPanel Margin="16,8,0,8">
                                <CheckBox Content="使用预训练权重" IsChecked="True" Margin="0,4"/>
                                <CheckBox Content="启用混合精度训练" IsChecked="False" Margin="0,4"/>
                                <CheckBox Content="启用早停机制" IsChecked="True" Margin="0,4"/>
                                <CheckBox Content="保存最佳模型" IsChecked="True" Margin="0,4"/>
                            </StackPanel>
                        </Expander>

                        <!-- nnUNet高级设置 -->
                        <Expander x:Name="NnUNetAdvancedSettings" Header="nnUNet高级设置" Margin="0,0,0,16" Visibility="Collapsed">
                            <StackPanel Margin="16,8,0,8">
                                <CheckBox x:Name="UseMixedPrecisionCheckBox" Content="启用混合精度训练" IsChecked="True" Margin="0,4"/>
                                <CheckBox x:Name="EnableDataAugmentationCheckBox" Content="启用数据增强" IsChecked="True" Margin="0,4"/>
                                <CheckBox x:Name="UseDeepSupervisionCheckBox" Content="使用深度监督" IsChecked="True" Margin="0,4"/>
                                <CheckBox x:Name="SaveFinalCheckpointCheckBox" Content="保存最终检查点" IsChecked="True" Margin="0,4"/>
                                <CheckBox x:Name="ContinueTrainingCheckBox" Content="继续训练" IsChecked="False" Margin="0,4"/>
                                <CheckBox x:Name="DisableProgressBarCheckBox" Content="禁用进度条" IsChecked="False" Margin="0,4"/>

                                <!-- 验证频率 -->
                                <StackPanel Margin="0,8,0,4">
                                    <TextBlock Text="验证频率 (每N个epoch)" FontWeight="Medium" Margin="0,0,0,4"/>
                                    <Slider x:Name="ValidationFrequencySlider"
                                          Minimum="10"
                                          Maximum="100"
                                          Value="50"
                                          TickFrequency="10"
                                          IsSnapToTickEnabled="True"/>
                                    <TextBlock Text="{Binding ElementName=ValidationFrequencySlider, Path=Value, StringFormat=F0}"
                                             HorizontalAlignment="Center"
                                             FontSize="12"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                </StackPanel>

                                <!-- 设备选择 -->
                                <StackPanel Margin="0,8,0,4">
                                    <TextBlock Text="计算设备" FontWeight="Medium" Margin="0,0,0,4"/>
                                    <ComboBox x:Name="DeviceComboBox" SelectedIndex="0">
                                        <ComboBoxItem Content="CUDA (GPU)" Tag="cuda"/>
                                        <ComboBoxItem Content="CPU" Tag="cpu"/>
                                    </ComboBox>
                                </StackPanel>
                            </StackPanel>
                        </Expander>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- 训练控制和监控 -->
            <materialDesign:Card Margin="0,24,0,0" materialDesign:ElevationAssist.Elevation="Dp2">
                <StackPanel Margin="24">
                    <TextBlock Text="训练控制与监控"
                             FontSize="20"
                             FontWeight="Medium"
                             Margin="0,0,0,16"/>

                    <!-- 训练控制按钮 -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                        <Button x:Name="StartTrainingButton"
                              Style="{StaticResource MaterialDesignRaisedButton}"
                              Margin="0,0,8,0"
                              Click="StartTraining_Click">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Play"
                                                           Width="16" Height="16"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="开始训练"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button x:Name="PauseTrainingButton"
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Margin="0,0,8,0"
                              IsEnabled="False"
                              Click="PauseTraining_Click">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Pause"
                                                           Width="16" Height="16"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="暂停"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button x:Name="StopTrainingButton"
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Margin="0,0,8,0"
                              IsEnabled="False"
                              Click="StopTraining_Click">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Stop"
                                                           Width="16" Height="16"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="停止"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                              Margin="0,0,8,0"
                              Click="ViewLogs_Click">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileDocument"
                                                           Width="16" Height="16"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="查看日志"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </StackPanel>

                    <!-- 训练进度 -->
                    <materialDesign:Card x:Name="TrainingProgressCard"
                                       Background="{DynamicResource MaterialDesignCardBackground}"
                                       Visibility="Collapsed"
                                       Margin="0,0,0,16">
                        <StackPanel Margin="16">
                            <TextBlock Text="训练进度" FontWeight="Medium" Margin="0,0,0,12"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 左侧进度信息 -->
                                <StackPanel Grid.Column="0" Margin="0,0,16,0">
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <TextBlock Text="当前轮次: " FontWeight="Medium"/>
                                        <TextBlock x:Name="CurrentEpochText" Text="0/100"/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <TextBlock Text="训练损失: " FontWeight="Medium"/>
                                        <TextBlock x:Name="TrainingLossText" Text="0.000"/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <TextBlock Text="验证损失: " FontWeight="Medium"/>
                                        <TextBlock x:Name="ValidationLossText" Text="0.000"/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <TextBlock Text="准确率: " FontWeight="Medium"/>
                                        <TextBlock x:Name="AccuracyText" Text="0.00%"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- 右侧进度条 -->
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="总体进度" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <ProgressBar x:Name="OverallProgressBar"
                                               Height="20"
                                               Style="{StaticResource MaterialDesignLinearProgressBar}"
                                               Value="0"
                                               Maximum="100"/>
                                    <TextBlock x:Name="OverallProgressText"
                                             Text="0%"
                                             HorizontalAlignment="Center"
                                             FontSize="12"
                                             Margin="0,4,0,0"/>

                                    <TextBlock Text="当前轮次进度" FontWeight="Medium" Margin="0,12,0,8"/>
                                    <ProgressBar x:Name="EpochProgressBar"
                                               Height="20"
                                               Style="{StaticResource MaterialDesignLinearProgressBar}"
                                               Value="0"
                                               Maximum="100"/>
                                    <TextBlock x:Name="EpochProgressText"
                                             Text="0%"
                                             HorizontalAlignment="Center"
                                             FontSize="12"
                                             Margin="0,4,0,0"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 训练状态 -->
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon x:Name="TrainingStatusIcon"
                                               Kind="Circle"
                                               Width="16" Height="16"
                                               Foreground="Gray"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock x:Name="TrainingStatusText"
                                 Text="准备就绪"
                                 VerticalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!-- 模型管理 -->
            <materialDesign:Card Margin="0,24,0,0" materialDesign:ElevationAssist.Elevation="Dp2">
                <StackPanel Margin="24">
                    <TextBlock Text="模型管理"
                             FontSize="20"
                             FontWeight="Medium"
                             Margin="0,0,0,16"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 已训练模型列表 -->
                        <StackPanel Grid.Column="0" Margin="0,0,12,0">
                            <TextBlock Text="已训练模型" FontWeight="Medium" Margin="0,0,0,8"/>
                            <ListView x:Name="TrainedModelsListView"
                                    Height="150"
                                    SelectionChanged="TrainedModelsListView_SelectionChanged">
                                <ListView.View>
                                    <GridView>
                                        <GridViewColumn Header="模型名称" Width="120">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <TextBlock Text="{Binding Name}"/>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>
                                        <GridViewColumn Header="准确率" Width="80">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <TextBlock Text="{Binding Accuracy, StringFormat={}{0:F2}%}"/>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>
                                        <GridViewColumn Header="创建时间" Width="100">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <TextBlock Text="{Binding CreatedTime, StringFormat=MM-dd HH:mm}"/>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>
                                    </GridView>
                                </ListView.View>
                            </ListView>
                        </StackPanel>

                        <!-- 模型操作 -->
                        <StackPanel Grid.Column="1" Margin="12,0,0,0">
                            <TextBlock Text="模型操作" FontWeight="Medium" Margin="0,0,0,8"/>
                            <Button Content="测试模型"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,4"
                                  Click="TestModel_Click"/>
                            <Button Content="导出模型"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,4"
                                  Click="ExportModel_Click"/>
                            <Button Content="部署模型"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,4"
                                  Click="DeployModel_Click"/>
                            <Button Content="删除模型"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,4"
                                  Click="DeleteModel_Click"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </ScrollViewer>
</UserControl>
