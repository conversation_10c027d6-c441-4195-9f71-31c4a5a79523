#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证预处理切片数据的脚本
检查是否能正确读取2076个标签
"""

import os
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verify_sliced_data():
    """
    验证sliced_output目录中的数据
    """
    logger.info("开始验证预切片数据...")
    
    # 定义路径
    sliced_images_dir = Path("e:/Trae/yolo_ohif/sliced_output/sliced_images/image_T2")
    bbox_labels_dir = Path("e:/Trae/yolo_ohif/sliced_output/bbox_labels")
    
    if not sliced_images_dir.exists():
        logger.error(f"切片图像目录不存在: {sliced_images_dir}")
        return False
    if not bbox_labels_dir.exists():
        logger.error(f"标签目录不存在: {bbox_labels_dir}")
        return False
    
    # 获取所有图像文件
    image_files = list(sliced_images_dir.glob("*.jpg"))
    logger.info(f"找到 {len(image_files)} 个图像文件")
    
    # 获取所有标签文件
    label_files = list(bbox_labels_dir.glob("*.txt"))
    logger.info(f"找到 {len(label_files)} 个标签文件")
    
    positive_samples = 0
    negative_samples = 0
    
    # 检查每个图像是否有对应的标签
    for img_file in image_files:
        label_file = bbox_labels_dir / f"{img_file.stem}.txt"
        
        if label_file.exists():
            positive_samples += 1
        else:
            negative_samples += 1
    
    logger.info(f"验证结果:")
    logger.info(f"  - 总图像数: {len(image_files)}")
    logger.info(f"  - 总标签数: {len(label_files)}")
    logger.info(f"  - 正样本(有标签): {positive_samples}")
    logger.info(f"  - 负样本(无标签): {negative_samples}")
    
    # 检查标签文件内容
    if label_files:
        sample_label = label_files[0]
        with open(sample_label, 'r') as f:
            content = f.read().strip()
        logger.info(f"标签文件示例 ({sample_label.name}): {content}")
    
    # 验证是否达到预期的2076个标签
    if len(label_files) >= 2076:
        logger.info(f"✓ 标签数量符合预期 (>= 2076)")
        return True
    else:
        logger.warning(f"⚠ 标签数量少于预期 (< 2076)")
        return False

if __name__ == "__main__":
    verify_sliced_data()