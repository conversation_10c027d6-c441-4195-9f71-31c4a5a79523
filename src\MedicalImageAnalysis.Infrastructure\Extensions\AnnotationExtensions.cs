using MedicalImageAnalysis.Core.Entities;

namespace MedicalImageAnalysis.Infrastructure.Extensions;

/// <summary>
/// 标注扩展方法
/// </summary>
public static class AnnotationExtensions
{
    /// <summary>
    /// 克隆标注对象
    /// </summary>
    public static Annotation Clone(this Annotation annotation)
    {
        return new Annotation
        {
            Id = annotation.Id,
            Label = annotation.Label,
            BoundingBox = annotation.BoundingBox,
            Confidence = annotation.Confidence,
            CreatedAt = annotation.CreatedAt,
            UpdatedAt = DateTime.UtcNow,
            CreatedBy = annotation.CreatedBy,
            InstanceId = annotation.InstanceId,
            Type = annotation.Type,
            Description = annotation.Description,
            VerificationStatus = annotation.VerificationStatus,
            VerifiedBy = annotation.VerifiedBy,
            Source = annotation.Source
        };
    }

    /// <summary>
    /// 计算两个标注的重叠面积
    /// </summary>
    public static double CalculateOverlapArea(this Annotation annotation1, Annotation annotation2, int imageWidth = 1000, int imageHeight = 1000)
    {
        var bbox1 = annotation1.BoundingBox;
        var bbox2 = annotation2.BoundingBox;

        // 转换为绝对坐标
        var abs1 = bbox1.ToAbsolute(imageWidth, imageHeight);
        var abs2 = bbox2.ToAbsolute(imageWidth, imageHeight);

        var left = Math.Max(abs1.X, abs2.X);
        var top = Math.Max(abs1.Y, abs2.Y);
        var right = Math.Min(abs1.X + abs1.Width, abs2.X + abs2.Width);
        var bottom = Math.Min(abs1.Y + abs1.Height, abs2.Y + abs2.Height);

        if (left < right && top < bottom)
        {
            return (right - left) * (bottom - top);
        }

        return 0;
    }

    /// <summary>
    /// 计算两个标注的IoU（交并比）
    /// </summary>
    public static double CalculateIoU(this Annotation annotation1, Annotation annotation2)
    {
        var overlapArea = annotation1.CalculateOverlapArea(annotation2);
        
        if (overlapArea == 0)
            return 0;

        var area1 = annotation1.BoundingBox.Width * annotation1.BoundingBox.Height;
        var area2 = annotation2.BoundingBox.Width * annotation2.BoundingBox.Height;
        var unionArea = area1 + area2 - overlapArea;

        return unionArea > 0 ? overlapArea / unionArea : 0;
    }

    /// <summary>
    /// 计算标注的中心点
    /// </summary>
    public static (double X, double Y) GetCenter(this Annotation annotation)
    {
        var bbox = annotation.BoundingBox;
        // BoundingBox已经是中心点坐标系统，直接返回中心点
        return (bbox.CenterX, bbox.CenterY);
    }

    /// <summary>
    /// 计算标注的面积
    /// </summary>
    public static double GetArea(this Annotation annotation)
    {
        return annotation.BoundingBox.Width * annotation.BoundingBox.Height;
    }

    /// <summary>
    /// 计算标注的长宽比
    /// </summary>
    public static double GetAspectRatio(this Annotation annotation)
    {
        var rect = annotation.BoundingBox;
        return rect.Height > 0 ? (double)rect.Width / rect.Height : 0;
    }

    /// <summary>
    /// 检查标注是否在指定区域内
    /// </summary>
    public static bool IsWithinBounds(this Annotation annotation, int imageWidth, int imageHeight)
    {
        var bbox = annotation.BoundingBox;
        var absoluteBox = bbox.ToAbsolute(imageWidth, imageHeight);

        return absoluteBox.X >= 0 && absoluteBox.Y >= 0 &&
               absoluteBox.X + absoluteBox.Width <= imageWidth &&
               absoluteBox.Y + absoluteBox.Height <= imageHeight;
    }

    /// <summary>
    /// 调整标注边界框以适应图像边界
    /// </summary>
    public static Annotation ClampToBounds(this Annotation annotation, int imageWidth, int imageHeight)
    {
        var cloned = annotation.Clone();
        var bbox = cloned.BoundingBox;

        // 将归一化坐标转换为绝对坐标
        var absoluteBox = bbox.ToAbsolute(imageWidth, imageHeight);

        // 限制在图像范围内
        var x = Math.Max(0, Math.Min(absoluteBox.X, imageWidth - 1));
        var y = Math.Max(0, Math.Min(absoluteBox.Y, imageHeight - 1));
        var width = Math.Max(1, Math.Min(absoluteBox.Width, imageWidth - x));
        var height = Math.Max(1, Math.Min(absoluteBox.Height, imageHeight - y));

        // 转换回归一化坐标
        cloned.BoundingBox = new BoundingBox
        {
            CenterX = (x + width / 2.0) / imageWidth,
            CenterY = (y + height / 2.0) / imageHeight,
            Width = (double)width / imageWidth,
            Height = (double)height / imageHeight
        };

        return cloned;
    }

    /// <summary>
    /// 扩展标注边界框
    /// </summary>
    public static Annotation ExpandBoundingBox(this Annotation annotation, double factor)
    {
        var cloned = annotation.Clone();
        var bbox = cloned.BoundingBox;

        // BoundingBox使用中心点坐标系统和归一化坐标
        var newWidth = bbox.Width * factor;
        var newHeight = bbox.Height * factor;

        cloned.BoundingBox = new BoundingBox
        {
            CenterX = bbox.CenterX,
            CenterY = bbox.CenterY,
            Width = newWidth,
            Height = newHeight
        };

        return cloned;
    }

    /// <summary>
    /// 计算两个标注中心点之间的距离
    /// </summary>
    public static double CalculateDistance(this Annotation annotation1, Annotation annotation2)
    {
        var center1 = annotation1.GetCenter();
        var center2 = annotation2.GetCenter();

        var dx = center1.X - center2.X;
        var dy = center1.Y - center2.Y;

        return Math.Sqrt(dx * dx + dy * dy);
    }

    /// <summary>
    /// 检查标注是否与另一个标注相似
    /// </summary>
    public static bool IsSimilarTo(this Annotation annotation1, Annotation annotation2, double threshold = 0.7)
    {
        // 基于多个特征计算相似度
        var iou = annotation1.CalculateIoU(annotation2);
        var labelMatch = annotation1.Label.Equals(annotation2.Label, StringComparison.OrdinalIgnoreCase) ? 1.0 : 0.0;
        var sizeRatio = Math.Min(annotation1.GetArea(), annotation2.GetArea()) / Math.Max(annotation1.GetArea(), annotation2.GetArea());
        
        var similarity = (iou + labelMatch + sizeRatio) / 3.0;
        return similarity >= threshold;
    }

    /// <summary>
    /// 获取标注的质量分数
    /// </summary>
    public static double GetQualityScore(this Annotation annotation)
    {
        // 基于多个因素计算质量分数
        var confidenceScore = annotation.Confidence;
        var validationScore = annotation.VerificationStatus == VerificationStatus.Approved ? 1.0 : 0.5;
        var sizeScore = Math.Min(1.0, annotation.GetArea() / 10000.0); // 归一化尺寸分数

        return (confidenceScore + validationScore + sizeScore) / 3.0;
    }

    /// <summary>
    /// 检查标注是否需要审查
    /// </summary>
    public static bool NeedsReview(this Annotation annotation, double confidenceThreshold = 0.7, double validationThreshold = 0.8)
    {
        return annotation.Confidence < confidenceThreshold ||
               annotation.VerificationStatus == VerificationStatus.NeedsRevision ||
               annotation.VerificationStatus == VerificationStatus.Pending;
    }

    /// <summary>
    /// 获取标注的描述性统计信息
    /// </summary>
    public static AnnotationStatistics GetStatistics(this IEnumerable<Annotation> annotations)
    {
        var annotationList = annotations.ToList();
        
        if (!annotationList.Any())
        {
            return new AnnotationStatistics();
        }

        var areas = annotationList.Select(a => a.GetArea()).ToList();
        var confidences = annotationList.Select(a => a.Confidence).ToList();
        var aspectRatios = annotationList.Select(a => a.GetAspectRatio()).ToList();

        return new AnnotationStatistics
        {
            Count = annotationList.Count,
            MeanArea = areas.Average(),
            MedianArea = GetMedian(areas),
            MinArea = areas.Min(),
            MaxArea = areas.Max(),
            MeanConfidence = confidences.Average(),
            MedianConfidence = GetMedian(confidences),
            MinConfidence = confidences.Min(),
            MaxConfidence = confidences.Max(),
            MeanAspectRatio = aspectRatios.Average(),
            ValidatedCount = annotationList.Count(a => a.VerificationStatus == VerificationStatus.Approved),
            LabelDistribution = annotationList.GroupBy(a => a.Label)
                .ToDictionary(g => g.Key, g => g.Count())
        };
    }

    private static double GetMedian(List<double> values)
    {
        var sorted = values.OrderBy(x => x).ToList();
        var count = sorted.Count;
        
        if (count % 2 == 0)
        {
            return (sorted[count / 2 - 1] + sorted[count / 2]) / 2.0;
        }
        else
        {
            return sorted[count / 2];
        }
    }
}

/// <summary>
/// 标注统计信息
/// </summary>
public class AnnotationStatistics
{
    public int Count { get; set; }
    public double MeanArea { get; set; }
    public double MedianArea { get; set; }
    public double MinArea { get; set; }
    public double MaxArea { get; set; }
    public double MeanConfidence { get; set; }
    public double MedianConfidence { get; set; }
    public double MinConfidence { get; set; }
    public double MaxConfidence { get; set; }
    public double MeanAspectRatio { get; set; }
    public int ValidatedCount { get; set; }
    public Dictionary<string, int> LabelDistribution { get; set; } = new();
    
    public double ValidationRate => Count > 0 ? (double)ValidatedCount / Count : 0;
}
