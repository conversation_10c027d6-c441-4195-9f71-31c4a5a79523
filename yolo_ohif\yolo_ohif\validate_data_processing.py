#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证数据处理结果是否符合预期
检查temp_images目录中的txt文件是否都对应有效的撕裂区域
"""

import os
import cv2
import numpy as np
import nibabel as nib
from pathlib import Path
import logging
from tqdm import tqdm
import matplotlib.pyplot as plt

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataProcessingValidator:
    def __init__(self, dataset_root, temp_images_dir, img_size=640):
        self.dataset_root = Path(dataset_root)
        self.temp_images_dir = Path(temp_images_dir)
        self.img_size = img_size
        
    def load_nii_image(self, nii_path):
        """加载NII图像"""
        try:
            nii_img = nib.load(str(nii_path))
            img_data = nii_img.get_fdata()
            return img_data
        except Exception as e:
            logger.error(f"加载NII文件失败 {nii_path}: {e}")
            return None
    
    def mask_to_bbox_info(self, mask, min_area_threshold=5):
        """获取mask的详细信息（适应小的冈上肌撕裂）"""
        # 找到mask中的非零像素
        coords = np.where(mask > 0)
        
        if len(coords[0]) == 0:
            return {
                'has_pixels': False,
                'area': 0,
                'bbox_valid': False,
                'reason': '无标签像素'
            }
        
        # 检查撕裂区域面积（降低阈值以检测小的冈上肌撕裂）
        tear_area = len(coords[0])
        if tear_area < min_area_threshold:
            return {
                'has_pixels': True,
                'area': tear_area,
                'bbox_valid': False,
                'reason': f'面积过小 ({tear_area} < {min_area_threshold})'
            }
        
        # 计算边界框
        y_min, y_max = coords[0].min(), coords[0].max()
        x_min, x_max = coords[1].min(), coords[1].max()
        
        # 检查边界框尺寸（降低阈值以适应小的撕裂区域）
        bbox_width = x_max - x_min + 1  # 加1确保至少1像素宽度
        bbox_height = y_max - y_min + 1  # 加1确保至少1像素高度
        
        if bbox_width < 3 or bbox_height < 3:
            return {
                'has_pixels': True,
                'area': tear_area,
                'bbox_valid': False,
                'bbox_size': (bbox_width, bbox_height),
                'reason': f'边界框过小 ({bbox_width}x{bbox_height})'
            }
        
        return {
            'has_pixels': True,
            'area': tear_area,
            'bbox_valid': True,
            'bbox_size': (bbox_width, bbox_height),
            'reason': '有效撕裂区域'
        }
    
    def validate_txt_file(self, txt_file):
        """验证单个txt文件是否应该存在"""
        img_file_name = txt_file.stem + '.jpg'
        
        # 解析文件名：例如 "130.nii_slice_000.jpg"
        parts = img_file_name.replace('.jpg', '').split('_slice_')
        if len(parts) != 2:
            return False, "文件名格式错误"
        
        nii_name = parts[0] + '.nii.gz'
        slice_idx = int(parts[1])
        
        # 查找对应的标签文件
        tear_labels_dir = self.dataset_root / "label_T2"
        label_file = tear_labels_dir / nii_name
        
        if not label_file.exists():
            return False, f"标签文件不存在: {nii_name}"
        
        # 加载标签volume
        label_volume = self.load_nii_image(label_file)
        if label_volume is None:
            return False, "无法加载标签文件"
        
        # 检查指定层面是否存在
        if slice_idx >= label_volume.shape[2]:
            return False, f"层面索引超出范围: {slice_idx} >= {label_volume.shape[2]}"
        
        label_slice = label_volume[:, :, slice_idx]
        
        # 调整尺寸并检查
        label_resized = cv2.resize(label_slice.astype(np.uint8), (self.img_size, self.img_size))
        info = self.mask_to_bbox_info(label_resized)
        
        return info['bbox_valid'], info['reason']
    
    def validate_all_txt_files(self):
        """验证所有txt文件"""
        if not self.temp_images_dir.exists():
            logger.error(f"临时目录不存在: {self.temp_images_dir}")
            return
        
        # 获取所有txt文件
        txt_files = list(self.temp_images_dir.glob('*.txt'))
        logger.info(f"找到 {len(txt_files)} 个txt文件")
        
        valid_files = []
        invalid_files = []
        
        for txt_file in tqdm(txt_files, desc="验证txt文件"):
            is_valid, reason = self.validate_txt_file(txt_file)
            
            if is_valid:
                valid_files.append((txt_file, reason))
            else:
                invalid_files.append((txt_file, reason))
                logger.warning(f"无效txt文件: {txt_file.name} - {reason}")
        
        # 统计结果
        logger.info(f"\n验证结果:")
        logger.info(f"  - 有效txt文件: {len(valid_files)} 个")
        logger.info(f"  - 无效txt文件: {len(invalid_files)} 个")
        
        if invalid_files:
            logger.warning(f"\n发现 {len(invalid_files)} 个不应该存在的txt文件:")
            for txt_file, reason in invalid_files:
                logger.warning(f"  - {txt_file.name}: {reason}")
        else:
            logger.info("\n✅ 所有txt文件都对应有效的撕裂区域！")
        
        return valid_files, invalid_files
    
    def check_missing_txt_files(self):
        """检查是否有应该生成txt文件但缺失的情况"""
        logger.info("检查是否有遗漏的txt文件...")
        
        # 获取所有jpg文件
        jpg_files = list(self.temp_images_dir.glob('*.jpg'))
        missing_txt = []
        
        for jpg_file in tqdm(jpg_files, desc="检查jpg文件"):
            txt_file = jpg_file.with_suffix('.txt')
            
            if not txt_file.exists():
                # 检查是否应该有txt文件
                is_valid, reason = self.validate_txt_file(txt_file)
                if is_valid:
                    missing_txt.append((jpg_file, reason))
        
        if missing_txt:
            logger.warning(f"\n发现 {len(missing_txt)} 个应该有txt文件但缺失的图像:")
            for jpg_file, reason in missing_txt:
                logger.warning(f"  - {jpg_file.name}: {reason}")
        else:
            logger.info("\n✅ 没有遗漏的txt文件！")
        
        return missing_txt
    
    def generate_summary_report(self):
        """生成总结报告"""
        logger.info("\n" + "="*60)
        logger.info("数据处理验证报告")
        logger.info("="*60)
        
        # 统计文件数量
        jpg_files = list(self.temp_images_dir.glob('*.jpg'))
        txt_files = list(self.temp_images_dir.glob('*.txt'))
        
        logger.info(f"文件统计:")
        logger.info(f"  - 图像文件 (.jpg): {len(jpg_files)} 个")
        logger.info(f"  - 标注文件 (.txt): {len(txt_files)} 个")
        logger.info(f"  - 标注率: {len(txt_files)/len(jpg_files)*100:.1f}%")
        
        # 验证txt文件
        valid_files, invalid_files = self.validate_all_txt_files()
        
        # 检查遗漏的txt文件
        missing_files = self.check_missing_txt_files()
        
        # 最终结论
        logger.info("\n" + "="*60)
        if len(invalid_files) == 0 and len(missing_files) == 0:
            logger.info("✅ 数据处理完全符合预期！")
            logger.info("   - 所有txt文件都对应有效的撕裂区域")
            logger.info("   - 没有遗漏应该生成的txt文件")
            logger.info("   - 没有多余的无效txt文件")
        else:
            logger.warning("⚠️  数据处理存在问题：")
            if invalid_files:
                logger.warning(f"   - {len(invalid_files)} 个无效txt文件需要删除")
            if missing_files:
                logger.warning(f"   - {len(missing_files)} 个txt文件缺失")
        logger.info("="*60)

def main():
    # 配置路径
    dataset_root = "e:/Trae/yolo_ohif/dataset"
    temp_images_dir = "e:/Trae/yolo_ohif/yolo_training_output/temp_images"
    
    # 创建验证器
    validator = DataProcessingValidator(dataset_root, temp_images_dir)
    
    # 生成验证报告
    validator.generate_summary_report()

if __name__ == "__main__":
    main()