# 冈上肌肌腱撕裂YOLO检测模型训练指南

本项目提供了一套完整的工具链，用于训练YOLO模型来检测冈上肌肌腱撕裂。

## 📁 数据集结构

确保您的数据集按以下结构组织：

```
dataset/
├── image_T2/          # 撕裂图像数据 (nii.gz格式)
│   ├── 130.nii.gz
│   ├── 131.nii.gz
│   └── ...
├── label_T2/          # 撕裂图像的冈上肌mask (nii.gz格式)
│   ├── 130.nii.gz
│   ├── 131.nii.gz
│   └── ...
└── image_T2_normal/   # 正常肩关节磁共振数据 (nii.gz格式)
    ├── 1.nii.gz
    ├── 2.nii.gz
    └── ...
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements_yolo_training.txt
```

### 2. 数据集可视化验证

在训练前，建议先可视化数据集以确保图像和标签正确匹配：

#### 方法一：使用交互式脚本
```bash
python visualize_examples.py
```

提供以下选项：
1. 快速检查数据集完整性
2. 可视化数据集样本（保存到文件）
3. 可视化特定图像（显示窗口）

#### 方法二：使用命令行工具
```bash
# 检查数据集完整性
python visualize_dataset.py --check_only

# 可视化训练集样本
python visualize_dataset.py --split train --num_samples 10 --save_dir ./visualization_results

# 可视化验证集样本
python visualize_dataset.py --split val --num_samples 5 --save_dir ./visualization_results
```

#### 方法三：使用Jupyter Notebook
```bash
jupyter notebook dataset_visualization.ipynb
```

提供交互式的可视化体验，包括：
- 数据集完整性检查
- 随机样本可视化
- 数据集统计分析
- 类别分布图表

### 3. 运行训练

**方法一：快速训练（推荐）**
```bash
python quick_train.py
```

**方法二：自定义训练**
```bash
python train_supraspinatus_yolo.py
```

## 📋 训练流程说明

### 数据预处理
1. **格式转换**：将nii.gz格式转换为YOLO可用的图像格式
2. **切片提取**：从3D医学图像中提取2D切片
3. **精确层面匹配**：
   - image_T2和label_T2精确匹配：逐层面对应处理
   - 完整图像保存：所有层面的图像都会被保存
   - 严格的撕裂区域检测：只有当label_T2某层面存在有效撕裂区域时，才生成对应的YOLO标签文件
   - 有效撕裂区域标准：面积≥5像素且边界框≥3x3像素（适应小的冈上肌撕裂）
   - 无标注层面处理：无撕裂区域或撕裂区域过小的层面只保存图像，不生成txt标签文件
   - 正常图像处理：正常图像不生成任何txt标注文件（符合YOLO训练要求）
   - 图像保持原样：不进行任何变换（旋转、镜像、翻转等）
   - image_T2_normal文件夹：所有层面作为正常样本，同样保持原样
4. **标注生成**：从有效mask生成YOLO格式的边界框标注
5. **质量控制**：过滤面积过小或尺寸不合理的标注
6. **数据增强**：应用旋转、翻转等增强技术

### 数据集划分
- **训练集**：70%
- **验证集**：20% 
- **测试集**：10%

### 模型配置
- **基础模型**：YOLO11x（最新版本，高精度，适合医学图像）
- **图像尺寸**：640x640
- **类别数**：1（冈上肌撕裂）

## ⚙️ 自定义配置

您可以修改 `train_supraspinatus_yolo.py` 中的参数：

```python
# 图像尺寸
img_size = 640

# 训练参数
epochs = 100
batch_size = 16

# 数据增强
augment_factor = 3

# 切片选择
slice_range = (0.3, 0.7)  # 选择中间30%-70%的切片

# 质量控制参数
min_area_threshold = 100  # 最小撕裂区域面积（像素数）
min_bbox_size = 10       # 最小边界框尺寸（像素）
```

## 🔍 数据质量控制

为确保训练数据质量，系统实施了多层质量控制：

### 精确层面处理
- **层面级别匹配**：确保图像层面和标签层面精确对应
- **撕裂区域验证**：只有确认存在mask=1的层面才生成标注
- **图像质量保证**：保持原始图像特征，无额外变换干扰
- **数据一致性**：统一的层面索引命名规则

### 标注级别验证
- **面积阈值**：撕裂区域面积必须≥100像素
- **尺寸检查**：边界框宽度和高度必须≥10像素
- **有效性验证**：确保生成的YOLO标注格式正确

### 数据统计
训练过程中会显示：
- 总切片数 vs 有效切片数
- 过滤掉的切片数量和原因
- 最终用于训练的样本统计

## 📊 输出文件

训练完成后，将在 `yolo_training_output/` 目录下生成：

```
yolo_training_output/
├── processed_data/           # 预处理后的数据
│   ├── images/
│   ├── labels/
│   └── dataset.yaml
├── supraspinatus_detection/  # 训练结果
│   ├── weights/
│   │   ├── best.pt          # 最佳模型权重
│   │   └── last.pt          # 最后一轮权重
│   ├── results.png          # 训练曲线
│   └── confusion_matrix.png # 混淆矩阵
└── yolo_training.log        # 训练日志
```

## 🔧 故障排除

### 常见问题

1. **内存不足**
   - 减少 `batch_size`
   - 减少 `img_size`
   - 减少 `augment_factor`

2. **训练速度慢**
   - 确保安装了CUDA版本的PyTorch
   - 检查GPU是否被正确识别

3. **数据加载错误**
   - 检查nii.gz文件是否损坏
   - 确保文件名匹配（image_T2和label_T2对应）

4. **标注质量差**
   - 检查mask文件质量
   - 调整 `min_area_threshold` 参数

### 日志查看

```bash
# 查看训练日志
tail -f yolo_training.log

# 查看TensorBoard（如果可用）
tensorboard --logdir yolo_training_output/supraspinatus_detection
```

## 📈 模型评估

训练完成后，可以使用以下指标评估模型性能：

- **mAP50**：IoU阈值0.5时的平均精度
- **mAP50-95**：IoU阈值0.5-0.95的平均精度
- **Precision**：精确率
- **Recall**：召回率

## 🔄 模型部署

训练完成的YOLO11模型可以直接集成到现有的DICOM查看器中：

1. 将 `best.pt` 复制到项目目录
2. 修改 `api.py` 中的模型路径
3. 重启服务器

**注意**：YOLO11x版本提供最高的检测精度，特别适合医学图像中的精确诊断需求

## 📝 注意事项

1. **数据质量**：确保mask标注准确，这直接影响模型性能
2. **计算资源**：建议使用GPU进行训练，CPU训练会非常慢
3. **存储空间**：预处理后的数据可能占用较大空间
4. **训练时间**：完整训练可能需要数小时到数天

## 🆘 技术支持

如遇到问题，请检查：
1. 依赖包是否正确安装
2. 数据集格式是否符合要求
3. 系统资源是否充足
4. 查看详细的错误日志

---

**祝您训练顺利！** 🎯