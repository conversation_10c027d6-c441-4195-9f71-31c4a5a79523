#!/usr/bin/env python3
"""
测试模型在正确数据集路径上的检测能力
使用训练时相同的数据集路径进行测试
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.detection_service import DetectionService
from src.config import YOLOConfig

def test_model_on_correct_dataset():
    """
    测试模型在正确数据集上的检测能力
    """
    # 使用正确的数据集路径（训练时使用的路径）
    TEST_IMAGE_DIR = "E:/Trae/yolo_ohif/yolo_dataset_output/yolo_dataset/images/test"
    TEST_LABEL_DIR = "E:/Trae/yolo_ohif/yolo_dataset_output/yolo_dataset/labels/test"
    
    # 模型路径
    MODEL_PATH = "E:/Trae/yolo_ohif/yolo11x_training_output/training_results/yolo11x_from_scratch_20250719_135134/weights/best.pt"
    
    print(f"测试图像目录: {TEST_IMAGE_DIR}")
    print(f"测试标签目录: {TEST_LABEL_DIR}")
    print(f"模型路径: {MODEL_PATH}")
    print("="*80)
    
    # 检查路径是否存在
    if not os.path.exists(TEST_IMAGE_DIR):
        print(f"错误：测试图像目录不存在: {TEST_IMAGE_DIR}")
        return
    
    if not os.path.exists(TEST_LABEL_DIR):
        print(f"错误：测试标签目录不存在: {TEST_LABEL_DIR}")
        return
        
    if not os.path.exists(MODEL_PATH):
        print(f"错误：模型文件不存在: {MODEL_PATH}")
        return
    
    # 获取有标签的图像列表
    label_files = [f for f in os.listdir(TEST_LABEL_DIR) if f.endswith('.txt')]
    print(f"找到 {len(label_files)} 个标签文件")
    
    if not label_files:
        print("没有找到标签文件！")
        return
    
    # 初始化检测服务
    try:
        detection_service = DetectionService(
            model_path=MODEL_PATH,
            confidence_threshold=0.1,  # 使用较低的置信度阈值
            iou_threshold=0.45
        )
        print("模型加载成功！")
    except Exception as e:
        print(f"模型加载失败: {e}")
        return
    
    # 测试前10个有标签的图像
    test_count = min(10, len(label_files))
    successful_detections = 0
    total_detections = 0
    
    print(f"\n开始测试前 {test_count} 个有标签的图像...")
    print("="*80)
    
    for i, label_file in enumerate(label_files[:test_count]):
        # 构建对应的图像文件路径
        image_name = label_file.replace('.txt', '.jpg')
        image_path = os.path.join(TEST_IMAGE_DIR, image_name)
        label_path = os.path.join(TEST_LABEL_DIR, label_file)
        
        if not os.path.exists(image_path):
            print(f"图像文件不存在: {image_path}")
            continue
        
        # 读取标签文件内容
        try:
            with open(label_path, 'r') as f:
                label_content = f.read().strip()
            ground_truth_count = len([line for line in label_content.split('\n') if line.strip()])
        except Exception as e:
            print(f"读取标签文件失败 {label_path}: {e}")
            continue
        
        # 进行检测
        try:
            results = detection_service.detect_objects(image_path)
            detected_count = len(results.get('detections', []))
            total_detections += detected_count
            
            if detected_count > 0:
                successful_detections += 1
            
            print(f"图像 {i+1}: {image_name}")
            print(f"  真实标注数量: {ground_truth_count}")
            print(f"  检测到数量: {detected_count}")
            
            if detected_count > 0:
                print(f"  检测结果:")
                for j, detection in enumerate(results['detections']):
                    conf = detection.get('confidence', 0)
                    cls = detection.get('class', 'unknown')
                    bbox = detection.get('bbox', {})
                    print(f"    检测 {j+1}: 类别={cls}, 置信度={conf:.3f}, 位置={bbox}")
            else:
                print(f"  未检测到任何目标")
            print()
            
        except Exception as e:
            print(f"检测失败 {image_path}: {e}")
            continue
    
    # 统计结果
    print("="*80)
    print("测试结果统计:")
    print(f"测试图像数量: {test_count}")
    print(f"成功检测图像数量: {successful_detections}")
    print(f"检测成功率: {successful_detections/test_count*100:.1f}%")
    print(f"总检测目标数量: {total_detections}")
    print(f"平均每张图像检测数量: {total_detections/test_count:.2f}")
    
    if successful_detections == 0:
        print("\n⚠️  警告：所有测试图像都没有检测到目标！")
        print("可能的原因：")
        print("1. 模型训练效果不佳")
        print("2. 置信度阈值设置过高")
        print("3. 测试图像与训练数据分布不匹配")
        print("4. 模型过拟合")
        
        # 尝试更低的置信度阈值
        print("\n尝试使用更低的置信度阈值 (0.01) 重新测试第一张图像...")
        if label_files:
            first_label = label_files[0]
            first_image = first_label.replace('.txt', '.jpg')
            first_image_path = os.path.join(TEST_IMAGE_DIR, first_image)
            
            try:
                detection_service_low = DetectionService(
                    model_path=MODEL_PATH,
                    confidence_threshold=0.01,  # 极低的置信度阈值
                    iou_threshold=0.45
                )
                
                results_low = detection_service_low.detect_objects(first_image_path)
                detected_count_low = len(results_low.get('detections', []))
                
                print(f"使用置信度阈值 0.01 检测结果: {detected_count_low} 个目标")
                
                if detected_count_low > 0:
                    for j, detection in enumerate(results_low['detections']):
                        conf = detection.get('confidence', 0)
                        cls = detection.get('class', 'unknown')
                        print(f"  检测 {j+1}: 类别={cls}, 置信度={conf:.4f}")
                else:
                    print("  即使使用极低置信度阈值也未检测到目标")
                    
            except Exception as e:
                print(f"低置信度测试失败: {e}")
    else:
        print(f"\n✅ 模型在有标签的测试图像上表现正常！")

if __name__ == "__main__":
    test_model_on_correct_dataset()