using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Models;
using PixelData = MedicalImageAnalysis.Core.Models.PixelData;
using ImageFormat = MedicalImageAnalysis.Core.Models.ImageFormat;

namespace MedicalImageAnalysis.Core.Interfaces;

/// <summary>
/// 图像处理服务接口，提供医学影像处理的核心功能
/// </summary>
public interface IImageProcessingService
{
    /// <summary>
    /// 图像预处理
    /// </summary>
    /// <param name="pixelData">原始像素数据</param>
    /// <param name="options">预处理选项</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>预处理后的像素数据</returns>
    Task<PixelData> PreprocessImageAsync(PixelData pixelData, ImagePreprocessingOptions options, CancellationToken cancellationToken = default);

    /// <summary>
    /// 图像增强
    /// </summary>
    /// <param name="pixelData">像素数据</param>
    /// <param name="enhancementType">增强类型</param>
    /// <param name="parameters">增强参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>增强后的像素数据</returns>
    Task<PixelData> EnhanceImageAsync(PixelData pixelData, ImageEnhancementType enhancementType, Dictionary<string, object> parameters, CancellationToken cancellationToken = default);

    /// <summary>
    /// 图像降噪
    /// </summary>
    /// <param name="pixelData">像素数据</param>
    /// <param name="denoiseType">降噪类型</param>
    /// <param name="strength">降噪强度 (0.0-1.0)</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>降噪后的像素数据</returns>
    Task<PixelData> DenoiseImageAsync(PixelData pixelData, DenoiseType denoiseType, double strength = 0.5, CancellationToken cancellationToken = default);

    /// <summary>
    /// 图像分割
    /// </summary>
    /// <param name="pixelData">像素数据</param>
    /// <param name="segmentationType">分割类型</param>
    /// <param name="parameters">分割参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分割结果</returns>
    Task<SegmentationResult> SegmentImageAsync(PixelData pixelData, SegmentationType segmentationType, Dictionary<string, object> parameters, CancellationToken cancellationToken = default);

    /// <summary>
    /// 图像配准
    /// </summary>
    /// <param name="fixedImage">固定图像</param>
    /// <param name="movingImage">移动图像</param>
    /// <param name="registrationType">配准类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配准结果</returns>
    Task<RegistrationResult> RegisterImagesAsync(PixelData fixedImage, PixelData movingImage, RegistrationType registrationType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 多平面重建 (MPR)
    /// </summary>
    /// <param name="volumeData">体数据</param>
    /// <param name="plane">重建平面</param>
    /// <param name="thickness">切片厚度</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重建后的图像</returns>
    Task<PixelData> MultiplanarReconstructionAsync(VolumeData volumeData, ReconstructionPlane plane, double thickness = 1.0, CancellationToken cancellationToken = default);

    /// <summary>
    /// 图像格式转换
    /// </summary>
    /// <param name="pixelData">像素数据</param>
    /// <param name="targetFormat">目标格式</param>
    /// <param name="quality">质量参数 (0-100)</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>转换后的图像数据</returns>
    Task<byte[]> ConvertImageFormatAsync(PixelData pixelData, ImageFormat targetFormat, int quality = 90, CancellationToken cancellationToken = default);

    /// <summary>
    /// 生成图像缩略图
    /// </summary>
    /// <param name="pixelData">像素数据</param>
    /// <param name="maxWidth">最大宽度</param>
    /// <param name="maxHeight">最大高度</param>
    /// <param name="maintainAspectRatio">是否保持宽高比</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>缩略图数据</returns>
    Task<byte[]> GenerateThumbnailAsync(PixelData pixelData, int maxWidth, int maxHeight, bool maintainAspectRatio = true, CancellationToken cancellationToken = default);

    /// <summary>
    /// 计算图像统计信息
    /// </summary>
    /// <param name="pixelData">像素数据</param>
    /// <returns>图像统计信息</returns>
    Task<ImageStatistics> CalculateImageStatisticsAsync(PixelData pixelData);

    /// <summary>
    /// 自动调整窗宽窗位
    /// </summary>
    /// <param name="pixelData">像素数据</param>
    /// <param name="percentile">百分位数 (默认 1% 和 99%)</param>
    /// <returns>推荐的窗宽窗位</returns>
    Task<(double WindowWidth, double WindowCenter)> AutoAdjustWindowLevelAsync(PixelData pixelData, double percentile = 0.01);
}

/// <summary>
/// 图像预处理选项
/// </summary>
public class ImagePreprocessingOptions
{
    /// <summary>
    /// 是否进行归一化
    /// </summary>
    public bool Normalize { get; set; } = true;

    /// <summary>
    /// 归一化范围
    /// </summary>
    public (double Min, double Max) NormalizationRange { get; set; } = (0.0, 1.0);

    /// <summary>
    /// 是否调整大小
    /// </summary>
    public bool Resize { get; set; } = false;

    /// <summary>
    /// 目标尺寸
    /// </summary>
    public (int Width, int Height) TargetSize { get; set; } = (512, 512);

    /// <summary>
    /// 插值方法
    /// </summary>
    public InterpolationMethod InterpolationMethod { get; set; } = InterpolationMethod.Bilinear;

    /// <summary>
    /// 是否进行直方图均衡化
    /// </summary>
    public bool HistogramEqualization { get; set; } = false;

    /// <summary>
    /// 是否应用 CLAHE (对比度限制自适应直方图均衡化)
    /// </summary>
    public bool ApplyClahe { get; set; } = false;

    /// <summary>
    /// CLAHE 参数
    /// </summary>
    public ClaheParameters ClaheParameters { get; set; } = new();
}

/// <summary>
/// CLAHE 参数
/// </summary>
public class ClaheParameters
{
    /// <summary>
    /// 对比度限制
    /// </summary>
    public double ClipLimit { get; set; } = 2.0;

    /// <summary>
    /// 网格大小
    /// </summary>
    public (int Width, int Height) TileGridSize { get; set; } = (8, 8);
}

/// <summary>
/// 体数据
/// </summary>
public class VolumeData
{
    /// <summary>
    /// 体素数据
    /// </summary>
    public Array Data { get; set; } = null!;

    /// <summary>
    /// 体积尺寸 (X, Y, Z)
    /// </summary>
    public (int X, int Y, int Z) Dimensions { get; set; }

    /// <summary>
    /// 体素间距 (X, Y, Z)
    /// </summary>
    public (double X, double Y, double Z) Spacing { get; set; }

    /// <summary>
    /// 数据类型
    /// </summary>
    public Type DataType { get; set; } = typeof(ushort);
}

/// <summary>
/// 分割结果
/// </summary>
public class SegmentationResult
{
    /// <summary>
    /// 分割掩码
    /// </summary>
    public PixelData Mask { get; set; } = null!;

    /// <summary>
    /// 分割区域集合
    /// </summary>
    public List<SegmentedRegion> Regions { get; set; } = new();

    /// <summary>
    /// 分割置信度
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// 处理时间 (毫秒)
    /// </summary>
    public long ProcessingTimeMs { get; set; }
}

/// <summary>
/// 分割区域
/// </summary>
public class SegmentedRegion
{
    /// <summary>
    /// 区域标签
    /// </summary>
    public int Label { get; set; }

    /// <summary>
    /// 区域名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 区域面积 (像素数)
    /// </summary>
    public int Area { get; set; }

    /// <summary>
    /// 边界框
    /// </summary>
    public BoundingBox BoundingBox { get; set; } = new();

    /// <summary>
    /// 轮廓点集合
    /// </summary>
    public List<Point2D> Contour { get; set; } = new();

    /// <summary>
    /// 区域质心
    /// </summary>
    public Point2D Centroid { get; set; } = new();
}

/// <summary>
/// 配准结果
/// </summary>
public class RegistrationResult
{
    /// <summary>
    /// 配准后的图像
    /// </summary>
    public PixelData RegisteredImage { get; set; } = null!;

    /// <summary>
    /// 变换矩阵
    /// </summary>
    public double[,] TransformMatrix { get; set; } = new double[4, 4];

    /// <summary>
    /// 配准误差
    /// </summary>
    public double RegistrationError { get; set; }

    /// <summary>
    /// 迭代次数
    /// </summary>
    public int Iterations { get; set; }

    /// <summary>
    /// 处理时间 (毫秒)
    /// </summary>
    public long ProcessingTimeMs { get; set; }
}

/// <summary>
/// 图像统计信息
/// </summary>
public class ImageStatistics
{
    /// <summary>
    /// 最小值
    /// </summary>
    public double Min { get; set; }

    /// <summary>
    /// 最大值
    /// </summary>
    public double Max { get; set; }

    /// <summary>
    /// 平均值
    /// </summary>
    public double Mean { get; set; }

    /// <summary>
    /// 标准差
    /// </summary>
    public double StandardDeviation { get; set; }

    /// <summary>
    /// 中位数
    /// </summary>
    public double Median { get; set; }

    /// <summary>
    /// 直方图
    /// </summary>
    public int[] Histogram { get; set; } = new int[256];

    /// <summary>
    /// 非零像素数量
    /// </summary>
    public int NonZeroPixelCount { get; set; }

    /// <summary>
    /// 总像素数量
    /// </summary>
    public int TotalPixelCount { get; set; }
}

/// <summary>
/// 图像增强类型枚举
/// </summary>
public enum ImageEnhancementType
{
    ContrastAdjustment = 1,     // 对比度调整
    BrightnessAdjustment = 2,   // 亮度调整
    GammaCorrection = 3,        // 伽马校正
    Sharpening = 4,             // 锐化
    EdgeEnhancement = 5,        // 边缘增强
    HistogramEqualization = 6,  // 直方图均衡化
    Clahe = 7                   // 对比度限制自适应直方图均衡化
}

/// <summary>
/// 降噪类型枚举
/// </summary>
public enum DenoiseType
{
    Gaussian = 1,           // 高斯滤波
    Median = 2,             // 中值滤波
    Bilateral = 3,          // 双边滤波
    NonLocalMeans = 4,      // 非局部均值
    Wiener = 5,             // 维纳滤波
    Anisotropic = 6         // 各向异性扩散
}

/// <summary>
/// 分割类型枚举
/// </summary>
public enum SegmentationType
{
    Threshold = 1,          // 阈值分割
    RegionGrowing = 2,      // 区域生长
    Watershed = 3,          // 分水岭算法
    ActiveContour = 4,      // 活动轮廓
    LevelSet = 5,           // 水平集
    GraphCut = 6,           // 图割
    DeepLearning = 7        // 深度学习分割
}

/// <summary>
/// 配准类型枚举
/// </summary>
public enum RegistrationType
{
    Rigid = 1,              // 刚性配准
    Affine = 2,             // 仿射配准
    Deformable = 3,         // 可变形配准
    NonRigid = 4,           // 非刚性配准
    Elastic = 5             // 弹性配准
}



/// <summary>
/// 插值方法枚举
/// </summary>
public enum InterpolationMethod
{
    NearestNeighbor = 1,    // 最近邻
    Bilinear = 2,           // 双线性
    Bicubic = 3,            // 双三次
    Lanczos = 4             // Lanczos
}

/// <summary>
/// 图像增强选项
/// </summary>
public class ImageEnhancementOptions
{
    /// <summary>
    /// 是否调整对比度
    /// </summary>
    public bool AdjustContrast { get; set; } = false;

    /// <summary>
    /// 对比度因子
    /// </summary>
    public double ContrastFactor { get; set; } = 1.0;

    /// <summary>
    /// 是否调整亮度
    /// </summary>
    public bool AdjustBrightness { get; set; } = false;

    /// <summary>
    /// 亮度因子
    /// </summary>
    public double BrightnessFactor { get; set; } = 0.0;

    /// <summary>
    /// 是否锐化
    /// </summary>
    public bool Sharpen { get; set; } = false;

    /// <summary>
    /// 锐化强度
    /// </summary>
    public double SharpenStrength { get; set; } = 1.0;
}

/// <summary>
/// 边缘检测选项
/// </summary>
public class EdgeDetectionOptions
{
    /// <summary>
    /// 边缘检测类型
    /// </summary>
    public EdgeDetectionType Type { get; set; } = EdgeDetectionType.Canny;

    /// <summary>
    /// 低阈值
    /// </summary>
    public double Threshold1 { get; set; } = 50;

    /// <summary>
    /// 高阈值
    /// </summary>
    public double Threshold2 { get; set; } = 150;

    /// <summary>
    /// 核大小
    /// </summary>
    public int KernelSize { get; set; } = 3;
}

/// <summary>
/// 分割选项
/// </summary>
public class SegmentationOptions
{
    /// <summary>
    /// 分割类型
    /// </summary>
    public SegmentationType Type { get; set; } = SegmentationType.Threshold;

    /// <summary>
    /// 阈值
    /// </summary>
    public double Threshold { get; set; } = 0.5;

    /// <summary>
    /// 最小区域大小
    /// </summary>
    public int MinRegionSize { get; set; } = 100;

    /// <summary>
    /// 最大区域大小
    /// </summary>
    public int MaxRegionSize { get; set; } = 10000;
}

/// <summary>
/// 边缘检测类型枚举
/// </summary>
public enum EdgeDetectionType
{
    Canny = 1,
    Sobel = 2,
    Laplacian = 3,
    Roberts = 4,
    Prewitt = 5
}

/// <summary>
/// 图像格式枚举
/// </summary>
public enum ImageFormat
{
    Png = 1,
    Jpeg = 2,
    Bmp = 3,
    Tiff = 4,
    Webp = 5
}


