#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的create_yolo_dataset.py与原始版本的一致性
特别针对Terminal#1027-1027等具体案例进行验证
"""

import os
import sys
import logging
import tempfile
import shutil
from pathlib import Path
import cv2
import numpy as np

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_specific_case():
    """
    测试特定案例：Terminal#1027-1027
    """
    logger.info("=== 测试特定案例：Terminal#1027-1027 ===")
    
    # 检查输入文件是否存在
    base_dir = Path("e:/Trae/yolo_ohif")
    input_dir = base_dir / "input"
    
    # 查找Terminal#1027-1027相关文件
    terminal_files = {
        'image_T2': None,
        'image_T2_normal': None,
        'label_T2': None
    }
    
    for subdir in ['image_T2', 'image_T2_normal', 'label_T2']:
        subdir_path = input_dir / subdir
        if subdir_path.exists():
            for file in subdir_path.glob("*1027*"):
                terminal_files[subdir] = file
                logger.info(f"找到文件: {subdir}/{file.name}")
    
    if not any(terminal_files.values()):
        logger.warning("未找到Terminal#1027-1027相关文件")
        return False
    
    # 测试原始流程
    logger.info("\n--- 测试原始分步流程 ---")
    test_original_workflow(terminal_files)
    
    # 测试合并流程
    logger.info("\n--- 测试合并流程 ---")
    test_merged_workflow(terminal_files)
    
    return True

def test_original_workflow(terminal_files):
    """
    测试原始的分步工作流程
    """
    try:
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 步骤1：运行slice_nii_to_jpg.py
            logger.info("步骤1：运行slice_nii_to_jpg.py")
            from slice_nii_to_jpg import NIISliceConverter
            
            converter = NIISliceConverter(
                input_dir="input",
                output_dir=str(temp_path / "sliced_output"),
                img_size=512
            )
            converter.run_conversion()
            
            # 步骤2：运行convert_label_to_bbox.py
            logger.info("步骤2：运行convert_label_to_bbox.py")
            from convert_label_to_bbox import process_label_images
            
            label_input_dir = temp_path / "sliced_output" / "sliced_images" / "label_T2"
            label_output_dir = temp_path / "original_labels"
            
            if label_input_dir.exists():
                process_label_images(str(label_input_dir), str(label_output_dir))
                
                # 检查Terminal#1027-1027的结果
                check_terminal_results(label_output_dir, "原始流程")
            else:
                logger.warning(f"标签目录不存在: {label_input_dir}")
                
    except Exception as e:
        logger.error(f"原始流程测试失败: {e}")

def test_merged_workflow(terminal_files):
    """
    测试合并的工作流程
    """
    try:
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 运行create_yolo_dataset.py
            logger.info("运行create_yolo_dataset.py")
            from create_yolo_dataset import YOLODatasetCreator
            
            creator = YOLODatasetCreator(
                input_dir="input",
                output_dir=str(temp_path / "merged_output"),
                img_size=512
            )
            creator.create_dataset()
            
            # 检查Terminal#1027-1027的结果
            label_output_dir = temp_path / "merged_output" / "labels"
            check_terminal_results(label_output_dir, "合并流程")
                
    except Exception as e:
        logger.error(f"合并流程测试失败: {e}")

def check_terminal_results(label_dir, workflow_name):
    """
    检查Terminal#1027-1027的处理结果
    """
    label_path = Path(label_dir)
    if not label_path.exists():
        logger.warning(f"{workflow_name}: 标签目录不存在: {label_path}")
        return
    
    # 查找Terminal#1027相关的标注文件
    terminal_labels = list(label_path.glob("*1027*"))
    
    logger.info(f"{workflow_name} - Terminal#1027相关标注文件:")
    for label_file in terminal_labels:
        logger.info(f"  文件: {label_file.name}")
        
        # 读取并显示标注内容
        try:
            with open(label_file, 'r') as f:
                content = f.read().strip()
                if content:
                    logger.info(f"    内容: {content}")
                else:
                    logger.info(f"    内容: 空文件")
        except Exception as e:
            logger.error(f"    读取失败: {e}")
    
    if not terminal_labels:
        logger.info(f"{workflow_name}: 未找到Terminal#1027相关标注文件")

def compare_results():
    """
    比较两种流程的结果差异
    """
    logger.info("\n=== 结果比较 ===")
    logger.info("请检查上述两种流程的输出，特别关注:")
    logger.info("1. Terminal#1027相关文件的标注内容是否一致")
    logger.info("2. 边界框坐标的精度和格式")
    logger.info("3. 处理的图像数量和跳过的图像数量")
    logger.info("4. 日志输出的格式和内容")

def main():
    """
    主函数
    """
    logger.info("开始一致性测试")
    
    # 检查当前工作目录
    current_dir = Path.cwd()
    logger.info(f"当前工作目录: {current_dir}")
    
    # 测试特定案例
    if test_specific_case():
        compare_results()
    else:
        logger.error("测试失败：无法找到测试数据")
    
    logger.info("一致性测试完成")

if __name__ == "__main__":
    main()