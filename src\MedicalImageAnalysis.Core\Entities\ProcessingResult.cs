using System.ComponentModel.DataAnnotations;

namespace MedicalImageAnalysis.Core.Entities;

/// <summary>
/// 处理结果实体，表示对医学影像的处理结果
/// </summary>
public class ProcessingResult
{
    /// <summary>
    /// 处理结果唯一标识符
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 处理类型
    /// </summary>
    public ProcessingType Type { get; set; }

    /// <summary>
    /// 处理器名称
    /// </summary>
    [StringLength(128)]
    public string ProcessorName { get; set; } = string.Empty;

    /// <summary>
    /// 处理器版本
    /// </summary>
    [StringLength(32)]
    public string ProcessorVersion { get; set; } = string.Empty;

    /// <summary>
    /// 输出路径
    /// </summary>
    [StringLength(512)]
    public string OutputPath { get; set; } = string.Empty;

    /// <summary>
    /// 结果类型
    /// </summary>
    public string ResultType { get; set; } = string.Empty;

    /// <summary>
    /// 结果数据
    /// </summary>
    public string Data { get; set; } = string.Empty;

    /// <summary>
    /// 关联的处理任务ID
    /// </summary>
    public Guid? TaskId { get; set; }

    /// <summary>
    /// 关联的处理任务
    /// </summary>
    public ProcessingTask? Task { get; set; }

    /// <summary>
    /// 处理状态
    /// </summary>
    public ProcessingStatus Status { get; set; } = ProcessingStatus.Pending;

    /// <summary>
    /// 处理进度 (0-100)
    /// </summary>
    public int Progress { get; set; } = 0;

    /// <summary>
    /// 处理开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 处理结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 处理耗时 (毫秒)
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [StringLength(1024)]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 处理参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 处理指标
    /// </summary>
    public Dictionary<string, double> Metrics { get; set; } = new();

    /// <summary>
    /// 处理结果数据 (JSON 格式)
    /// </summary>
    public string ResultData { get; set; } = "{}";

    /// <summary>
    /// 输出文件路径集合
    /// </summary>
    public List<string> OutputFiles { get; set; } = new();

    /// <summary>
    /// 检测到的对象数量
    /// </summary>
    public int DetectedObjectsCount { get; set; }

    /// <summary>
    /// 平均置信度
    /// </summary>
    public double AverageConfidence { get; set; }

    /// <summary>
    /// 所属研究ID
    /// </summary>
    public Guid StudyId { get; set; }

    /// <summary>
    /// 所属研究
    /// </summary>
    public DicomStudy Study { get; set; } = null!;

    /// <summary>
    /// 使用的模型信息
    /// </summary>
    public ModelInfo? ModelInfo { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 获取处理耗时的友好显示
    /// </summary>
    public string ProcessingTimeDisplay
    {
        get
        {
            if (ProcessingTimeMs < 1000)
                return $"{ProcessingTimeMs} ms";
            else if (ProcessingTimeMs < 60000)
                return $"{ProcessingTimeMs / 1000.0:F1} s";
            else
                return $"{ProcessingTimeMs / 60000.0:F1} min";
        }
    }

    /// <summary>
    /// 检查处理是否成功
    /// </summary>
    public bool IsSuccessful => Status == ProcessingStatus.Completed && string.IsNullOrEmpty(ErrorMessage);

    /// <summary>
    /// 检查处理是否正在进行
    /// </summary>
    public bool IsInProgress => Status == ProcessingStatus.Processing;

    /// <summary>
    /// 标记处理开始
    /// </summary>
    public void MarkAsStarted()
    {
        Status = ProcessingStatus.Processing;
        StartTime = DateTime.UtcNow;
        Progress = 0;
    }

    /// <summary>
    /// 更新处理进度
    /// </summary>
    public void UpdateProgress(int progress, string? message = null)
    {
        Progress = Math.Clamp(progress, 0, 100);
        if (!string.IsNullOrEmpty(message))
        {
            // 可以添加进度消息记录逻辑
        }
    }

    /// <summary>
    /// 标记处理完成
    /// </summary>
    public void MarkAsCompleted(string? resultData = null)
    {
        Status = ProcessingStatus.Completed;
        EndTime = DateTime.UtcNow;
        Progress = 100;
        ProcessingTimeMs = (long)(EndTime.Value - StartTime).TotalMilliseconds;
        
        if (!string.IsNullOrEmpty(resultData))
            ResultData = resultData;
    }

    /// <summary>
    /// 标记处理失败
    /// </summary>
    public void MarkAsFailed(string errorMessage)
    {
        Status = ProcessingStatus.Failed;
        EndTime = DateTime.UtcNow;
        ErrorMessage = errorMessage;
        ProcessingTimeMs = (long)(EndTime.Value - StartTime).TotalMilliseconds;
    }

    /// <summary>
    /// 标记处理取消
    /// </summary>
    public void MarkAsCancelled()
    {
        Status = ProcessingStatus.Cancelled;
        EndTime = DateTime.UtcNow;
        ProcessingTimeMs = (long)(EndTime.Value - StartTime).TotalMilliseconds;
    }
}

/// <summary>
/// 模型信息
/// </summary>
public class ModelInfo
{
    /// <summary>
    /// 模型唯一标识符
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 模型名称
    /// </summary>
    [StringLength(128)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 模型版本
    /// </summary>
    [StringLength(32)]
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 模型类型
    /// </summary>
    [StringLength(64)]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 模型文件路径
    /// </summary>
    [StringLength(512)]
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 模型描述
    /// </summary>
    [StringLength(512)]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 训练数据集信息
    /// </summary>
    [StringLength(256)]
    public string TrainingDataset { get; set; } = string.Empty;

    /// <summary>
    /// 模型性能指标 (JSON 格式)
    /// </summary>
    public string PerformanceMetrics { get; set; } = "{}";

    /// <summary>
    /// 支持的类别
    /// </summary>
    public List<string> SupportedClasses { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 处理类型枚举
/// </summary>
public enum ProcessingType
{
    DicomParsing = 1,       // DICOM 解析
    ImagePreprocessing = 2,  // 图像预处理
    ObjectDetection = 3,     // 目标检测
    Segmentation = 4,        // 图像分割
    Classification = 5,      // 图像分类
    Registration = 6,        // 图像配准
    Reconstruction = 7,      // 图像重建
    Annotation = 8,          // 自动标注
    DatasetGeneration = 9,   // 数据集生成
    ModelTraining = 10,      // 模型训练
    ModelValidation = 11,    // 模型验证
    ModelTesting = 12        // 模型测试
}

/// <summary>
/// 处理状态枚举
/// </summary>
public enum ProcessingStatus
{
    Pending = 0,        // 待处理
    Processing = 1,     // 处理中
    Completed = 2,      // 已完成
    Failed = 3,         // 处理失败
    Cancelled = 4       // 已取消
}

/// <summary>
/// 形态学操作类型枚举
/// </summary>
public enum MorphologyOperation
{
    Erosion = 1,        // 腐蚀
    Dilation = 2,       // 膨胀
    Opening = 3,        // 开运算
    Closing = 4,        // 闭运算
    Gradient = 5,       // 形态学梯度
    TopHat = 6,         // 顶帽变换
    BlackHat = 7        // 黑帽变换
}


