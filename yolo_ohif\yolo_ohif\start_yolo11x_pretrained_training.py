#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO11x预训练权重训练启动器

功能:
- 检查YOLO数据集是否存在
- 启动基于预训练权重的YOLO11x模型训练
- 提供用户友好的交互界面

注意: 此脚本只负责模型训练，数据准备请先运行 create_yolo_dataset.py
"""

import os
import sys
from pathlib import Path

def main():
    print("🚀 YOLO11x预训练权重训练启动器")
    print("=" * 50)
    
    # 检查YOLO数据集是否存在
    possible_dataset_paths = [
        "./yolo_dataset_output/yolo_dataset",
        "./yolo11x_training_output/yolo_dataset",
        "./complete_yolo_training_output/yolo_dataset"
    ]
    
    dataset_path = None
    config_file = None
    
    for path in possible_dataset_paths:
        if os.path.exists(path):
            config_path = os.path.join(path, "data.yaml")
            if os.path.exists(config_path):
                dataset_path = path
                config_file = Path(config_path)
                break
    
    if not dataset_path:
        print("❌ 未找到YOLO数据集!")
        print("\n请先运行以下命令准备数据集:")
        print("python create_yolo_dataset.py")
        print("\n或确保以下路径之一存在YOLO数据集:")
        for path in possible_dataset_paths:
            print(f"  - {path}")
        return
    
    print(f"✅ 找到YOLO数据集: {dataset_path}")
    print(f"📄 配置文件: {config_file}")
    
    # 统计数据集信息
    train_images_dir = Path(dataset_path) / "train" / "images"
    val_images_dir = Path(dataset_path) / "val" / "images"
    
    if train_images_dir.exists():
        train_count = len(list(train_images_dir.glob("*.jpg")))
        print(f"🖼️  训练图像数量: {train_count}")
    
    if val_images_dir.exists():
        val_count = len(list(val_images_dir.glob("*.jpg")))
        print(f"🔍 验证图像数量: {val_count}")
    
    # 显示训练配置
    print("\n📋 预训练权重训练配置:")
    print("- 模型: YOLO11x (使用预训练权重)")
    print("- 训练类型: 迁移学习/微调")
    print("- 训练轮数: 100 (预训练模型通常需要较少轮数)")
    print("- 批次大小: 16")
    print("- 学习率: 0.001 (较小，适合微调)")
    print("- 冻结层数: 10 (前10层参数不更新)")
    print("- 图像尺寸: 640x640")
    print("- 数据准备: ✅ 已完成 (跳过数据集创建)")
    
    # 确认开始训练
    response = input("\n是否开始预训练权重微调? (y/n): ").lower().strip()
    if response != 'y':
        print("训练已取消")
        return
    
    print("\n🔥 开始预训练权重微调...")
    
    # 导入并运行训练脚本
    try:
        from train_yolo11x_pretrained import YOLO11xPretrainedTrainer
        
        # 创建训练器
        trainer = YOLO11xPretrainedTrainer(
            output_root='./yolo11x_pretrained_output',
            img_size=640
        )
        
        # 开始预训练权重微调
        print("🚀 开始YOLO预训练权重微调...")
        trainer.train_with_pretrained(
            config_path=str(config_file),
            epochs=100,        # 预训练模型需要较少轮数
            batch_size=16,     # 根据GPU内存调整
            learning_rate=0.001,  # 微调专用较小学习率
            freeze_layers=10,  # 冻结前10层
            model_size=None    # 让用户选择模型大小
        )
        
        print("\n🎉 预训练权重微调完成!")
        print("📁 查看结果: ./yolo11x_pretrained_output")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保train_yolo11x_pretrained.py文件存在")
    except Exception as e:
        print(f"❌ 训练错误: {e}")

if __name__ == "__main__":
    main()