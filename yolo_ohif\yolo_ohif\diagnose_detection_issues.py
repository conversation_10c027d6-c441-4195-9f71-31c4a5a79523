#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断检测结果错误问题

问题描述：
- 冈上肌撕裂病人的检测结果显示为 "item (16.9%)"
- 检测位置可能不准确，没有找到冈上肌位置

可能原因：
1. 类别名称映射错误
2. 模型置信度阈值设置不当
3. 模型本身性能问题
4. 图像预处理问题
"""

import os
import sys
import json
import numpy as np
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.detection_service import DetectionService
from config import YOLOConfig, Config
from ultralytics import YOLO

def check_model_info():
    """检查模型基本信息"""
    print("=== 模型信息检查 ===")
    
    config = YOLOConfig()
    model_path = os.path.join(config.MODEL_PATH, config.DEFAULT_MODEL)
    
    print(f"模型路径: {model_path}")
    print(f"模型文件存在: {os.path.exists(model_path)}")
    
    if not os.path.exists(model_path):
        print("❌ 模型文件不存在！")
        return False
    
    try:
        # 直接加载模型检查
        model = YOLO(model_path)
        print(f"✅ 模型加载成功")
        
        # 检查模型信息
        if hasattr(model, 'names'):
            print(f"模型类别数量: {len(model.names)}")
            print(f"模型类别名称: {model.names}")
            
            # 检查是否包含正确的类别
            if 0 in model.names and model.names[0] == 'supraspinatus_tear':
                print("✅ 模型包含正确的类别名称")
            else:
                print(f"❌ 模型类别名称错误: {model.names}")
                print("期望: {0: 'supraspinatus_tear'}")
        else:
            print("❌ 模型没有类别信息")
            
        # 检查模型任务类型
        if hasattr(model, 'task'):
            print(f"模型任务类型: {model.task}")
            
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False

def check_detection_service():
    """检查检测服务配置"""
    print("\n=== 检测服务配置检查 ===")
    
    try:
        config = YOLOConfig()
        model_path = os.path.join(config.MODEL_PATH, config.DEFAULT_MODEL)
        
        print(f"置信度阈值: {config.CONFIDENCE_THRESHOLD}")
        print(f"IOU阈值: {config.IOU_THRESHOLD}")
        print(f"设备: {config.DEVICE}")
        
        # 初始化检测服务
        detection_service = DetectionService(
            model_path=model_path,
            confidence_threshold=config.CONFIDENCE_THRESHOLD,
            iou_threshold=config.IOU_THRESHOLD,
            device=config.DEVICE
        )
        
        print("✅ 检测服务初始化成功")
        
        # 检查类别名称
        print(f"检测服务类别名称: {detection_service.class_names}")
        
        if 0 in detection_service.class_names:
            class_name = detection_service.class_names[0]
            print(f"类别0名称: '{class_name}'")
            
            if class_name == 'supraspinatus_tear':
                print("✅ 检测服务类别名称正确")
            else:
                print(f"❌ 检测服务类别名称错误: '{class_name}'")
                print("这解释了为什么显示 'item' 而不是 'supraspinatus_tear'")
        
        return detection_service
        
    except Exception as e:
        print(f"❌ 检测服务初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_detection_with_different_thresholds(detection_service):
    """测试不同置信度阈值下的检测结果"""
    print("\n=== 不同置信度阈值测试 ===")
    
    if not detection_service:
        print("❌ 检测服务不可用")
        return
    
    # 创建测试图像（模拟医学图像）
    test_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
    
    # 测试不同的置信度阈值
    thresholds = [0.01, 0.05, 0.1, 0.2, 0.3, 0.5]
    
    for threshold in thresholds:
        print(f"\n测试置信度阈值: {threshold}")
        
        # 临时修改置信度阈值
        original_threshold = detection_service.confidence_threshold
        detection_service.confidence_threshold = threshold
        
        try:
            detections = detection_service._detect_image(test_image)
            print(f"  检测到 {len(detections)} 个目标")
            
            for i, detection in enumerate(detections):
                print(f"    目标{i+1}: 类别='{detection['class']}', 置信度={detection['confidence']:.3f}")
                
        except Exception as e:
            print(f"  ❌ 检测失败: {e}")
        finally:
            # 恢复原始阈值
            detection_service.confidence_threshold = original_threshold

def check_dataset_configuration():
    """检查数据集配置"""
    print("\n=== 数据集配置检查 ===")
    
    dataset_yaml_path = "E:/Trae/yolo_ohif/yolo_dataset_output/yolo_dataset/dataset.yaml"
    
    if os.path.exists(dataset_yaml_path):
        print(f"✅ 数据集配置文件存在: {dataset_yaml_path}")
        
        try:
            import yaml
            with open(dataset_yaml_path, 'r', encoding='utf-8') as f:
                dataset_config = yaml.safe_load(f)
            
            print(f"数据集配置: {json.dumps(dataset_config, indent=2, ensure_ascii=False)}")
            
            if 'names' in dataset_config:
                names = dataset_config['names']
                print(f"数据集类别名称: {names}")
                
                if isinstance(names, list) and len(names) > 0:
                    if names[0] == 'supraspinatus_tear':
                        print("✅ 数据集类别名称正确")
                    else:
                        print(f"❌ 数据集类别名称错误: {names[0]}")
                        
        except Exception as e:
            print(f"❌ 读取数据集配置失败: {e}")
    else:
        print(f"❌ 数据集配置文件不存在: {dataset_yaml_path}")

def check_recent_detection_results():
    """检查最近的检测结果"""
    print("\n=== 最近检测结果检查 ===")
    
    # 检查结果目录
    results_dir = "E:/Trae/yolo_ohif/results"
    if os.path.exists(results_dir):
        print(f"结果目录: {results_dir}")
        
        # 查找最近的结果文件
        result_files = []
        for root, dirs, files in os.walk(results_dir):
            for file in files:
                if file.endswith('.json'):
                    result_files.append(os.path.join(root, file))
        
        if result_files:
            # 按修改时间排序，获取最新的文件
            result_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            latest_file = result_files[0]
            
            print(f"最新结果文件: {latest_file}")
            
            try:
                with open(latest_file, 'r', encoding='utf-8') as f:
                    results = json.load(f)
                
                print("最新检测结果:")
                print(json.dumps(results, indent=2, ensure_ascii=False))
                
                # 分析结果
                if isinstance(results, list):
                    for i, result in enumerate(results):
                        if 'detections' in result:
                            detections = result['detections']
                            print(f"\n图像{i+1}检测结果分析:")
                            print(f"  检测数量: {len(detections)}")
                            
                            for j, detection in enumerate(detections):
                                class_name = detection.get('class', 'unknown')
                                confidence = detection.get('confidence', 0)
                                print(f"    检测{j+1}: 类别='{class_name}', 置信度={confidence:.3f}")
                                
                                if class_name == 'item':
                                    print(f"      ❌ 发现问题：类别显示为 'item' 而不是 'supraspinatus_tear'")
                                    
            except Exception as e:
                print(f"❌ 读取结果文件失败: {e}")
        else:
            print("没有找到检测结果文件")
    else:
        print(f"结果目录不存在: {results_dir}")

def suggest_fixes():
    """提供修复建议"""
    print("\n=== 修复建议 ===")
    
    print("基于诊断结果，以下是可能的修复方案：")
    
    print("\n1. 类别名称问题修复：")
    print("   - 检查模型是否正确加载类别名称")
    print("   - 确保数据集配置文件正确")
    print("   - 验证前端显示代码使用正确的字段名")
    
    print("\n2. 检测精度问题修复：")
    print("   - 降低置信度阈值（当前可能过高）")
    print("   - 检查图像预处理是否与训练时一致")
    print("   - 验证模型是否针对冈上肌撕裂正确训练")
    
    print("\n3. 模型性能问题：")
    print("   - 检查训练数据质量和数量")
    print("   - 考虑重新训练模型")
    print("   - 验证模型在验证集上的性能")
    
    print("\n4. 系统配置问题：")
    print("   - 确保CUDA环境正确配置")
    print("   - 检查依赖库版本兼容性")
    print("   - 验证文件路径配置正确")

def main():
    """主诊断流程"""
    print("冈上肌撕裂检测问题诊断工具")
    print("=" * 60)
    
    # 1. 检查模型信息
    model_ok = check_model_info()
    
    # 2. 检查检测服务
    detection_service = check_detection_service()
    
    # 3. 测试不同置信度阈值
    if detection_service:
        test_detection_with_different_thresholds(detection_service)
    
    # 4. 检查数据集配置
    check_dataset_configuration()
    
    # 5. 检查最近的检测结果
    check_recent_detection_results()
    
    # 6. 提供修复建议
    suggest_fixes()
    
    print("\n=== 诊断完成 ===")
    print("请根据上述诊断结果进行相应的修复操作。")

if __name__ == "__main__":
    main()