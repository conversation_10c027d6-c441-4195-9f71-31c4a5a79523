"""依赖注入容器

提供依赖注入和服务管理功能
"""

import logging
from typing import Any, Dict, Type, TypeVar, Callable, Optional, Union
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from .exceptions import ContainerError, ConfigurationError

logger = logging.getLogger(__name__)

T = TypeVar('T')


class ServiceLifetime(Enum):
    """服务生命周期"""
    SINGLETON = "singleton"  # 单例
    TRANSIENT = "transient"  # 瞬态
    SCOPED = "scoped"       # 作用域


@dataclass
class ServiceDescriptor:
    """服务描述符"""
    service_type: Type
    implementation_type: Optional[Type]
    factory: Optional[Callable[..., Any]]
    instance: Optional[Any]
    lifetime: ServiceLifetime
    dependencies: list
    created_at: datetime
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


class ServiceContainer:
    """服务容器
    
    提供依赖注入和服务生命周期管理
    """
    
    def __init__(self):
        """初始化服务容器"""
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._instances: Dict[Type, Any] = {}
        self._scoped_instances: Dict[str, Dict[Type, Any]] = {}
        self._current_scope: Optional[str] = None
        self._building_stack: list = []
        
        logger.info("服务容器初始化完成")
    
    def register_singleton(self, 
                          service_type: Type[T], 
                          implementation_type: Optional[Type[T]] = None,
                          factory: Optional[Callable[..., T]] = None,
                          instance: Optional[T] = None) -> 'ServiceContainer':
        """注册单例服务
        
        Args:
            service_type: 服务类型
            implementation_type: 实现类型
            factory: 工厂函数
            instance: 实例对象
            
        Returns:
            服务容器（支持链式调用）
        """
        return self._register_service(
            service_type, 
            implementation_type, 
            factory, 
            instance, 
            ServiceLifetime.SINGLETON
        )
    
    def register_transient(self, 
                          service_type: Type[T], 
                          implementation_type: Optional[Type[T]] = None,
                          factory: Optional[Callable[..., T]] = None) -> 'ServiceContainer':
        """注册瞬态服务
        
        Args:
            service_type: 服务类型
            implementation_type: 实现类型
            factory: 工厂函数
            
        Returns:
            服务容器（支持链式调用）
        """
        return self._register_service(
            service_type, 
            implementation_type, 
            factory, 
            None, 
            ServiceLifetime.TRANSIENT
        )
    
    def register_scoped(self, 
                       service_type: Type[T], 
                       implementation_type: Optional[Type[T]] = None,
                       factory: Optional[Callable[..., T]] = None) -> 'ServiceContainer':
        """注册作用域服务
        
        Args:
            service_type: 服务类型
            implementation_type: 实现类型
            factory: 工厂函数
            
        Returns:
            服务容器（支持链式调用）
        """
        return self._register_service(
            service_type, 
            implementation_type, 
            factory, 
            None, 
            ServiceLifetime.SCOPED
        )
    
    def register_instance(self, service_type: Type[T], instance: T) -> 'ServiceContainer':
        """注册实例
        
        Args:
            service_type: 服务类型
            instance: 实例对象
            
        Returns:
            服务容器（支持链式调用）
        """
        return self.register_singleton(service_type, instance=instance)
    
    def register_factory(self, 
                        service_type: Type[T], 
                        factory: Callable[..., T],
                        lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT) -> 'ServiceContainer':
        """注册工厂函数
        
        Args:
            service_type: 服务类型
            factory: 工厂函数
            lifetime: 生命周期
            
        Returns:
            服务容器（支持链式调用）
        """
        return self._register_service(service_type, None, factory, None, lifetime)
    
    def get_service(self, service_type: Type[T]) -> T:
        """获取服务
        
        Args:
            service_type: 服务类型
            
        Returns:
            服务实例
        """
        if service_type not in self._services:
            raise ContainerError(f"服务未注册: {service_type.__name__}")
        
        # 检查循环依赖
        if service_type in self._building_stack:
            cycle = " -> ".join([t.__name__ for t in self._building_stack] + [service_type.__name__])
            raise ContainerError(f"检测到循环依赖: {cycle}")
        
        descriptor = self._services[service_type]
        
        try:
            self._building_stack.append(service_type)
            return self._create_instance(descriptor)
        finally:
            self._building_stack.pop()
    
    def get_required_service(self, service_type: Type[T]) -> T:
        """获取必需服务（如果不存在则抛出异常）
        
        Args:
            service_type: 服务类型
            
        Returns:
            服务实例
        """
        return self.get_service(service_type)
    
    def get_optional_service(self, service_type: Type[T]) -> Optional[T]:
        """获取可选服务（如果不存在则返回None）
        
        Args:
            service_type: 服务类型
            
        Returns:
            服务实例或None
        """
        try:
            return self.get_service(service_type)
        except ContainerError:
            return None
    
    def is_registered(self, service_type: Type) -> bool:
        """检查服务是否已注册
        
        Args:
            service_type: 服务类型
            
        Returns:
            是否已注册
        """
        return service_type in self._services
    
    def unregister(self, service_type: Type) -> bool:
        """取消注册服务
        
        Args:
            service_type: 服务类型
            
        Returns:
            是否成功取消注册
        """
        if service_type in self._services:
            del self._services[service_type]
            
            # 清理实例
            if service_type in self._instances:
                del self._instances[service_type]
            
            # 清理作用域实例
            for scope_instances in self._scoped_instances.values():
                if service_type in scope_instances:
                    del scope_instances[service_type]
            
            logger.debug(f"取消注册服务: {service_type.__name__}")
            return True
        
        return False
    
    def create_scope(self, scope_id: Optional[str] = None) -> 'ServiceScope':
        """创建服务作用域
        
        Args:
            scope_id: 作用域ID
            
        Returns:
            服务作用域
        """
        if scope_id is None:
            scope_id = f"scope_{int(datetime.now().timestamp() * 1000)}"
        
        return ServiceScope(self, scope_id)
    
    def enter_scope(self, scope_id: str) -> None:
        """进入作用域
        
        Args:
            scope_id: 作用域ID
        """
        self._current_scope = scope_id
        if scope_id not in self._scoped_instances:
            self._scoped_instances[scope_id] = {}
        
        logger.debug(f"进入作用域: {scope_id}")
    
    def exit_scope(self, scope_id: str) -> None:
        """退出作用域
        
        Args:
            scope_id: 作用域ID
        """
        if self._current_scope == scope_id:
            self._current_scope = None
        
        # 清理作用域实例
        if scope_id in self._scoped_instances:
            scope_instances = self._scoped_instances[scope_id]
            
            # 调用清理方法
            for instance in scope_instances.values():
                if hasattr(instance, 'cleanup'):
                    try:
                        instance.cleanup()
                    except Exception as e:
                        logger.warning(f"清理服务实例失败: {e}")
            
            del self._scoped_instances[scope_id]
        
        logger.debug(f"退出作用域: {scope_id}")
    
    def get_registered_services(self) -> Dict[Type, ServiceDescriptor]:
        """获取已注册的服务
        
        Returns:
            服务描述符字典
        """
        return self._services.copy()
    
    def get_service_info(self, service_type: Type) -> Optional[Dict[str, Any]]:
        """获取服务信息
        
        Args:
            service_type: 服务类型
            
        Returns:
            服务信息
        """
        if service_type not in self._services:
            return None
        
        descriptor = self._services[service_type]
        
        return {
            "service_type": service_type.__name__,
            "implementation_type": descriptor.implementation_type.__name__ if descriptor.implementation_type else None,
            "has_factory": descriptor.factory is not None,
            "has_instance": descriptor.instance is not None,
            "lifetime": descriptor.lifetime.value,
            "dependencies": [dep.__name__ for dep in descriptor.dependencies],
            "created_at": descriptor.created_at.isoformat(),
            "is_instantiated": service_type in self._instances
        }
    
    def validate_dependencies(self) -> Dict[str, list]:
        """验证依赖关系
        
        Returns:
            验证结果
        """
        errors = []
        warnings = []
        
        for service_type, descriptor in self._services.items():
            # 检查实现类型
            if descriptor.implementation_type and not issubclass(descriptor.implementation_type, service_type):
                errors.append(f"{descriptor.implementation_type.__name__} 不是 {service_type.__name__} 的子类")
            
            # 检查依赖是否已注册
            for dep_type in descriptor.dependencies:
                if dep_type not in self._services:
                    errors.append(f"{service_type.__name__} 依赖的服务未注册: {dep_type.__name__}")
            
            # 检查循环依赖（简单检查）
            visited = set()
            
            def check_circular(current_type, path):
                if current_type in path:
                    cycle = " -> ".join([t.__name__ for t in path] + [current_type.__name__])
                    errors.append(f"检测到循环依赖: {cycle}")
                    return
                
                if current_type in visited or current_type not in self._services:
                    return
                
                visited.add(current_type)
                current_descriptor = self._services[current_type]
                
                for dep in current_descriptor.dependencies:
                    check_circular(dep, path + [current_type])
            
            check_circular(service_type, [])
        
        return {
            "errors": errors,
            "warnings": warnings
        }
    
    def _register_service(self, 
                         service_type: Type, 
                         implementation_type: Optional[Type],
                         factory: Optional[Callable],
                         instance: Optional[Any],
                         lifetime: ServiceLifetime) -> 'ServiceContainer':
        """注册服务"""
        # 验证参数
        if not any([implementation_type, factory, instance]):
            implementation_type = service_type
        
        if sum([bool(implementation_type), bool(factory), bool(instance)]) > 1:
            raise ConfigurationError("只能指定一个：实现类型、工厂函数或实例")
        
        # 分析依赖
        dependencies = []
        if implementation_type:
            dependencies = self._analyze_dependencies(implementation_type)
        elif factory:
            dependencies = self._analyze_dependencies(factory)
        
        # 创建服务描述符
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation_type=implementation_type,
            factory=factory,
            instance=instance,
            lifetime=lifetime,
            dependencies=dependencies,
            created_at=datetime.now()
        )
        
        self._services[service_type] = descriptor
        
        # 如果是实例注册，直接保存
        if instance is not None:
            self._instances[service_type] = instance
        
        logger.debug(f"注册服务: {service_type.__name__} ({lifetime.value})")
        return self
    
    def _create_instance(self, descriptor: ServiceDescriptor) -> Any:
        """创建服务实例"""
        # 检查生命周期
        if descriptor.lifetime == ServiceLifetime.SINGLETON:
            if descriptor.service_type in self._instances:
                return self._instances[descriptor.service_type]
        elif descriptor.lifetime == ServiceLifetime.SCOPED:
            if self._current_scope and self._current_scope in self._scoped_instances:
                scope_instances = self._scoped_instances[self._current_scope]
                if descriptor.service_type in scope_instances:
                    return scope_instances[descriptor.service_type]
        
        # 创建实例
        if descriptor.instance is not None:
            instance = descriptor.instance
        elif descriptor.factory is not None:
            # 解析工厂函数依赖
            args = self._resolve_dependencies(descriptor.dependencies)
            instance = descriptor.factory(*args)
        elif descriptor.implementation_type is not None:
            # 解析构造函数依赖
            args = self._resolve_dependencies(descriptor.dependencies)
            instance = descriptor.implementation_type(*args)
        else:
            raise ContainerError(f"无法创建服务实例: {descriptor.service_type.__name__}")
        
        # 保存实例
        if descriptor.lifetime == ServiceLifetime.SINGLETON:
            self._instances[descriptor.service_type] = instance
        elif descriptor.lifetime == ServiceLifetime.SCOPED and self._current_scope:
            self._scoped_instances[self._current_scope][descriptor.service_type] = instance
        
        logger.debug(f"创建服务实例: {descriptor.service_type.__name__}")
        return instance
    
    def _resolve_dependencies(self, dependencies: list) -> list:
        """解析依赖"""
        args = []
        for dep_type in dependencies:
            args.append(self.get_service(dep_type))
        return args
    
    def _analyze_dependencies(self, target: Union[Type, Callable]) -> list:
        """分析依赖关系"""
        import inspect
        
        dependencies = []
        
        try:
            if inspect.isclass(target):
                # 分析类的构造函数
                sig = inspect.signature(target.__init__)
            else:
                # 分析函数
                sig = inspect.signature(target)
            
            for param_name, param in sig.parameters.items():
                if param_name == 'self':
                    continue
                
                if param.annotation != inspect.Parameter.empty:
                    # 使用类型注解
                    dependencies.append(param.annotation)
                elif param.default == inspect.Parameter.empty:
                    # 没有默认值的参数，但没有类型注解
                    logger.warning(f"参数 {param_name} 缺少类型注解")
        
        except Exception as e:
            logger.warning(f"分析依赖失败: {e}")
        
        return dependencies
    
    def cleanup(self) -> None:
        """清理容器"""
        # 清理所有作用域
        for scope_id in list(self._scoped_instances.keys()):
            self.exit_scope(scope_id)
        
        # 清理单例实例
        for instance in self._instances.values():
            if hasattr(instance, 'cleanup'):
                try:
                    instance.cleanup()
                except Exception as e:
                    logger.warning(f"清理服务实例失败: {e}")
        
        self._services.clear()
        self._instances.clear()
        self._scoped_instances.clear()
        self._current_scope = None
        self._building_stack.clear()
        
        logger.info("服务容器已清理")


class ServiceScope:
    """服务作用域
    
    提供作用域内的服务管理
    """
    
    def __init__(self, container: ServiceContainer, scope_id: str):
        """初始化服务作用域
        
        Args:
            container: 服务容器
            scope_id: 作用域ID
        """
        self._container = container
        self._scope_id = scope_id
        self._entered = False
    
    def __enter__(self) -> 'ServiceScope':
        """进入作用域"""
        self._container.enter_scope(self._scope_id)
        self._entered = True
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """退出作用域"""
        if self._entered:
            self._container.exit_scope(self._scope_id)
            self._entered = False
    
    def get_service(self, service_type: Type[T]) -> T:
        """获取服务
        
        Args:
            service_type: 服务类型
            
        Returns:
            服务实例
        """
        if not self._entered:
            raise ContainerError("必须在作用域内调用")
        
        return self._container.get_service(service_type)
    
    @property
    def scope_id(self) -> str:
        """作用域ID"""
        return self._scope_id


# 全局容器实例
_global_container: Optional[ServiceContainer] = None


def get_container() -> ServiceContainer:
    """获取全局容器
    
    Returns:
        服务容器
    """
    global _global_container
    if _global_container is None:
        _global_container = ServiceContainer()
    return _global_container


def set_container(container: ServiceContainer) -> None:
    """设置全局容器
    
    Args:
        container: 服务容器
    """
    global _global_container
    _global_container = container


def reset_container() -> None:
    """重置全局容器"""
    global _global_container
    if _global_container:
        _global_container.cleanup()
    _global_container = None