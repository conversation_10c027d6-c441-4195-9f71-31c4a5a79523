using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace MedicalImageAnalysis.Wpf.Windows
{
    /// <summary>
    /// BatchAnnotationProgressWindow.xaml 的交互逻辑
    /// </summary>
    public partial class BatchAnnotationProgressWindow : Window
    {
        public int TotalFiles { get; set; }
        public event Action? CancelRequested;

        public BatchAnnotationProgressWindow()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 更新进度
        /// </summary>
        public void UpdateProgress(int processedCount, string currentFile)
        {
            Dispatcher.Invoke(() =>
            {
                var percentage = TotalFiles > 0 ? (double)processedCount / TotalFiles * 100 : 0;
                
                ProgressText.Text = $"{processedCount} / {TotalFiles} 文件已处理";
                PercentageText.Text = $"{percentage:F0}%";
                MainProgressBar.Value = percentage;
                
                if (!string.IsNullOrEmpty(currentFile))
                {
                    CurrentFileText.Text = $"正在处理: {currentFile}";
                    StatusText.Text = "正在进行AI批量标注...";
                }
                else if (processedCount >= TotalFiles)
                {
                    CurrentFileText.Text = "处理完成";
                    StatusText.Text = "AI批量标注已完成";
                    CancelButton.IsEnabled = false;
                    CloseButton.IsEnabled = true;
                }
            });
        }

        /// <summary>
        /// 添加成功处理的文件
        /// </summary>
        public void AddSuccessFile(string filePath, int annotationCount)
        {
            Dispatcher.Invoke(() =>
            {
                var fileName = Path.GetFileName(filePath);
                var resultItem = CreateResultItem(fileName, $"成功 - 检测到 {annotationCount} 个标注", Colors.Green);
                ResultsPanel.Children.Add(resultItem);
                
                // 自动滚动到底部
                if (ResultsPanel.Parent is ScrollViewer scrollViewer)
                {
                    scrollViewer.ScrollToBottom();
                }
            });
        }

        /// <summary>
        /// 添加失败处理的文件
        /// </summary>
        public void AddFailedFile(string filePath, string errorMessage)
        {
            Dispatcher.Invoke(() =>
            {
                var fileName = Path.GetFileName(filePath);
                var resultItem = CreateResultItem(fileName, $"失败 - {errorMessage}", Colors.Red);
                ResultsPanel.Children.Add(resultItem);
                
                // 自动滚动到底部
                if (ResultsPanel.Parent is ScrollViewer scrollViewer)
                {
                    scrollViewer.ScrollToBottom();
                }
            });
        }

        /// <summary>
        /// 创建结果项
        /// </summary>
        private Border CreateResultItem(string fileName, string status, Color statusColor)
        {
            var border = new Border
            {
                Margin = new Thickness(0, 2, 0, 2),
                Padding = new Thickness(12, 8, 12, 8),
                Background = new SolidColorBrush(Colors.White),
                BorderBrush = new SolidColorBrush(Colors.LightGray),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(4)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var fileNameText = new TextBlock
            {
                Text = fileName,
                FontWeight = FontWeights.Medium,
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(fileNameText, 0);

            var statusText = new TextBlock
            {
                Text = status,
                Foreground = new SolidColorBrush(statusColor),
                FontSize = 12,
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(statusText, 1);

            grid.Children.Add(fileNameText);
            grid.Children.Add(statusText);
            border.Child = grid;

            return border;
        }

        /// <summary>
        /// 显示完成信息
        /// </summary>
        public void ShowCompletion(string message)
        {
            Dispatcher.Invoke(() =>
            {
                StatusText.Text = "处理完成";
                CancelButton.IsEnabled = false;
                CloseButton.IsEnabled = true;
                
                MessageBox.Show(message, "批量标注完成", MessageBoxButton.OK, MessageBoxImage.Information);
            });
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要取消批量标注吗？", "确认取消", 
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                CancelRequested?.Invoke();
                CancelButton.IsEnabled = false;
                StatusText.Text = "正在取消...";
            }
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        /// <summary>
        /// 窗口关闭事件
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            CancelRequested?.Invoke();
            base.OnClosed(e);
        }
    }
}
