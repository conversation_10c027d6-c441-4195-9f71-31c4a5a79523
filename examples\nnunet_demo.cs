using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Core.Models;
using MedicalImageAnalysis.Infrastructure.Extensions;

namespace MedicalImageAnalysis.Examples;

/// <summary>
/// nnUNet功能演示
/// </summary>
public class NnUNetDemo
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<NnUNetDemo> _logger;

    public NnUNetDemo()
    {
        _serviceProvider = CreateServiceProvider();
        _logger = _serviceProvider.GetRequiredService<ILogger<NnUNetDemo>>();
    }

    private IServiceProvider CreateServiceProvider()
    {
        var services = new ServiceCollection();

        // 配置
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

        services.AddSingleton<IConfiguration>(configuration);

        // 日志
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        // 添加医学图像分析服务
        services.AddMedicalImageAnalysisServices();

        return services.BuildServiceProvider();
    }

    /// <summary>
    /// 运行完整的nnUNet演示
    /// </summary>
    public async Task RunDemoAsync()
    {
        _logger.LogInformation("开始nnUNet功能演示");

        try
        {
            // 1. 检查环境
            await CheckEnvironmentAsync();

            // 2. 创建示例数据集配置
            await CreateSampleDatasetConfigAsync();

            // 3. 演示训练配置
            await DemonstrateTrainingConfigAsync();

            // 4. 演示推理配置
            await DemonstrateInferenceConfigAsync();

            // 5. 显示可用的预训练模型
            await ShowAvailableModelsAsync();

            _logger.LogInformation("nnUNet功能演示完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "演示过程中发生异常");
        }
    }

    /// <summary>
    /// 检查nnUNet环境
    /// </summary>
    private async Task CheckEnvironmentAsync()
    {
        _logger.LogInformation("=== 检查nnUNet环境 ===");

        var nnunetService = _serviceProvider.GetRequiredService<INnUNetService>();
        var isReady = await nnunetService.CheckEnvironmentAsync();

        if (isReady)
        {
            _logger.LogInformation("✓ nnUNet环境检查通过");
        }
        else
        {
            _logger.LogWarning("⚠ nnUNet环境检查失败，可能需要安装依赖");
            _logger.LogInformation("可以运行以下命令安装nnUNet:");
            _logger.LogInformation("python scripts/nnunet/install_nnunet.py");
        }
    }

    /// <summary>
    /// 创建示例数据集配置
    /// </summary>
    private async Task CreateSampleDatasetConfigAsync()
    {
        _logger.LogInformation("=== 创建示例数据集配置 ===");

        var nnunetService = _serviceProvider.GetRequiredService<INnUNetService>();

        // 创建数据集配置
        var datasetConfig = new NnUNetDatasetConfig
        {
            DatasetId = 1,
            DatasetName = "MedicalDemo",
            Description = "医学图像分割演示数据集",
            Reference = "Medical Image Analysis System Demo",
            License = "Research Use Only",
            Release = "1.0",
            FileEnding = ".nii.gz",
            TensorImageSize = "4D"
        };

        // 添加模态信息
        datasetConfig.Modality.Add("0", "CT");

        // 添加标签信息
        datasetConfig.Labels.Add("0", "background");
        datasetConfig.Labels.Add("1", "tumor");
        datasetConfig.Labels.Add("2", "organ");

        // 添加示例训练数据
        for (int i = 1; i <= 5; i++)
        {
            datasetConfig.TrainingData.Add(new NnUNetDataFile
            {
                Image = $"./data/imagesTr/case_{i:D3}_0000.nii.gz",
                Label = $"./data/labelsTr/case_{i:D3}.nii.gz",
                FileId = $"case_{i:D3}"
            });
        }

        // 添加示例测试数据
        for (int i = 6; i <= 8; i++)
        {
            datasetConfig.TestData.Add(new NnUNetDataFile
            {
                Image = $"./data/imagesTs/case_{i:D3}_0000.nii.gz",
                FileId = $"case_{i:D3}"
            });
        }

        // 创建配置文件
        var outputPath = "./demo_output";
        Directory.CreateDirectory(outputPath);

        var configFilePath = await nnunetService.CreateDatasetConfigAsync(datasetConfig, outputPath);
        _logger.LogInformation($"✓ 数据集配置文件已创建: {configFilePath}");

        // 显示配置信息
        _logger.LogInformation($"数据集ID: {datasetConfig.DatasetId}");
        _logger.LogInformation($"数据集名称: {datasetConfig.DatasetName}");
        _logger.LogInformation($"模态数量: {datasetConfig.Modality.Count}");
        _logger.LogInformation($"标签数量: {datasetConfig.Labels.Count}");
        _logger.LogInformation($"训练样本数量: {datasetConfig.TrainingData.Count}");
        _logger.LogInformation($"测试样本数量: {datasetConfig.TestData.Count}");
    }

    /// <summary>
    /// 演示训练配置
    /// </summary>
    private async Task DemonstrateTrainingConfigAsync()
    {
        _logger.LogInformation("=== 演示训练配置 ===");

        // 创建不同架构的训练配置
        var architectures = new[]
        {
            NnUNetArchitecture.TwoD,
            NnUNetArchitecture.ThreeD_LowRes,
            NnUNetArchitecture.ThreeD_FullRes,
            NnUNetArchitecture.ThreeD_Cascade
        };

        foreach (var architecture in architectures)
        {
            var config = new NnUNetTrainingConfig
            {
                DatasetId = 1,
                DatasetName = "MedicalDemo",
                DatasetPath = "./data/nnUNet_raw/Dataset001_MedicalDemo",
                Architecture = architecture,
                MaxEpochs = architecture == NnUNetArchitecture.TwoD ? 500 : 1000,
                BatchSize = architecture == NnUNetArchitecture.ThreeD_FullRes ? 2 : 4,
                LearningRate = 0.01,
                OutputDirectory = "./demo_output/nnunet_training",
                ExperimentName = $"demo_{architecture}_{DateTime.Now:yyyyMMdd}",
                UseMixedPrecision = true,
                EnableDataAugmentation = true,
                UseDeepSupervision = true,
                ValidationFrequency = 50,
                Device = "cuda"
            };

            _logger.LogInformation($"架构: {architecture}");
            _logger.LogInformation($"  最大轮数: {config.MaxEpochs}");
            _logger.LogInformation($"  批次大小: {config.BatchSize}");
            _logger.LogInformation($"  学习率: {config.LearningRate}");
            _logger.LogInformation($"  混合精度: {config.UseMixedPrecision}");
            _logger.LogInformation($"  数据增强: {config.EnableDataAugmentation}");
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 演示推理配置
    /// </summary>
    private async Task DemonstrateInferenceConfigAsync()
    {
        _logger.LogInformation("=== 演示推理配置 ===");

        var inferenceConfig = new NnUNetInferenceConfig
        {
            ModelPath = "./models/nnunet/Dataset001_MedicalDemo/nnUNetTrainer__nnUNetPlans__3d_fullres",
            InputPath = "./data/input_images",
            OutputPath = "./demo_output/predictions",
            SaveProbabilities = false,
            UseTestTimeAugmentation = true,
            Device = "cuda",
            UseMixedPrecision = true,
            StepSize = 0.5,
            DisableProgressBar = false
        };

        _logger.LogInformation($"模型路径: {inferenceConfig.ModelPath}");
        _logger.LogInformation($"输入路径: {inferenceConfig.InputPath}");
        _logger.LogInformation($"输出路径: {inferenceConfig.OutputPath}");
        _logger.LogInformation($"保存概率图: {inferenceConfig.SaveProbabilities}");
        _logger.LogInformation($"测试时增强: {inferenceConfig.UseTestTimeAugmentation}");
        _logger.LogInformation($"混合精度: {inferenceConfig.UseMixedPrecision}");
        _logger.LogInformation($"步长因子: {inferenceConfig.StepSize}");

        await Task.CompletedTask;
    }

    /// <summary>
    /// 显示可用的预训练模型
    /// </summary>
    private async Task ShowAvailableModelsAsync()
    {
        _logger.LogInformation("=== 可用的预训练模型 ===");

        var nnunetService = _serviceProvider.GetRequiredService<INnUNetService>();
        var models = await nnunetService.GetAvailablePretrainedModelsAsync();

        _logger.LogInformation($"共有 {models.Count} 个预训练模型可用:");
        foreach (var model in models)
        {
            _logger.LogInformation($"  - {model}");
        }

        _logger.LogInformation("");
        _logger.LogInformation("使用示例:");
        _logger.LogInformation("var modelPath = await nnunetService.DownloadPretrainedModelAsync(\"Task001_BrainTumour\", \"./models\");");
    }

    /// <summary>
    /// 演示完整的训练流程（模拟）
    /// </summary>
    public async Task DemonstrateTrainingWorkflowAsync()
    {
        _logger.LogInformation("=== 演示完整训练流程 ===");

        var nnunetService = _serviceProvider.GetRequiredService<INnUNetService>();

        // 1. 数据预处理配置
        var preprocessingConfig = new NnUNetPreprocessingConfig
        {
            SourceDataPath = "./data/raw",
            TargetDataPath = "./data/nnUNet_raw",
            DatasetId = 1,
            VerifyDatasetIntegrity = true,
            NumThreads = 8,
            OverwriteExisting = false,
            Verbose = true
        };

        _logger.LogInformation("1. 数据预处理配置:");
        _logger.LogInformation($"   源数据路径: {preprocessingConfig.SourceDataPath}");
        _logger.LogInformation($"   目标路径: {preprocessingConfig.TargetDataPath}");
        _logger.LogInformation($"   线程数: {preprocessingConfig.NumThreads}");

        // 2. 训练配置
        var trainingConfig = new NnUNetTrainingConfig
        {
            DatasetId = 1,
            DatasetName = "MedicalDemo",
            Architecture = NnUNetArchitecture.ThreeD_FullRes,
            MaxEpochs = 100, // 演示用较少轮数
            BatchSize = 2,
            LearningRate = 0.01,
            OutputDirectory = "./demo_output/training"
        };

        _logger.LogInformation("2. 训练配置:");
        _logger.LogInformation($"   架构: {trainingConfig.Architecture}");
        _logger.LogInformation($"   最大轮数: {trainingConfig.MaxEpochs}");
        _logger.LogInformation($"   批次大小: {trainingConfig.BatchSize}");

        // 3. 推理配置
        var inferenceConfig = new NnUNetInferenceConfig
        {
            ModelPath = "./demo_output/training/best_model.pth",
            InputPath = "./data/test_images",
            OutputPath = "./demo_output/predictions"
        };

        _logger.LogInformation("3. 推理配置:");
        _logger.LogInformation($"   模型路径: {inferenceConfig.ModelPath}");
        _logger.LogInformation($"   输入路径: {inferenceConfig.InputPath}");
        _logger.LogInformation($"   输出路径: {inferenceConfig.OutputPath}");

        _logger.LogInformation("");
        _logger.LogInformation("注意: 这是配置演示，实际训练需要准备相应的数据集");
        _logger.LogInformation("请参考 scripts/nnunet/README.md 了解详细使用方法");
    }
}

/// <summary>
/// 程序入口点
/// </summary>
public class Program
{
    public static async Task Main(string[] args)
    {
        var demo = new NnUNetDemo();
        
        Console.WriteLine("nnUNet功能演示");
        Console.WriteLine("================");
        
        await demo.RunDemoAsync();
        
        Console.WriteLine("");
        Console.WriteLine("演示完整训练流程配置:");
        Console.WriteLine("===================");
        
        await demo.DemonstrateTrainingWorkflowAsync();
        
        Console.WriteLine("");
        Console.WriteLine("演示完成！");
        Console.WriteLine("更多信息请查看 scripts/nnunet/README.md");
    }
}
