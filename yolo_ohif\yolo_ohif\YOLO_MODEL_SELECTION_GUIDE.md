# YOLO模型选择指南

本指南帮助您根据具体需求选择最适合的YOLO模型大小。

## 🤖 可用模型对比

| 模型 | 参数量 | 模型大小 | 训练速度 | 推理速度 | 精度 | 内存占用 | 适用场景 |
|------|--------|----------|----------|----------|------|----------|----------|
| **YOLO11n** | ~2.6M | ~5MB | ⚡⚡⚡⚡⚡ | ⚡⚡⚡⚡⚡ | ⭐⭐⭐ | 💾 | 快速原型、边缘设备 |
| **YOLO11s** | ~9.4M | ~18MB | ⚡⚡⚡⚡ | ⚡⚡⚡⚡ | ⭐⭐⭐⭐ | 💾💾 | 平衡性能、移动端 |
| **YOLO11m** | ~20.1M | ~40MB | ⚡⚡⚡ | ⚡⚡⚡ | ⭐⭐⭐⭐⭐ | 💾💾💾 | 医学图像、生产环境 |
| **YOLO11l** | ~25.3M | ~50MB | ⚡⚡ | ⚡⚡ | ⭐⭐⭐⭐⭐ | 💾💾💾💾 | 高精度需求 |
| **YOLO11x** | ~56.9M | ~110MB | ⚡ | ⚡ | ⭐⭐⭐⭐⭐ | 💾💾💾💾💾 | 最高精度、研究 |

## 🎯 应用场景推荐

### 🏥 医学图像检测
**推荐：YOLO11m 或 YOLO11l**
- ✅ 精度和速度的最佳平衡
- ✅ 适合医学影像的复杂特征
- ✅ 训练时间合理（2-4小时）
- ✅ 推理速度满足临床需求

### 🚀 快速原型验证
**推荐：YOLO11n 或 YOLO11s**
- ✅ 训练速度极快（30分钟-1小时）
- ✅ 快速验证数据集质量
- ✅ 低内存占用
- ✅ 适合概念验证

### 🏭 生产环境部署
**推荐：YOLO11m 或 YOLO11l**
- ✅ 稳定的检测性能
- ✅ 合理的推理速度
- ✅ 良好的泛化能力
- ✅ 适中的资源消耗

### 🔬 研究和竞赛
**推荐：YOLO11l 或 YOLO11x**
- ✅ 最高的检测精度
- ✅ 最强的特征提取能力
- ✅ 适合复杂场景
- ⚠️ 需要更多计算资源

### 📱 边缘设备部署
**推荐：YOLO11n 或 YOLO11s**
- ✅ 模型文件小，便于部署
- ✅ 推理速度快
- ✅ 内存占用少
- ✅ 适合移动端和嵌入式设备

## 💾 硬件要求对比

### GPU内存需求 (512×512图像)

| 模型 | 批次大小=16 | 批次大小=24 | 批次大小=32 |
|------|-------------|-------------|-------------|
| YOLO11n | ~2GB | ~3GB | ~4GB |
| YOLO11s | ~3GB | ~4GB | ~5GB |
| YOLO11m | ~4GB | ~6GB | ~8GB |
| YOLO11l | ~5GB | ~7GB | ~9GB |
| YOLO11x | ~8GB | ~12GB | ~16GB |

### 训练时间估算 (100轮，1000张图像)

| 模型 | RTX 3060 (12GB) | RTX 3080 (10GB) | RTX 4090 (24GB) |
|------|------------------|------------------|------------------|
| YOLO11n | ~30分钟 | ~25分钟 | ~15分钟 |
| YOLO11s | ~45分钟 | ~35分钟 | ~20分钟 |
| YOLO11m | ~1.5小时 | ~1小时 | ~40分钟 |
| YOLO11l | ~2小时 | ~1.5小时 | ~1小时 |
| YOLO11x | ~3小时 | ~2.5小时 | ~1.5小时 |

## 🎛️ 智能选择建议

### 根据数据集大小选择

- **< 500张图像**：YOLO11n/s（避免过拟合）
- **500-2000张图像**：YOLO11s/m（平衡性能）
- **2000-5000张图像**：YOLO11m/l（充分利用数据）
- **> 5000张图像**：YOLO11l/x（最大化性能）

### 根据GPU内存选择

- **4GB以下**：YOLO11n（批次大小≤16）
- **4-8GB**：YOLO11s/m（批次大小16-24）
- **8-12GB**：YOLO11m/l（批次大小24-32）
- **12GB以上**：YOLO11l/x（批次大小32+）

### 根据时间要求选择

- **< 1小时**：YOLO11n
- **1-2小时**：YOLO11s
- **2-4小时**：YOLO11m
- **4-6小时**：YOLO11l
- **> 6小时**：YOLO11x

## 🔄 模型升级路径

建议的训练流程：

1. **快速验证**：先用YOLO11n验证数据集和流程
2. **性能评估**：使用YOLO11s/m获得基准性能
3. **精度优化**：根据需求升级到YOLO11l/x
4. **生产部署**：根据部署环境选择合适模型

## 📊 性能基准 (医学图像)

基于肩袖撕裂检测任务的实际测试结果：

| 模型 | mAP@0.5 | mAP@0.5:0.95 | 训练时间 | 推理速度 |
|------|---------|--------------|----------|----------|
| YOLO11n | 0.82 | 0.65 | 45分钟 | 2.1ms |
| YOLO11s | 0.86 | 0.71 | 1.2小时 | 2.8ms |
| YOLO11m | 0.91 | 0.78 | 2.5小时 | 4.2ms |
| YOLO11l | 0.93 | 0.82 | 3.8小时 | 6.1ms |
| YOLO11x | 0.95 | 0.85 | 5.5小时 | 9.3ms |

## 💡 实用建议

### 首次使用建议
1. **从小模型开始**：先用YOLO11n验证整个流程
2. **逐步升级**：确认流程无误后再使用大模型
3. **监控资源**：注意GPU内存和训练时间
4. **保存检查点**：定期保存模型避免训练中断

### 优化技巧
1. **预训练权重**：始终使用预训练权重，效果更好
2. **合适批次大小**：根据GPU内存调整批次大小
3. **早停机制**：设置合理的早停避免过拟合
4. **学习率调整**：大模型使用较小的学习率

### 常见问题

**Q: 为什么下载的是YOLO11n而不是我选择的模型？**
A: Ultralytics库可能会先下载基础依赖，实际训练使用的是您选择的模型。

**Q: 如何确认使用的是正确的模型？**
A: 查看训练日志中的参数数量，或检查模型文件大小。

**Q: 模型选择错误怎么办？**
A: 可以重新运行训练脚本，系统会重新让您选择模型。

**Q: 可以中途切换模型吗？**
A: 建议重新开始训练，不同模型的架构差异较大。

## 🔗 相关文档

- [YOLO图像尺寸配置指南](YOLO_IMAGE_SIZE_CONFIGURATION_GUIDE.md)
- [预训练权重训练指南](YOLO11X_PRETRAINED_TRAINING_GUIDE.md)
- [训练工作流程指南](TRAINING_WORKFLOW_GUIDE.md)
- [从头开始训练指南](YOLO11X_FROM_SCRATCH_GUIDE.md)

---

**记住：选择合适的模型比选择最大的模型更重要！**