# 预览数据集功能使用指南

## 🎯 功能简介

"预览数据集"功能允许用户一键打开所选数据集的文件夹，快速查看和管理训练数据。

## 📍 功能位置

在**模型训练**页面的左侧数据集配置区域，您会看到以下按钮：

```
数据集操作
├── 浏览数据集
├── 预览数据集  ← 新增功能
├── 验证数据集
└── 数据增强
```

## 🚀 使用步骤

### 1. 选择数据集
- 在"数据集选择"下拉框中选择一个数据集
- 支持的数据集包括：
  - 医学影像数据集 v1.0
  - DICOM标注数据集
  - 自定义数据集
  - nnUNet医学分割数据集
  - 脑部MRI数据集
  - 肺部CT数据集

### 2. 点击预览按钮
- 点击带有眼睛图标的"预览数据集"按钮
- 系统会自动查找数据集路径

### 3. 查看结果
系统会执行以下操作：
- 🔍 **自动打开文件夹**：在Windows资源管理器中打开数据集目录
- 📊 **显示统计信息**：弹出对话框显示数据集详细信息
- 📁 **分析目录结构**：统计图像文件、标注文件和子文件夹数量

## 📊 信息显示示例

点击预览后，您会看到类似以下的信息对话框：

```
数据集: 医学影像数据集 v1.0
路径: C:\Projects\medical-imaging\data\medical_dataset_v1

结构分析:
• 图像文件: 1,250 个
• 标注文件: 1,250 个
• 子文件夹: 6 个
• 总大小: 2.34 GB

文件夹已在资源管理器中打开。
```

## 🛠️ 智能路径解析

### 预配置路径
系统预设了以下数据集路径映射：

| 数据集名称 | 默认路径 |
|-----------|----------|
| 医学影像数据集 v1.0 | `./data/medical_dataset_v1` |
| DICOM标注数据集 | `./data/dicom_annotated_dataset` |
| 自定义数据集 | `./data/custom_dataset` |
| nnUNet医学分割数据集 | `./data/nnUNet_raw/Dataset001_Medical` |
| 脑部MRI数据集 | `./data/nnUNet_raw/Dataset002_BrainMRI` |
| 肺部CT数据集 | `./data/nnUNet_raw/Dataset003_LungCT` |

### 自动搜索
如果预配置路径不存在，系统会自动搜索：
1. `./data/[数据集名称]`
2. `./datasets/[数据集名称]`
3. `C:\Data\[数据集名称]`
4. `D:\Data\[数据集名称]`

## 🏗️ 自动创建数据集结构

### 当数据集不存在时
如果选择的数据集路径不存在，系统会：
1. 显示友好的错误提示
2. 询问是否创建示例数据集结构
3. 自动生成标准的目录结构

### 创建的目录结构
```
数据集名称/
├── images/
│   ├── train/          # 训练图像
│   ├── val/            # 验证图像
│   └── test/           # 测试图像
├── labels/
│   ├── train/          # 训练标注
│   └── val/            # 验证标注
└── README.md           # 详细说明文件
```

### README.md 内容
自动生成的说明文件包含：
- 数据集结构说明
- 使用指南
- 支持的文件格式
- 创建时间戳

## 📁 支持的文件格式

### 图像文件
- `.jpg`, `.jpeg` - JPEG图像
- `.png` - PNG图像
- `.bmp` - 位图图像
- `.tiff` - TIFF图像
- `.dcm` - DICOM医学图像
- `.nii`, `.nii.gz` - NIfTI医学图像

### 标注文件
- `.txt` - YOLO格式标注
- `.xml` - Pascal VOC格式
- `.json` - COCO格式
- `.csv` - 表格格式

## ⚠️ 注意事项

### 1. 权限要求
- 确保对数据集目录有读取权限
- 创建新目录时需要写入权限

### 2. 路径限制
- 支持包含空格和中文的路径
- 避免使用特殊字符（如 `<>|*?"`）

### 3. 性能考虑
- 大型数据集的分析可能需要几秒钟
- 网络驱动器可能响应较慢

## 🔧 故障排除

### 问题：点击预览没有反应
**解决方案**：
1. 检查是否已选择数据集
2. 确认Windows资源管理器未被阻止
3. 查看应用程序日志获取详细错误信息

### 问题：显示"数据集路径不存在"
**解决方案**：
1. 点击"是"创建示例结构
2. 手动创建数据集目录
3. 使用"浏览数据集"功能设置正确路径

### 问题：文件夹打开但统计信息错误
**解决方案**：
1. 检查文件扩展名是否正确
2. 确认文件未被其他程序占用
3. 验证目录权限设置

## 🎯 使用技巧

### 1. 快速验证数据集
- 使用预览功能快速检查数据集是否存在
- 通过统计信息验证数据完整性

### 2. 数据准备工作流
1. 选择数据集 → 预览检查
2. 如不存在 → 创建结构
3. 添加数据文件 → 再次预览验证
4. 开始训练

### 3. 多数据集管理
- 为不同项目创建不同的数据集
- 使用描述性的数据集名称
- 定期备份重要数据集

## 📈 后续功能规划

- 🖼️ **图像预览**：在应用内直接显示图像缩略图
- 📊 **详细统计**：提供更丰富的数据分布统计
- 🔄 **批量操作**：支持多个数据集的批量管理
- ☁️ **云存储支持**：支持云端数据集的预览和同步

---

**提示**：如果您在使用过程中遇到任何问题，请查看应用程序日志文件或联系技术支持。
