#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试原始数据处理流程
验证从nii.gz图像和mask开始的完整训练流程
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from train_supraspinatus_yolo import SupraspinatusYOLOTrainer
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_original_pipeline():
    """
    测试原始数据处理流程
    """
    logger.info("开始测试原始数据处理流程...")
    
    # 检查数据集目录
    dataset_root = Path('./dataset')
    if not dataset_root.exists():
        logger.error(f"数据集目录不存在: {dataset_root}")
        return False
    
    # 检查必要的子目录
    required_dirs = ['image_T2', 'label_T2', 'image_T2_normal']
    for dir_name in required_dirs:
        dir_path = dataset_root / dir_name
        if not dir_path.exists():
            logger.error(f"必要目录不存在: {dir_path}")
            return False
        
        # 检查是否有nii.gz文件
        nii_files = list(dir_path.glob('*.nii.gz'))
        logger.info(f"{dir_name} 目录包含 {len(nii_files)} 个nii.gz文件")
    
    # 创建训练器
    trainer = SupraspinatusYOLOTrainer(
        dataset_root='./dataset',
        output_root='./test_original_output',
        img_size=640
    )
    
    logger.info("测试数据处理功能...")
    
    try:
        # 测试撕裂图像处理
        logger.info("测试撕裂图像处理...")
        tear_data = trainer.process_tear_images()
        logger.info(f"撕裂图像处理完成，生成 {len(tear_data)} 个样本")
        
        # 统计有标注和无标注的样本
        samples_with_labels = sum(1 for _, label_path, _ in tear_data if label_path is not None)
        samples_without_labels = len(tear_data) - samples_with_labels
        
        logger.info(f"  - 有标注的样本: {samples_with_labels} 个")
        logger.info(f"  - 无标注的样本: {samples_without_labels} 个")
        
        # 测试正常图像处理
        logger.info("测试正常图像处理...")
        normal_data = trainer.process_normal_images()
        logger.info(f"正常图像处理完成，生成 {len(normal_data)} 个样本")
        
        # 合并数据
        all_data = tear_data + normal_data
        logger.info(f"总共处理 {len(all_data)} 个样本")
        
        # 统计总的标注数量
        total_labels = sum(1 for _, label_path, _ in all_data if label_path is not None)
        logger.info(f"总共生成 {total_labels} 个YOLO标注文件")
        
        logger.info("原始数据处理流程测试成功！")
        return True
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_original_pipeline()
    if success:
        print("\n✅ 原始数据处理流程测试通过")
        print("现在可以运行完整训练: python train_supraspinatus_yolo.py")
    else:
        print("\n❌ 原始数据处理流程测试失败")
        sys.exit(1)