<Window x:Class="MedicalImageAnalysis.Wpf.Windows.DatasetDownloadWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:MedicalImageAnalysis.Wpf.Converters"
        Title="开源数据集下载" Height="600" Width="900"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
    </Window.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
            <materialDesign:PackIcon Kind="Download"
                                   Width="32" Height="32"
                                   VerticalAlignment="Center"
                                   Margin="0,0,12,0"/>
            <TextBlock Text="开源医学影像数据集下载"
                     FontSize="24"
                     FontWeight="Medium"
                     VerticalAlignment="Center"/>
        </StackPanel>

        <!-- 数据集列表 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <ItemsControl x:Name="DatasetsItemsControl">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <materialDesign:Card Margin="0,0,0,12" 
                                           materialDesign:ElevationAssist.Elevation="Dp2">
                            <Grid Margin="16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- 数据集信息 -->
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="{Binding Name, Mode=OneWay}"
                                             FontSize="16"
                                             FontWeight="Medium"
                                             Margin="0,0,0,4"/>

                                    <TextBlock Text="{Binding Description, Mode=OneWay}"
                                             TextWrapping="Wrap"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             Margin="0,0,0,8"/>

                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                        <materialDesign:PackIcon Kind="Tag" Width="16" Height="16"
                                                               Margin="0,0,4,0" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding Category, Mode=OneWay}" FontSize="12" VerticalAlignment="Center"/>
                                        
                                        <materialDesign:PackIcon Kind="Image" Width="16" Height="16" 
                                                               Margin="16,0,4,0" VerticalAlignment="Center"/>
                                        <TextBlock FontSize="12" VerticalAlignment="Center">
                                            <Run Text="{Binding ImageCount, Mode=OneWay, StringFormat='{}{0:N0}'}"/>
                                            <Run Text="张图像"/>
                                        </TextBlock>
                                        
                                        <materialDesign:PackIcon Kind="HardDrive" Width="16" Height="16"
                                                               Margin="16,0,4,0" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding SizeFormatted, Mode=OneWay}" FontSize="12" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="License" Width="16" Height="16"
                                                               Margin="0,0,4,0" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding License, Mode=OneWay}" FontSize="12" VerticalAlignment="Center"/>

                                        <materialDesign:PackIcon Kind="Account" Width="16" Height="16"
                                                               Margin="16,0,4,0" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding Author, Mode=OneWay}" FontSize="12" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- 操作按钮 -->
                                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                    <Button x:Name="DownloadButton"
                                          Style="{StaticResource MaterialDesignRaisedButton}"
                                          Content="下载"
                                          Width="80"
                                          Margin="0,0,0,8"
                                          Click="DownloadButton_Click"
                                          Tag="{Binding}"
                                          Visibility="{Binding IsDownloaded, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>
                                    
                                    <Button x:Name="OpenButton"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Content="打开"
                                          Width="80"
                                          Margin="0,0,0,8"
                                          Click="OpenButton_Click"
                                          Tag="{Binding}"
                                          Visibility="{Binding IsDownloaded, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                    
                                    <Button x:Name="DeleteButton"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Content="删除"
                                          Width="80"
                                          Click="DeleteButton_Click"
                                          Tag="{Binding}"
                                          Visibility="{Binding IsDownloaded, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                </StackPanel>
                            </Grid>
                        </materialDesign:Card>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- 下载进度 -->
        <materialDesign:Card Grid.Row="2" 
                           x:Name="ProgressCard"
                           Visibility="Collapsed"
                           Margin="0,16,0,0"
                           materialDesign:ElevationAssist.Elevation="Dp2">
            <StackPanel Margin="16">
                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                    <TextBlock x:Name="ProgressDatasetName" 
                             Text="下载中..." 
                             FontWeight="Medium"
                             VerticalAlignment="Center"/>
                    <TextBlock x:Name="ProgressStatus" 
                             Text="准备中..."
                             Margin="16,0,0,0"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                             VerticalAlignment="Center"/>
                </StackPanel>
                
                <ProgressBar x:Name="DownloadProgressBar"
                           Height="8"
                           Margin="0,0,0,8"/>
                
                <StackPanel Orientation="Horizontal">
                    <TextBlock x:Name="ProgressDetails" 
                             Text="0 MB / 0 MB"
                             FontSize="12"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    <TextBlock x:Name="ProgressSpeed" 
                             Text=""
                             FontSize="12"
                             Margin="16,0,0,0"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    <TextBlock x:Name="ProgressETA" 
                             Text=""
                             FontSize="12"
                             Margin="16,0,0,0"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</Window>
