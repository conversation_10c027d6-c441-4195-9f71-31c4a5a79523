<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DICOM 智能阅读器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/dicom-viewer.css') }}" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            overflow: hidden;
        }
        
        .main-container {
            display: flex;
            height: 100vh;
            background: #1a1a1a;
            width: 100%;
            position: relative;
        }
        
        .top-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            width: 100%;
            height: 50px;
            background: #2d2d2d;
            border-bottom: 1px solid #404040;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 1000;
            box-sizing: border-box;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .header-title {
            color: #ffffff;
            font-size: 16px;
            font-weight: 600;
        }
        
        .patient-info {
            color: #cccccc;
            font-size: 14px;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .left-toolbar {
            width: 40px;
            background: #2d2d2d;
            border-right: 1px solid #404040;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 70px 5px 20px 5px;
            gap: 10px;
            position: fixed;
            left: 0;
            top: 0;
            bottom: 0;
            z-index: 1001;
        }
        
        .tool-btn {
            width: 35px;
            height: 35px;
            background: #404040;
            border: none;
            border-radius: 5px;
            color: #ffffff;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            font-size: 14px;
        }
        
        .tool-btn:hover {
            background: #505050;
            transform: translateY(-1px);
        }
        
        .tool-btn.active {
            background: #0078d4;
            box-shadow: 0 0 10px rgba(0, 120, 212, 0.5);
        }
        
        .tool-btn#runDetection {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border: 2px solid #ff6b35;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }
        
        .tool-btn#runDetection:hover {
            background: linear-gradient(135deg, #ff8c5a, #ffb347);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.5);
            transform: translateY(-2px);
        }
        
        .tool-btn#runDetection:disabled {
            background: #666666;
            border-color: #666666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .main-content {
            margin-left: 40px;
            margin-top: 50px;
            display: flex;
            height: calc(100vh - 50px);
            overflow: visible;
            width: calc(100vw - 40px);
        }
        
        .viewer-area {
            flex: 1;
            background: #000000;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            min-width: 800px;
            min-height: 600px;
        }
        
        .dicom-viewport {
            position: relative;
            width: 100%;
            height: 100%;
            min-width: 800px;
            min-height: 600px;
            background: #000;
            overflow: hidden;
        }
        
        .dicom-canvas {
            width: 100%;
            height: 100%;
            cursor: crosshair;
        }
        
        .detection-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        
        .detection-box {
            position: absolute;
            border: 2px solid #00ff00;
            background: transparent;
            pointer-events: none;
        }
        
        .detection-label {
            position: absolute;
            top: -25px;
            left: 0;
            background: #00ff00;
            color: white;
            padding: 2px 6px;
            font-size: 12px;
            border-radius: 3px;
            white-space: nowrap;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 20;
        }
        
        .image-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 15;
        }
        
        .image-info div {
            margin-bottom: 3px;
        }
        
        .controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 15;
        }
        
        .slider-container {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .slider-container label {
            width: 60px;
            margin-right: 10px;
            font-size: 12px;
        }
        
        .slider-container input[type="range"] {
            flex: 1;
            margin-right: 10px;
        }
        
        .slider-container span {
            width: 60px;
            text-align: right;
            font-size: 12px;
        }
        
        .info-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .info-panel h5 {
            color: #495057;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e9ecef;
        }
        
        /* 面板区域样式 */
        .panel-section {
            margin-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 15px;
        }
        
        .panel-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .panel-title {
            color: #495057;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .series-list, .detection-results-container, .measurement-results {
            max-height: 150px;
            overflow-y: auto;
            font-size: 0.85rem;
        }
        
        .series-item, .detection-item {
            padding: 8px 10px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            margin-bottom: 6px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 0.8rem;
        }
        
        .series-item:hover, .detection-item:hover {
            background: #f8f9fa;
            border-color: #007bff;
            transform: translateX(2px);
        }
        
        /* 图像信息网格 */
        .image-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 0.8rem;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 8px;
            background: #f8f9fa;
            border-radius: 3px;
            border: 1px solid #e9ecef;
        }
        
        .info-label {
            color: #6c757d;
            font-weight: 500;
        }
        
        .info-value {
            color: #495057;
            font-weight: 600;
        }
        
        /* 检测结果样式 */
        .detection-result-item {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 8px;
            transition: all 0.2s;
            cursor: pointer;
        }
        
        .detection-result-item:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.1);
            transform: translateY(-1px);
        }
        
        .detection-result-item.active {
            border-color: #007bff;
            background: #f8f9ff;
        }
        
        .detection-class {
            font-weight: 600;
            color: #495057;
            font-size: 0.85rem;
        }
        
        .detection-confidence {
            color: #28a745;
            font-weight: 500;
            font-size: 0.8rem;
        }
        
        .detection-location {
            color: #6c757d;
            font-size: 0.75rem;
            margin-top: 2px;
        }
        
        /* 测量结果样式 */
        .measurement-item {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 6px;
            font-size: 0.8rem;
        }
        
        .measurement-type {
            font-weight: 600;
            color: #495057;
        }
        
        .measurement-value {
            color: #007bff;
            font-weight: 500;
        }
        
        /* AI结果面板专用样式 */
        #ai-results-panel {
            animation: slideInUp 0.3s ease-out;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ai-summary {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }
        
        .ai-summary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .detection-item.card {
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .detection-item.card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
        }
        
        .progress {
            background-color: #f8f9fa;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-bar {
            transition: width 0.6s ease;
            border-radius: 10px;
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }
        
        .bg-opacity-10 {
            background-color: rgba(var(--bs-bg-opacity-rgb), 0.1) !important;
        }
        
        /* 风险等级颜色增强 */
        .border-danger {
            border-color: #dc3545 !important;
        }
        
        .border-warning {
            border-color: #ffc107 !important;
        }
        
        .border-info {
            border-color: #0dcaf0 !important;
        }
        
        /* 响应式优化 */
        @media (max-width: 768px) {
            #ai-results-panel {
                max-height: 400px;
            }
            
            .ai-summary .col-4 {
                margin-bottom: 1rem;
            }
            
            .detection-item .row .col-6 {
                margin-bottom: 0.5rem;
            }
        }
         
         .series-item.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .detection-item {
            border-left: 4px solid #28a745;
        }
        
        .detection-result-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
        }
        
        .detection-result-item:hover {
            background: #ffffff;
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.15);
        }
        
        .detection-class {
            font-weight: 600;
            font-size: 14px;
        }
        
        .detection-details {
            font-size: 12px;
        }
        
        .coordinate-text {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            color: #6c757d;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .stat-item:last-child {
            border-bottom: none;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6c757d;
        }
        
        .stat-value {
            font-weight: 600;
            font-size: 14px;
        }
        
        .detection-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        
        .detection-box {
            position: absolute;
            border: 2px solid #ff0000;
            background: transparent;
            pointer-events: none;
        }
        
        .detection-label {
            position: absolute;
            background: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 2px 6px;
            font-size: 12px;
            border-radius: 3px;
            top: -20px;
            left: 0;
        }
        
        .debug-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 5px;
            max-width: 300px;
            max-height: 400px;
            overflow-y: auto;
            z-index: 15;
        }
        
        .debug-panel h6 {
            margin-bottom: 10px;
            color: #ffc107;
        }
        
        .far-right-panel {
            width: 320px;
            background: #2d2d2d;
            border-left: 1px solid #404040;
            overflow-y: auto;
            flex-shrink: 0;
            z-index: 100;
        }
        
        .info-panel {
            padding: 15px;
            color: #ffffff;
        }
        
        .panel-section {
            margin-bottom: 20px;
            background: #3a3a3a;
            border-radius: 8px;
            padding: 15px;
        }
        
        .panel-title {
            color: #ffffff;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .series-list {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .series-item {
            padding: 8px 12px;
            background: #4a4a4a;
            border-radius: 4px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 13px;
        }
        
        .series-item:hover {
            background: #5a5a5a;
        }
        
        .series-item.active {
            background: #0078d4;
        }
        
        .detection-results-container {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .detection-item {
            padding: 10px;
            background: #4a4a4a;
            border-radius: 4px;
            margin-bottom: 8px;
            font-size: 13px;
        }
        
        .detection-status {
            padding: 8px 12px;
            background: #4a4a4a;
            border-radius: 4px;
            font-size: 13px;
        }
        
        .measurement-results {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .measurement-item {
            padding: 8px 12px;
            background: #4a4a4a;
            border-radius: 4px;
            margin-bottom: 8px;
            font-size: 13px;
        }
        
        .image-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px;
            background: #4a4a4a;
            border-radius: 4px;
            font-size: 13px;
        }
        
        .info-label {
            color: #cccccc;
        }
        
        .info-value {
            color: #ffffff;
            font-weight: 500;
        }
        
        /* 病灶显示栏样式 */
        .lesion-images-container {
            max-height: 250px;
            overflow-y: auto;
        }
        
        .lesion-image-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: #4a4a4a;
            border-radius: 4px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .lesion-image-item:hover {
            background: #5a5a5a;
        }
        
        .lesion-image-item.active {
            background: #0d6efd;
        }
        
        .lesion-thumbnail {
            width: 40px;
            height: 40px;
            background: #666;
            border-radius: 4px;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #fff;
        }
        
        .lesion-info {
            flex: 1;
            font-size: 13px;
        }
        
        .lesion-count {
            color: #ffc107;
            font-weight: bold;
        }
        
        /* 检查所见和指南意见样式 */
        .findings-container, .guideline-container {
            margin-top: 8px;
        }
        
        .findings-container textarea, .guideline-container textarea {
            background: #4a4a4a;
            border: 1px solid #666;
            color: #fff;
            font-size: 13px;
            resize: vertical;
        }
        
        .findings-container textarea:focus, .guideline-container textarea:focus {
            background: #5a5a5a;
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ffffff;
            font-size: 18px;
            z-index: 100;
        }
        
        .image-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: #ffffff;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 20;
        }
        
        .controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 4px;
            z-index: 20;
        }
        
        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            color: #ffffff;
            font-size: 12px;
        }
        
        .slider-container label {
            min-width: 50px;
            color: #cccccc;
        }
        
        .slider-container input[type="range"] {
            flex: 1;
            margin: 0 10px;
        }
        
        .slider-container span {
            min-width: 60px;
            text-align: right;
            color: #ffffff;
        }
        
        .badge {
            background: #0078d4 !important;
        }
        
        .btn-outline-light {
            border-color: #6c757d;
            color: #ffffff;
        }
        
        .btn-outline-light:hover {
            background: #6c757d;
            border-color: #6c757d;
        }
        
        .btn-outline-info {
            border-color: #0dcaf0;
            color: #0dcaf0;
        }
        
        .btn-outline-info:hover {
            background: #0dcaf0;
            border-color: #0dcaf0;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 顶部标题栏 -->
        <div class="top-header">
            <div class="header-left">
                <div class="header-title">
                    <i class="fas fa-eye me-2"></i>专业DICOM阅读器
                </div>
                <div class="patient-info">
                    <span id="patientName">{{ study.patient_name if study else '未知患者' }}</span>
                    <span class="ms-3" id="patientId">{{ study.patient_id if study else '未知ID' }}</span>
                    <span class="ms-3" id="studyDate">{{ study.study_date if study else '未知日期' }}</span>
                    <span class="ms-3">性别: <span id="patient-sex">未知</span></span>
                    <span class="ms-3">年龄: <span id="patient-age">未知</span></span>
                </div>
            </div>
            <div class="header-right">
                <a href="{{ url_for('web.index') }}" class="btn btn-sm btn-outline-light">
                    <i class="fas fa-home me-1"></i>返回首页
                </a>
                {% if study and study.detection_status == 'detected' %}
                <a href="{{ url_for('web.results', study_id=study.id) }}" class="btn btn-sm btn-outline-info">
                    <i class="fas fa-chart-bar me-1"></i>检测报告
                </a>
                {% endif %}
            </div>
        </div>

        <!-- 左侧工具栏 -->
        <div class="left-toolbar">
            <button class="tool-btn active" id="zoomTool" title="缩放工具">
                <i class="fas fa-search-plus"></i>
            </button>
            <button class="tool-btn" id="panTool" title="平移工具">
                <i class="fas fa-hand-paper"></i>
            </button>
            <button class="tool-btn" id="windowTool" title="窗宽窗位">
                <i class="fas fa-adjust"></i>
            </button>
            <button class="tool-btn" id="resetTool" title="重置视图">
                <i class="fas fa-undo"></i>
            </button>
            <div style="height: 20px;"></div>
            <button class="tool-btn" id="lengthTool" title="长度测量">
                <i class="fas fa-ruler"></i>
            </button>
            <button class="tool-btn" id="areaTool" title="面积测量">
                <i class="fas fa-draw-polygon"></i>
            </button>
            <button class="tool-btn" id="probeTool" title="CT值测量">
                <i class="fas fa-crosshairs"></i>
            </button>
            <button class="tool-btn" id="clearMeasurements" title="清除测量">
                <i class="fas fa-eraser"></i>
            </button>
            <div style="height: 20px;"></div>
            <button class="tool-btn" id="runDetection" title="运行YOLO检测">
                <i class="fas fa-brain"></i>
            </button>
            <button class="tool-btn" id="toggleDetection" title="切换检测结果">
                <i class="fas fa-search"></i>
            </button>
            <button class="tool-btn" id="clearDetection" title="清除检测结果">
                <i class="fas fa-trash"></i>
            </button>
            <button class="tool-btn" id="debugDetection" title="调试信息">
                <i class="fas fa-bug"></i>
            </button>
            <button class="tool-btn" id="fullscreenTool" title="全屏">
                <i class="fas fa-expand"></i>
            </button>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 图像查看器区域 -->
            <div class="viewer-area">
                <div class="dicom-viewport" id="dicomViewport">
                    <canvas class="dicom-canvas" id="dicomCanvas"></canvas>
                    <div class="loading" id="loadingIndicator">
                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                    </div>
                    
                    <div class="image-info" id="imageInfo">
                        <div>患者: <span id="patientName">-</span></div>
                        <div>序列: <span id="seriesDescription">-</span></div>
                        <div>图像: <span id="imageNumber">-</span> / <span id="totalImages">-</span></div>
                        <div>窗宽: <span id="windowWidth">-</span></div>
                        <div>窗位: <span id="windowCenter">-</span></div>
                        <div>缩放: <span id="zoomLevel">100%</span></div>
                    </div>
                    
                    <div class="detection-overlay" id="detectionOverlay"></div>
                    
                    <div class="debug-panel" id="debugPanel" style="display: none;">
                        <h6>调试信息 <button class="btn btn-sm btn-outline-light" onclick="viewer.clearDebugInfo()">清除</button></h6>
                        <div id="debugContent">
                            <div><strong>Cornerstone状态:</strong> <span id="cornerstoneStatus">未知</span></div>
                            <div><strong>WADO加载器:</strong> <span id="wadoStatus">未知</span></div>
                            <div><strong>当前图像ID:</strong> <span id="currentImageId">无</span></div>
                            <div><strong>API连接:</strong> <span id="apiStatus">未测试</span></div>
                            <div><strong>错误日志:</strong></div>
                            <div id="errorLog" style="max-height: 200px; overflow-y: auto; font-size: 12px;">无错误</div>
                        </div>
                    </div>
                    
                    <div class="controls" id="imageControls">
                        <div class="slider-container">
                            <label>图像:</label>
                            <input type="range" id="imageSlider" min="0" max="0" value="0">
                            <span id="imageCounter">0/0</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 最右侧信息面板 - 所有信息 -->
            <div class="far-right-panel">
                <div class="info-panel">
                    <!-- 序列列表 -->
                    <div class="panel-section">
                        <h6 class="panel-title"><i class="fas fa-layer-group me-2"></i>序列列表</h6>
                        <div class="series-list" id="seriesList">
                            <div class="text-center text-muted small">暂无数据</div>
                        </div>
                    </div>
                    
                    <!-- 检测结果面板 -->
                    <div class="panel-section">
                        <h6 class="panel-title">
                            <i class="fas fa-crosshairs me-2"></i>检测结果
                            <span class="badge bg-primary ms-2" id="detectionCount">0</span>
                        </h6>
                        <div class="detection-status mb-2" id="detectionStatus">
                            <div class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                暂无检测结果
                            </div>
                        </div>
                        <div class="detection-results-container" id="detectionResults">
                            <div class="text-center text-muted small">暂无检测结果</div>
                        </div>
                    </div>
                    
                    <!-- 测量工具结果 -->
                    <div class="panel-section">
                        <h6 class="panel-title">
                            <i class="fas fa-ruler me-2"></i>测量结果
                            <button class="btn btn-sm btn-outline-danger ms-auto" onclick="viewer.clearMeasurements()" title="清除所有测量">
                                <i class="fas fa-trash"></i>
                            </button>
                        </h6>
                        <div class="measurement-results" id="measurementResults">
                            <div class="text-center text-muted small">暂无测量数据</div>
                        </div>
                    </div>
                    
                    <!-- 病灶显示栏 -->
                    <div class="panel-section">
                        <h6 class="panel-title">
                            <i class="fas fa-exclamation-triangle me-2"></i>病灶分布
                            <span class="badge bg-warning ms-2" id="lesionImageCount">0</span>
                        </h6>
                        <div class="lesion-images-container" id="lesionImages">
                            <div class="text-center text-muted small">暂无病灶图像</div>
                        </div>
                    </div>
                    
                    <!-- 检查所见 -->
                    <div class="panel-section">
                        <h6 class="panel-title">
                            <i class="fas fa-eye me-2"></i>检查所见
                            <button class="btn btn-sm btn-outline-primary ms-auto" onclick="generateFindings()" title="生成检查所见">
                                <i class="fas fa-magic"></i>
                            </button>
                        </h6>
                        <div class="findings-container" id="findingsContainer">
                            <textarea class="form-control" id="findingsText" rows="4" placeholder="请输入检查所见或点击生成按钮自动生成..."></textarea>
                        </div>
                    </div>
                    
                    <!-- 指南意见 -->
                    <div class="panel-section">
                        <h6 class="panel-title">
                            <i class="fas fa-book-medical me-2"></i>指南意见
                            <button class="btn btn-sm btn-outline-success ms-auto" onclick="generateGuideline()" title="生成指南意见">
                                <i class="fas fa-lightbulb"></i>
                            </button>
                        </h6>
                        <div class="guideline-container" id="guidelineContainer">
                            <textarea class="form-control" id="guidelineText" rows="3" placeholder="请输入指南意见或点击生成按钮自动生成..."></textarea>
                        </div>
                    </div>
                    
                    <!-- 图像信息 -->
                    <div class="panel-section">
                        <h6 class="panel-title"><i class="fas fa-info-circle me-2"></i>图像信息</h6>
                        <div class="image-info-grid">
                            <div class="info-item">
                                <span class="info-label">窗宽:</span>
                                <span class="info-value" id="currentWindowWidth">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">窗位:</span>
                                <span class="info-value" id="currentWindowCenter">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">缩放:</span>
                                <span class="info-value" id="currentZoom">100%</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">图像:</span>
                                <span class="info-value"><span id="currentImageNum">1</span>/<span id="totalImageNum">1</span></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 使用更稳定的Cornerstone库版本 -->
    <script src="https://unpkg.com/cornerstone-core@2.6.1/dist/cornerstone.min.js"></script>
    <script src="https://unpkg.com/dicom-parser@1.8.21/dist/dicomParser.min.js"></script>
    <script src="https://unpkg.com/cornerstone-wado-image-loader@4.1.3/dist/cornerstoneWADOImageLoader.bundle.min.js"></script>
    <script src="https://unpkg.com/cornerstone-web-image-loader@2.1.1/dist/cornerstoneWebImageLoader.min.js"></script>
    <script src="https://unpkg.com/cornerstone-tools@4.21.1/dist/cornerstoneTools.min.js"></script>
    
    <script>

        
        // DICOM 查看器类
        class DICOMViewer {
            constructor() {
                this.element = document.getElementById('dicomViewport');
                this.canvas = document.getElementById('dicomCanvas');
                this.currentTool = 'zoom';
                this.currentStudyId = null;
                this.currentSeriesId = null;
                this.currentImageIndex = 0;
                this.images = [];
                this.detections = [];
                this.detectionVisible = true;
                this.detectionOverlay = document.getElementById('detectionOverlay');
                
                this.init();
            }
            
            bindEventListeners() {
                // 绑定工具栏按钮事件
                document.getElementById('zoomTool').addEventListener('click', () => this.setTool('zoom'));
                document.getElementById('panTool').addEventListener('click', () => this.setTool('pan'));
                document.getElementById('windowTool').addEventListener('click', () => this.setTool('window'));
                document.getElementById('lengthTool').addEventListener('click', () => this.setTool('length'));
                document.getElementById('areaTool').addEventListener('click', () => this.setTool('area'));
                document.getElementById('probeTool').addEventListener('click', () => this.setTool('probe'));
                document.getElementById('clearMeasurements').addEventListener('click', () => this.clearMeasurements());
                document.getElementById('resetTool').addEventListener('click', () => this.resetView());
                document.getElementById('runDetection').addEventListener('click', () => this.runDetection());
                document.getElementById('toggleDetection').addEventListener('click', () => this.toggleDetection());
                document.getElementById('clearDetection').addEventListener('click', () => this.clearDetectionResults());
                document.getElementById('debugDetection').addEventListener('click', () => this.toggleDebug());
                
                // 绑定其他功能按钮
                document.getElementById('fullscreenTool').addEventListener('click', () => this.toggleFullscreen());
                
                // 绑定图像滑块事件
                const imageSlider = document.getElementById('imageSlider');
                if (imageSlider) {
                    imageSlider.addEventListener('input', (e) => {
                        const index = parseInt(e.target.value);
                        this.displayImage(index);
                    });
                }
                
                // 绑定鼠标事件
                this.element.addEventListener('wheel', (e) => this.handleWheel(e));
                this.element.addEventListener('mousedown', (e) => this.handleMouseDown(e));
                this.element.addEventListener('mousemove', (e) => this.handleMouseMove(e));
                this.element.addEventListener('mouseup', (e) => this.handleMouseUp(e));
                
                console.log('事件监听器已绑定');
            }
            
            init() {
                // 初始化 Cornerstone
                cornerstone.enable(this.element);
                
                // 绑定事件监听器
                this.bindEventListeners();
                
                // 配置 WADO 图像加载器
                cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
                cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
                
                // 配置WADO图像加载器
                 cornerstoneWADOImageLoader.configure({
                     useWebWorkers: true,
                     decodeConfig: {
                         convertFloatPixelDataToInt: false
                     },
                     beforeSend: function(xhr) {
                         // 设置超时时间
                         xhr.timeout = 30000;
                         // 添加错误处理
                         xhr.onerror = function() {
                             console.error('DICOM图像请求失败');
                         };
                         xhr.ontimeout = function() {
                             console.error('DICOM图像请求超时');
                         };
                     }
                 });
                 
                 // 注册WADO URI图像加载器
                 cornerstone.registerImageLoader('wadouri', cornerstoneWADOImageLoader.wadouri.loadImage);
                 console.log('WADO URI图像加载器已注册');
                 
                 // 初始化Cornerstone Tools
                 if (typeof cornerstoneTools !== 'undefined') {
                     cornerstoneTools.external.cornerstone = cornerstone;
                     cornerstoneTools.external.cornerstoneWADOImageLoader = cornerstoneWADOImageLoader;
                     cornerstoneTools.init({
                         mouseEnabled: true,
                         touchEnabled: true,
                         globalToolSyncEnabled: false,
                         showSVGCursors: true
                     });
                     
                     // 注意：在Cornerstone Tools 4.x中，不需要调用cornerstoneTools.enable()
                     // 元素已经通过cornerstone.enable()启用
                     
                     // 添加所有工具
                     try {
                         // 为当前元素添加工具
                         cornerstoneTools.addToolForElement(this.element, cornerstoneTools.LengthTool);
                         cornerstoneTools.addToolForElement(this.element, cornerstoneTools.EllipticalRoiTool);
                         cornerstoneTools.addToolForElement(this.element, cornerstoneTools.ProbeTool);
                         cornerstoneTools.addToolForElement(this.element, cornerstoneTools.ZoomTool);
                         cornerstoneTools.addToolForElement(this.element, cornerstoneTools.PanTool);
                         cornerstoneTools.addToolForElement(this.element, cornerstoneTools.WwwcTool);
                         cornerstoneTools.addToolForElement(this.element, cornerstoneTools.ZoomWheelTool);
                         
                         // 设置默认工具状态
                         cornerstoneTools.setToolActiveForElement(this.element, 'Zoom', { mouseButtonMask: 1 });
                         cornerstoneTools.setToolActiveForElement(this.element, 'Pan', { mouseButtonMask: 4 });
                         cornerstoneTools.setToolActiveForElement(this.element, 'ZoomWheel', {});
                         
                         console.log('所有工具添加成功');
                     } catch (toolError) {
                         console.error('添加工具时出错:', toolError);
                         // 如果添加工具失败，至少添加基本工具
                         try {
                             cornerstoneTools.addToolForElement(this.element, cornerstoneTools.ZoomTool);
                             cornerstoneTools.addToolForElement(this.element, cornerstoneTools.PanTool);
                             cornerstoneTools.addToolForElement(this.element, cornerstoneTools.WwwcTool);
                             cornerstoneTools.addToolForElement(this.element, cornerstoneTools.ZoomWheelTool);
                             console.log('基本工具添加成功');
                         } catch (basicToolError) {
                             console.error('添加基本工具也失败:', basicToolError);
                         }
                     }
                     
                     // 设置默认工具状态
                     cornerstoneTools.setToolActive('Zoom', { mouseButtonMask: 1 });
                     cornerstoneTools.setToolActive('Pan', { mouseButtonMask: 4 });
                     cornerstoneTools.setToolActive('Wwwc', { mouseButtonMask: 2 });
                     cornerstoneTools.setToolActive('ZoomWheel', {});
                     
                     this.toolsEnabled = true;
                     console.log('Cornerstone Tools初始化成功，包含测量工具');
                 } else {
                     console.warn('Cornerstone Tools not available');
                     this.toolsEnabled = false;
                 }
                
                // 设置事件监听器
                this.setupEventListeners();
                
                // 解析URL参数
                this.parseUrlParameters();
                
                // 获取study_id - 支持两种格式
                // 格式1: /dicom-viewer?study_id={study_id} (查询参数)
                // 格式2: /dicom-viewer/{study_id} (路径参数)
                const urlParams = new URLSearchParams(window.location.search);
                let studyId = urlParams.get('study_id');
                
                if (!studyId) {
                    // 尝试从路径参数获取
                    const pathParts = window.location.pathname.split('/');
                    const lastPart = pathParts[pathParts.length - 1];
                    if (lastPart && lastPart !== 'dicom-viewer') {
                        studyId = lastPart;
                    }
                }
                
                this.currentStudyId = studyId;
                console.log('获取的study_id:', this.currentStudyId);
                
                if (this.currentStudyId) {
                    this.loadStudy(this.currentStudyId);
                } else {
                    console.warn('未找到study_id参数');
                    alert('缺少研究ID参数，请从研究列表页面访问');
                }
            }
            
            setupEventListeners() {
                 // 工具栏按钮事件
                  document.getElementById('zoomTool').addEventListener('click', () => this.setTool('zoom'));
                  document.getElementById('panTool').addEventListener('click', () => this.setTool('pan'));
                  document.getElementById('windowTool').addEventListener('click', () => this.setTool('window'));
                  document.getElementById('resetTool').addEventListener('click', () => this.resetView());
                  
                  // 测量工具事件
                  document.getElementById('lengthTool').addEventListener('click', () => this.setTool('length'));
                  document.getElementById('areaTool').addEventListener('click', () => this.setTool('area'));
                  document.getElementById('probeTool').addEventListener('click', () => this.setTool('probe'));
                  document.getElementById('clearMeasurements').addEventListener('click', () => this.clearMeasurements());
                  
                  // 其他功能
                  document.getElementById('toggleDetection').addEventListener('click', () => this.toggleDetection());
                  document.getElementById('clearDetection').addEventListener('click', () => this.clearDetectionResults());
                  document.getElementById('debugDetection').addEventListener('click', () => this.toggleDebugPanel());
                  document.getElementById('fullscreenTool').addEventListener('click', () => this.toggleFullscreen());
                
                // 图像滑块
                document.getElementById('imageSlider').addEventListener('input', (e) => {
                    this.currentImageIndex = parseInt(e.target.value);
                    this.displayImage(this.currentImageIndex);
                });
                

                
                // 鼠标事件
                this.element.addEventListener('wheel', (e) => this.handleWheel(e));
                this.element.addEventListener('mousedown', (e) => this.handleMouseDown(e));
                this.element.addEventListener('mousemove', (e) => this.handleMouseMove(e));
                this.element.addEventListener('mouseup', (e) => this.handleMouseUp(e));
                
                // Cornerstone事件 - 确保检测框与图像变换同步
                this.element.addEventListener('cornerstoneimagerendered', (e) => {
                    // 图像渲染后更新检测框位置
                    if (this.detectionVisible) {
                        this.updateDetectionOverlay();
                    }
                });
                
                this.element.addEventListener('cornerstonenewimage', (e) => {
                    // 新图像加载后更新检测框
                    if (this.detectionVisible) {
                        this.updateDetectionOverlay();
                    }
                });
            }
            
            async loadStudy(studyId) {
                try {
                    console.log('开始加载研究:', studyId);
                    document.getElementById('loadingIndicator').style.display = 'block';
                    
                    // 获取研究信息
                    console.log('请求研究信息:', `/api/v1/studies/${studyId}`);
                    const response = await fetch(`/api/v1/studies/${studyId}`);
                    console.log('研究信息响应状态:', response.status);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    const studyData = await response.json();
                    console.log('研究数据:', studyData);
                    
                    if (studyData.success) {
                        // 更新患者信息
                        this.updateStudyInfo(studyData.data);
                        
                        this.loadSeriesList(studyData.series);
                        
                        // 加载第一个序列
                        if (studyData.series.length > 0) {
                            console.log('加载第一个序列:', studyData.series[0].id);
                            this.loadSeries(studyData.series[0].id);
                        } else {
                            console.warn('没有找到序列数据');
                            alert('该研究没有可用的序列数据');
                        }
                        
                        // 加载检测结果
                        this.loadDetectionResults(studyId);
                    } else {
                        throw new Error(studyData.message || '获取研究信息失败');
                    }
                } catch (error) {
                    console.error('加载研究失败:', error);
                    this.logError('加载研究失败: ' + error.message);
                    alert('加载研究失败: ' + error.message + '\n\n请打开调试面板查看详细日志');
                } finally {
                    document.getElementById('loadingIndicator').style.display = 'none';
                }
            }
            
            loadSeriesList(series) {
                const seriesList = document.getElementById('seriesList');
                seriesList.innerHTML = '';
                
                series.forEach((s, index) => {
                    const item = document.createElement('div');
                    item.className = 'series-item';
                    if (index === 0) item.classList.add('active');
                    
                    item.innerHTML = `
                        <div><strong>${s.description || '未知序列'}</strong></div>
                        <div><small>图像数: ${s.image_count}</small></div>
                    `;
                    
                    item.addEventListener('click', () => {
                        document.querySelectorAll('.series-item').forEach(el => el.classList.remove('active'));
                        item.classList.add('active');
                        this.loadSeries(s.id);
                    });
                    
                    seriesList.appendChild(item);
                });
            }
            
            async loadSeries(seriesId) {
                try {
                    console.log('开始加载序列:', seriesId);
                    this.currentSeriesId = seriesId;
                    
                    // 显示详细的调试信息
                    this.updateDebugInfo();
                    
                    // 获取序列图像
                    console.log('请求序列图像:', `/api/v1/series/${seriesId}/images`);
                    const response = await fetch(`/api/v1/series/${seriesId}/images`);
                    console.log('序列图像响应状态:', response.status);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    const imageData = await response.json();
                    console.log('序列图像数据:', imageData);
                    
                    if (imageData.success) {
                        this.images = imageData.images;
                        this.currentImageIndex = 0;
                        console.log('加载了', this.images.length, '张图像');
                        
                        // 更新图像滑块
                        const slider = document.getElementById('imageSlider');
                        slider.max = this.images.length - 1;
                        slider.value = 0;
                        
                        // 显示第一张图像
                        if (this.images.length > 0) {
                            console.log('显示第一张图像');
                            this.displayImage(0);
                        } else {
                            console.warn('序列中没有图像');
                            alert('该序列没有可用的图像');
                        }
                        
                        // 更新信息显示
                        this.updateImageInfo();
                    } else {
                        throw new Error(imageData.message || '获取序列图像失败');
                    }
                } catch (error) {
                    console.error('加载序列失败:', error);
                    this.logError('加载序列失败: ' + error.message);
                    alert('加载序列失败: ' + error.message + '\n\n请打开调试面板查看详细日志');
                }
            }
            
            async displayImage(index, retryCount = 0) {
                if (!this.images || index >= this.images.length) {
                    console.warn('无效的图像索引:', index, '总图像数:', this.images?.length);
                    return;
                }
                
                const maxRetries = 3;
                
                try {
                    const image = this.images[index];
                    const dicomEndpoint = `/api/v1/images/${image.id}/dicom`;
                    const imageId = `wadouri:${dicomEndpoint}`;
                    console.log(`开始加载图像 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, imageId);
                    
                    // 显示加载状态
                    const loadingIndicator = document.getElementById('loadingIndicator');
                    if (loadingIndicator) {
                        loadingIndicator.style.display = 'block';
                        loadingIndicator.textContent = `正在加载图像... (${retryCount + 1}/${maxRetries + 1})`;
                    }
                    
                    // 测试API端点是否可访问
                    console.log('测试DICOM API端点:', dicomEndpoint);
                    
                    // 使用AbortController实现超时
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 10000);
                    
                    const testResponse = await fetch(dicomEndpoint, { 
                        method: 'HEAD',
                        signal: controller.signal
                    });
                    clearTimeout(timeoutId);
                    console.log('API端点测试结果:', testResponse.status, testResponse.statusText);
                    
                    if (!testResponse.ok) {
                        throw new Error(`DICOM API端点不可访问: ${testResponse.status} ${testResponse.statusText}`);
                    }
                    
                    // 加载并显示图像
                    console.log('调用cornerstone.loadImage');
                    this.updateDebugInfo();
                    
                    const loadedImage = await cornerstone.loadImage(imageId);
                    console.log('图像加载成功，开始显示');
                    console.log('加载的图像信息:', loadedImage);
                    this.updateDebugInfo();
                    
                    cornerstone.displayImage(this.element, loadedImage);
                    console.log('图像显示完成');
                    this.updateDebugInfo();
                    
                    // 启用Cornerstone Tools（每次显示新图像后都需要重新启用）
                    if (this.toolsEnabled && typeof cornerstoneTools !== 'undefined') {
                        try {
                            // 注意：在Cornerstone Tools 4.x中，不需要调用cornerstoneTools.enable()
                            // 元素已经通过cornerstone.enable()启用
                            
                            // 重新设置默认工具
                            cornerstoneTools.setToolActiveForElement(this.element, 'Zoom', { mouseButtonMask: 1 });
                            cornerstoneTools.setToolActiveForElement(this.element, 'Pan', { mouseButtonMask: 4 });
                            cornerstoneTools.setToolActiveForElement(this.element, 'Wwwc', { mouseButtonMask: 2 });
                            cornerstoneTools.setToolActiveForElement(this.element, 'ZoomWheel', {});
                            
                            // 如果有当前工具，重新激活它
                            if (this.currentTool && this.currentTool !== 'zoom') {
                                this.setTool(this.currentTool);
                            }
                            
                            console.log('Cornerstone Tools已重新启用');
                        } catch (toolError) {
                            console.warn('重新启用工具时出错:', toolError);
                        }
                    }
                    
                    // 更新当前图像索引
                    this.currentImageIndex = index;
                    
                    // 更新信息显示
                    this.updateImageInfo();
                    
                    // 从DICOM数据中提取患者信息
                    this.updatePatientInfo(loadedImage);
                    
                    // 更新检测结果覆盖层
                    this.updateDetectionOverlay();
                    
                    // 更新检测结果显示
                    this.displayDetectionResults();
                    
                    // 更新检测状态显示
                    this.updateDetectionStatus();
                    
                    // 隐藏加载状态
                    const loadingIndicator1 = document.getElementById('loadingIndicator');
                    if (loadingIndicator1) {
                        loadingIndicator1.style.display = 'none';
                    }
                    
                } catch (error) {
                    console.error(`显示图像失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, error);
                    console.error('错误详情:', error.stack);
                    
                    // 记录错误到调试面板
                    this.logError(`显示图像失败 (尝试 ${retryCount + 1}): ${error.message}`);
                    
                    // 如果还有重试次数，则重试
                    if (retryCount < maxRetries) {
                        console.log(`将在2秒后重试...`);
                        this.logError(`将在2秒后重试 (${retryCount + 1}/${maxRetries})`);
                        setTimeout(() => {
                            this.displayImage(index, retryCount + 1);
                        }, 2000);
                        return;
                    }
                    
                    // 所有重试都失败了，显示错误
                    const loadingIndicator2 = document.getElementById('loadingIndicator');
                    if (loadingIndicator2) {
                        loadingIndicator2.style.display = 'none';
                    }
                    
                    // 更新调试面板
                    this.updateDebugInfo();
                    
                    // 显示更详细的错误信息
                    const errorMsg = `显示图像失败 (已重试${maxRetries}次): ${error.message}\n\n详细信息:\n- 图像ID: ${this.images[index]?.id}\n- 图像索引: ${index}\n- 总图像数: ${this.images?.length}\n- 错误类型: ${error.name}\n- 建议: 请检查网络连接和Orthanc服务状态\n\n请打开调试面板查看详细日志`;
                    alert(errorMsg);
                }
            }
            
            updateImageInfo() {
                if (!this.images || this.currentImageIndex >= this.images.length) return;
                
                const image = this.images[this.currentImageIndex];
                const viewport = cornerstone.getViewport(this.element);
                
                // 安全检查viewport对象
                if (!viewport || !viewport.voi) {
                    console.warn('Viewport或VOI信息不可用');
                    return;
                }
                
                // 安全更新DOM元素
                const imageNumber = document.getElementById('imageNumber');
                const totalImages = document.getElementById('totalImages');
                const windowWidth = document.getElementById('windowWidth');
                const windowCenter = document.getElementById('windowCenter');
                const zoomLevel = document.getElementById('zoomLevel');
                const imageCounter = document.getElementById('imageCounter');

                
                if (imageNumber) imageNumber.textContent = this.currentImageIndex + 1;
                if (totalImages) totalImages.textContent = this.images.length;
                if (windowWidth) windowWidth.textContent = Math.round(viewport.voi.windowWidth);
                if (windowCenter) windowCenter.textContent = Math.round(viewport.voi.windowCenter);
                if (zoomLevel) zoomLevel.textContent = Math.round(viewport.scale * 100) + '%';
                if (imageCounter) imageCounter.textContent = `${this.currentImageIndex + 1}/${this.images.length}`;

            }
            
            updatePatientInfo(loadedImage) {
                try {
                    const dataSet = loadedImage.data;
                    if (dataSet) {
                        // 提取患者性别
                        const patientSex = dataSet.string('x00100040');
                        if (patientSex) {
                            const sexDisplay = patientSex === 'M' ? '男' : patientSex === 'F' ? '女' : '未知';
                            const patientSexElement = document.getElementById('patient-sex');
                            if (patientSexElement) {
                                patientSexElement.textContent = sexDisplay;
                            }
                        }
                        
                        // 提取患者年龄
                        const patientAge = dataSet.string('x00101010');
                        if (patientAge) {
                            const patientAgeElement = document.getElementById('patient-age');
                            if (patientAgeElement) {
                                patientAgeElement.textContent = patientAge;
                            }
                        }
                        
                        // 提取患者出生日期并计算年龄
                        const patientBirthDate = dataSet.string('x00100030');
                        const studyDate = dataSet.string('x00080020');
                        if (patientBirthDate && studyDate && !patientAge) {
                            const birthYear = parseInt(patientBirthDate.substring(0, 4));
                            const studyYear = parseInt(studyDate.substring(0, 4));
                            const calculatedAge = studyYear - birthYear;
                            const patientAgeElement = document.getElementById('patient-age');
                            if (patientAgeElement) {
                                patientAgeElement.textContent = calculatedAge + '岁';
                            }
                        }
                        
                        // 提取序列描述
                        const seriesDescription = dataSet.string('x0008103e');
                        if (seriesDescription) {
                            const seriesDescriptionElement = document.getElementById('seriesDescription');
                            if (seriesDescriptionElement) {
                                seriesDescriptionElement.textContent = seriesDescription;
                            }
                        }
                        
                        // 提取患者姓名（如果页面上没有显示）
                        const patientName = dataSet.string('x00100010');
                        if (patientName) {
                            const patientNameElement = document.getElementById('patientName');
                            if (patientNameElement) {
                                patientNameElement.textContent = patientName;
                            }
                        }
                    }
                } catch (error) {
                    console.error('提取患者信息失败:', error);
                }
            }
            
            // 更新研究信息
            updateStudyInfo(studyData) {
                try {
                    if (studyData) {
                        // 更新患者姓名
                        const patientNameElement = document.getElementById('patientName');
                        if (patientNameElement && studyData.patient_name) {
                            patientNameElement.textContent = studyData.patient_name;
                        }
                        
                        // 更新患者ID
                        const patientIdElement = document.getElementById('patientId');
                        if (patientIdElement && studyData.patient_id) {
                            patientIdElement.textContent = studyData.patient_id;
                        }
                        
                        // 更新患者性别
                        const patientSexElement = document.getElementById('patient-sex');
                        if (patientSexElement && studyData.patient_sex) {
                            const sexDisplay = studyData.patient_sex === 'M' ? '男' : studyData.patient_sex === 'F' ? '女' : '未知';
                            patientSexElement.textContent = sexDisplay;
                        }
                        
                        // 更新患者年龄
                        const patientAgeElement = document.getElementById('patient-age');
                        if (patientAgeElement && studyData.patient_age) {
                            patientAgeElement.textContent = studyData.patient_age;
                        }
                        
                        // 更新研究日期
                        const studyDateElement = document.getElementById('studyDate');
                        if (studyDateElement && studyData.study_date) {
                            studyDateElement.textContent = studyData.study_date;
                        }
                        
                        console.log('研究信息已更新:', studyData);
                    }
                } catch (error) {
                    console.error('更新研究信息失败:', error);
                }
            }
            
            setTool(toolName) {
                 // 重置所有工具按钮状态
                 document.querySelectorAll('.toolbar button').forEach(btn => {
                     btn.classList.remove('active');
                 });
                 
                 // 激活当前工具按钮
                 const activeButton = document.getElementById(toolName + 'Tool');
                 if (activeButton) {
                     activeButton.classList.add('active');
                 }
                 
                 this.currentTool = toolName;
                 
                 // 如果工具可用，设置工具状态
                 if (this.toolsEnabled && typeof cornerstoneTools !== 'undefined') {
                     try {
                         // 禁用所有工具（安全地）
                         const toolsToDisable = ['Zoom', 'Pan', 'Wwwc', 'Length', 'EllipticalRoi', 'Probe'];
                         toolsToDisable.forEach(tool => {
                             try {
                                 cornerstoneTools.setToolDisabledForElement(this.element, tool);
                             } catch (e) {
                                 console.warn(`无法禁用工具 ${tool}:`, e.message);
                             }
                         });
                         
                         // 激活选定的工具
                         switch(toolName) {
                             case 'zoom':
                                 cornerstoneTools.setToolActiveForElement(this.element, 'Zoom', { mouseButtonMask: 1 });
                                 break;
                             case 'pan':
                                 cornerstoneTools.setToolActiveForElement(this.element, 'Pan', { mouseButtonMask: 1 });
                                 break;
                             case 'window':
                                 cornerstoneTools.setToolActiveForElement(this.element, 'Wwwc', { mouseButtonMask: 1 });
                                 break;
                             case 'length':
                                 try {
                                     // 为当前元素激活长度工具
                                     cornerstoneTools.setToolActiveForElement(this.element, 'Length', { mouseButtonMask: 1 });
                                     console.log('长度工具已激活');
                                 } catch (e) {
                                     console.warn('长度工具不可用:', e.message);
                                     return;
                                 }
                                 break;
                             case 'area':
                                 try {
                                     // 为当前元素激活面积工具
                                     cornerstoneTools.setToolActiveForElement(this.element, 'EllipticalRoi', { mouseButtonMask: 1 });
                                     console.log('面积工具已激活');
                                 } catch (e) {
                                     console.warn('面积工具不可用:', e.message);
                                     return;
                                 }
                                 break;
                             case 'probe':
                                 try {
                                     // 为当前元素激活CT值工具
                                     cornerstoneTools.setToolActiveForElement(this.element, 'Probe', { mouseButtonMask: 1 });
                                     console.log('CT值工具已激活');
                                 } catch (e) {
                                     console.warn('CT值工具不可用:', e.message);
                                     return;
                                 }
                                 break;
                         }
                         
                         // 更新按钮状态
                         this.updateToolbarButtons(toolName);
                         console.log('工具切换成功:', toolName);
                         
                         cornerstone.updateImage(this.element);
                     } catch (error) {
                         console.error('设置工具失败:', error);
                     }
                 }
             }
             
             updateToolbarButtons(activeTool) {
                 // 移除所有按钮的激活状态
                 const toolButtons = document.querySelectorAll('.left-toolbar .tool-btn');
                 toolButtons.forEach(btn => btn.classList.remove('active'));
                 
                 // 激活当前工具按钮
                 const toolMap = {
                     'zoom': 'zoomTool',
                     'pan': 'panTool',
                     'window': 'windowTool',
                     'length': 'lengthTool',
                     'area': 'areaTool',
                     'probe': 'probeTool'
                 };
                 
                 const buttonId = toolMap[activeTool];
                 if (buttonId) {
                     const button = document.getElementById(buttonId);
                     if (button) {
                         button.classList.add('active');
                     }
                 }
             }
             
             clearMeasurements() {
                 if (this.toolsEnabled && typeof cornerstoneTools !== 'undefined') {
                     try {
                         // 尝试使用全局工具状态管理器清除
                         if (cornerstoneTools.globalImageIdSpecificToolStateManager) {
                             cornerstoneTools.globalImageIdSpecificToolStateManager.clear(this.element);
                         }
                         
                         // 逐个清除特定工具的状态
                         const toolNames = ['Length', 'EllipticalRoi', 'Probe'];
                         toolNames.forEach(toolName => {
                             try {
                                 const toolState = cornerstoneTools.getToolState(this.element, toolName);
                                 if (toolState && toolState.data) {
                                     toolState.data.length = 0;
                                 }
                             } catch (toolError) {
                                 console.warn(`清除${toolName}工具状态失败:`, toolError.message);
                             }
                         });
                         
                         cornerstone.updateImage(this.element);
                         console.log('测量数据已清除');
                     } catch (error) {
                         console.error('清除测量失败:', error);
                         // 即使清除失败，也尝试更新图像
                         try {
                             cornerstone.updateImage(this.element);
                         } catch (updateError) {
                             console.error('更新图像也失败:', updateError);
                         }
                     }
                 }
             }
            
            setWindowLevel(windowWidth, windowCenter) {
                const viewport = cornerstone.getViewport(this.element);
                
                // 安全检查viewport对象
                if (!viewport || !viewport.voi) {
                    console.warn('Viewport或VOI信息不可用，无法设置窗宽窗位');
                    return;
                }
                
                if (windowWidth !== null) viewport.voi.windowWidth = windowWidth;
                if (windowCenter !== null) viewport.voi.windowCenter = windowCenter;
                
                cornerstone.setViewport(this.element, viewport);
                this.updateImageInfo();
            }
            
            resetView() {
                cornerstone.reset(this.element);
                this.updateImageInfo();
            }
            
            toggleFullscreen() {
                if (!document.fullscreenElement) {
                    this.element.requestFullscreen();
                } else {
                    document.exitFullscreen();
                }
            }
            
            // 鼠标事件处理
            handleWheel(e) {
                e.preventDefault();
                
                if (this.currentTool === 'zoom') {
                    const viewport = cornerstone.getViewport(this.element);
                    if (!viewport) {
                        console.warn('Viewport不可用，无法进行缩放');
                        return;
                    }
                    const scaleFactor = e.deltaY > 0 ? 0.9 : 1.1;
                    viewport.scale *= scaleFactor;
                    cornerstone.setViewport(this.element, viewport);
                    this.updateImageInfo();
                    this.updateDetectionOverlay();
                } else {
                    // 滚轮切换图像
                    const direction = e.deltaY > 0 ? 1 : -1;
                    const newIndex = Math.max(0, Math.min(this.images.length - 1, this.currentImageIndex + direction));
                    
                    if (newIndex !== this.currentImageIndex) {
                        this.displayImage(newIndex);
                        document.getElementById('imageSlider').value = newIndex;
                    }
                }
            }
            
            handleMouseDown(e) {
                this.isDragging = true;
                this.lastMousePos = { x: e.clientX, y: e.clientY };
            }
            
            handleMouseMove(e) {
                if (!this.isDragging) return;
                
                const deltaX = e.clientX - this.lastMousePos.x;
                const deltaY = e.clientY - this.lastMousePos.y;
                
                if (this.currentTool === 'pan') {
                    const viewport = cornerstone.getViewport(this.element);
                    if (!viewport) {
                        console.warn('Viewport不可用，无法进行平移');
                        return;
                    }
                    viewport.translation.x += deltaX;
                    viewport.translation.y += deltaY;
                    cornerstone.setViewport(this.element, viewport);
                } else if (this.currentTool === 'window') {
                    const viewport = cornerstone.getViewport(this.element);
                    if (!viewport || !viewport.voi) {
                        console.warn('Viewport或VOI信息不可用，无法调整窗宽窗位');
                        return;
                    }
                    viewport.voi.windowWidth += deltaX * 4;
                    viewport.voi.windowCenter += deltaY * 4;
                    cornerstone.setViewport(this.element, viewport);
                    this.updateImageInfo();
                }
                
                this.lastMousePos = { x: e.clientX, y: e.clientY };
            }
            
            handleMouseUp(e) {
                this.isDragging = false;
            }
            
            // 解析URL参数
            parseUrlParameters() {
                const urlParams = new URLSearchParams(window.location.search);
                const detectionParam = urlParams.get('detection');
                if (detectionParam) {
                    try {
                        const detectionData = JSON.parse(decodeURIComponent(detectionParam));
                        this.detections = detectionData;
                        console.log('从URL参数加载检测结果:', this.detections);
                    } catch (error) {
                        console.error('解析检测结果参数失败:', error);
                    }
                }
            }
            
            // 加载检测结果
            async loadDetectionResults(studyId) {
                try {
                    const response = await fetch(`/api/v1/studies/${studyId}/detections`);
                    const data = await response.json();
                    
                    if (data.success && data.detections && data.detections.length > 0) {
                        // 处理检测结果数据结构
                        this.allDetections = [];
                        data.detections.forEach(result => {
                            if (result.result_data && Array.isArray(result.result_data)) {
                                result.result_data.forEach(imageResult => {
                                    if (imageResult.detections && Array.isArray(imageResult.detections)) {
                                        imageResult.detections.forEach(detection => {
                                            this.allDetections.push({
                                                ...detection,
                                                image_index: imageResult.image_index !== undefined ? imageResult.image_index : 0
                                            });
                                        });
                                    }
                                });
                            }
                        });
                        
                        this.displayDetectionResults();
                        console.log('检测结果加载成功:', this.allDetections);
                    } else {
                        console.log('暂无检测结果，自动启动检测...');
                        this.allDetections = [];
                        // 自动启动检测
                        await this.autoStartDetection(studyId);
                    }
                } catch (error) {
                    console.error('加载检测结果失败:', error);
                    this.allDetections = [];
                    // 如果加载失败，也尝试自动启动检测
                    try {
                        await this.autoStartDetection(studyId);
                    } catch (detectError) {
                        console.error('自动检测也失败了:', detectError);
                    }
                }
            }
            
            // 显示检测结果
            displayDetectionResults() {
                const resultsContainer = document.getElementById('detectionResults');
                
                if (!this.allDetections || this.allDetections.length === 0) {
                    resultsContainer.innerHTML = '<div class="text-center text-muted">暂无检测结果</div>';
                    // 更新病灶分布显示
                    this.updateLesionImages();
                    return;
                }
                
                resultsContainer.innerHTML = '';
                
                // 获取当前图像的检测结果
                const currentImageDetections = this.getCurrentImageDetections();
                
                if (currentImageDetections.length === 0) {
                    resultsContainer.innerHTML = '<div class="text-center text-muted">当前图像无检测结果</div>';
                } else {
                    currentImageDetections.forEach((detection, index) => {
                        const item = document.createElement('div');
                        item.className = 'detection-item';
                        item.innerHTML = `
                            <div><strong>${detection.class || '未知类别'}</strong></div>
                            <div><small>置信度: ${(detection.confidence * 100).toFixed(1)}%</small></div>
                            <div><small>位置: (${Math.round(detection.x)}, ${Math.round(detection.y)})</small></div>
                        `;
                        
                        item.addEventListener('click', () => {
                            // 高亮显示对应的检测框
                            this.highlightDetection(index);
                        });
                        
                        resultsContainer.appendChild(item);
                    });
                }
                
                // 更新检测覆盖层
                this.updateDetectionOverlay();
                
                // 更新病灶分布显示
                this.updateLesionImages();
            }
            
            // 更新检测覆盖层
            updateDetectionOverlay() {
                if (!this.detectionOverlay) return;
                
                this.detectionOverlay.innerHTML = '';
                
                if (!this.detectionVisible || !this.allDetections || this.allDetections.length === 0) {
                    return;
                }
                
                const viewport = cornerstone.getViewport(this.element);
                if (!viewport) return;
                
                // 只显示当前图像的检测结果
                const currentImageDetections = this.getCurrentImageDetections();
                
                currentImageDetections.forEach((detection, index) => {
                    const box = document.createElement('div');
                    box.className = 'detection-box';
                    
                    // 获取canvas的边界矩形
                    const canvasRect = this.element.getBoundingClientRect();
                    const overlayRect = this.detectionOverlay.getBoundingClientRect();
                    
                    // 计算canvas相对于overlay的偏移
                    const offsetX = canvasRect.left - overlayRect.left;
                    const offsetY = canvasRect.top - overlayRect.top;
                    
                    // 转换坐标（detection.x和detection.y是中心点，需要转换为左上角坐标）
                    const width = detection.width * viewport.scale;
                    const height = detection.height * viewport.scale;
                    const x = (detection.x - detection.width / 2) * viewport.scale + viewport.translation.x + offsetX;
                    const y = (detection.y - detection.height / 2) * viewport.scale + viewport.translation.y + offsetY;
                    
                    box.style.left = x + 'px';
                    box.style.top = y + 'px';
                    box.style.width = width + 'px';
                    box.style.height = height + 'px';
                    
                    // 添加标签
                    const label = document.createElement('div');
                    label.className = 'detection-label';
                    label.textContent = `${detection.class} (${(detection.confidence * 100).toFixed(1)}%)`;
                    box.appendChild(label);
                    
                    this.detectionOverlay.appendChild(box);
                });
            }
            
            // 切换检测结果显示
            toggleDetection() {
                this.detectionVisible = !this.detectionVisible;
                this.updateDetectionOverlay();
                
                const button = document.getElementById('toggleDetection');
                if (this.detectionVisible) {
                    button.classList.add('active');
                } else {
                    button.classList.remove('active');
                }
            }
            
            // 清除检测结果
            clearDetectionResults() {
                // 先清空前端显示
                this.allDetections = [];
                this.detectionVisible = false;
                this.updateDetectionOverlay();
                
                // 清空检测结果显示区域
                const resultsContainer = document.getElementById('detectionResults');
                if (resultsContainer) {
                    resultsContainer.innerHTML = '<div class="text-center text-muted"><i class="fas fa-info-circle"></i> 检测结果已清除</div>';
                }
                
                // 更新病灶分布显示
                this.updateLesionImages();
                
                // 清空检测统计信息
                const statsElement = document.getElementById('detection-stats');
                if (statsElement) {
                    statsElement.innerHTML = `
                        <div class="stat-item">
                            <span class="stat-label">总检测数</span>
                            <span class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">高风险</span>
                            <span class="stat-value text-danger">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">中风险</span>
                            <span class="stat-value text-warning">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">低风险</span>
                            <span class="stat-value text-success">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">平均置信度</span>
                            <span class="stat-value">0%</span>
                        </div>
                    `;
                }
                
                // 更新切换按钮状态
                const toggleButton = document.getElementById('toggleDetection');
                if (toggleButton) {
                    toggleButton.classList.remove('active');
                }
                
                // 调用后端API彻底删除数据库中的检测结果
                if (this.studyId) {
                    fetch(`/api/studies/${this.studyId}/detections`, {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            console.log('检测结果已从数据库中彻底删除');
                        } else {
                            console.error('删除数据库检测结果失败:', result.message);
                        }
                    })
                    .catch(error => {
                        console.error('删除检测结果API调用失败:', error);
                    });
                }
                
                console.log('检测结果已清除');
            }
            
            // 切换调试面板
            toggleDebugPanel() {
                const debugPanel = document.getElementById('debugPanel');
                if (debugPanel.style.display === 'none') {
                    debugPanel.style.display = 'block';
                    this.updateDebugInfo();
                } else {
                    debugPanel.style.display = 'none';
                }
            }
            
            // 更新调试信息
            updateDebugInfo() {
                try {
                    // 更新Cornerstone状态
                    const cornerstoneStatus = document.getElementById('cornerstoneStatus');
                    cornerstoneStatus.textContent = typeof cornerstone !== 'undefined' ? '已加载' : '未加载';
                    cornerstoneStatus.className = typeof cornerstone !== 'undefined' ? 'text-success' : 'text-danger';
                    
                    // 更新WADO加载器状态
                    const wadoStatus = document.getElementById('wadoStatus');
                    wadoStatus.textContent = typeof cornerstoneWADOImageLoader !== 'undefined' ? '已加载' : '未加载';
                    wadoStatus.className = typeof cornerstoneWADOImageLoader !== 'undefined' ? 'text-success' : 'text-danger';
                    
                    // 更新当前图像ID
                    const currentImageId = document.getElementById('currentImageId');
                    if (this.images && this.currentImageIndex < this.images.length) {
                        const image = this.images[this.currentImageIndex];
                        const dicomEndpoint = `/api/v1/images/${image.id}/dicom`;
                        currentImageId.textContent = `wadouri:${dicomEndpoint}`;
                    } else {
                        currentImageId.textContent = '无';
                    }
                    
                    // 测试API连接状态
                    this.testApiConnection();
                    
                } catch (error) {
                    this.logError('更新调试信息失败: ' + error.message);
                }
            }
            
            // 测试API连接
            async testApiConnection() {
                try {
                    const apiStatus = document.getElementById('apiStatus');
                    apiStatus.textContent = '测试中...';
                    apiStatus.className = 'text-warning';
                    
                    const response = await fetch('/api/v1/health');
                    if (response.ok) {
                        apiStatus.textContent = '连接正常';
                        apiStatus.className = 'text-success';
                    } else {
                        apiStatus.textContent = `连接异常 (${response.status})`;
                        apiStatus.className = 'text-danger';
                    }
                } catch (error) {
                    const apiStatus = document.getElementById('apiStatus');
                    apiStatus.textContent = '连接失败';
                    apiStatus.className = 'text-danger';
                    this.logError('API连接测试失败: ' + error.message);
                }
            }
            
            // 记录错误到调试面板
            logError(message) {
                const errorLog = document.getElementById('errorLog');
                
                // 添加安全检查
                if (!errorLog) {
                    console.warn('errorLog元素未找到，错误信息:', message);
                    return;
                }
                
                const timestamp = new Date().toLocaleTimeString();
                const errorEntry = `[${timestamp}] ${message}`;
                
                if (errorLog.textContent === '无错误') {
                    errorLog.textContent = errorEntry;
                } else {
                    errorLog.textContent = errorEntry + '\n' + errorLog.textContent;
                }
                
                // 限制日志长度
                const lines = errorLog.textContent.split('\n');
                if (lines.length > 20) {
                    errorLog.textContent = lines.slice(0, 20).join('\n');
                }
            }
            
            // 清除调试信息
            clearDebugInfo() {
                const errorLog = document.getElementById('errorLog');
                if (errorLog) {
                    errorLog.textContent = '无错误';
                }
                console.log('调试信息已清除');
            }
            
            // 更新检测状态
            updateDetectionStatus() {
                const statusContainer = document.getElementById('detectionStatus');
                
                // 添加安全检查，防止访问null元素
                if (!statusContainer) {
                    console.warn('detectionStatus元素未找到');
                    return;
                }
                
                if (this.allDetections && this.allDetections.length > 0) {
                    statusContainer.innerHTML = `
                        <div class="text-success">
                            <i class="fas fa-check-circle me-1"></i>
                            检测完成 (${this.allDetections.length} 个结果)
                        </div>
                    `;
                } else {
                    statusContainer.innerHTML = `
                        <div class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            暂无检测结果
                        </div>
                    `;
                }
            }
            
            // 获取当前图像的检测结果
            getCurrentImageDetections() {
                if (!this.allDetections || this.currentImageIndex === undefined) {
                    return [];
                }
                return this.allDetections.filter(detection => 
                    detection.image_index === this.currentImageIndex
                );
            }
            
            // 手动运行检测
            async runDetection() {
                try {
                    if (!this.currentStudyId) {
                        alert('未找到研究ID，无法进行检测');
                        return;
                    }
                    
                    console.log('开始手动检测研究:', this.currentStudyId);
                    
                    // 清除之前的检测结果
                    this.allDetections = [];
                    this.detectionVisible = false;
                    this.updateDetectionOverlay();
                    
                    // 显示检测进度提示
                    const resultsContainer = document.getElementById('detectionResults');
                    if (resultsContainer) {
                        resultsContainer.innerHTML = '<div class="text-center text-info"><i class="fas fa-spinner fa-spin"></i> 正在检测中...</div>';
                    }
                    
                    // 更新按钮状态
                    const runButton = document.getElementById('runDetection');
                    if (runButton) {
                        runButton.disabled = true;
                        runButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    }
                    
                    // 调用检测API
                    const response = await fetch(`/api/v1/detect/${this.currentStudyId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            confidence: 0.25,
                            save_images: true
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.status === 'success') {
                        console.log('手动检测完成:', result);
                        
                        // 重新加载检测结果
                        await this.loadDetectionResults(this.currentStudyId);
                        
                        // 显示成功消息
                        if (resultsContainer) {
                            resultsContainer.innerHTML = '<div class="text-center text-success"><i class="fas fa-check"></i> 检测完成！</div>';
                            setTimeout(() => {
                                this.displayDetectionResults();
                            }, 1000);
                        }
                        
                        // 自动显示检测结果
                        this.detectionVisible = true;
                        this.updateDetectionOverlay();
                        
                        alert('YOLO检测完成！检测结果已显示在图像上。');
                    } else {
                        throw new Error(result.message || '检测失败');
                    }
                } catch (error) {
                    console.error('手动检测失败:', error);
                    
                    // 显示错误消息
                    const resultsContainer = document.getElementById('detectionResults');
                    if (resultsContainer) {
                        resultsContainer.innerHTML = `<div class="text-center text-danger"><i class="fas fa-exclamation-triangle"></i> 检测失败: ${error.message}</div>`;
                    }
                    
                    alert(`检测失败: ${error.message}`);
                } finally {
                    // 恢复按钮状态
                    const runButton = document.getElementById('runDetection');
                    if (runButton) {
                        runButton.disabled = false;
                        runButton.innerHTML = '<i class="fas fa-brain"></i>';
                    }
                }
            }
            
            // 自动启动检测
            async autoStartDetection(studyId) {
                try {
                    console.log('开始自动检测研究:', studyId);
                    
                    // 显示检测进度提示
                    const resultsContainer = document.getElementById('detectionResults');
                    if (resultsContainer) {
                        resultsContainer.innerHTML = '<div class="text-center text-info"><i class="fas fa-spinner fa-spin"></i> 正在自动检测中...</div>';
                    }
                    
                    // 调用检测API
                    const response = await fetch(`/api/v1/detect/${studyId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            confidence: 0.25,
                            save_images: true
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.status === 'success') {
                        console.log('自动检测完成:', result);
                        
                        // 直接加载检测结果，不调用loadDetectionResults避免递归
                        try {
                            const detectionResponse = await fetch(`/api/v1/studies/${studyId}/detections`);
                            const detectionData = await detectionResponse.json();
                            
                            if (detectionData.success && detectionData.detections && detectionData.detections.length > 0) {
                                // 处理检测结果数据结构
                                this.allDetections = [];
                                detectionData.detections.forEach(result => {
                                    if (result.result_data && Array.isArray(result.result_data)) {
                                        result.result_data.forEach(imageResult => {
                                            if (imageResult.detections && Array.isArray(imageResult.detections)) {
                                                imageResult.detections.forEach(detection => {
                                                    this.allDetections.push({
                                                        ...detection,
                                                        image_index: imageResult.image_index !== undefined ? imageResult.image_index : 0
                                                    });
                                                });
                                            }
                                        });
                                    }
                                });
                                
                                this.displayDetectionResults();
                                console.log('自动检测结果加载成功:', this.allDetections);
                            }
                        } catch (loadError) {
                            console.error('加载检测结果失败:', loadError);
                        }
                        
                        // 显示成功消息
                        if (resultsContainer) {
                            resultsContainer.innerHTML = '<div class="text-center text-success"><i class="fas fa-check"></i> 检测完成！</div>';
                            setTimeout(() => {
                                this.displayDetectionResults();
                            }, 1000);
                        }
                    } else {
                        throw new Error(result.message || '检测失败');
                    }
                } catch (error) {
                    console.error('自动检测失败:', error);
                    
                    // 显示错误消息
                    const resultsContainer = document.getElementById('detectionResults');
                    if (resultsContainer) {
                        resultsContainer.innerHTML = `<div class="text-center text-danger"><i class="fas fa-exclamation-triangle"></i> 自动检测失败: ${error.message}</div>`;
                    }
                }
            }
            
            // 更新病灶分布显示
            updateLesionImages() {
                const lesionContainer = document.getElementById('lesionImages');
                const lesionCountBadge = document.getElementById('lesionImageCount');
                
                if (!lesionContainer || !lesionCountBadge) {
                    console.warn('病灶显示元素未找到');
                    return;
                }
                
                // 统计有病灶的图像
                const lesionImages = new Map();
                
                if (this.allDetections && this.allDetections.length > 0) {
                    this.allDetections.forEach((detection) => {
                        const imageIndex = detection.image_index !== undefined ? detection.image_index : 0;
                        if (!lesionImages.has(imageIndex)) {
                            lesionImages.set(imageIndex, []);
                        }
                        lesionImages.get(imageIndex).push(detection);
                    });
                }
                
                lesionCountBadge.textContent = lesionImages.size;
                
                if (lesionImages.size === 0) {
                    lesionContainer.innerHTML = '<div class="text-center text-muted small">暂无病灶图像</div>';
                    return;
                }
                
                lesionContainer.innerHTML = '';
                
                lesionImages.forEach((detections, imageIndex) => {
                    const item = document.createElement('div');
                    item.className = 'lesion-image-item';
                    item.innerHTML = `
                        <div class="lesion-thumbnail">
                            ${imageIndex + 1}
                        </div>
                        <div class="lesion-info">
                            <div>图像 ${imageIndex + 1}</div>
                            <div class="lesion-count">${detections.length} 个病灶</div>
                        </div>
                    `;
                    
                    item.addEventListener('click', () => {
                        // 跳转到对应图像
                        if (imageIndex < this.images.length) {
                            this.currentImageIndex = imageIndex;
                            this.displayImage(this.currentImageIndex);
                            
                            // 高亮当前项
                            document.querySelectorAll('.lesion-image-item').forEach(el => el.classList.remove('active'));
                            item.classList.add('active');
                        }
                    });
                    
                    lesionContainer.appendChild(item);
                });
            }
            
            // 生成检查所见
            generateFindings() {
                const findingsText = document.getElementById('findingsText');
                if (!findingsText) {
                    console.warn('检查所见文本框未找到');
                    return;
                }
                
                let findings = '';
                
                if (this.allDetections && this.allDetections.length > 0) {
                    const totalLesionCount = this.allDetections.length;
                    const allLesionTypes = [...new Set(this.allDetections.map(d => d.class || '异常病灶'))];
                    
                    // 统计有病灶的图像数量
                    const lesionImageCount = new Set(this.allDetections.map(d => d.image_index)).size;
                    
                    findings = `检查所见：\n`;
                    findings += `本次检查共发现 ${totalLesionCount} 处异常病灶，分布在 ${lesionImageCount} 张图像中。\n`;
                    
                    if (allLesionTypes.length > 0) {
                        findings += `病灶类型包括：${allLesionTypes.join('、')}。\n`;
                    }
                    
                    findings += `病灶分布情况：\n`;
                    
                    // 按图像分组显示病灶
                    const lesionsByImage = new Map();
                    this.allDetections.forEach(detection => {
                        const imageIndex = detection.image_index || 0;
                        if (!lesionsByImage.has(imageIndex)) {
                            lesionsByImage.set(imageIndex, []);
                        }
                        lesionsByImage.get(imageIndex).push(detection);
                    });
                    
                    lesionsByImage.forEach((detections, imageIndex) => {
                        findings += `图像 ${imageIndex + 1}：\n`;
                        detections.forEach((detection, index) => {
                            const confidence = (detection.confidence * 100).toFixed(1);
                            findings += `  ${index + 1}. ${detection.class || '异常病灶'}，置信度 ${confidence}%\n`;
                        });
                    });
                } else {
                    findings = '检查所见：\n未发现明显异常病灶。\n影像学表现正常。';
                }
                
                findingsText.value = findings;
            }
            
            // 生成指南意见
            generateGuideline() {
                const guidelineText = document.getElementById('guidelineText');
                if (!guidelineText) {
                    console.warn('指南意见文本框未找到');
                    return;
                }
                
                let guideline = '';
                
                if (this.allDetections && this.allDetections.length > 0) {
                    const lesionCount = this.allDetections.length;
                    const lesionImageCount = new Set(this.allDetections.map(d => d.image_index)).size;
                    
                    guideline = `指南意见：\n`;
                    
                    if (lesionCount > 0) {
                        guideline += `1. 建议结合临床症状进一步评估\n`;
                        guideline += `2. 必要时可行增强扫描或其他影像学检查\n`;
                        guideline += `3. 建议定期随访观察病灶变化\n`;
                        
                        if (lesionCount > 5) {
                            guideline += `4. 病灶较多，建议多学科会诊\n`;
                        }
                        
                        if (lesionImageCount > 1) {
                            guideline += `5. 病灶分布较广，建议密切关注病情进展`;
                        }
                    }
                } else {
                    guideline = '指南意见：\n影像学表现正常，建议定期体检随访。';
                }
                
                guidelineText.value = guideline;
            }

        }
        
        // 初始化查看器
        document.addEventListener('DOMContentLoaded', () => {
            window.dicomViewer = new DICOMViewer();
            window.dicomViewer.init();
            
            // 将查看器实例设置为全局变量，以便HTML中的按钮可以访问
            window.viewer = window.dicomViewer;
        });
        
        // 全局函数，供HTML按钮调用
        function generateFindings() {
            if (window.viewer) {
                window.viewer.generateFindings();
            }
        }
        
        function generateGuideline() {
            if (window.viewer) {
                window.viewer.generateGuideline();
            }
        }
    </script>
    
    <!-- AI预测扩展集成脚本 -->
    <script>
        // AI预测扩展配置
        const AI_EXTENSION_CONFIG = {
            apiEndpoints: {
                models: '/api/v1/ai/models',
                predict: '/api/v1/ai/predict',
                switch: '/api/v1/ai/models/{model_id}/switch',
                info: '/api/v1/ai/models/{model_id}/info',
                extensionInfo: '/api/v1/ai/extension/info'
            },
            options: {
                autoDetection: false,
                showConfidence: true,
                minConfidence: 0.5,
                maxDetections: 10
            }
        };
        
        // 扩展DICOMViewer类以支持AI预测
        (function() {
            // 保存原始初始化方法
            const originalInit = DICOMViewer.prototype.init;
            
            // 重写初始化方法
            DICOMViewer.prototype.init = function() {
                // 调用原始初始化
                originalInit.call(this);
                
                // 初始化AI扩展
                this.initAIExtension();
            };
            
            // AI扩展初始化
            DICOMViewer.prototype.initAIExtension = function() {
                try {
                    // 创建AI扩展实例
                    this.aiExtension = {
                        config: AI_EXTENSION_CONFIG,
                        models: [],
                        activeModel: null,
                        isLoading: false,
                        lastPrediction: null
                    };
                    
                    // 加载可用模型
                    this.loadAIModels();
                    
                    // 添加AI工具栏按钮
                    this.addAIToolbarButtons();
                    
                    // 添加AI结果面板
                    this.addAIResultsPanel();
                    
                    console.log('AI预测扩展已集成');
                } catch (error) {
                    console.error('AI预测扩展集成失败:', error);
                }
            };
            
            // 加载可用AI模型
            DICOMViewer.prototype.loadAIModels = function() {
                fetch(AI_EXTENSION_CONFIG.apiEndpoints.models)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.aiExtension.models = data.models;
                            this.aiExtension.activeModel = data.models.find(m => m.is_active) || data.models[0];
                            this.updateAIModelSelector();
                            console.log('已加载AI模型:', data.models.length);
                        }
                    })
                    .catch(error => {
                        console.error('加载AI模型失败:', error);
                    });
            };
            
            // 添加AI工具栏按钮
            DICOMViewer.prototype.addAIToolbarButtons = function() {
                const toolbar = document.querySelector('.left-toolbar');
                if (!toolbar) return;
                
                // 创建AI工具组
                const aiToolGroup = document.createElement('div');
                aiToolGroup.className = 'ai-tool-group d-flex gap-2 align-items-center';
                aiToolGroup.style.marginLeft = 'auto';
                
                // 模型选择器
                const modelSelector = document.createElement('select');
                modelSelector.id = 'ai-model-selector';
                modelSelector.className = 'form-select form-select-sm';
                modelSelector.style.width = '200px';
                modelSelector.innerHTML = '<option value="">选择AI模型...</option>';
                modelSelector.onchange = (e) => this.switchAIModel(e.target.value);
                
                // AI预测按钮
                const predictBtn = document.createElement('button');
                predictBtn.id = 'ai-predict-btn';
                predictBtn.className = 'btn btn-outline-primary btn-sm';
                predictBtn.innerHTML = '<i class="fas fa-brain me-1"></i>AI检测';
                predictBtn.onclick = () => this.handleAIPrediction();
                
                // 切换边界框按钮
                const toggleBtn = document.createElement('button');
                toggleBtn.id = 'ai-toggle-bbox-btn';
                toggleBtn.className = 'btn btn-outline-success btn-sm';
                toggleBtn.innerHTML = '<i class="fas fa-eye me-1"></i>显示边界框';
                toggleBtn.onclick = () => this.toggleAIBoundingBoxes();
                
                // 清除结果按钮
                const clearBtn = document.createElement('button');
                clearBtn.id = 'ai-clear-btn';
                clearBtn.className = 'btn btn-outline-danger btn-sm';
                clearBtn.innerHTML = '<i class="fas fa-trash me-1"></i>清除';
                clearBtn.onclick = () => this.clearAIResults();
                
                aiToolGroup.appendChild(modelSelector);
                aiToolGroup.appendChild(predictBtn);
                aiToolGroup.appendChild(toggleBtn);
                aiToolGroup.appendChild(clearBtn);
                
                toolbar.appendChild(aiToolGroup);
            };
            
            // 添加AI结果面板
            DICOMViewer.prototype.addAIResultsPanel = function() {
                const container = document.querySelector('.container-fluid');
                if (!container) return;
                
                const aiPanel = document.createElement('div');
                aiPanel.id = 'ai-results-panel';
                aiPanel.className = 'card mt-3 shadow-sm';
                aiPanel.style.cssText = `
                    display: none;
                    max-height: 500px;
                    overflow-y: auto;
                    border: none;
                    border-radius: 12px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
                `;
                aiPanel.innerHTML = `
                    <div class="card-header bg-gradient" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px 12px 0 0;">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 text-white">
                                <i class="fas fa-brain me-2"></i>
                                AI智能检测报告
                            </h5>
                            <div class="d-flex gap-2">
                                <button class="btn btn-sm btn-light btn-outline-light" onclick="this.parentElement.parentElement.parentElement.parentElement.scrollIntoView({behavior: 'smooth'})" title="回到顶部">
                                    <i class="fas fa-arrow-up"></i>
                                </button>
                                <button class="btn btn-sm btn-light btn-outline-light" onclick="document.getElementById('ai-results-panel').style.display='none'" title="关闭面板">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div style="padding: 1.5rem;">
                            <div class="text-center py-4">
                                <i class="fas fa-robot text-muted fa-2x mb-2"></i>
                                <div class="text-muted">此面板已被新的检测结果面板替代</div>
                            </div>
                        </div>
                    </div>
                `;
                
                container.appendChild(aiPanel);
            };
            
            // 更新AI模型选择器
            DICOMViewer.prototype.updateAIModelSelector = function() {
                const selector = document.getElementById('ai-model-selector');
                if (!selector || !this.aiExtension.models) return;
                
                selector.innerHTML = '<option value="">选择AI模型...</option>';
                
                this.aiExtension.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = `${model.name} (${model.modality})`;
                    option.selected = model.is_active;
                    selector.appendChild(option);
                });
            };
            
            // 切换AI模型
            DICOMViewer.prototype.switchAIModel = function(modelId) {
                if (!modelId) return;
                
                const url = AI_EXTENSION_CONFIG.apiEndpoints.switch.replace('{model_id}', modelId);
                
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.aiExtension.activeModel = this.aiExtension.models.find(m => m.id === modelId);
                        console.log('已切换到模型:', modelId);
                    } else {
                        alert('切换模型失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('切换模型错误:', error);
                    alert('切换模型发生错误');
                });
            };
            
            // 处理AI预测
            DICOMViewer.prototype.handleAIPrediction = function() {
                if (!this.aiExtension.activeModel) {
                    alert('请先选择AI模型');
                    return;
                }
                
                if (this.aiExtension.isLoading) {
                    alert('检测正在进行中，请稍候...');
                    return;
                }
                
                // 显示加载状态
                this.setAILoadingState(true);
                
                // 准备请求数据
                const requestData = {
                    model_id: this.aiExtension.activeModel.id,
                    study_id: this.currentStudyId,
                    series_id: this.currentSeriesId,
                    image_id: this.images[this.currentImageIndex]?.imageId,
                    options: AI_EXTENSION_CONFIG.options
                };
                
                // 运行预测
                fetch(AI_EXTENSION_CONFIG.apiEndpoints.predict, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => response.json())
                .then(result => {
                    this.setAILoadingState(false);
                    
                    if (result.status === 'success') {
                        // 初始化检测结果存储
                        if (!this.aiExtension.detectionResults) {
                            this.aiExtension.detectionResults = new Map();
                        }
                        
                        // 存储当前图像的检测结果
                        const currentImageId = this.images[this.currentImageIndex]?.imageId;
                        this.aiExtension.detectionResults.set(currentImageId, result.detections || []);
                        
                        this.aiExtension.lastPrediction = result;
                        this.updateAIBoundingBoxes(result.detections || []);
                        this.updateAIResultsPanel(result);
                        this.showAIResultsPanel();
                        console.log('AI预测完成:', result);
                    } else {
                        alert('AI预测失败: ' + result.error);
                    }
                })
                .catch(error => {
                    this.setAILoadingState(false);
                    console.error('AI预测错误:', error);
                    alert('AI预测发生错误');
                });
            };
            
            // 设置AI加载状态
            DICOMViewer.prototype.setAILoadingState = function(loading) {
                this.aiExtension.isLoading = loading;
                
                const btn = document.getElementById('ai-predict-btn');
                if (!btn) return;
                
                if (loading) {
                    btn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>检测中...';
                    btn.disabled = true;
                } else {
                    btn.innerHTML = '<i class="fas fa-brain me-1"></i>AI检测';
                    btn.disabled = false;
                }
            };
            
            // 更新AI边界框
            DICOMViewer.prototype.updateAIBoundingBoxes = function(detections) {
                // 清除现有边界框
                this.clearAIBoundingBoxes();
                
                if (!detections || detections.length === 0) return;
                
                // 获取或创建AI覆盖层
                let overlay = document.getElementById('ai-detection-overlay');
                if (!overlay) {
                    overlay = document.createElement('div');
                    overlay.id = 'ai-detection-overlay';
                    overlay.style.cssText = `
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        pointer-events: none;
                        z-index: 10;
                    `;
                    this.element.appendChild(overlay);
                }
                
                // 获取当前视口信息
                const viewport = cornerstone.getViewport(this.element);
                const image = cornerstone.getImage(this.element);
                
                if (!viewport || !image) return;
                
                // 创建边界框
                detections.forEach((detection, index) => {
                    const bbox = detection.bbox;
                    if (!bbox || bbox.length < 4) return;
                    
                    // 转换坐标到显示坐标系
                    const displayCoords = this.convertImageToDisplayCoords(bbox, viewport, image);
                    
                    const boxElement = document.createElement('div');
                    boxElement.className = 'ai-detection-box';
                    boxElement.style.cssText = `
                        position: absolute;
                        left: ${displayCoords.left}px;
                        top: ${displayCoords.top}px;
                        width: ${displayCoords.width}px;
                        height: ${displayCoords.height}px;
                        border: 2px solid #ff4444;
                        background: transparent;
                        pointer-events: none;
                        transition: all 0.2s;
                        box-sizing: border-box;
                    `;
                    
                    // 添加标签
                    if (AI_EXTENSION_CONFIG.options.showConfidence) {
                        const label = document.createElement('div');
                        label.style.cssText = `
                            position: absolute;
                            top: -25px;
                            left: 0;
                            background: #ff4444;
                            color: white;
                            padding: 2px 6px;
                            font-size: 11px;
                            border-radius: 3px;
                            white-space: nowrap;
                            font-weight: 500;
                        `;
                        label.textContent = `${detection.class} (${(detection.confidence * 100).toFixed(1)}%)`;
                        
                        boxElement.appendChild(label);
                    }
                    
                    overlay.appendChild(boxElement);
                });
                
                // 更新边界框可见性按钮状态
                this.updateBoundingBoxToggleButton(true);
            };
            
            // 转换图像坐标到显示坐标
            DICOMViewer.prototype.convertImageToDisplayCoords = function(bbox, viewport, image) {
                const canvas = this.element.querySelector('canvas');
                if (!canvas) return { left: 0, top: 0, width: 0, height: 0 };
                
                const canvasRect = canvas.getBoundingClientRect();
                const elementRect = this.element.getBoundingClientRect();
                
                // 计算图像在canvas中的实际显示尺寸和位置
                const imageAspect = image.width / image.height;
                const canvasAspect = canvasRect.width / canvasRect.height;
                
                let displayWidth, displayHeight, offsetX, offsetY;
                
                if (imageAspect > canvasAspect) {
                    // 图像较宽，以宽度为准
                    displayWidth = canvasRect.width;
                    displayHeight = canvasRect.width / imageAspect;
                    offsetX = 0;
                    offsetY = (canvasRect.height - displayHeight) / 2;
                } else {
                    // 图像较高，以高度为准
                    displayWidth = canvasRect.height * imageAspect;
                    displayHeight = canvasRect.height;
                    offsetX = 0; // 改为左对齐
                    offsetY = 0;
                }
                
                // 应用viewport的缩放和平移
                const scale = viewport.scale || 1;
                const translationX = (viewport.translation?.x || 0) * scale;
                const translationY = (viewport.translation?.y || 0) * scale;
                
                // 计算检测框在显示坐标系中的位置
                const scaleX = (displayWidth * scale) / image.width;
                const scaleY = (displayHeight * scale) / image.height;
                
                const finalOffsetX = (canvasRect.left - elementRect.left) + offsetX * scale + translationX;
                const finalOffsetY = (canvasRect.top - elementRect.top) + offsetY * scale + translationY;
                
                return {
                    left: bbox[0] * scaleX + finalOffsetX,
                    top: bbox[1] * scaleY + finalOffsetY,
                    width: (bbox[2] - bbox[0]) * scaleX,
                    height: (bbox[3] - bbox[1]) * scaleY
                };
            };
            
            // 切换AI边界框可见性
            DICOMViewer.prototype.toggleAIBoundingBoxes = function() {
                const overlay = document.getElementById('ai-detection-overlay');
                if (!overlay) return;
                
                const isVisible = overlay.style.display !== 'none';
                overlay.style.display = isVisible ? 'none' : 'block';
                
                this.updateBoundingBoxToggleButton(!isVisible);
            };
            
            // 更新边界框切换按钮状态
            DICOMViewer.prototype.updateBoundingBoxToggleButton = function(visible) {
                const btn = document.getElementById('ai-toggle-bbox-btn');
                if (!btn) return;
                
                if (visible) {
                    btn.className = 'btn btn-success btn-sm';
                    btn.innerHTML = '<i class="fas fa-eye me-1"></i>隐藏边界框';
                } else {
                    btn.className = 'btn btn-outline-success btn-sm';
                    btn.innerHTML = '<i class="fas fa-eye-slash me-1"></i>显示边界框';
                }
            };
            
            // 清除AI边界框
            DICOMViewer.prototype.clearAIBoundingBoxes = function() {
                const overlay = document.getElementById('ai-detection-overlay');
                if (overlay) {
                    overlay.innerHTML = '';
                }
            };
            
            // 清除AI结果
            DICOMViewer.prototype.clearAIResults = function() {
                this.clearAIBoundingBoxes();
                this.hideAIResultsPanel();
                this.aiExtension.lastPrediction = null;
                // 清除所有图像的检测结果
                if (this.aiExtension.detectionResults) {
                    this.aiExtension.detectionResults.clear();
                }
                this.updateBoundingBoxToggleButton(false);
            };
            
            // 更新AI结果面板
            DICOMViewer.prototype.updateAIResultsPanel = function(result) {
                const content = document.getElementById('detection-results-list');
                if (!content || !result) return;
                
                const detections = result.detections || [];
                
                if (detections.length === 0) {
                    content.innerHTML = `
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                            <div class="text-muted">未检测到异常</div>
                        </div>
                    `;
                    return;
                }
                
                // 按置信度排序
                const sortedDetections = [...detections].sort((a, b) => b.confidence - a.confidence);
                
                let html = '';
                
                sortedDetections.forEach((detection, index) => {
                    const confidence = (detection.confidence * 100).toFixed(1);
                    const confidenceLevel = detection.confidence > 0.8 ? 'high' : 
                                           detection.confidence > 0.6 ? 'medium' : 'low';
                    
                    const confidenceColor = {
                        'high': 'danger',
                        'medium': 'warning', 
                        'low': 'info'
                    }[confidenceLevel];
                    
                    const riskLevel = {
                        'high': '高风险',
                        'medium': '中风险',
                        'low': '低风险'
                    }[confidenceLevel];
                    
                    html += `
                        <div class="detection-result-item">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="detection-class text-${confidenceColor}">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    ${detection.class}
                                </div>
                                <span class="badge bg-${confidenceColor}">${confidence}%</span>
                            </div>
                            <div class="detection-details">
                                <div class="row g-2">
                                    <div class="col-6">
                                        <small class="text-muted">风险等级</small>
                                        <div class="fw-bold text-${confidenceColor}">${riskLevel}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">尺寸</small>
                                        <div class="fw-bold">${Math.round(detection.bbox[2] - detection.bbox[0])} × ${Math.round(detection.bbox[3] - detection.bbox[1])}</div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">位置坐标</small>
                                    <div class="coordinate-text">
                                        (${detection.bbox[0].toFixed(0)}, ${detection.bbox[1].toFixed(0)}) → 
                                        (${detection.bbox[2].toFixed(0)}, ${detection.bbox[3].toFixed(0)})
                                    </div>
                                </div>
                                <div class="progress mt-2" style="height: 3px;">
                                    <div class="progress-bar bg-${confidenceColor}" style="width: ${confidence}%"></div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                content.innerHTML = html;
                
                // 更新检测统计
                const statsElement = document.getElementById('detection-stats');
                if (statsElement) {
                    const highRisk = detections.filter(d => d.confidence > 0.8).length;
                    const mediumRisk = detections.filter(d => d.confidence > 0.6 && d.confidence <= 0.8).length;
                    const lowRisk = detections.filter(d => d.confidence <= 0.6).length;
                    const avgConfidence = (detections.reduce((sum, d) => sum + d.confidence, 0) / detections.length * 100).toFixed(1);
                    
                    statsElement.innerHTML = `
                        <div class="stat-item">
                            <span class="stat-label">总检测数</span>
                            <span class="stat-value">${detections.length}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">高风险</span>
                            <span class="stat-value text-danger">${highRisk}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">中风险</span>
                            <span class="stat-value text-warning">${mediumRisk}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">低风险</span>
                            <span class="stat-value text-success">${lowRisk}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">平均置信度</span>
                            <span class="stat-value">${avgConfidence}%</span>
                        </div>
                    `;
                }
            };
            
            // 显示AI结果面板
            DICOMViewer.prototype.showAIResultsPanel = function() {
                const panel = document.getElementById('ai-results-panel');
                if (panel) {
                    panel.style.display = 'block';
                    // 平滑滚动到面板
                    setTimeout(() => {
                        panel.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }, 100);
                }
            };
            
            // 导出AI检测报告
            DICOMViewer.prototype.exportAIReport = function() {
                const currentImageId = this.images[this.currentImageIndex]?.imageId;
                const detections = this.aiExtension?.detectionResults?.get(currentImageId) || [];
                
                if (detections.length === 0) {
                    alert('当前图像没有检测结果可导出');
                    return;
                }
                
                const reportData = {
                    patientInfo: {
                        studyId: this.currentStudyId || 'Unknown',
                        imageIndex: this.currentImageIndex + 1,
                        totalImages: this.images.length
                    },
                    aiModel: this.aiExtension.activeModel?.name || 'Unknown',
                    detectionTime: new Date().toLocaleString(),
                    detections: detections.map((detection, index) => ({
                        id: index + 1,
                        className: detection.class,
                        confidence: (detection.confidence * 100).toFixed(1),
                        bbox: detection.bbox,
                        riskLevel: detection.confidence > 0.8 ? '高风险' : 
                                  detection.confidence > 0.6 ? '中风险' : '低风险'
                    }))
                };
                
                const reportText = this.generateReportText(reportData);
                const blob = new Blob([reportText], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `AI检测报告_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            };
            
            // 生成报告文本
            DICOMViewer.prototype.generateReportText = function(data) {
                let report = `DICOM智能阅读器 - AI检测报告\n`;
                report += `${'='.repeat(50)}\n\n`;
                
                report += `患者信息:\n`;
                report += `- 研究ID: ${data.patientInfo.studyId}\n`;
                report += `- 当前图像: ${data.patientInfo.imageIndex}/${data.patientInfo.totalImages}\n\n`;
                
                report += `检测信息:\n`;
                report += `- AI模型: ${data.aiModel}\n`;
                report += `- 检测时间: ${data.detectionTime}\n`;
                report += `- 检测数量: ${data.detections.length}\n\n`;
                
                report += `检测结果详情:\n`;
                report += `${'-'.repeat(30)}\n`;
                
                data.detections.forEach(detection => {
                    report += `\n检测项目 #${detection.id}:\n`;
                    report += `- 类别: ${detection.className}\n`;
                    report += `- 置信度: ${detection.confidence}%\n`;
                    report += `- 风险等级: ${detection.riskLevel}\n`;
                    report += `- 位置坐标: (${detection.bbox[0].toFixed(0)}, ${detection.bbox[1].toFixed(0)}) - (${detection.bbox[2].toFixed(0)}, ${detection.bbox[3].toFixed(0)})\n`;
                    report += `- 尺寸: ${Math.round(detection.bbox[2] - detection.bbox[0])} × ${Math.round(detection.bbox[3] - detection.bbox[1])} 像素\n`;
                });
                
                report += `\n${'='.repeat(50)}\n`;
                report += `报告生成时间: ${new Date().toLocaleString()}\n`;
                report += `系统: DICOM智能阅读器 v1.0\n`;
                
                return report;
            };
            
            // 打印AI检测报告
            DICOMViewer.prototype.printAIReport = function() {
                const panel = document.getElementById('ai-results-panel');
                if (!panel || panel.style.display === 'none') {
                    alert('请先进行AI检测并查看结果');
                    return;
                }
                
                const printWindow = window.open('', '_blank');
                const panelContent = panel.innerHTML;
                
                // 构建HTML内容，避免模板字符串中的特殊字符问题
                const htmlContent = '<!DOCTYPE html>' +
                    '<html>' +
                    '<head>' +
                        '<title>AI检测报告</title>' +
                        '<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">' +
                        '<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">' +
                        '<style>' +
                            'body { font-family: Arial, sans-serif; margin: 20px; }' +
                            '.card { border: 1px solid #ddd; border-radius: 8px; }' +
                            '.card-header { background: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd; }' +
                            '.card-body { padding: 20px; }' +
                            '@media print {' +
                                '.btn { display: none !important; }' +
                                '.card { box-shadow: none !important; }' +
                            '}' +
                        '</style>' +
                    '</head>' +
                    '<body>' +
                        '<div class="container">' +
                            '<h2 class="text-center mb-4">DICOM智能阅读器 - AI检测报告</h2>' +
                            panelContent +
                        '</div>' +
                        '<script>' +
                            'window.onload = function() {' +
                                'window.print();' +
                                'window.onafterprint = function() {' +
                                    'window.close();' +
                                '};' +
                            '};' +
                        '</script>' +
                    '</body>' +
                    '</html>';
                
                printWindow.document.write(htmlContent);
                
                printWindow.document.close();
            };
            
            // 隐藏AI结果面板
            DICOMViewer.prototype.hideAIResultsPanel = function() {
                const panel = document.getElementById('ai-results-panel');
                if (panel) {
                    panel.style.display = 'none';
                }
            };
            
            // 重写显示图像方法以更新AI覆盖层
            const originalDisplayImage = DICOMViewer.prototype.displayImage;
            DICOMViewer.prototype.displayImage = function(index) {
                // 调用原始方法
                const result = originalDisplayImage.call(this, index);
                
                // 更新AI覆盖层 - 只显示当前图像的检测结果
                if (this.aiExtension && this.aiExtension.detectionResults) {
                    setTimeout(() => {
                        const currentImageId = this.images[index]?.imageId;
                        const detections = this.aiExtension.detectionResults.get(currentImageId) || [];
                        this.updateAIBoundingBoxes(detections);
                        
                        // 更新结果面板显示
                        if (detections.length > 0) {
                            const mockResult = {
                                detections: detections,
                                detection_count: detections.length,
                                timestamp: new Date().toISOString()
                            };
                            this.updateAIResultsPanel(mockResult);
                        } else {
                            // 如果当前图像没有检测结果，清除结果面板
                            const content = document.getElementById('detection-results-list');
                            if (content) {
                                content.innerHTML = '<div class="text-center text-muted">当前图像未检测到异常</div>';
                            }
                        }
                    }, 100);
                } else {
                    // 如果没有检测结果，清除所有检测框
                    setTimeout(() => {
                        this.clearAIBoundingBoxes();
                    }, 100);
                }
                
                return result;
            };
            
            // 重写鼠标滚轮事件以更新AI覆盖层
            const originalHandleWheel = DICOMViewer.prototype.handleWheel;
            DICOMViewer.prototype.handleWheel = function(e) {
                const oldIndex = this.currentImageIndex;
                
                // 调用原始方法
                const result = originalHandleWheel.call(this, e);
                
                // 如果是缩放操作或图像切换，更新AI覆盖层
                if (this.aiExtension && this.aiExtension.detectionResults) {
                    setTimeout(() => {
                        const currentImageId = this.images[this.currentImageIndex]?.imageId;
                        const detections = this.aiExtension.detectionResults.get(currentImageId) || [];
                        this.updateAIBoundingBoxes(detections);
                        
                        // 如果图像切换了，更新结果面板
                        if (oldIndex !== this.currentImageIndex) {
                            if (detections.length > 0) {
                                const mockResult = {
                                    detections: detections,
                                    detection_count: detections.length,
                                    timestamp: new Date().toISOString()
                                };
                                this.updateAIResultsPanel(mockResult);
                            } else {
                                const content = document.getElementById('detection-results-list');
                                if (content) {
                                    content.innerHTML = '<div class="text-center text-muted">当前图像未检测到异常</div>';
                                }
                            }
                        }
                    }, 50);
                }
                
                return result;
            };
            
            // 重写鼠标移动事件以更新AI覆盖层
            const originalHandleMouseMove = DICOMViewer.prototype.handleMouseMove;
            DICOMViewer.prototype.handleMouseMove = function(e) {
                // 调用原始方法
                const result = originalHandleMouseMove.call(this, e);
                
                // 如果正在拖拽，更新AI覆盖层 - 只显示当前图像的检测结果
                if (this.isDragging && this.aiExtension && this.aiExtension.detectionResults) {
                    setTimeout(() => {
                        const currentImageId = this.images[this.currentImageIndex]?.imageId;
                        const detections = this.aiExtension.detectionResults.get(currentImageId) || [];
                        this.updateAIBoundingBoxes(detections);
                    }, 10);
                }
                
                return result;
            };
            
        })();
    </script>
</body>
</html>