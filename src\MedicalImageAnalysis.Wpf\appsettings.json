{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Application": {"Name": "医学影像解析系统", "Version": "1.0.0", "DataDirectory": "./data", "TempDirectory": "./temp", "LogDirectory": "./logs"}, "Api": {"BaseUrl": "http://localhost:5000", "Timeout": 30, "RetryCount": 3}, "Processing": {"MaxConcurrency": 2, "DefaultConfidenceThreshold": 0.5, "MaxFileSize": 524288000, "SupportedFormats": [".dcm", ".dicom", ".png", ".jpg", ".jpeg", ".bmp"]}, "Training": {"DefaultEpochs": 100, "DefaultBatchSize": 8, "DefaultLearningRate": 0.001, "ModelOutputDirectory": "./models", "CheckpointInterval": 10, "SupportedMethods": ["YOLO", "nnUNet"], "DefaultMethod": "YOLO"}, "NnUNet": {"PythonExecutable": "python", "EnvironmentPath": {"nnUNet_raw": "./data/nnUNet_raw", "nnUNet_preprocessed": "./data/nnUNet_preprocessed", "nnUNet_results": "./data/nnUNet_results"}, "DefaultConfig": {"MaxEpochs": 1000, "BatchSize": 2, "LearningRate": 0.01, "Architecture": "3d_fullres", "TrainerType": "nnUNetTrainer", "UseMixedPrecision": true, "EnableDataAugmentation": true, "UseDeepSupervision": true, "ValidationFrequency": 50, "Device": "cuda"}, "DatasetDefaults": {"FileEnding": ".nii.gz", "TensorImageSize": "4D", "VerifyDatasetIntegrity": true, "NumThreads": 8}, "InferenceDefaults": {"SaveProbabilities": false, "UseTestTimeAugmentation": true, "StepSize": 0.5, "DisableProgressBar": false}, "PretrainedModels": ["Task001_BrainTumour", "Task002_Heart", "Task003_Liver", "Task004_Hippocampus", "Task005_Prostate", "Task006_Lung", "Task007_Pancreas", "Task008_He<PERSON>tic<PERSON><PERSON><PERSON>", "Task009_Spleen", "Task010_Colon"]}, "UI": {"Theme": "Light", "PrimaryColor": "Blue", "SecondaryColor": "LightBlue", "AutoRefreshInterval": 30, "MaxRecentFiles": 10}}