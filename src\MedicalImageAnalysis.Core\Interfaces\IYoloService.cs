using MedicalImageAnalysis.Core.Entities;

namespace MedicalImageAnalysis.Core.Interfaces;

/// <summary>
/// YOLO 服务接口，提供 YOLOv11 模型的训练、验证和推理功能
/// </summary>
public interface IYoloService
{
    /// <summary>
    /// 训练 YOLO 模型
    /// </summary>
    /// <param name="trainingConfig">训练配置</param>
    /// <param name="progressCallback">进度回调</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>训练结果</returns>
    Task<TrainingResult> TrainModelAsync(YoloTrainingConfig trainingConfig, IProgress<TrainingProgress>? progressCallback = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证模型性能
    /// </summary>
    /// <param name="modelPath">模型文件路径</param>
    /// <param name="validationDataPath">验证数据路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateModelAsync(string modelPath, string validationDataPath, CancellationToken cancellationToken = default);

    /// <summary>
    /// 使用模型进行推理
    /// </summary>
    /// <param name="modelPath">模型文件路径</param>
    /// <param name="imageData">图像数据</param>
    /// <param name="inferenceConfig">推理配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>检测结果</returns>
    Task<List<Detection>> InferAsync(string modelPath, byte[] imageData, YoloInferenceConfig inferenceConfig, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量推理
    /// </summary>
    /// <param name="modelPath">模型文件路径</param>
    /// <param name="imagePaths">图像路径集合</param>
    /// <param name="inferenceConfig">推理配置</param>
    /// <param name="progressCallback">进度回调</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量检测结果</returns>
    Task<List<BatchDetectionResult>> BatchInferAsync(string modelPath, IEnumerable<string> imagePaths, YoloInferenceConfig inferenceConfig, IProgress<int>? progressCallback = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 导出模型为不同格式
    /// </summary>
    /// <param name="modelPath">源模型路径</param>
    /// <param name="exportFormat">导出格式</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>导出的模型路径</returns>
    Task<string> ExportModelAsync(string modelPath, ModelExportFormat exportFormat, string outputPath, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取模型信息
    /// </summary>
    /// <param name="modelPath">模型文件路径</param>
    /// <returns>模型信息</returns>
    Task<YoloModelInfo> GetModelInfoAsync(string modelPath);

    /// <summary>
    /// 创建数据集配置文件
    /// </summary>
    /// <param name="datasetConfig">数据集配置</param>
    /// <param name="outputPath">输出路径</param>
    /// <returns>配置文件路径</returns>
    Task<string> CreateDatasetConfigAsync(DatasetConfig datasetConfig, string outputPath);

    /// <summary>
    /// 验证数据集格式
    /// </summary>
    /// <param name="datasetPath">数据集路径</param>
    /// <returns>验证结果</returns>
    Task<DatasetValidationResult> ValidateDatasetAsync(string datasetPath);

    /// <summary>
    /// 生成数据增强
    /// </summary>
    /// <param name="sourceDataPath">源数据路径</param>
    /// <param name="augmentationConfig">增强配置</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>增强后的数据路径</returns>
    Task<string> GenerateDataAugmentationAsync(string sourceDataPath, DataAugmentationConfig augmentationConfig, string outputPath, CancellationToken cancellationToken = default);
}

/// <summary>
/// YOLO 训练配置
/// </summary>
public class YoloTrainingConfig
{
    /// <summary>
    /// 数据集配置文件路径
    /// </summary>
    public string DatasetConfigPath { get; set; } = string.Empty;

    /// <summary>
    /// 预训练模型路径
    /// </summary>
    public string? PretrainedModelPath { get; set; }

    /// <summary>
    /// 训练轮数
    /// </summary>
    public int Epochs { get; set; } = 100;

    /// <summary>
    /// 批次大小
    /// </summary>
    public int BatchSize { get; set; } = 16;

    /// <summary>
    /// 图像尺寸
    /// </summary>
    public int ImageSize { get; set; } = 640;

    /// <summary>
    /// 学习率
    /// </summary>
    public double LearningRate { get; set; } = 0.01;

    /// <summary>
    /// 优化器类型
    /// </summary>
    public OptimizerType Optimizer { get; set; } = OptimizerType.SGD;

    /// <summary>
    /// 设备类型 (CPU/GPU)
    /// </summary>
    public string Device { get; set; } = "0"; // GPU 0

    /// <summary>
    /// 工作线程数
    /// </summary>
    public int Workers { get; set; } = 8;

    /// <summary>
    /// 早停耐心值
    /// </summary>
    public int Patience { get; set; } = 50;

    /// <summary>
    /// 是否使用混合精度训练
    /// </summary>
    public bool UseMixedPrecision { get; set; } = true;

    /// <summary>
    /// 是否缓存数据
    /// </summary>
    public bool CacheData { get; set; } = true;

    /// <summary>
    /// 输出目录
    /// </summary>
    public string OutputDirectory { get; set; } = "./runs/train";

    /// <summary>
    /// 实验名称
    /// </summary>
    public string ExperimentName { get; set; } = "exp";

    /// <summary>
    /// 是否保存检查点
    /// </summary>
    public bool SaveCheckpoints { get; set; } = true;

    /// <summary>
    /// 验证频率 (每 N 个 epoch)
    /// </summary>
    public int ValidationFrequency { get; set; } = 1;

    /// <summary>
    /// 是否启用数据增强
    /// </summary>
    public bool EnableDataAugmentation { get; set; } = true;

    /// <summary>
    /// 是否启用数据预处理
    /// </summary>
    public bool EnableDataPreprocessing { get; set; } = true;

    /// <summary>
    /// 是否使用类别权重
    /// </summary>
    public bool UseClassWeights { get; set; } = false;

    /// <summary>
    /// 是否启用Mosaic增强
    /// </summary>
    public bool EnableMosaic { get; set; } = true;

    /// <summary>
    /// 是否启用Mixup增强
    /// </summary>
    public bool EnableMixup { get; set; } = false;

    /// <summary>
    /// 数据增强配置
    /// </summary>
    public TrainingAugmentationConfig? AugmentationConfig { get; set; }
}

/// <summary>
/// YOLO 推理配置
/// </summary>
public class YoloInferenceConfig
{
    /// <summary>
    /// 置信度阈值
    /// </summary>
    public double ConfidenceThreshold { get; set; } = 0.5;

    /// <summary>
    /// IoU 阈值 (用于 NMS)
    /// </summary>
    public double IouThreshold { get; set; } = 0.45;

    /// <summary>
    /// 最大检测数量
    /// </summary>
    public int MaxDetections { get; set; } = 300;

    /// <summary>
    /// 图像尺寸
    /// </summary>
    public int ImageSize { get; set; } = 640;

    /// <summary>
    /// 设备类型
    /// </summary>
    public string Device { get; set; } = "0";

    /// <summary>
    /// 是否使用半精度
    /// </summary>
    public bool UseHalfPrecision { get; set; } = false;

    /// <summary>
    /// 是否启用 TensorRT 优化
    /// </summary>
    public bool UseTensorRT { get; set; } = false;

    /// <summary>
    /// 是否保存结果图像
    /// </summary>
    public bool SaveResults { get; set; } = false;

    /// <summary>
    /// 结果保存路径
    /// </summary>
    public string? ResultsPath { get; set; }
}

/// <summary>
/// 训练进度
/// </summary>
public class TrainingProgress
{
    /// <summary>
    /// 当前轮数
    /// </summary>
    public int CurrentEpoch { get; set; }

    /// <summary>
    /// 总轮数
    /// </summary>
    public int TotalEpochs { get; set; }

    /// <summary>
    /// 当前批次
    /// </summary>
    public int CurrentBatch { get; set; }

    /// <summary>
    /// 总批次数
    /// </summary>
    public int TotalBatches { get; set; }

    /// <summary>
    /// 训练损失
    /// </summary>
    public double TrainingLoss { get; set; }

    /// <summary>
    /// 验证损失
    /// </summary>
    public double ValidationLoss { get; set; }

    /// <summary>
    /// mAP@0.5
    /// </summary>
    public double Map50 { get; set; }

    /// <summary>
    /// mAP@0.5:0.95
    /// </summary>
    public double Map5095 { get; set; }

    /// <summary>
    /// 学习率
    /// </summary>
    public double LearningRate { get; set; }

    /// <summary>
    /// 估计剩余时间 (秒)
    /// </summary>
    public double EstimatedTimeRemaining { get; set; }

    /// <summary>
    /// 进度百分比 (0-100)
    /// </summary>
    public double ProgressPercentage => TotalEpochs > 0 ? (double)CurrentEpoch / TotalEpochs * 100 : 0;
}

/// <summary>
/// 训练结果
/// </summary>
public class TrainingResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 最佳模型路径
    /// </summary>
    public string? BestModelPath { get; set; }

    /// <summary>
    /// 最后模型路径
    /// </summary>
    public string? LastModelPath { get; set; }

    /// <summary>
    /// 训练指标
    /// </summary>
    public TrainingMetrics Metrics { get; set; } = new();

    /// <summary>
    /// 训练耗时 (秒)
    /// </summary>
    public double TrainingTimeSeconds { get; set; }

    /// <summary>
    /// 输出目录
    /// </summary>
    public string OutputDirectory { get; set; } = string.Empty;
}

/// <summary>
/// 训练指标
/// </summary>
public class TrainingMetrics
{
    /// <summary>
    /// 最佳 mAP@0.5
    /// </summary>
    public double BestMap50 { get; set; }

    /// <summary>
    /// 最佳 mAP@0.5:0.95
    /// </summary>
    public double BestMap5095 { get; set; }

    /// <summary>
    /// 最终训练损失
    /// </summary>
    public double FinalTrainingLoss { get; set; }

    /// <summary>
    /// 最终验证损失
    /// </summary>
    public double FinalValidationLoss { get; set; }

    /// <summary>
    /// 收敛轮数
    /// </summary>
    public int ConvergedEpoch { get; set; }

    /// <summary>
    /// 每个类别的 AP
    /// </summary>
    public Dictionary<string, double> ClassAP { get; set; } = new();
}

/// <summary>
/// 优化器类型枚举
/// </summary>
public enum OptimizerType
{
    SGD = 1,
    Adam = 2,
    AdamW = 3,
    RMSprop = 4
}

/// <summary>
/// 模型导出格式枚举
/// </summary>
public enum ModelExportFormat
{
    ONNX = 1,
    TensorRT = 2,
    CoreML = 3,
    TensorFlowLite = 4,
    OpenVINO = 5
}
