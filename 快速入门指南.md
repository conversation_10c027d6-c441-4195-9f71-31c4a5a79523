# 医学影像智能标注与模型训练快速入门指南

## 🚀 5分钟快速开始

### 第一步：启动应用程序
```bash
# 方法1：双击批处理文件（推荐）
启动桌面端应用.bat

# 方法2：命令行启动
dotnet run --project src/MedicalImageAnalysis.Wpf
```

### 第二步：加载示例图像
1. 点击左侧导航栏的 **"智能标注"**
2. 点击 **"打开图像"** 按钮
3. 选择 `Brain/DJ01.dcm` 示例文件
4. 图像将自动加载并显示

### 第三步：开始标注
1. 选择 **"矩形"** 工具
2. 在图像上拖拽创建标注框
3. 在右侧选择标注类别（如"病灶区域"）
4. 点击 **"保存标注"** 保存结果

### 第四步：AI辅助标注
1. 勾选 **"启用AI预标注"**
2. 调整 **"检测置信度阈值"** 到0.7
3. AI会自动生成候选标注
4. 确认或调整AI生成的标注

### 第五步：导出训练数据
1. 点击 **"导出"** 按钮
2. 选择YOLO格式
3. 选择保存目录
4. 系统生成训练数据集

## 🎯 智能标注快速指南

### 界面布局
```
┌─────────────┬─────────────────────┬─────────────┐
│   工具栏    │      图像显示区      │  控制面板   │
│             │                     │             │
│ ○ 矩形      │                     │ 标注类别    │
│ ○ 椭圆      │     [医学影像]      │ ○ 病灶区域  │
│ ○ 多边形    │                     │ ○ 正常组织  │
│ ○ 自由绘制  │                     │ ○ 骨折      │
│             │                     │             │
│ 打开图像    │                     │ AI设置      │
│ 保存标注    │                     │ ☑ 启用AI   │
│ 导出数据    │                     │ 置信度: 0.7 │
└─────────────┴─────────────────────┴─────────────┘
```

### 基础操作
| 操作 | 方法 | 快捷键 |
|------|------|--------|
| 创建矩形标注 | 选择矩形工具 + 拖拽 | R |
| 创建椭圆标注 | 选择椭圆工具 + 拖拽 | E |
| 删除标注 | 选中标注 + Delete | Del |
| 撤销操作 | 撤销按钮 | Ctrl+Z |
| 保存标注 | 保存按钮 | Ctrl+S |
| 缩放图像 | 鼠标滚轮 | +/- |

### AI辅助设置
```
AI 辅助设置
├── ☑ 启用AI预标注        # 自动生成标注
├── ☑ 智能边缘吸附        # 标注自动吸附边缘
├── ☑ 相似区域建议        # 建议相似未标注区域
└── 检测置信度阈值: 0.7   # AI检测的最低置信度
```

## 🏋️ 模型训练快速指南

### 方法1：WPF界面训练（推荐新手）

#### 步骤1：准备数据
1. 使用智能标注模块标注至少100张图像
2. 导出为YOLO格式数据集
3. 确保数据集结构正确

#### 步骤2：配置训练
1. 点击 **"模型训练"** 模块
2. 设置基础参数：
   - 训练轮数：100
   - 批次大小：16
   - 学习率：0.01
   - 图像尺寸：640

#### 步骤3：开始训练
1. 点击 **"开始训练"** 按钮
2. 实时监控训练进度
3. 等待训练完成（约1-2小时）

### 方法2：Python脚本训练（推荐专业用户）

#### 快速训练脚本
```python
from ultralytics import YOLO

# 加载模型
model = YOLO('yolo11x.pt')

# 开始训练
results = model.train(
    data='dataset.yaml',    # 数据集配置
    epochs=100,             # 训练轮数
    batch=16,               # 批次大小
    imgsz=640,              # 图像尺寸
    device='auto',          # 自动选择设备
    project='runs/train',   # 输出目录
    name='medical_model'    # 实验名称
)

# 评估模型
val_results = model.val()
print(f"mAP@0.5: {val_results.box.map50:.3f}")
```

#### 使用项目脚本
```bash
# 进入YOLO训练目录
cd yolo_ohif/yolo_ohif

# 从头训练（推荐）
python start_yolo11x_training.py

# 使用预训练权重（更快收敛）
python start_yolo11x_pretrained_training.py

# 512×512医学图像专用
python start_yolo11x_training_512.py
```

## 📊 数据集准备清单

### 数据收集
- [ ] 收集至少100张医学影像
- [ ] 确保图像质量良好
- [ ] 涵盖不同病例类型
- [ ] 包含正常和异常样本

### 数据标注
- [ ] 使用一致的标注标准
- [ ] 标注边界准确
- [ ] 类别分类正确
- [ ] 避免遗漏重要病灶

### 数据集划分
- [ ] 训练集：70%（至少70张）
- [ ] 验证集：20%（至少20张）
- [ ] 测试集：10%（至少10张）
- [ ] 确保各集合类别分布均衡

### 数据格式
- [ ] 图像格式：DICOM、PNG、JPEG
- [ ] 标注格式：YOLO txt文件
- [ ] 配置文件：dataset.yaml
- [ ] 目录结构正确

## ⚙️ 训练参数推荐

### 新手推荐配置
```yaml
# 基础配置（适合新手）
epochs: 100          # 训练轮数
batch: 16            # 批次大小
imgsz: 640           # 图像尺寸
lr0: 0.01            # 初始学习率
device: 'auto'       # 自动选择设备
patience: 20         # 早停耐心值
```

### 专业配置
```yaml
# 高级配置（适合专业用户）
epochs: 200          # 更多训练轮数
batch: 32            # 更大批次（需要更多GPU内存）
imgsz: 512           # 医学图像推荐尺寸
lr0: 0.005           # 较小学习率（医学图像）
weight_decay: 0.0005 # 权重衰减
warmup_epochs: 5     # 预热轮数
optimizer: 'AdamW'   # 优化器
```

### 医学图像专用配置
```yaml
# 医学图像优化配置
epochs: 150
batch: 24
imgsz: 512           # 医学图像常用尺寸
lr0: 0.008           # 保守的学习率
hsv_h: 0.01          # 较小的色调变化
hsv_s: 0.3           # 适中的饱和度变化
hsv_v: 0.2           # 适中的亮度变化
degrees: 5.0         # 小角度旋转
translate: 0.05      # 小幅平移
fliplr: 0.5          # 水平翻转
```

## 🔍 常见问题快速解决

### Q1: 应用程序无法启动
**解决方案**:
```bash
# 检查.NET版本
dotnet --version

# 重新构建项目
dotnet build src/MedicalImageAnalysis.Wpf

# 以管理员身份运行
```

### Q2: 图像无法加载
**解决方案**:
- 确认文件格式：支持DICOM(.dcm)、PNG、JPEG、BMP
- 检查文件路径：避免中文路径和特殊字符
- 查看日志文件：`logs/medical-image-analysis-*.txt`

### Q3: AI功能不工作
**解决方案**:
- 确认AI功能已启用：勾选"启用AI预标注"
- 调整置信度阈值：尝试降低到0.5
- 检查图像质量：确保图像清晰度足够

### Q4: 训练速度太慢
**解决方案**:
```python
# 减小批次大小
batch = 8

# 减小图像尺寸
imgsz = 320

# 启用混合精度
amp = True

# 增加工作进程
workers = 8
```

### Q5: 内存不足错误
**解决方案**:
```python
# CUDA内存不足
batch = 4            # 减小批次
imgsz = 320          # 减小图像尺寸

# 系统内存不足
workers = 2          # 减少工作进程
cache = False        # 禁用缓存
```

## 📞 获取帮助

### 日志文件位置
```
# WPF应用日志
src/MedicalImageAnalysis.Wpf/bin/Debug/net8.0-windows/logs/

# 训练日志
runs/train/exp/

# Python脚本日志
yolo_ohif/yolo_ohif/logs/
```

### 技术支持
1. **查看日志**：首先查看相关日志文件
2. **重启应用**：尝试重启应用程序
3. **检查环境**：验证.NET和Python环境
4. **联系支持**：创建GitHub Issue

### 学习资源
- **智能打标教程**：`智能打标教程.md`
- **模型训练教程**：`模型训练教程.md`
- **项目文档**：`README.md`
- **代码示例**：`yolo_ohif/yolo_ohif/` 目录

---

**快速提示**:
- 🎯 从示例数据开始，熟悉基本操作
- 🔄 先标注少量数据，测试完整流程
- 📊 关注训练指标，及时调整参数
- 💾 定期保存标注数据和训练结果
- 🤝 遇到问题及时查看日志和文档
