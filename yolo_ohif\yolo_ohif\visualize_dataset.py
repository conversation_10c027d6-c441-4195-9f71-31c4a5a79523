#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集可视化工具
用于检查切出来的图像和YOLO标签是否匹配
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import argparse
import random
from typing import List, Tuple, Optional

class YOLODatasetVisualizer:
    def __init__(self, dataset_root: str):
        """
        初始化可视化器
        
        Args:
            dataset_root: YOLO数据集根目录
        """
        self.dataset_root = Path(dataset_root)
        self.classes = {
            0: 'supraspinatus_tear',  # 冈上肌撕裂
            1: 'normal'               # 正常
        }
        self.colors = {
            0: 'red',    # 撕裂用红色
            1: 'green'   # 正常用绿色
        }
    
    def load_yolo_annotations(self, label_path: Path) -> List[Tuple[int, float, float, float, float]]:
        """
        加载YOLO格式的标注文件
        
        Args:
            label_path: 标注文件路径
            
        Returns:
            List of (class_id, x_center, y_center, width, height)
        """
        annotations = []
        if label_path.exists():
            with open(label_path, 'r') as f:
                for line in f.readlines():
                    line = line.strip()
                    if line:
                        parts = line.split()
                        if len(parts) == 5:
                            class_id = int(parts[0])
                            x_center = float(parts[1])
                            y_center = float(parts[2])
                            width = float(parts[3])
                            height = float(parts[4])
                            annotations.append((class_id, x_center, y_center, width, height))
        return annotations
    
    def yolo_to_bbox(self, x_center: float, y_center: float, width: float, height: float, 
                     img_width: int, img_height: int) -> Tuple[int, int, int, int]:
        """
        将YOLO格式坐标转换为边界框坐标
        
        Args:
            x_center, y_center, width, height: YOLO格式坐标（归一化）
            img_width, img_height: 图像尺寸
            
        Returns:
            (x_min, y_min, x_max, y_max)
        """
        x_min = int((x_center - width / 2) * img_width)
        y_min = int((y_center - height / 2) * img_height)
        x_max = int((x_center + width / 2) * img_width)
        y_max = int((y_center + height / 2) * img_height)
        return x_min, y_min, x_max, y_max
    
    def visualize_single_image(self, img_path: Path, label_path: Path, save_path: Optional[Path] = None):
        """
        可视化单张图像和其标注
        
        Args:
            img_path: 图像文件路径
            label_path: 标注文件路径
            save_path: 保存路径（可选）
        """
        # 加载图像
        img = cv2.imread(str(img_path))
        if img is None:
            print(f"无法加载图像: {img_path}")
            return
        
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        img_height, img_width = img.shape[:2]
        
        # 加载标注
        annotations = self.load_yolo_annotations(label_path)
        
        # 创建图像显示
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.imshow(img)
        
        # 绘制边界框
        for class_id, x_center, y_center, width, height in annotations:
            x_min, y_min, x_max, y_max = self.yolo_to_bbox(
                x_center, y_center, width, height, img_width, img_height
            )
            
            # 创建矩形框
            rect = patches.Rectangle(
                (x_min, y_min), x_max - x_min, y_max - y_min,
                linewidth=2, edgecolor=self.colors.get(class_id, 'blue'),
                facecolor='none'
            )
            ax.add_patch(rect)
            
            # 添加类别标签
            class_name = self.classes.get(class_id, f'Class_{class_id}')
            ax.text(x_min, y_min - 5, class_name, 
                   color=self.colors.get(class_id, 'blue'),
                   fontsize=12, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.7))
        
        # 设置标题和显示信息
        title = f"图像: {img_path.name}\n标注: {label_path.name if label_path.exists() else '无标注文件'}"
        if annotations:
            title += f"\n检测到 {len(annotations)} 个目标"
        else:
            title += "\n无目标检测"
        
        ax.set_title(title, fontsize=14, pad=20)
        ax.axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"可视化结果已保存到: {save_path}")
        else:
            plt.show()
        
        plt.close()
    
    def visualize_dataset_samples(self, split: str = 'train', num_samples: int = 10, 
                                save_dir: Optional[str] = None):
        """
        可视化数据集样本
        
        Args:
            split: 数据集分割 ('train', 'val', 'test')
            num_samples: 要可视化的样本数量
            save_dir: 保存目录（可选）
        """
        images_dir = self.dataset_root / "images" / split
        labels_dir = self.dataset_root / "labels" / split
        
        if not images_dir.exists():
            print(f"图像目录不存在: {images_dir}")
            return
        
        # 获取所有图像文件
        image_files = list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.png"))
        
        if not image_files:
            print(f"在 {images_dir} 中没有找到图像文件")
            return
        
        # 随机选择样本
        selected_files = random.sample(image_files, min(num_samples, len(image_files)))
        
        print(f"正在可视化 {len(selected_files)} 个 {split} 样本...")
        
        for i, img_path in enumerate(selected_files):
            # 对应的标注文件
            label_path = labels_dir / (img_path.stem + '.txt')
            
            # 保存路径
            save_path = None
            if save_dir:
                save_dir_path = Path(save_dir)
                save_dir_path.mkdir(parents=True, exist_ok=True)
                save_path = save_dir_path / f"{split}_sample_{i+1:03d}_{img_path.stem}.png"
            
            print(f"处理 {i+1}/{len(selected_files)}: {img_path.name}")
            self.visualize_single_image(img_path, label_path, save_path)
    
    def check_dataset_integrity(self):
        """
        检查数据集完整性
        """
        print("=== 数据集完整性检查 ===")
        
        for split in ['train', 'val', 'test']:
            images_dir = self.dataset_root / "images" / split
            labels_dir = self.dataset_root / "labels" / split
            
            if not images_dir.exists():
                print(f"❌ {split} 图像目录不存在: {images_dir}")
                continue
            
            if not labels_dir.exists():
                print(f"❌ {split} 标签目录不存在: {labels_dir}")
                continue
            
            # 统计文件
            image_files = list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.png"))
            label_files = list(labels_dir.glob("*.txt"))
            
            print(f"\n📁 {split.upper()} 数据集:")
            print(f"   图像文件: {len(image_files)} 个")
            print(f"   标签文件: {len(label_files)} 个")
            
            # 检查匹配情况
            matched = 0
            unmatched_images = []
            unmatched_labels = []
            
            for img_file in image_files:
                label_file = labels_dir / (img_file.stem + '.txt')
                if label_file.exists():
                    matched += 1
                else:
                    unmatched_images.append(img_file.name)
            
            for label_file in label_files:
                img_file_jpg = images_dir / (label_file.stem + '.jpg')
                img_file_png = images_dir / (label_file.stem + '.png')
                if not (img_file_jpg.exists() or img_file_png.exists()):
                    unmatched_labels.append(label_file.name)
            
            print(f"   匹配的图像-标签对: {matched} 个")
            
            if unmatched_images:
                print(f"   ⚠️  无对应标签的图像: {len(unmatched_images)} 个")
                if len(unmatched_images) <= 5:
                    print(f"      {unmatched_images}")
                else:
                    print(f"      {unmatched_images[:5]} ... (还有{len(unmatched_images)-5}个)")
            
            if unmatched_labels:
                print(f"   ⚠️  无对应图像的标签: {len(unmatched_labels)} 个")
                if len(unmatched_labels) <= 5:
                    print(f"      {unmatched_labels}")
                else:
                    print(f"      {unmatched_labels[:5]} ... (还有{len(unmatched_labels)-5}个)")
            
            if not unmatched_images and not unmatched_labels:
                print(f"   ✅ 数据集完整，所有文件都有对应匹配")

def main():
    parser = argparse.ArgumentParser(description='YOLO数据集可视化工具')
    parser.add_argument('--dataset_root', type=str, default='./yolo_training_output/supraspinatus_detection',
                       help='YOLO数据集根目录')
    parser.add_argument('--split', type=str, default='train', choices=['train', 'val', 'test'],
                       help='要可视化的数据集分割')
    parser.add_argument('--num_samples', type=int, default=10,
                       help='要可视化的样本数量')
    parser.add_argument('--save_dir', type=str, default=None,
                       help='保存可视化结果的目录')
    parser.add_argument('--check_only', action='store_true',
                       help='只检查数据集完整性，不进行可视化')
    
    args = parser.parse_args()
    
    # 创建可视化器
    visualizer = YOLODatasetVisualizer(args.dataset_root)
    
    # 检查数据集完整性
    visualizer.check_dataset_integrity()
    
    if not args.check_only:
        print(f"\n=== 开始可视化 {args.split} 数据集 ===")
        visualizer.visualize_dataset_samples(
            split=args.split,
            num_samples=args.num_samples,
            save_dir=args.save_dir
        )
        print("\n可视化完成！")

if __name__ == "__main__":
    main()