{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ApiSettings": {"BaseUrl": "http://localhost:5000", "SignalRUrl": "http://localhost:5000/hubs/processing", "Timeout": 30}, "AppSettings": {"Theme": "Light", "Language": "zh-CN", "AutoConnect": true, "EnableNotifications": true, "DefaultOutputDirectory": "Output", "MaxConcurrentTasks": 3, "SaveWindowState": true}, "DicomSettings": {"SupportedExtensions": [".dcm", ".dicom"], "MaxFileSize": 536870912, "TempDirectory": "Temp"}, "ModelSettings": {"SupportedFormats": [".pt", ".onnx", ".engine", ".pth"], "DefaultModelType": "yolo11n", "ModelDirectory": "Models", "SupportedTrainingMethods": ["YOLO", "nnUNet"], "DefaultTrainingMethod": "YOLO"}, "NnUNetSettings": {"PythonExecutable": "python", "ScriptsDirectory": "scripts/nnunet", "EnvironmentPaths": {"nnUNet_raw": "./data/nnUNet_raw", "nnUNet_preprocessed": "./data/nnUNet_preprocessed", "nnUNet_results": "./data/nnUNet_results"}, "DefaultTrainingConfig": {"MaxEpochs": 1000, "BatchSize": 2, "LearningRate": 0.01, "Architecture": "3d_fullres", "TrainerType": "nnUNetTrainer", "UseMixedPrecision": true, "EnableDataAugmentation": true, "UseDeepSupervision": true, "ValidationFrequency": 50, "Device": "cuda", "DisableProgressBar": false}, "DatasetConfig": {"FileEnding": ".nii.gz", "TensorImageSize": "4D", "VerifyDatasetIntegrity": true, "NumThreads": 8, "OverwriteExisting": false, "Verbose": true}, "InferenceConfig": {"SaveProbabilities": false, "UseTestTimeAugmentation": true, "StepSize": 0.5, "DisableProgressBar": false, "UseMixedPrecision": true}}}