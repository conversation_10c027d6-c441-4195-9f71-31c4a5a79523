import unittest
import requests
import json
import time
import os
import tempfile
import sqlite3
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from datetime import datetime
import logging
from unittest.mock import Mock, patch, MagicMock

logger = logging.getLogger(__name__)

@dataclass
class TestResult:
    """测试结果"""
    test_name: str
    passed: bool
    duration: float
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

@dataclass
class APITestCase:
    """API测试用例"""
    name: str
    method: str
    url: str
    headers: Optional[Dict[str, str]] = None
    data: Optional[Dict[str, Any]] = None
    files: Optional[Dict[str, Any]] = None
    expected_status: int = 200
    expected_response: Optional[Dict[str, Any]] = None
    timeout: int = 30
    setup_func: Optional[Callable] = None
    cleanup_func: Optional[Callable] = None

class APITestRunner:
    """API测试运行器"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.test_results: List[TestResult] = []
        self.auth_token: Optional[str] = None
    
    def set_auth_token(self, token: str):
        """设置认证令牌"""
        self.auth_token = token
        self.session.headers.update({'Authorization': f'Bearer {token}'})
    
    def run_test_case(self, test_case: APITestCase) -> TestResult:
        """运行单个测试用例"""
        start_time = time.time()
        
        try:
            # 执行设置函数
            if test_case.setup_func:
                test_case.setup_func()
            
            # 准备请求
            url = f"{self.base_url}{test_case.url}"
            headers = test_case.headers or {}
            
            # 发送请求
            response = self.session.request(
                method=test_case.method,
                url=url,
                headers=headers,
                json=test_case.data,
                files=test_case.files,
                timeout=test_case.timeout
            )
            
            # 检查状态码
            if response.status_code != test_case.expected_status:
                raise AssertionError(
                    f"期望状态码 {test_case.expected_status}, 实际 {response.status_code}"
                )
            
            # 检查响应内容
            if test_case.expected_response:
                try:
                    response_json = response.json()
                    self._validate_response(response_json, test_case.expected_response)
                except json.JSONDecodeError:
                    raise AssertionError("响应不是有效的JSON格式")
            
            duration = time.time() - start_time
            
            result = TestResult(
                test_name=test_case.name,
                passed=True,
                duration=duration,
                details={
                    'status_code': response.status_code,
                    'response_size': len(response.content),
                    'response_time': duration
                }
            )
            
        except Exception as e:
            duration = time.time() - start_time
            result = TestResult(
                test_name=test_case.name,
                passed=False,
                duration=duration,
                error_message=str(e)
            )
        
        finally:
            # 执行清理函数
            if test_case.cleanup_func:
                try:
                    test_case.cleanup_func()
                except Exception as e:
                    logger.warning(f"清理函数执行失败: {str(e)}")
        
        self.test_results.append(result)
        return result
    
    def _validate_response(self, actual: Dict[str, Any], expected: Dict[str, Any]):
        """验证响应内容"""
        for key, expected_value in expected.items():
            if key not in actual:
                raise AssertionError(f"响应中缺少字段: {key}")
            
            if isinstance(expected_value, dict):
                if not isinstance(actual[key], dict):
                    raise AssertionError(f"字段 {key} 应该是对象类型")
                self._validate_response(actual[key], expected_value)
            elif expected_value != actual[key]:
                raise AssertionError(
                    f"字段 {key} 值不匹配: 期望 {expected_value}, 实际 {actual[key]}"
                )
    
    def run_test_suite(self, test_cases: List[APITestCase]) -> Dict[str, Any]:
        """运行测试套件"""
        logger.info(f"开始运行 {len(test_cases)} 个测试用例")
        
        start_time = time.time()
        passed_count = 0
        failed_count = 0
        
        for test_case in test_cases:
            logger.info(f"运行测试: {test_case.name}")
            result = self.run_test_case(test_case)
            
            if result.passed:
                passed_count += 1
                logger.info(f"✓ {test_case.name} - 通过 ({result.duration:.2f}s)")
            else:
                failed_count += 1
                logger.error(f"✗ {test_case.name} - 失败: {result.error_message}")
        
        total_time = time.time() - start_time
        
        summary = {
            'total_tests': len(test_cases),
            'passed': passed_count,
            'failed': failed_count,
            'success_rate': (passed_count / len(test_cases)) * 100 if test_cases else 0,
            'total_time': total_time,
            'results': self.test_results
        }
        
        logger.info(f"测试完成: {passed_count}/{len(test_cases)} 通过 ({summary['success_rate']:.1f}%)")
        
        return summary

class DatabaseTestHelper:
    """数据库测试辅助类"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or ":memory:"
        self.connection = None
    
    def setup_test_database(self):
        """设置测试数据库"""
        self.connection = sqlite3.connect(self.db_path)
        
        # 创建测试表
        self.connection.executescript("""
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT NOT NULL DEFAULT 'doctor',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE TABLE IF NOT EXISTS studies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            orthanc_id TEXT UNIQUE NOT NULL,
            patient_id TEXT NOT NULL,
            patient_name TEXT,
            study_date DATE,
            modality TEXT,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE TABLE IF NOT EXISTS detection_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            study_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            detection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            results TEXT NOT NULL,
            confidence_threshold REAL DEFAULT 0.5,
            model_version TEXT,
            FOREIGN KEY (study_id) REFERENCES studies (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        );
        """)
        
        self.connection.commit()
        logger.info("测试数据库设置完成")
    
    def insert_test_data(self):
        """插入测试数据"""
        test_data = [
            # 用户数据
            "INSERT INTO users (username, email, password_hash, role) VALUES ('testuser', '<EMAIL>', 'hashed_password', 'doctor')",
            "INSERT INTO users (username, email, password_hash, role) VALUES ('admin', '<EMAIL>', 'admin_hash', 'admin')",
            
            # 研究数据
            "INSERT INTO studies (orthanc_id, patient_id, patient_name, study_date, modality) VALUES ('study1', 'P001', 'Test Patient', '2025-01-01', 'CT')",
            "INSERT INTO studies (orthanc_id, patient_id, patient_name, study_date, modality) VALUES ('study2', 'P002', 'Another Patient', '2025-01-02', 'MRI')",
        ]
        
        for sql in test_data:
            self.connection.execute(sql)
        
        self.connection.commit()
        logger.info("测试数据插入完成")
    
    def cleanup_database(self):
        """清理数据库"""
        if self.connection:
            self.connection.close()
            self.connection = None
        
        if self.db_path != ":memory:" and os.path.exists(self.db_path):
            os.remove(self.db_path)
        
        logger.info("测试数据库清理完成")

class MockServices:
    """模拟服务类"""
    
    @staticmethod
    def mock_orthanc_service():
        """模拟Orthanc服务"""
        mock_service = Mock()
        mock_service.check_health.return_value = {'status': 'healthy', 'version': '1.0.0'}
        mock_service.upload_dicom.return_value = {'success': True, 'orthanc_id': 'mock_id'}
        mock_service.get_study.return_value = {
            'ID': 'mock_id',
            'PatientID': 'P001',
            'StudyDate': '20250101'
        }
        return mock_service
    
    @staticmethod
    def mock_detection_service():
        """模拟检测服务"""
        mock_service = Mock()
        mock_service.check_health.return_value = {'status': 'healthy', 'model_loaded': True}
        mock_service.detect_dicom.return_value = {
            'detections': [
                {'class': 'abnormality', 'confidence': 0.85, 'bbox': [10, 10, 100, 100]}
            ],
            'processing_time': 1.5
        }
        return mock_service
    
    @staticmethod
    def mock_database_service():
        """模拟数据库服务"""
        mock_service = Mock()
        mock_service.check_connection.return_value = True
        mock_service.get_user.return_value = {
            'id': 1,
            'username': 'testuser',
            'email': '<EMAIL>',
            'role': 'doctor'
        }
        return mock_service

def create_test_suite() -> List[APITestCase]:
    """创建标准测试套件"""
    return [
        # 健康检查测试
        APITestCase(
            name="健康检查",
            method="GET",
            url="/health",
            expected_status=200,
            expected_response={'status': 'healthy'}
        ),
        
        # API健康检查测试
        APITestCase(
            name="API健康检查",
            method="GET",
            url="/api/health",
            expected_status=200
        ),
        
        # 性能指标测试
        APITestCase(
            name="性能指标",
            method="GET",
            url="/api/metrics",
            expected_status=200
        ),
        
        # 用户登录测试
        APITestCase(
            name="用户登录",
            method="POST",
            url="/login",
            data={'username': 'testuser', 'password': 'testpass'},
            expected_status=200
        ),
        
        # 文件上传测试（需要认证）
        APITestCase(
            name="文件上传",
            method="POST",
            url="/upload",
            expected_status=401  # 未认证应该返回401
        ),
        
        # 研究列表测试
        APITestCase(
            name="研究列表",
            method="GET",
            url="/studies",
            expected_status=200
        ),
        
        # 无效端点测试
        APITestCase(
            name="无效端点",
            method="GET",
            url="/nonexistent",
            expected_status=404
        )
    ]

class PerformanceTestRunner:
    """性能测试运行器"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def run_load_test(self, endpoint: str, concurrent_users: int = 10, 
                     duration: int = 60) -> Dict[str, Any]:
        """运行负载测试"""
        import threading
        import queue
        
        results_queue = queue.Queue()
        start_time = time.time()
        
        def worker():
            """工作线程"""
            worker_results = []
            while time.time() - start_time < duration:
                try:
                    request_start = time.time()
                    response = self.session.get(f"{self.base_url}{endpoint}", timeout=10)
                    request_time = time.time() - request_start
                    
                    worker_results.append({
                        'status_code': response.status_code,
                        'response_time': request_time,
                        'success': response.status_code == 200
                    })
                    
                    time.sleep(0.1)  # 短暂休息
                    
                except Exception as e:
                    worker_results.append({
                        'status_code': 0,
                        'response_time': 0,
                        'success': False,
                        'error': str(e)
                    })
            
            results_queue.put(worker_results)
        
        # 启动工作线程
        threads = []
        for _ in range(concurrent_users):
            thread = threading.Thread(target=worker)
            thread.start()
            threads.append(thread)
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 收集结果
        all_results = []
        while not results_queue.empty():
            all_results.extend(results_queue.get())
        
        # 计算统计信息
        total_requests = len(all_results)
        successful_requests = sum(1 for r in all_results if r['success'])
        response_times = [r['response_time'] for r in all_results if r['success']]
        
        stats = {
            'endpoint': endpoint,
            'concurrent_users': concurrent_users,
            'duration': duration,
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'failed_requests': total_requests - successful_requests,
            'success_rate': (successful_requests / total_requests) * 100 if total_requests > 0 else 0,
            'requests_per_second': total_requests / duration,
            'avg_response_time': sum(response_times) / len(response_times) if response_times else 0,
            'min_response_time': min(response_times) if response_times else 0,
            'max_response_time': max(response_times) if response_times else 0
        }
        
        logger.info(f"负载测试完成: {endpoint}")
        logger.info(f"成功率: {stats['success_rate']:.1f}%")
        logger.info(f"平均响应时间: {stats['avg_response_time']:.3f}s")
        logger.info(f"每秒请求数: {stats['requests_per_second']:.1f}")
        
        return stats

class IntegrationTestSuite:
    """集成测试套件"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.api_runner = APITestRunner(base_url)
        self.db_helper = DatabaseTestHelper()
        self.performance_runner = PerformanceTestRunner(base_url)
    
    def run_full_test_suite(self) -> Dict[str, Any]:
        """运行完整测试套件"""
        logger.info("开始运行完整测试套件")
        
        results = {
            'start_time': datetime.now().isoformat(),
            'api_tests': None,
            'performance_tests': None,
            'database_tests': None
        }
        
        try:
            # 运行API测试
            logger.info("运行API测试...")
            test_cases = create_test_suite()
            results['api_tests'] = self.api_runner.run_test_suite(test_cases)
            
            # 运行性能测试
            logger.info("运行性能测试...")
            performance_results = []
            for endpoint in ['/health', '/api/health', '/studies']:
                perf_result = self.performance_runner.run_load_test(
                    endpoint, concurrent_users=5, duration=30
                )
                performance_results.append(perf_result)
            results['performance_tests'] = performance_results
            
            # 运行数据库测试
            logger.info("运行数据库测试...")
            results['database_tests'] = self._run_database_tests()
            
        except Exception as e:
            logger.error(f"测试套件运行失败: {str(e)}")
            results['error'] = str(e)
        
        results['end_time'] = datetime.now().isoformat()
        
        logger.info("完整测试套件运行完成")
        return results
    
    def _run_database_tests(self) -> Dict[str, Any]:
        """运行数据库测试"""
        try:
            self.db_helper.setup_test_database()
            self.db_helper.insert_test_data()
            
            # 执行一些基本的数据库查询测试
            conn = self.db_helper.connection
            
            # 测试用户查询
            cursor = conn.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            
            # 测试研究查询
            cursor = conn.execute("SELECT COUNT(*) FROM studies")
            study_count = cursor.fetchone()[0]
            
            return {
                'passed': True,
                'user_count': user_count,
                'study_count': study_count,
                'message': '数据库测试通过'
            }
            
        except Exception as e:
            return {
                'passed': False,
                'error': str(e),
                'message': '数据库测试失败'
            }
        
        finally:
            self.db_helper.cleanup_database()

def run_tests():
    """运行测试的主函数"""
    logging.basicConfig(level=logging.INFO)
    
    # 创建集成测试套件
    test_suite = IntegrationTestSuite()
    
    # 运行完整测试
    results = test_suite.run_full_test_suite()
    
    # 保存测试结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"test_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    
    logger.info(f"测试结果已保存到: {results_file}")
    
    return results

if __name__ == "__main__":
    run_tests()