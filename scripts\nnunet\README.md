# nnUNet 集成指南

本文档介绍如何在医学图像分析系统中使用 nnUNet 进行医学图像分割训练。

## 概述

nnUNet (No-new-Net) 是一个自适应的医学图像分割框架，能够自动配置网络架构、预处理和训练策略。本系统已集成 nnUNet，提供完整的训练和推理功能。

## 环境安装

### 自动安装

使用提供的安装脚本自动安装 nnUNet 环境：

```bash
# 安装 nnUNet 及其依赖
python scripts/nnunet/install_nnunet.py

# 仅验证现有安装
python scripts/nnunet/install_nnunet.py --verify-only

# 指定 CUDA 版本安装
python scripts/nnunet/install_nnunet.py --cuda-version 11.8
```

### 手动安装

如果自动安装失败，可以手动安装：

```bash
# 1. 安装 PyTorch (根据你的 CUDA 版本选择)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# 2. 安装依赖
pip install numpy scipy nibabel SimpleITK batchgenerators scikit-image

# 3. 安装 nnUNet
pip install nnunetv2
```

## 数据准备

### 数据格式要求

nnUNet 要求特定的数据格式：

```
Dataset001_YourDataset/
├── dataset.json
├── imagesTr/
│   ├── case_001_0000.nii.gz
│   ├── case_002_0000.nii.gz
│   └── ...
├── labelsTr/
│   ├── case_001.nii.gz
│   ├── case_002.nii.gz
│   └── ...
└── imagesTs/
    ├── case_101_0000.nii.gz
    ├── case_102_0000.nii.gz
    └── ...
```

### 数据集配置文件 (dataset.json)

```json
{
  "channel_names": {
    "0": "CT"
  },
  "labels": {
    "0": "background",
    "1": "tumor"
  },
  "numTraining": 100,
  "numTest": 20,
  "file_ending": ".nii.gz",
  "dataset_name": "YourDataset",
  "description": "Medical segmentation dataset",
  "reference": "Your Reference",
  "licence": "Research Use Only",
  "release": "1.0",
  "tensorImageSize": "4D"
}
```

## 使用方法

### 1. 通过 C# API 使用

```csharp
// 创建 nnUNet 服务
var nnunetService = serviceProvider.GetRequiredService<INnUNetService>();

// 配置训练参数
var trainingConfig = new NnUNetTrainingConfig
{
    DatasetId = 1,
    DatasetName = "MedicalDemo",
    DatasetPath = @"C:\data\nnunet\Dataset001_MedicalDemo",
    Architecture = NnUNetArchitecture.ThreeD_FullRes,
    MaxEpochs = 1000,
    BatchSize = 2,
    LearningRate = 0.01,
    OutputDirectory = @"C:\output\nnunet"
};

// 开始训练
var progress = new Progress<NnUNetTrainingProgress>(p => 
{
    Console.WriteLine($"Epoch {p.CurrentEpoch}/{p.TotalEpochs}, Loss: {p.TrainingLoss:F4}");
});

var result = await nnunetService.TrainModelAsync(trainingConfig, progress);

if (result.Success)
{
    Console.WriteLine($"训练成功！最佳模型: {result.BestModelPath}");
}
```

### 2. 通过 Python 脚本使用

```bash
# 使用示例训练脚本
python scripts/nnunet/train_nnunet_example.py \
    --dataset_id 1 \
    --dataset_name MedicalDemo \
    --architecture 3d_fullres \
    --epochs 1000

# 仅执行预处理
python scripts/nnunet/train_nnunet_example.py \
    --dataset_id 1 \
    --only_preprocess

# 跳过预处理，直接训练
python scripts/nnunet/train_nnunet_example.py \
    --dataset_id 1 \
    --skip_preprocessing
```

## 配置选项

### 网络架构

- `2d`: 2D U-Net，适用于 2D 图像
- `3d_lowres`: 3D U-Net 低分辨率，适用于大体积数据
- `3d_fullres`: 3D U-Net 全分辨率，默认选择
- `3d_cascade_fullres`: 3D U-Net 级联，两阶段训练

### 训练参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| MaxEpochs | 1000 | 最大训练轮数 |
| BatchSize | 2 | 批次大小 |
| LearningRate | 0.01 | 学习率 |
| UseMixedPrecision | true | 混合精度训练 |
| EnableDataAugmentation | true | 数据增强 |
| UseDeepSupervision | true | 深度监督 |
| ValidationFrequency | 50 | 验证频率 |

## 推理使用

### 单个图像推理

```csharp
var inferenceConfig = new NnUNetInferenceConfig
{
    ModelPath = @"C:\models\nnunet\best_model.pth",
    InputPath = @"C:\input\image.nii.gz",
    OutputPath = @"C:\output\segmentation.nii.gz",
    UseTestTimeAugmentation = true,
    SaveProbabilities = false
};

var outputPath = await nnunetService.InferAsync(inferenceConfig);
```

### 批量推理

```csharp
var inputPaths = Directory.GetFiles(@"C:\input", "*.nii.gz");
var outputPaths = await nnunetService.BatchInferAsync(
    modelPath: @"C:\models\nnunet\best_model.pth",
    inputPaths: inputPaths,
    outputPath: @"C:\output",
    inferenceConfig: inferenceConfig,
    progressCallback: new Progress<int>(count => 
        Console.WriteLine($"已处理 {count} 个文件"))
);
```

## 环境变量

系统会自动设置以下环境变量：

- `nnUNet_raw`: 原始数据目录
- `nnUNet_preprocessed`: 预处理数据目录  
- `nnUNet_results`: 训练结果目录

## 故障排除

### 常见问题

1. **CUDA 内存不足**
   - 减小批次大小 (BatchSize)
   - 使用混合精度训练
   - 选择较小的网络架构

2. **数据格式错误**
   - 确保图像为 NIfTI 格式 (.nii.gz)
   - 检查数据集目录结构
   - 验证 dataset.json 格式

3. **训练速度慢**
   - 确保使用 GPU 训练
   - 启用混合精度训练
   - 增加线程数

### 日志查看

```csharp
// 获取训练日志
var logs = await nnunetService.GetTrainingLogsAsync(outputDirectory);
Console.WriteLine(logs);
```

### 环境检查

```csharp
// 检查 nnUNet 环境
var isReady = await nnunetService.CheckEnvironmentAsync();
if (!isReady)
{
    // 安装依赖
    await nnunetService.InstallDependenciesAsync();
}
```

## 性能优化

### 硬件要求

- **GPU**: NVIDIA GPU with CUDA support (推荐 8GB+ VRAM)
- **内存**: 16GB+ RAM (取决于数据大小)
- **存储**: SSD 推荐 (加速数据加载)

### 训练优化

1. **数据预处理**: 使用多线程预处理
2. **混合精度**: 启用 FP16 训练
3. **数据增强**: 根据数据特点调整
4. **学习率调度**: 使用自适应学习率

## 扩展功能

### 自定义训练器

可以通过继承 nnUNet 的训练器类来实现自定义功能：

```python
from nnunetv2.training.nnUNetTrainer.nnUNetTrainer import nnUNetTrainer

class CustomTrainer(nnUNetTrainer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 自定义初始化
    
    def configure_optimizers(self):
        # 自定义优化器
        pass
```

### 模型集成

```csharp
// 集成多个模型的预测结果
var modelPaths = new List<string> 
{
    @"C:\models\model1.pth",
    @"C:\models\model2.pth",
    @"C:\models\model3.pth"
};

var ensembleResult = await nnunetService.EnsemblePredictionsAsync(
    modelPaths, inputPath, outputPath);
```

## 参考资源

- [nnUNet 官方文档](https://github.com/MIC-DKFZ/nnUNet)
- [nnUNet 论文](https://www.nature.com/articles/s41592-020-01008-z)
- [医学图像分割最佳实践](https://link-to-best-practices)

## 支持

如有问题，请查看：
1. 系统日志文件
2. nnUNet 官方文档
3. 提交 Issue 到项目仓库
