"""重构后的AI预测扩展

使用依赖注入和分层架构的新版本扩展
"""

import logging
from typing import Any, Dict, List, Optional
from datetime import datetime

from .core.container import ServiceContainer, get_container
from .core.interfaces import (
    ModelManagerInterface,
    PredictionServiceInterface,
    UIComponentsInterface,
    CacheManagerInterface,
    EventManagerInterface,
    ConfigValidatorInterface
)
from .core.exceptions import ExtensionError, ConfigurationError
from .core.types import ExtensionConfig, ModelConfig, PredictionResult, DetectionResult

from .services import (
    ModelManagerService,
    PredictionService,
    UIService,
    CacheService,
    EventService,
    ConfigService,
    ValidationService
)

logger = logging.getLogger(__name__)


class RefactoredAIPredictionExtension:
    """重构后的AI预测扩展
    
    使用依赖注入和分层架构，提供更好的可维护性和可测试性
    """
    
    def __init__(self, 
                 config: Optional[Dict[str, Any]] = None,
                 container: Optional[ServiceContainer] = None):
        """初始化AI预测扩展
        
        Args:
            config: 扩展配置
            container: 服务容器
        """
        self._container = container or get_container()
        self._config: Optional[ExtensionConfig] = None
        self._initialized = False
        self._active_model_id: Optional[str] = None
        
        # 注册服务
        self._register_services()
        
        # 加载配置
        if config:
            self._load_config(config)
        
        logger.info("重构后的AI预测扩展初始化完成")
    
    def initialize(self, config: Optional[Dict[str, Any]] = None) -> None:
        """初始化扩展
        
        Args:
            config: 扩展配置
        """
        try:
            if self._initialized:
                logger.warning("扩展已经初始化")
                return
            
            # 加载配置
            if config:
                self._load_config(config)
            elif not self._config:
                self._load_default_config()
            
            # 初始化服务
            self._initialize_services()
            
            # 注册事件监听器
            self._register_event_listeners()
            
            # 初始化UI组件
            self._initialize_ui_components()
            
            # 加载模型
            self._load_models()
            
            self._initialized = True
            
            # 发布初始化完成事件
            event_service = self._container.get_service(EventManagerInterface)
            event_service.publish('extension_initialized', {
                'extension_id': 'ai_model_prediction',
                'models_count': len(self._config.models),
                'timestamp': datetime.now().isoformat()
            })
            
            logger.info("AI预测扩展初始化成功")
            
        except Exception as e:
            logger.error(f"AI预测扩展初始化失败: {e}")
            raise ExtensionError(f"扩展初始化失败: {str(e)}")
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用模型列表
        
        Returns:
            模型列表
        """
        try:
            model_manager = self._container.get_service(ModelManagerInterface)
            return model_manager.get_available_models()
        except Exception as e:
            logger.error(f"获取可用模型失败: {e}")
            return []
    
    def set_active_model(self, model_id: str) -> bool:
        """设置活动模型
        
        Args:
            model_id: 模型ID
            
        Returns:
            是否成功设置
        """
        try:
            model_manager = self._container.get_service(ModelManagerInterface)
            success = model_manager.set_active_model(model_id)
            
            if success:
                self._active_model_id = model_id
                
                # 发布模型切换事件
                event_service = self._container.get_service(EventManagerInterface)
                event_service.publish('model_changed', {
                    'model_id': model_id,
                    'timestamp': datetime.now().isoformat()
                })
                
                logger.info(f"活动模型已设置: {model_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"设置活动模型失败: {e}")
            return False
    
    def get_active_model(self) -> Optional[Dict[str, Any]]:
        """获取当前活动模型
        
        Returns:
            活动模型信息
        """
        try:
            if not self._active_model_id:
                return None
            
            model_manager = self._container.get_service(ModelManagerInterface)
            return model_manager.get_model_info(self._active_model_id)
            
        except Exception as e:
            logger.error(f"获取活动模型失败: {e}")
            return None
    
    async def run_prediction_async(self, 
                                   image_data: Any,
                                   model_id: Optional[str] = None,
                                   options: Optional[Dict[str, Any]] = None) -> Optional[PredictionResult]:
        """异步运行预测
        
        Args:
            image_data: 图像数据
            model_id: 模型ID（如果为None则使用活动模型）
            options: 预测选项
            
        Returns:
            预测结果
        """
        try:
            # 使用指定模型或活动模型
            target_model_id = model_id or self._active_model_id
            if not target_model_id:
                raise ExtensionError("没有可用的模型")
            
            # 发布预测开始事件
            event_service = self._container.get_service(EventManagerInterface)
            event_service.publish('prediction_started', {
                'model_id': target_model_id,
                'timestamp': datetime.now().isoformat()
            })
            
            # 运行预测
            prediction_service = self._container.get_service(PredictionServiceInterface)
            result = await prediction_service.predict_async(
                image_data=image_data,
                model_id=target_model_id,
                options=options or {}
            )
            
            if result:
                # 更新UI
                await self._update_ui_with_results(result)
                
                # 发布预测完成事件
                event_service.publish('prediction_completed', {
                    'model_id': target_model_id,
                    'detections_count': len(result.detections),
                    'processing_time': result.processing_time,
                    'timestamp': datetime.now().isoformat()
                })
                
                logger.info(f"预测完成: {len(result.detections)}个检测结果")
            
            return result
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            
            # 发布预测失败事件
            event_service = self._container.get_service(EventManagerInterface)
            event_service.publish('prediction_failed', {
                'model_id': model_id or self._active_model_id,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            
            return None
    
    def run_prediction(self, 
                       image_data: Any,
                       model_id: Optional[str] = None,
                       options: Optional[Dict[str, Any]] = None) -> Optional[PredictionResult]:
        """同步运行预测
        
        Args:
            image_data: 图像数据
            model_id: 模型ID（如果为None则使用活动模型）
            options: 预测选项
            
        Returns:
            预测结果
        """
        try:
            # 使用指定模型或活动模型
            target_model_id = model_id or self._active_model_id
            if not target_model_id:
                raise ExtensionError("没有可用的模型")
            
            # 运行预测
            prediction_service = self._container.get_service(PredictionServiceInterface)
            result = prediction_service.predict(
                image_data=image_data,
                model_id=target_model_id,
                options=options or {}
            )
            
            if result:
                # 更新UI
                self._update_ui_with_results_sync(result)
                logger.info(f"预测完成: {len(result.detections)}个检测结果")
            
            return result
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return None
    
    def add_model(self, model_config: Dict[str, Any]) -> bool:
        """添加模型
        
        Args:
            model_config: 模型配置
            
        Returns:
            是否成功添加
        """
        try:
            model_manager = self._container.get_service(ModelManagerInterface)
            success = model_manager.add_model(model_config)
            
            if success:
                # 发布模型添加事件
                event_service = self._container.get_service(EventManagerInterface)
                event_service.publish('model_added', {
                    'model_id': model_config.get('id'),
                    'model_name': model_config.get('name'),
                    'timestamp': datetime.now().isoformat()
                })
                
                logger.info(f"模型添加成功: {model_config.get('id')}")
            
            return success
            
        except Exception as e:
            logger.error(f"添加模型失败: {e}")
            return False
    
    def remove_model(self, model_id: str) -> bool:
        """移除模型
        
        Args:
            model_id: 模型ID
            
        Returns:
            是否成功移除
        """
        try:
            model_manager = self._container.get_service(ModelManagerInterface)
            success = model_manager.remove_model(model_id)
            
            if success:
                # 如果移除的是活动模型，清除活动模型
                if self._active_model_id == model_id:
                    self._active_model_id = None
                
                # 发布模型移除事件
                event_service = self._container.get_service(EventManagerInterface)
                event_service.publish('model_removed', {
                    'model_id': model_id,
                    'timestamp': datetime.now().isoformat()
                })
                
                logger.info(f"模型移除成功: {model_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"移除模型失败: {e}")
            return False
    
    def get_extension_info(self) -> Dict[str, Any]:
        """获取扩展信息
        
        Returns:
            扩展信息
        """
        try:
            model_manager = self._container.get_service(ModelManagerInterface)
            cache_service = self._container.get_service(CacheManagerInterface)
            event_service = self._container.get_service(EventManagerInterface)
            ui_service = self._container.get_service(UIComponentsInterface)
            
            return {
                'name': 'AI模型预测扩展（重构版）',
                'version': '2.0.0',
                'description': '使用依赖注入和分层架构的AI预测扩展',
                'initialized': self._initialized,
                'active_model': self._active_model_id,
                'models_count': len(model_manager.get_available_models()),
                'cache_stats': cache_service.get_stats(),
                'event_stats': event_service.get_stats(),
                'ui_stats': ui_service.get_stats(),
                'container_services': len(self._container.get_registered_services()),
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取扩展信息失败: {e}")
            return {
                'name': 'AI模型预测扩展（重构版）',
                'version': '2.0.0',
                'error': str(e)
            }
    
    def cleanup(self) -> None:
        """清理扩展资源"""
        try:
            if self._initialized:
                # 发布清理开始事件
                event_service = self._container.get_service(EventManagerInterface)
                event_service.publish('extension_cleanup_started', {
                    'timestamp': datetime.now().isoformat()
                })
                
                # 清理容器
                self._container.cleanup()
                
                self._initialized = False
                self._active_model_id = None
                
                logger.info("AI预测扩展清理完成")
            
        except Exception as e:
            logger.error(f"清理扩展失败: {e}")
    
    def _register_services(self) -> None:
        """注册服务"""
        # 注册核心服务
        self._container.register_singleton(ConfigValidatorInterface, ConfigService)
        self._container.register_singleton(CacheManagerInterface, CacheService)
        self._container.register_singleton(EventManagerInterface, EventService)
        self._container.register_singleton(UIComponentsInterface, UIService)
        
        # 注册业务服务
        self._container.register_singleton(ModelManagerInterface, ModelManagerService)
        self._container.register_singleton(PredictionServiceInterface, PredictionService)
        
        # 注册验证服务
        self._container.register_singleton(ValidationService)
        
        logger.debug("服务注册完成")
    
    def _load_config(self, config: Dict[str, Any]) -> None:
        """加载配置"""
        try:
            config_service = self._container.get_service(ConfigValidatorInterface)
            self._config = config_service.validate_extension_config(config)
            logger.info("扩展配置加载成功")
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            raise ConfigurationError(f"配置加载失败: {str(e)}")
    
    def _load_default_config(self) -> None:
        """加载默认配置"""
        try:
            config_service = self._container.get_service(ConfigValidatorInterface)
            default_config = config_service.get_default_extension_config()
            self._config = config_service.validate_extension_config(default_config)
            logger.info("默认配置加载成功")
            
        except Exception as e:
            logger.error(f"加载默认配置失败: {e}")
            raise ConfigurationError(f"默认配置加载失败: {str(e)}")
    
    def _initialize_services(self) -> None:
        """初始化服务"""
        # 初始化模型管理器
        model_manager = self._container.get_service(ModelManagerInterface)
        model_manager.initialize()
        
        # 初始化预测服务
        prediction_service = self._container.get_service(PredictionServiceInterface)
        prediction_service.initialize()
        
        logger.debug("服务初始化完成")
    
    def _register_event_listeners(self) -> None:
        """注册事件监听器"""
        event_service = self._container.get_service(EventManagerInterface)
        
        # 注册图像加载事件
        event_service.subscribe('image_loaded', self._on_image_loaded)
        
        # 注册研究加载事件
        event_service.subscribe('study_loaded', self._on_study_loaded)
        
        # 注册模型状态变化事件
        event_service.subscribe('model_status_changed', self._on_model_status_changed)
        
        logger.debug("事件监听器注册完成")
    
    def _initialize_ui_components(self) -> None:
        """初始化UI组件"""
        try:
            ui_service = self._container.get_service(UIComponentsInterface)
            
            # 创建工具栏按钮
            ui_service.create_toolbar_button(
                button_id="ai_prediction_btn",
                label="AI预测",
                icon="brain",
                tooltip="运行AI模型预测"
            )
            
            # 创建模型选择面板
            ui_service.create_panel(
                panel_id="ai_models_panel",
                title="AI模型",
                position="right",
                width=300
            )
            
            # 创建模型选择器
            models = self.get_available_models()
            ui_service.create_model_selector(
                selector_id="model_selector",
                models=models
            )
            
            logger.debug("UI组件初始化完成")
            
        except Exception as e:
            logger.error(f"UI组件初始化失败: {e}")
    
    def _load_models(self) -> None:
        """加载模型"""
        try:
            if not self._config or not self._config.models:
                logger.warning("没有配置的模型")
                return
            
            model_manager = self._container.get_service(ModelManagerInterface)
            
            for model_config in self._config.models:
                model_manager.add_model(model_config.__dict__)
            
            # 设置第一个模型为活动模型
            if self._config.models:
                first_model = self._config.models[0]
                self.set_active_model(first_model.id)
            
            logger.info(f"加载了 {len(self._config.models)} 个模型")
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
    
    def _on_image_loaded(self, event_data) -> None:
        """图像加载事件处理"""
        try:
            # 如果启用了自动检测，运行预测
            if self._config and self._config.options.get('auto_detection', False):
                image_data = event_data.data.get('image_data')
                if image_data:
                    # 异步运行预测
                    import asyncio
                    asyncio.create_task(self.run_prediction_async(image_data))
            
        except Exception as e:
            logger.error(f"处理图像加载事件失败: {e}")
    
    def _on_study_loaded(self, event_data) -> None:
        """研究加载事件处理"""
        try:
            # 更新UI状态
            ui_service = self._container.get_service(UIComponentsInterface)
            ui_service.show_notification(
                message="新研究已加载",
                type="info",
                duration=3000
            )
            
        except Exception as e:
            logger.error(f"处理研究加载事件失败: {e}")
    
    def _on_model_status_changed(self, event_data) -> None:
        """模型状态变化事件处理"""
        try:
            # 更新UI组件状态
            ui_service = self._container.get_service(UIComponentsInterface)
            
            model_id = event_data.data.get('model_id')
            status = event_data.data.get('status')
            
            if status == 'loading':
                ui_service.show_notification(
                    message=f"正在加载模型: {model_id}",
                    type="info"
                )
            elif status == 'ready':
                ui_service.show_notification(
                    message=f"模型已就绪: {model_id}",
                    type="success"
                )
            elif status == 'error':
                ui_service.show_notification(
                    message=f"模型加载失败: {model_id}",
                    type="error"
                )
            
        except Exception as e:
            logger.error(f"处理模型状态变化事件失败: {e}")
    
    async def _update_ui_with_results(self, result: PredictionResult) -> None:
        """异步更新UI显示结果"""
        try:
            ui_service = self._container.get_service(UIComponentsInterface)
            
            # 显示边界框
            if result.detections:
                bounding_boxes = [detection.bbox for detection in result.detections]
                class_names = [detection.class_name for detection in result.detections]
                confidences = [detection.confidence for detection in result.detections]
                
                ui_service.show_bounding_boxes(
                    overlay_id="prediction_overlay",
                    bounding_boxes=bounding_boxes,
                    class_names=class_names,
                    confidences=confidences
                )
            
            # 创建结果显示
            ui_service.create_results_display(
                display_id="prediction_results",
                results=result.detections,
                show_confidence=True
            )
            
            # 显示成功通知
            ui_service.show_notification(
                message=f"检测完成，发现 {len(result.detections)} 个目标",
                type="success",
                duration=5000
            )
            
        except Exception as e:
            logger.error(f"更新UI失败: {e}")
    
    def _update_ui_with_results_sync(self, result: PredictionResult) -> None:
        """同步更新UI显示结果"""
        try:
            ui_service = self._container.get_service(UIComponentsInterface)
            
            # 显示边界框
            if result.detections:
                bounding_boxes = [detection.bbox for detection in result.detections]
                class_names = [detection.class_name for detection in result.detections]
                confidences = [detection.confidence for detection in result.detections]
                
                ui_service.show_bounding_boxes(
                    overlay_id="prediction_overlay",
                    bounding_boxes=bounding_boxes,
                    class_names=class_names,
                    confidences=confidences
                )
            
            # 创建结果显示
            ui_service.create_results_display(
                display_id="prediction_results",
                results=result.detections,
                show_confidence=True
            )
            
        except Exception as e:
            logger.error(f"更新UI失败: {e}")
    
    @property
    def container(self) -> ServiceContainer:
        """获取服务容器"""
        return self._container
    
    @property
    def config(self) -> Optional[ExtensionConfig]:
        """获取扩展配置"""
        return self._config
    
    @property
    def is_initialized(self) -> bool:
        """是否已初始化"""
        return self._initialized