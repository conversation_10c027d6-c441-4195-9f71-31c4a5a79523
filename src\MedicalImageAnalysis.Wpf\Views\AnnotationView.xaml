<UserControl x:Class="MedicalImageAnalysis.Wpf.Views.AnnotationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:controls="clr-namespace:MedicalImageAnalysis.Wpf.Controls"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
            <materialDesign:PackIcon Kind="Tag"
                                   Width="32" Height="32"
                                   VerticalAlignment="Center"
                                   Margin="0,0,12,0"/>
            <TextBlock Text="智能标注系统"
                     FontSize="28"
                     FontWeight="Medium"
                     VerticalAlignment="Center"/>
        </StackPanel>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：图像标注区域 -->
            <materialDesign:Card Grid.Column="0"
                               Margin="0,0,8,0"
                               materialDesign:ElevationAssist.Elevation="Dp2">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 工具栏 -->
                    <Border Grid.Row="0"
                          Background="{DynamicResource MaterialDesignCardBackground}"
                          BorderBrush="{DynamicResource MaterialDesignDivider}"
                          BorderThickness="0,0,0,1"
                          Padding="16,12">
                        <StackPanel Orientation="Horizontal">
                            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                  Margin="0,0,8,0"
                                  Click="OpenImage_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FolderOpen"
                                                               Width="16" Height="16"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="打开图像"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="8,0"/>

                            <!-- 标注工具 -->
                            <ToggleButton x:Name="RectangleToolButton"
                                        Content="矩形"
                                        Style="{StaticResource MaterialDesignFlatToggleButton}"
                                        Margin="4,0"
                                        Click="AnnotationTool_Click"
                                        Tag="Rectangle"/>
                            <ToggleButton x:Name="CircleToolButton"
                                        Content="圆形"
                                        Style="{StaticResource MaterialDesignFlatToggleButton}"
                                        Margin="4,0"
                                        Click="AnnotationTool_Click"
                                        Tag="Circle"/>
                            <ToggleButton x:Name="PolygonToolButton"
                                        Content="多边形"
                                        Style="{StaticResource MaterialDesignFlatToggleButton}"
                                        Margin="4,0"
                                        Click="AnnotationTool_Click"
                                        Tag="Polygon"/>
                            <ToggleButton x:Name="PointToolButton"
                                        Content="点"
                                        Style="{StaticResource MaterialDesignFlatToggleButton}"
                                        Margin="4,0"
                                        Click="AnnotationTool_Click"
                                        Tag="Point"/>

                            <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="8,0"/>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="4,0"
                                  Click="AIDetection_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Brain"
                                                               Width="16" Height="16"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="AI检测"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                  Margin="4,0"
                                  Background="#FF6B35"
                                  BorderBrush="#FF6B35"
                                  Click="BatchAIAnnotation_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FolderMultiple"
                                                               Width="16" Height="16"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="AI批量标注"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="4,0"
                                  Click="ClearAnnotations_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Delete"
                                                               Width="16" Height="16"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="清除"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>
                        </StackPanel>
                    </Border>

                    <!-- 图像标注区域 -->
                    <Border Grid.Row="1" Background="Black">
                        <ScrollViewer x:Name="AnnotationScrollViewer"
                                    HorizontalScrollBarVisibility="Auto"
                                    VerticalScrollBarVisibility="Auto">
                            <Grid>
                                <!-- 占位符 -->
                                <StackPanel x:Name="AnnotationPlaceholderPanel"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Visibility="Visible">
                                    <materialDesign:PackIcon Kind="ImageOutline"
                                                           Width="64" Height="64"
                                                           Foreground="Gray"
                                                           HorizontalAlignment="Center"
                                                           Margin="0,0,0,16"/>
                                    <TextBlock Text="点击&quot;打开图像&quot;开始标注"
                                             Foreground="Gray"
                                             HorizontalAlignment="Center"
                                             FontSize="16"/>
                                    <TextBlock Text="支持格式：DICOM (.dcm), PNG, JPEG, BMP"
                                             Foreground="DarkGray"
                                             HorizontalAlignment="Center"
                                             FontSize="12"
                                             Margin="0,8,0,0"/>
                                </StackPanel>

                                <!-- 图像显示 -->
                                <Image x:Name="AnnotationImage"
                                     Visibility="Collapsed"
                                     Stretch="Uniform"
                                     RenderOptions.BitmapScalingMode="HighQuality"/>

                                <!-- 标注画布 -->
                                <Canvas x:Name="AnnotationCanvas"
                                      Background="Transparent"
                                      Visibility="Collapsed"
                                      MouseLeftButtonDown="AnnotationCanvas_MouseLeftButtonDown"
                                      MouseMove="AnnotationCanvas_MouseMove"
                                      MouseLeftButtonUp="AnnotationCanvas_MouseLeftButtonUp"/>
                            </Grid>
                        </ScrollViewer>
                    </Border>

                    <!-- 状态栏 -->
                    <Border Grid.Row="2"
                          Background="{DynamicResource MaterialDesignCardBackground}"
                          BorderBrush="{DynamicResource MaterialDesignDivider}"
                          BorderThickness="0,1,0,0"
                          Padding="16,8">
                        <StackPanel x:Name="AnnotationStatusPanel"
                                  Orientation="Horizontal"
                                  Visibility="Collapsed">
                            <TextBlock x:Name="AnnotationStatusText"
                                     Text="准备就绪"
                                     FontSize="12"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock Text=" | "
                                     FontSize="12"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     Margin="8,0"/>
                            <TextBlock x:Name="AnnotationCountText"
                                     Text="标注数量: 0"
                                     FontSize="12"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </materialDesign:Card>

            <!-- 右侧：标注控制面板 -->
            <materialDesign:Card Grid.Column="1"
                               Margin="8,0,0,0"
                               materialDesign:ElevationAssist.Elevation="Dp2">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <TextBlock Text="标注工具"
                                 FontSize="18"
                                 FontWeight="Medium"
                                 Margin="0,0,0,16"/>

                        <!-- 窗宽窗位调整 (仅对DICOM文件显示) -->
                        <controls:WindowLevelControl x:Name="WindowLevelControl"
                                                   Visibility="Collapsed"
                                                   Margin="0,0,0,16"/>

                        <!-- 标注类别 -->
                        <Expander Header="标注类别" IsExpanded="True" Margin="0,0,0,8">
                            <StackPanel Margin="16,8,0,8">
                                <ComboBox x:Name="AnnotationCategoryComboBox"
                                        materialDesign:HintAssist.Hint="选择标注类别"
                                        Margin="0,0,0,8">
                                    <ComboBoxItem Content="病灶区域"/>
                                    <ComboBoxItem Content="正常组织"/>
                                    <ComboBoxItem Content="骨折"/>
                                    <ComboBoxItem Content="肿瘤"/>
                                    <ComboBoxItem Content="血管"/>
                                    <ComboBoxItem Content="其他"/>
                                </ComboBox>

                                <TextBox x:Name="CustomCategoryTextBox"
                                       materialDesign:HintAssist.Hint="自定义类别"
                                       Margin="0,8,0,0"/>

                                <Grid Margin="0,8,0,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <Button Grid.Column="0"
                                          Content="添加类别"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Margin="0,0,4,0"
                                          Click="AddCategory_Click"/>

                                    <Button Grid.Column="1"
                                          Content="删除类别"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Margin="4,0,0,0"
                                          Click="RemoveCategory_Click"
                                          ToolTip="删除当前选中的类别"/>
                                </Grid>

                                <!-- 类别管理说明 -->
                                <TextBlock Text="提示：只能删除自定义添加的类别，系统预设类别无法删除"
                                         FontSize="10"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"
                                         Margin="0,4,0,0"
                                         TextWrapping="Wrap"/>
                            </StackPanel>
                        </Expander>

                        <!-- 类别管理 -->
                        <Expander Header="类别管理" Margin="0,0,0,8">
                            <StackPanel Margin="16,8,0,8">
                                <TextBlock Text="当前类别列表："
                                         FontWeight="Medium"
                                         Margin="0,0,0,8"/>

                                <ListBox x:Name="CategoryListBox"
                                       MaxHeight="120"
                                       SelectionChanged="CategoryListBox_SelectionChanged">
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <!-- 颜色指示器 -->
                                                <Rectangle Grid.Column="0"
                                                         Width="12" Height="12"
                                                         Margin="0,0,8,0"
                                                         VerticalAlignment="Center">
                                                    <Rectangle.Fill>
                                                        <SolidColorBrush Color="{Binding ColorIndicator}"/>
                                                    </Rectangle.Fill>
                                                    <Rectangle.Stroke>
                                                        <SolidColorBrush Color="{Binding BorderColor}"/>
                                                    </Rectangle.Stroke>
                                                    <Rectangle.StrokeThickness>1</Rectangle.StrokeThickness>
                                                </Rectangle>

                                                <!-- 类别名称 -->
                                                <TextBlock Grid.Column="1"
                                                         Text="{Binding Name}"
                                                         VerticalAlignment="Center"/>

                                                <!-- 类型标识 -->
                                                <TextBlock Grid.Column="2"
                                                         Text="{Binding TypeLabel}"
                                                         FontSize="10"
                                                         Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                         VerticalAlignment="Center"/>
                                            </Grid>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>

                                <Button Content="刷新类别列表"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,8,0,0"
                                      Click="RefreshCategoryList_Click"/>
                            </StackPanel>
                        </Expander>

                        <!-- 标注颜色说明 -->
                        <Expander Header="标注颜色说明" Margin="0,0,0,8">
                            <StackPanel Margin="16,8,0,8">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- 病灶区域 - 绿色 -->
                                    <Rectangle Grid.Row="0" Grid.Column="0" Width="16" Height="16"
                                             Stroke="DarkGreen" StrokeThickness="2" Fill="Green"
                                             Margin="0,2,8,2"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="病灶区域"
                                             VerticalAlignment="Center" FontSize="12"/>

                                    <!-- 肿瘤 - 红色 -->
                                    <Rectangle Grid.Row="1" Grid.Column="0" Width="16" Height="16"
                                             Stroke="DarkRed" StrokeThickness="2" Fill="Red"
                                             Margin="0,2,8,2"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="肿瘤"
                                             VerticalAlignment="Center" FontSize="12"/>

                                    <!-- 骨折 - 橙色 -->
                                    <Rectangle Grid.Row="2" Grid.Column="0" Width="16" Height="16"
                                             Stroke="DarkOrange" StrokeThickness="2" Fill="Orange"
                                             Margin="0,2,8,2"/>
                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="骨折"
                                             VerticalAlignment="Center" FontSize="12"/>

                                    <!-- 血管 - 蓝色 -->
                                    <Rectangle Grid.Row="3" Grid.Column="0" Width="16" Height="16"
                                             Stroke="DarkBlue" StrokeThickness="2" Fill="Blue"
                                             Margin="0,2,8,2"/>
                                    <TextBlock Grid.Row="3" Grid.Column="1" Text="血管"
                                             VerticalAlignment="Center" FontSize="12"/>

                                    <!-- 正常组织 - 浅蓝色 -->
                                    <Rectangle Grid.Row="4" Grid.Column="0" Width="16" Height="16"
                                             Stroke="Blue" StrokeThickness="2" Fill="LightBlue"
                                             Margin="0,2,8,2"/>
                                    <TextBlock Grid.Row="4" Grid.Column="1" Text="正常组织"
                                             VerticalAlignment="Center" FontSize="12"/>

                                    <!-- 其他 - 黄色 -->
                                    <Rectangle Grid.Row="5" Grid.Column="0" Width="16" Height="16"
                                             Stroke="Orange" StrokeThickness="2" Fill="Yellow"
                                             Margin="0,2,8,2"/>
                                    <TextBlock Grid.Row="5" Grid.Column="1" Text="其他"
                                             VerticalAlignment="Center" FontSize="12"/>
                                </Grid>
                            </StackPanel>
                        </Expander>

                        <!-- AI 辅助设置 -->
                        <Expander Header="AI 辅助设置" Margin="0,0,0,8">
                            <StackPanel Margin="16,8,0,8">
                                <CheckBox Content="启用AI预标注"
                                        x:Name="EnableAIPreAnnotationCheckBox"
                                        IsChecked="True"
                                        Margin="0,4"/>
                                <CheckBox Content="智能边缘吸附"
                                        x:Name="EnableSmartSnapCheckBox"
                                        IsChecked="True"
                                        Margin="0,4"/>
                                <CheckBox Content="相似区域建议"
                                        x:Name="EnableSimilarRegionSuggestionCheckBox"
                                        IsChecked="False"
                                        Margin="0,4"/>

                                <StackPanel Margin="0,16,0,0">
                                    <TextBlock Text="检测置信度阈值" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <Slider x:Name="ConfidenceThresholdSlider"
                                          Minimum="0"
                                          Maximum="1.0"
                                          Value="0.7"
                                          TickFrequency="0.1"
                                          SmallChange="0.1"
                                          LargeChange="0.1"
                                          IsSnapToTickEnabled="True"
                                          ValueChanged="ConfidenceThresholdSlider_ValueChanged"
                                          ToolTip="设置AI检测的最低置信度阈值"/>

                                    <Grid Margin="0,4,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0" Text="0.1"
                                                 HorizontalAlignment="Left"
                                                 FontSize="10"
                                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>

                                        <TextBlock Grid.Column="1"
                                                 Text="{Binding ElementName=ConfidenceThresholdSlider, Path=Value, StringFormat=F1}"
                                                 HorizontalAlignment="Center"
                                                 FontSize="14"
                                                 FontWeight="Medium"
                                                 Foreground="{DynamicResource MaterialDesignBody}"/>

                                        <TextBlock Grid.Column="2" Text="1.0"
                                                 HorizontalAlignment="Right"
                                                 FontSize="10"
                                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </Grid>

                                    <TextBlock Text="只显示置信度高于此阈值的AI检测结果"
                                             HorizontalAlignment="Center"
                                             FontSize="10"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             Margin="0,4,0,0"
                                             TextWrapping="Wrap"/>

                                    <!-- 快捷置信度设置 -->
                                    <TextBlock Text="快捷设置:"
                                             FontSize="11"
                                             FontWeight="Medium"
                                             Margin="0,8,0,4"/>

                                    <UniformGrid Columns="5" Margin="0,0,0,0">
                                        <Button Content="0.5"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              FontSize="10"
                                              Padding="4,2"
                                              Margin="1"
                                              Click="QuickConfidence_Click"
                                              Tag="0.5"/>
                                        <Button Content="0.6"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              FontSize="10"
                                              Padding="4,2"
                                              Margin="1"
                                              Click="QuickConfidence_Click"
                                              Tag="0.6"/>
                                        <Button Content="0.7"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              FontSize="10"
                                              Padding="4,2"
                                              Margin="1"
                                              Click="QuickConfidence_Click"
                                              Tag="0.7"/>
                                        <Button Content="0.8"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              FontSize="10"
                                              Padding="4,2"
                                              Margin="1"
                                              Click="QuickConfidence_Click"
                                              Tag="0.8"/>
                                        <Button Content="0.9"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              FontSize="10"
                                              Padding="4,2"
                                              Margin="1"
                                              Click="QuickConfidence_Click"
                                              Tag="0.9"/>
                                    </UniformGrid>
                                </StackPanel>
                            </StackPanel>
                        </Expander>

                        <!-- 标注列表 -->
                        <Expander Header="标注列表" IsExpanded="True" Margin="0,0,0,8">
                            <StackPanel Margin="16,8,0,8">
                                <ListView x:Name="AnnotationListView"
                                        MaxHeight="200"
                                        SelectionChanged="AnnotationListView_SelectionChanged">
                                    <ListView.View>
                                        <GridView>
                                            <GridViewColumn Header="类型" Width="60">
                                                <GridViewColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <TextBlock Text="{Binding Type}"/>
                                                    </DataTemplate>
                                                </GridViewColumn.CellTemplate>
                                            </GridViewColumn>
                                            <GridViewColumn Header="类别" Width="80">
                                                <GridViewColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <TextBlock Text="{Binding Category}"/>
                                                    </DataTemplate>
                                                </GridViewColumn.CellTemplate>
                                            </GridViewColumn>
                                            <GridViewColumn Header="置信度" Width="60">
                                                <GridViewColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <TextBlock Text="{Binding Confidence, StringFormat=F1}"/>
                                                    </DataTemplate>
                                                </GridViewColumn.CellTemplate>
                                            </GridViewColumn>
                                        </GridView>
                                    </ListView.View>
                                </ListView>

                                <StackPanel Orientation="Horizontal"
                                          HorizontalAlignment="Right"
                                          Margin="0,8,0,0">
                                    <Button Content="编辑"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Margin="0,0,4,0"
                                          Click="EditAnnotation_Click"/>
                                    <Button Content="删除"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Click="DeleteAnnotation_Click"/>
                                </StackPanel>
                            </StackPanel>
                        </Expander>

                        <!-- 导出设置 -->
                        <Expander Header="导出设置" Margin="0,0,0,8">
                            <StackPanel Margin="16,8,0,8">
                                <ComboBox x:Name="ExportFormatComboBox"
                                        materialDesign:HintAssist.Hint="导出格式"
                                        SelectedIndex="0"
                                        Margin="0,0,0,8">
                                    <ComboBoxItem Content="YOLO格式"/>
                                    <ComboBoxItem Content="COCO格式"/>
                                    <ComboBoxItem Content="Pascal VOC格式"/>
                                    <ComboBoxItem Content="JSON格式"/>
                                </ComboBox>

                                <CheckBox Content="包含图像文件"
                                        IsChecked="True"
                                        Margin="0,4"/>
                                <CheckBox Content="生成统计报告"
                                        IsChecked="False"
                                        Margin="0,4"/>

                                <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                      Margin="0,16,0,0"
                                      Click="ExportAnnotations_Click">
                                    <Button.Content>
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Export"
                                                                   Width="16" Height="16"
                                                                   Margin="0,0,8,0"/>
                                            <TextBlock Text="导出"/>
                                        </StackPanel>
                                    </Button.Content>
                                </Button>
                            </StackPanel>
                        </Expander>

                        <!-- 操作按钮 -->
                        <StackPanel Margin="0,24,0,0">
                            <Button Content="保存标注"
                                  Style="{StaticResource MaterialDesignRaisedButton}"
                                  Margin="0,4"
                                  Click="SaveAnnotations_Click"/>
                            <Button Content="加载标注"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,4"
                                  Click="LoadAnnotations_Click"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>
        </Grid>
    </Grid>
</UserControl>
