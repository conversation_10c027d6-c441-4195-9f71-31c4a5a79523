#!/usr/bin/env pwsh
# 医学影像智能标注与模型训练系统 - 发布包创建脚本

param(
    [string]$OutputPath = ".\Release",
    [switch]$IncludePython = $true,
    [switch]$IncludeModels = $true,
    [switch]$CreateInstaller = $true
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-Host "=" * 60 -ForegroundColor Cyan
    Write-Host " $Title" -ForegroundColor Cyan
    Write-Host "=" * 60 -ForegroundColor Cyan
}

function Write-Step {
    param([string]$Message)
    Write-Host "🔄 $Message" -ForegroundColor Yellow
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

# 主函数
function Main {
    Write-Header "医学影像智能标注与模型训练系统 - 发布包创建"
    
    # 检查环境
    Test-Environment
    
    # 创建发布目录结构
    Create-ReleaseStructure
    
    # 发布.NET应用程序
    Publish-DotNetApp
    
    # 复制Python环境和依赖
    if ($IncludePython) {
        Copy-PythonEnvironment
    }
    
    # 复制模型文件
    if ($IncludeModels) {
        Copy-ModelFiles
    }
    
    # 复制数据和配置文件
    Copy-DataAndConfig
    
    # 创建启动脚本
    Create-LaunchScripts
    
    # 创建用户指南
    Create-UserGuide
    
    # 创建安装程序
    if ($CreateInstaller) {
        Create-Installer
    }
    
    Write-Header "发布包创建完成"
    Write-Success "发布包位置: $OutputPath"
    Write-Success "可以将整个文件夹分发给用户使用"
}

function Test-Environment {
    Write-Step "检查构建环境"
    
    # 检查.NET SDK
    try {
        $dotnetVersion = dotnet --version
        Write-Success ".NET SDK版本: $dotnetVersion"
    }
    catch {
        Write-Error ".NET SDK未安装或配置错误"
        exit 1
    }
    
    # 检查Python
    if ($IncludePython) {
        try {
            $pythonVersion = python --version
            Write-Success "Python版本: $pythonVersion"
        }
        catch {
            Write-Error "Python未安装或配置错误"
            exit 1
        }
    }
    
    # 检查项目文件
    if (!(Test-Path "src\MedicalImageAnalysis.Wpf\MedicalImageAnalysis.Wpf.csproj")) {
        Write-Error "WPF项目文件不存在"
        exit 1
    }
    
    Write-Success "环境检查通过"
}

function Create-ReleaseStructure {
    Write-Step "创建发布目录结构"
    
    # 清理旧的发布目录
    if (Test-Path $OutputPath) {
        Remove-Item $OutputPath -Recurse -Force
    }
    
    # 创建目录结构
    $directories = @(
        "$OutputPath\App",
        "$OutputPath\Data\DICOM",
        "$OutputPath\Data\Models",
        "$OutputPath\Data\Datasets\Train\images",
        "$OutputPath\Data\Datasets\Train\labels", 
        "$OutputPath\Data\Datasets\Val\images",
        "$OutputPath\Data\Datasets\Val\labels",
        "$OutputPath\Data\Datasets\Test\images",
        "$OutputPath\Data\Datasets\Test\labels",
        "$OutputPath\Data\Output",
        "$OutputPath\Data\Temp",
        "$OutputPath\Data\Logs",
        "$OutputPath\Python",
        "$OutputPath\Scripts",
        "$OutputPath\Docs",
        "$OutputPath\Examples"
    )
    
    foreach ($dir in $directories) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "  创建目录: $dir" -ForegroundColor Gray
    }
    
    Write-Success "目录结构创建完成"
}

function Publish-DotNetApp {
    Write-Step "发布.NET应用程序"
    
    try {
        # 发布WPF应用为自包含应用
        dotnet publish "src\MedicalImageAnalysis.Wpf\MedicalImageAnalysis.Wpf.csproj" `
            --configuration Release `
            --runtime win-x64 `
            --self-contained true `
            --output "$OutputPath\App" `
            --verbosity minimal
            
        Write-Success ".NET应用程序发布完成"
    }
    catch {
        Write-Error "发布.NET应用程序失败: $_"
        exit 1
    }
}

function Copy-PythonEnvironment {
    Write-Step "复制Python环境和依赖"
    
    try {
        # 创建Python虚拟环境
        python -m venv "$OutputPath\Python\venv"
        
        # 激活虚拟环境并安装依赖
        $activateScript = "$OutputPath\Python\venv\Scripts\Activate.ps1"
        & $activateScript
        
        # 安装YOLO训练依赖
        if (Test-Path "yolo_ohif\yolo_ohif\requirements_yolo_training.txt") {
            pip install -r "yolo_ohif\yolo_ohif\requirements_yolo_training.txt" --no-cache-dir
        }
        
        # 复制训练脚本
        Copy-Item "yolo_ohif\yolo_ohif\*.py" "$OutputPath\Python\" -Force
        
        # 停用虚拟环境
        deactivate
        
        Write-Success "Python环境配置完成"
    }
    catch {
        Write-Error "Python环境配置失败: $_"
    }
}

function Copy-ModelFiles {
    Write-Step "复制模型文件"
    
    # 复制预训练模型（如果存在）
    $modelSources = @(
        "yolo_ohif\yolo_ohif\models\*",
        "data\models\*",
        "Models\*"
    )
    
    foreach ($source in $modelSources) {
        if (Test-Path $source) {
            Copy-Item $source "$OutputPath\Data\Models\" -Recurse -Force -ErrorAction SilentlyContinue
        }
    }
    
    # 创建模型说明文件
    @"
# 模型文件说明

## 目录结构
- `pretrained/` - 预训练模型文件
- `custom/` - 自定义训练的模型
- `temp/` - 临时模型文件

## 支持的模型格式
- `.pt` - PyTorch模型文件
- `.onnx` - ONNX格式模型
- `.engine` - TensorRT引擎文件

## 模型放置说明
1. 将YOLO模型文件（.pt格式）放置在此目录下
2. 在应用程序中选择相应的模型文件进行推理
3. 训练完成的模型会自动保存到 `custom/` 子目录

## 推荐模型
- `yolo11n.pt` - 轻量级模型，速度快
- `yolo11s.pt` - 小型模型，平衡速度和精度
- `yolo11m.pt` - 中型模型，较高精度
- `yolo11l.pt` - 大型模型，高精度
- `yolo11x.pt` - 超大型模型，最高精度
"@ | Out-File "$OutputPath\Data\Models\README.md" -Encoding UTF8
    
    Write-Success "模型文件复制完成"
}

function Copy-DataAndConfig {
    Write-Step "复制数据和配置文件"
    
    # 复制示例DICOM文件
    if (Test-Path "Brain") {
        Copy-Item "Brain\*" "$OutputPath\Data\DICOM\" -Force
        Write-Host "  复制示例DICOM文件" -ForegroundColor Gray
    }
    
    # 复制配置文件
    Copy-Item "src\MedicalImageAnalysis.Wpf\appsettings.json" "$OutputPath\App\" -Force
    
    # 复制教程文档
    $docs = @(
        "智能打标教程.md",
        "模型训练教程.md", 
        "快速入门指南.md",
        "教程使用说明.md",
        "README.md"
    )
    
    foreach ($doc in $docs) {
        if (Test-Path $doc) {
            Copy-Item $doc "$OutputPath\Docs\" -Force
        }
    }
    
    Write-Success "数据和配置文件复制完成"
}

function Create-LaunchScripts {
    Write-Step "创建启动脚本"
    
    # 创建主启动脚本
    @"
@echo off
chcp 65001 > nul
title 医学影像智能标注与模型训练系统

echo.
echo ========================================
echo   医学影像智能标注与模型训练系统
echo ========================================
echo.

cd /d "%~dp0"

echo 正在启动应用程序...
start "" "App\MedicalImageAnalysis.Wpf.exe"

echo.
echo 应用程序已启动！
echo.
echo 如需帮助，请查看 Docs 目录中的教程文档
echo.
pause
"@ | Out-File "$OutputPath\启动应用程序.bat" -Encoding UTF8

    # 创建Python训练脚本
    @"
@echo off
chcp 65001 > nul
title YOLO模型训练

echo.
echo ========================================
echo        YOLO模型训练环境
echo ========================================
echo.

cd /d "%~dp0"

echo 激活Python环境...
call Python\venv\Scripts\activate.bat

echo.
echo Python环境已激活，可以运行以下训练脚本：
echo.
echo 1. python Python\start_yolo11x_training.py
echo 2. python Python\train_yolo11x_from_scratch.py
echo 3. python Python\create_yolo_dataset.py
echo.

cmd /k
"@ | Out-File "$OutputPath\启动训练环境.bat" -Encoding UTF8

    # 创建环境验证脚本
    Copy-Item "验证训练环境.py" "$OutputPath\Scripts\" -Force
    
    Write-Success "启动脚本创建完成"
}

function Create-UserGuide {
    Write-Step "创建用户指南"
    
    @"
# 医学影像智能标注与模型训练系统 - 用户指南

## 🚀 快速开始

### 1. 启动应用程序
双击 `启动应用程序.bat` 文件启动主应用程序。

### 2. 数据文件放置位置

#### DICOM影像文件
📁 **位置**: `Data\DICOM\`
📝 **说明**: 将您的DICOM文件（.dcm格式）放置在此目录下
📋 **示例**: 系统已包含示例脑部CT文件（DJ01.dcm - DJ10.dcm）

#### YOLO模型文件  
📁 **位置**: `Data\Models\`
📝 **说明**: 将训练好的YOLO模型文件（.pt格式）放置在此目录下
📋 **推荐**: 
- `yolo11n.pt` - 轻量级模型（快速）
- `yolo11x.pt` - 高精度模型（推荐）

#### 训练数据集
📁 **训练集**: `Data\Datasets\Train\`
  - `images\` - 训练图像文件
  - `labels\` - 训练标签文件（YOLO格式）

📁 **验证集**: `Data\Datasets\Val\`
  - `images\` - 验证图像文件  
  - `labels\` - 验证标签文件

📁 **测试集**: `Data\Datasets\Test\`
  - `images\` - 测试图像文件
  - `labels\` - 测试标签文件

## 📖 详细教程

### 智能标注教程
📄 查看: `Docs\智能打标教程.md`
🎯 内容: 详细的智能标注功能使用指南

### 模型训练教程  
📄 查看: `Docs\模型训练教程.md`
🏋️ 内容: 完整的AI模型训练指南

### 快速入门指南
📄 查看: `Docs\快速入门指南.md`
⚡ 内容: 5分钟快速上手指南

## 🔧 环境配置

### Python训练环境
1. 双击 `启动训练环境.bat` 激活Python环境
2. 运行 `python Scripts\验证训练环境.py` 验证环境配置

### 系统要求
- **操作系统**: Windows 10/11 (x64)
- **内存**: 至少 8GB RAM (推荐 16GB)
- **存储**: 至少 10GB 可用空间
- **显卡**: NVIDIA GPU (推荐，用于训练加速)

## 📞 技术支持

### 日志文件位置
- **应用程序日志**: `Data\Logs\`
- **训练日志**: `Python\runs\train\`

### 常见问题
1. **应用无法启动**: 检查.NET 8运行时是否安装
2. **Python环境问题**: 运行环境验证脚本检查
3. **模型加载失败**: 确认模型文件格式和路径

### 获取帮助
1. 查看 `Docs\` 目录中的详细教程
2. 检查日志文件获取错误信息
3. 运行环境验证脚本诊断问题

---

**开始您的医学影像AI之旅！** 🚀
"@ | Out-File "$OutputPath\用户指南.md" -Encoding UTF8

    Write-Success "用户指南创建完成"
}

function Create-Installer {
    Write-Step "创建安装程序配置"
    
    # 创建NSIS安装脚本（如果需要）
    @"
; 医学影像智能标注与模型训练系统安装脚本
; 需要NSIS (Nullsoft Scriptable Install System)

!define APP_NAME "医学影像智能标注与模型训练系统"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "Medical AI Team"
!define APP_URL "https://github.com/medical-ai/medical-image-analysis"

Name "`${APP_NAME}"
OutFile "MedicalImageAnalysis_Setup.exe"
InstallDir "`$PROGRAMFILES64\MedicalImageAnalysis"

Section "MainSection" SEC01
    SetOutPath "`$INSTDIR"
    File /r "Release\*"
    
    CreateDirectory "`$SMPROGRAMS\`${APP_NAME}"
    CreateShortCut "`$SMPROGRAMS\`${APP_NAME}\`${APP_NAME}.lnk" "`$INSTDIR\启动应用程序.bat"
    CreateShortCut "`$DESKTOP\`${APP_NAME}.lnk" "`$INSTDIR\启动应用程序.bat"
SectionEnd
"@ | Out-File "$OutputPath\installer.nsi" -Encoding UTF8

    Write-Success "安装程序配置创建完成"
}

# 执行主函数
try {
    Main
}
catch {
    Write-Error "发布包创建失败: $_"
    exit 1
}
