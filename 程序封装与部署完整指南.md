# 医学影像智能标注与模型训练系统 - 程序封装与部署完整指南

## 📦 封装概述

本指南提供了完整的程序封装和部署方案，包含依赖环境、应用图标、用户指南等所有必要组件。

## 🎨 应用图标和界面图片

### 已创建的图标资源
运行 `python 创建应用图标.py` 生成以下图标文件：

```
Assets/
├── app_icon.ico                    # Windows应用程序图标
├── app_icon_16x16.png             # 16x16像素图标
├── app_icon_32x32.png             # 32x32像素图标
├── app_icon_48x48.png             # 48x48像素图标
├── app_icon_64x64.png             # 64x64像素图标
├── app_icon_128x128.png           # 128x128像素图标
├── app_icon_256x256.png           # 256x256像素图标
├── interface_preview.png          # 应用界面预览图
└── directory_structure.png        # 目录结构说明图
```

### 图标特色
- **医学主题**: 采用医学蓝色配色方案
- **AI元素**: 包含神经网络节点和连接线
- **医学十字**: 体现医学影像应用特色
- **多尺寸**: 支持各种显示需求

## 📁 文件放置位置详细说明

### 🏥 DICOM影像文件
**📍 放置位置**: `Data\DICOM\`
**📋 支持格式**: 
- `.dcm` - 标准DICOM格式
- `.dicom` - DICOM扩展格式

**📝 使用说明**:
1. 将医学影像DICOM文件直接复制到此目录
2. 支持单个文件或批量文件
3. 文件名可以是任意格式
4. 系统已包含示例文件：DJ01.dcm - DJ10.dcm（脑部CT）

**📂 目录示例**:
```
Data\DICOM\
├── DJ01.dcm                       # 示例脑部CT切片1
├── DJ02.dcm                       # 示例脑部CT切片2
├── ...
├── DJ10.dcm                       # 示例脑部CT切片10
├── patient001_ct_head.dcm         # 用户文件示例
├── patient002_mri_brain.dcm       # 用户文件示例
└── chest_xray_001.dcm             # 用户文件示例
```

### 🤖 YOLO模型文件
**📍 放置位置**: `Data\Models\`
**📋 支持格式**:
- `.pt` - PyTorch模型文件（推荐）
- `.onnx` - ONNX格式模型
- `.engine` - TensorRT引擎文件

**📝 使用说明**:
1. 将训练好的YOLO模型文件放入此目录
2. 在应用程序中选择相应模型进行推理
3. 训练完成的模型会自动保存到此目录

**🎯 推荐模型**:
```
Data\Models\
├── yolo11n.pt                     # 轻量级模型（快速推理）
├── yolo11s.pt                     # 小型模型（平衡性能）
├── yolo11m.pt                     # 中型模型（较高精度）
├── yolo11l.pt                     # 大型模型（高精度）
├── yolo11x.pt                     # 超大型模型（最高精度）
├── medical_lesion_detection.pt    # 自定义医学病灶检测模型
├── bone_fracture_detection.pt     # 自定义骨折检测模型
└── pretrained/                    # 预训练模型子目录
    ├── coco_pretrained.pt
    └── medical_pretrained.pt
```

### 📊 训练数据集
**📍 放置位置**: `Data\Datasets\`
**📋 数据集结构**: YOLO格式（图像 + 标签文件）

**📝 组织方式**:
```
Data\Datasets\
├── Train\                         # 训练集（70%数据）
│   ├── images\
│   │   ├── image001.jpg
│   │   ├── image002.jpg
│   │   ├── image003.png
│   │   └── ...
│   └── labels\
│       ├── image001.txt           # 对应的YOLO标签文件
│       ├── image002.txt
│       ├── image003.txt
│       └── ...
├── Val\                           # 验证集（20%数据）
│   ├── images\
│   │   ├── val001.jpg
│   │   └── ...
│   └── labels\
│       ├── val001.txt
│       └── ...
└── Test\                          # 测试集（10%数据）
    ├── images\
    │   ├── test001.jpg
    │   └── ...
    └── labels\
        ├── test001.txt
        └── ...
```

**🏷️ YOLO标签格式**:
每个标签文件(.txt)包含目标信息，每行一个目标：
```
# 格式：class_id center_x center_y width height
0 0.5 0.5 0.3 0.4                 # 类别0，中心点(0.5,0.5)，宽0.3高0.4
1 0.2 0.3 0.1 0.2                 # 类别1，中心点(0.2,0.3)，宽0.1高0.2
```

**📋 数据集配置文件**:
创建 `Data\Datasets\dataset.yaml`：
```yaml
# 数据集路径
path: ./Data/Datasets
train: Train/images
val: Val/images
test: Test/images

# 类别数量
nc: 2

# 类别名称
names:
  0: lesion      # 病灶
  1: normal      # 正常组织
```

### 📈 测试集
**📍 放置位置**: `Data\Datasets\Test\`
**📝 用途**: 
- 最终模型性能评估
- 独立于训练和验证的数据
- 模型泛化能力测试

**📊 建议比例**:
- 训练集：70%
- 验证集：20%
- 测试集：10%

## 🚀 手动封装步骤

### 第一步：准备环境
```bash
# 检查.NET环境
dotnet --version

# 检查Python环境
python --version

# 安装必要的Python包
pip install ultralytics torch torchvision opencv-python pillow
```

### 第二步：创建目录结构
```bash
mkdir Release
mkdir Release\App
mkdir Release\Data\DICOM
mkdir Release\Data\Models
mkdir Release\Data\Datasets\Train\images
mkdir Release\Data\Datasets\Train\labels
mkdir Release\Data\Datasets\Val\images
mkdir Release\Data\Datasets\Val\labels
mkdir Release\Data\Datasets\Test\images
mkdir Release\Data\Datasets\Test\labels
mkdir Release\Data\Output
mkdir Release\Data\Logs
mkdir Release\Docs
mkdir Release\Assets
```

### 第三步：发布.NET应用
```bash
# 发布为自包含应用
dotnet publish src\MedicalImageAnalysis.Wpf\MedicalImageAnalysis.Wpf.csproj ^
    --configuration Release ^
    --runtime win-x64 ^
    --self-contained true ^
    --output Release\App
```

### 第四步：复制数据文件
```bash
# 复制示例DICOM文件
copy Brain\*.dcm Release\Data\DICOM\

# 复制文档
copy *.md Release\Docs\

# 复制图标
xcopy Assets Release\Assets\ /E /I

# 复制配置文件
copy src\MedicalImageAnalysis.Wpf\appsettings.json Release\App\
```

### 第五步：创建启动脚本
创建 `Release\启动应用程序.bat`：
```batch
@echo off
title 医学影像智能标注与模型训练系统

echo ========================================
echo   医学影像智能标注与模型训练系统
echo ========================================

cd /d "%~dp0"
echo 正在启动应用程序...
start "" "App\MedicalImageAnalysis.Wpf.exe"

echo 应用程序已启动！
echo 如需帮助，请查看 Docs 目录中的教程文档
pause
```

## 📖 用户使用指南

### 快速开始
1. **解压发布包**到任意目录
2. **双击"启动应用程序.bat"**启动主程序
3. **查看用户指南**了解详细使用方法

### 数据准备
1. **DICOM文件**：放入 `Data\DICOM\` 目录
2. **模型文件**：放入 `Data\Models\` 目录
3. **训练数据**：按照Train/Val/Test结构组织

### 功能使用
1. **智能标注**：加载DICOM文件，使用AI辅助标注
2. **模型训练**：准备数据集，配置训练参数
3. **结果导出**：导出标注数据和训练模型

## 🔧 故障排除

### 常见问题
1. **应用无法启动**
   - 检查.NET 8运行时是否安装
   - 确认文件权限设置正确

2. **DICOM文件无法加载**
   - 确认文件格式为.dcm或.dicom
   - 检查文件是否损坏

3. **模型加载失败**
   - 确认模型文件格式正确
   - 检查模型文件路径

4. **训练数据格式错误**
   - 确认使用YOLO格式标签
   - 检查图像和标签文件名对应

### 日志查看
- **应用日志**：`Data\Logs\`
- **训练日志**：训练输出目录

## 📞 技术支持

### 获取帮助
1. 查看 `Docs\` 目录中的详细教程
2. 检查日志文件获取错误信息
3. 运行环境验证脚本诊断问题
4. 联系技术支持团队

### 教程文档
- **智能打标教程.md** - 详细的标注功能指南
- **模型训练教程.md** - 完整的训练流程
- **快速入门指南.md** - 5分钟上手指南
- **教程使用说明.md** - 学习路线图

---

**🎉 恭喜！您已完成医学影像智能标注与模型训练系统的完整封装！**

用户现在可以：
1. 解压发布包到任意目录
2. 双击启动脚本即可使用
3. 按照指南放置数据文件
4. 开始智能标注和模型训练之旅！
