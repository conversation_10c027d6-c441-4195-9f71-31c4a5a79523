#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从头开始训练YOLO11x模型
整合数据集创建和模型训练的完整流程

功能:
1. 使用修复后的create_yolo_dataset.py创建标准YOLO数据集
2. 从头开始训练YOLO11x模型（不使用预训练权重）
3. 提供完整的训练监控和结果保存
"""

import os
import sys
import logging
import argparse
from pathlib import Path
import yaml
import shutil
from datetime import datetime
import json
import time
import psutil
from contextlib import contextmanager

# 导入YOLO相关模块
try:
    from ultralytics import YOLO
except ImportError:
    print("错误: 请安装ultralytics库")
    print("运行: pip install ultralytics>=8.3.0")
    sys.exit(1)

# 导入数据集创建模块
try:
    from create_yolo_dataset import YOLODatasetCreator
except ImportError:
    print("错误: 无法导入create_yolo_dataset模块")
    print("请确保create_yolo_dataset.py文件存在")
    sys.exit(1)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('yolo11x_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class YOLO11xTrainer:
    def __init__(self, dataset_root, output_root, img_size=640):
        # 文件操作配置
        self.file_retry_count = 5
        self.file_retry_delay = 1.0
        """
        初始化YOLO11x训练器
        
        Args:
            dataset_root: 原始数据集根目录
            output_root: 输出目录
            img_size: 图像尺寸
        """
        self.dataset_root = Path(dataset_root)
        self.output_root = Path(output_root)
        self.img_size = img_size
        
        # 创建输出目录
        self.output_root.mkdir(parents=True, exist_ok=True)
        
        # 设置子目录
        self.dataset_output = self.output_root / "yolo_dataset"
        self.training_output = self.output_root / "training_results"
        self.logs_output = self.output_root / "logs"
        
        # 创建子目录
        for dir_path in [self.dataset_output, self.training_output, self.logs_output]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"YOLO11x训练器初始化完成")
        logger.info(f"数据集根目录: {self.dataset_root}")
        logger.info(f"输出根目录: {self.output_root}")
        logger.info(f"图像尺寸: {self.img_size}")
    
    def check_existing_dataset(self):
        """
        检查是否已存在YOLO数据集
        """
        config_path = self.dataset_output / "data.yaml"
        
        if config_path.exists():
            # 检查数据集目录结构
            train_dir = self.dataset_output / "train"
            val_dir = self.dataset_output / "val"
            
            if train_dir.exists() and val_dir.exists():
                # 检查是否有图像和标签文件
                train_images = list((train_dir / "images").glob("*.jpg")) if (train_dir / "images").exists() else []
                train_labels = list((train_dir / "labels").glob("*.txt")) if (train_dir / "labels").exists() else []
                val_images = list((val_dir / "images").glob("*.jpg")) if (val_dir / "images").exists() else []
                val_labels = list((val_dir / "labels").glob("*.txt")) if (val_dir / "labels").exists() else []
                
                if train_images and train_labels and val_images and val_labels:
                    logger.info(f"✅ 发现已存在的YOLO数据集: {config_path}")
                    logger.info(f"   训练集: {len(train_images)} 图像, {len(train_labels)} 标签")
                    logger.info(f"   验证集: {len(val_images)} 图像, {len(val_labels)} 标签")
                    return str(config_path)
        
        return None

    def _detect_device(self):
        """
        智能检测可用的训练设备
        """
        try:
            import torch
            import os
            
            # 打印调试信息
            logger.info(f"🔍 PyTorch版本: {torch.__version__}")
            logger.info(f"🔍 CUDA编译版本: {torch.version.cuda}")
            logger.info(f"🔍 CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES', 'None')}")
            
            # 尝试修复CUDA_VISIBLE_DEVICES环境变量
            if os.environ.get('CUDA_VISIBLE_DEVICES') == '':
                logger.info("🔧 检测到空的CUDA_VISIBLE_DEVICES，正在清除...")
                del os.environ['CUDA_VISIBLE_DEVICES']
            
            # 检查CUDA可用性
            cuda_available = torch.cuda.is_available()
            logger.info(f"🔍 torch.cuda.is_available(): {cuda_available}")
            logger.info(f"🔍 torch.cuda.device_count(): {torch.cuda.device_count()}")
            
            if cuda_available:
                device_count = torch.cuda.device_count()
                logger.info(f"✅ 检测到 {device_count} 个CUDA设备")
                
                # 获取当前设备信息
                current_device = torch.cuda.current_device()
                device_name = torch.cuda.get_device_name(current_device)
                logger.info(f"   当前设备: GPU {current_device} ({device_name})")
                
                # 检查GPU内存
                memory_total = torch.cuda.get_device_properties(current_device).total_memory / 1024**3
                memory_allocated = torch.cuda.memory_allocated(current_device) / 1024**3
                memory_free = memory_total - memory_allocated
                logger.info(f"   GPU内存: {memory_free:.1f}GB 可用 / {memory_total:.1f}GB 总计")
                
                return '0'  # 使用第一个GPU
            else:
                logger.warning("⚠️ CUDA不可用，将使用CPU训练")
                logger.warning("   注意: CPU训练速度会很慢，建议检查CUDA安装")
                return 'cpu'
                
        except ImportError:
            logger.error("❌ 无法导入torch，使用CPU")
            return 'cpu'
        except Exception as e:
            logger.error(f"❌ 设备检测失败: {e}，使用CPU")
            return 'cpu'

    @contextmanager
    def safe_file_operation(self, file_path, operation_name="文件操作"):
        """
        安全的文件操作上下文管理器
        """
        for attempt in range(self.file_retry_count):
            try:
                yield file_path
                break
            except (PermissionError, OSError, FileNotFoundError) as e:
                if "另一个程序正在使用此文件" in str(e) or "WinError 32" in str(e):
                    if attempt == self.file_retry_count - 1:
                        logger.error(f"❌ {operation_name}失败，文件被占用: {file_path}")
                        logger.error("💡 建议解决方案:")
                        logger.error("   1. 关闭可能占用文件的程序（如文本编辑器、杀毒软件）")
                        logger.error("   2. 重启终端或IDE")
                        logger.error("   3. 检查是否有其他训练进程在运行")
                        self._check_file_usage(file_path)
                        raise
                    
                    wait_time = self.file_retry_delay * (attempt + 1)
                    logger.warning(f"⚠️ {operation_name}失败 (尝试 {attempt + 1}/{self.file_retry_count}): {e}")
                    logger.warning(f"   等待 {wait_time:.1f}s 后重试...")
                    time.sleep(wait_time)
                else:
                    raise
    
    def _check_file_usage(self, file_path):
        """
        检查文件使用情况
        """
        try:
            file_path = Path(file_path).resolve()
            logger.info(f"🔍 检查文件使用情况: {file_path}")
            
            processes_using_file = []
            for proc in psutil.process_iter(['pid', 'name', 'open_files']):
                try:
                    if proc.info['open_files']:
                        for file_info in proc.info['open_files']:
                            if Path(file_info.path).resolve() == file_path:
                                processes_using_file.append({
                                    'pid': proc.info['pid'],
                                    'name': proc.info['name']
                                })
                except (psutil.NoSuchProcess, psutil.AccessDenied, AttributeError):
                    continue
            
            if processes_using_file:
                logger.warning("📋 发现以下进程正在使用该文件:")
                for proc_info in processes_using_file:
                    logger.warning(f"   - {proc_info['name']} (PID: {proc_info['pid']})")
            else:
                logger.info("✅ 未发现其他进程使用该文件")
                
        except Exception as e:
            logger.warning(f"检查文件使用情况失败: {e}")
    
    def safe_write_file(self, file_path, content, encoding='utf-8'):
        """
        安全的文件写入函数
        """
        file_path = Path(file_path)
        temp_path = file_path.with_suffix(file_path.suffix + '.tmp')
        backup_path = None
        
        try:
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with self.safe_file_operation(temp_path, f"写入临时文件 {temp_path.name}"):
                # 写入临时文件
                with open(temp_path, 'w', encoding=encoding) as f:
                    f.write(content)
                    f.flush()  # 强制刷新缓冲区
                    os.fsync(f.fileno())  # 强制写入磁盘
            
            # 原子性重命名
            if file_path.exists():
                backup_path = file_path.with_suffix(file_path.suffix + '.bak')
                with self.safe_file_operation(backup_path, f"创建备份 {backup_path.name}"):
                    if backup_path.exists():
                        backup_path.unlink()
                    file_path.rename(backup_path)
            
            with self.safe_file_operation(file_path, f"重命名文件 {file_path.name}"):
                temp_path.rename(file_path)
            
            # 清理备份文件
            if backup_path and backup_path.exists():
                try:
                    backup_path.unlink()
                except:
                    pass  # 备份文件清理失败不影响主流程
                    
            logger.info(f"✅ 文件写入成功: {file_path}")
            
        except Exception as e:
            # 清理临时文件
            try:
                if temp_path.exists():
                    temp_path.unlink()
            except:
                pass
            
            # 恢复备份文件
            if backup_path and backup_path.exists() and not file_path.exists():
                try:
                    backup_path.rename(file_path)
                    logger.info(f"🔄 已恢复备份文件: {file_path}")
                except:
                    pass
            
            raise

    def create_dataset(self):
        """
        创建YOLO数据集（如果不存在）
        """
        logger.info("=== 步骤1: 检查/创建YOLO数据集 ===")
        
        # 首先检查是否已存在数据集
        existing_config = self.check_existing_dataset()
        if existing_config:
            logger.info("🚀 跳过数据集创建，使用已存在的数据集")
            return existing_config
        
        logger.info("📁 未发现已存在的数据集，开始创建新数据集...")
        
        # 使用修复后的数据集创建器
        creator = YOLODatasetCreator(
            dataset_root=str(self.dataset_root),
            output_root=str(self.dataset_output),
            img_size=self.img_size
        )
        
        # 创建数据集
        config_path = creator.create_dataset()
        
        logger.info(f"✅ 数据集创建完成: {config_path}")
        return config_path
    
    def train_from_scratch(self, config_path, epochs=200, batch_size=16, learning_rate=0.01):
        """
        从头开始训练YOLO11x模型
        
        Args:
            config_path: 数据集配置文件路径
            epochs: 训练轮数
            batch_size: 批次大小
            learning_rate: 学习率
        """
        logger.info("=== 步骤2: 从头开始训练YOLO11x模型 ===")
        
        # 智能设备检测
        device = self._detect_device()
        logger.info(f"🔧 使用设备: {device}")
        
        # 创建YOLO11x模型（使用预训练权重）
        model = YOLO('yolo11x.pt')  # 使用YOLO11x预训练权重
        
        # 训练参数配置
        train_args = {
            # 基本参数
            'data': str(config_path),
            'epochs': epochs,
            'batch': batch_size,
            'imgsz': self.img_size,
            
            # 输出设置
            'project': str(self.training_output),
            'name': f'yolo11x_from_scratch_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
            'save_period': 10,  # 每10个epoch保存一次
            
            # 设备和性能
            'device': device,
            'workers': 4,
            'patience': 30,  # 早停耐心值
            
            # 优化器设置
            'optimizer': 'AdamW',
            'lr0': learning_rate,
            'lrf': 0.01,  # 最终学习率因子
            'momentum': 0.937,
            'weight_decay': 0.0005,
            'warmup_epochs': 5,
            'warmup_momentum': 0.8,
            'warmup_bias_lr': 0.1,
            
            # 损失函数权重
            'box': 7.5,
            'cls': 0.5,
            'dfl': 1.5,
            
            # 数据增强
            'hsv_h': 0.015,
            'hsv_s': 0.7,
            'hsv_v': 0.4,
            'degrees': 0.0,
            'translate': 0.1,
            'scale': 0.5,
            'shear': 0.0,
            'perspective': 0.0,
            'flipud': 0.0,
            'fliplr': 0.5,
            'mosaic': 1.0,
            'mixup': 0.0,
            'copy_paste': 0.0,
            
            # 验证设置
            'val': True,
            'plots': True,
            'save_json': True,
            
            # 其他设置
            'verbose': True,
            'seed': 42,
            'deterministic': True,
            'single_cls': True,  # 单类检测
            'pretrained': False,  # 确保从头训练，不使用预训练权重
        }
        
        logger.info("开始训练YOLO11x模型...")
        logger.info(f"训练参数: {json.dumps(train_args, indent=2, ensure_ascii=False)}")
        
        # 开始训练
        try:
            results = model.train(**train_args)
            logger.info("模型训练完成!")
            return results
        except Exception as e:
            logger.error(f"训练过程中出现错误: {e}")
            raise
    
    def save_training_summary(self, results, config_path):
        """
        保存训练总结
        """
        logger.info("=== 步骤3: 保存训练总结 ===")
        
        summary = {
            'training_time': datetime.now().isoformat(),
            'dataset_config': str(config_path),
            'model_type': 'YOLO11x',
            'training_mode': 'from_scratch',
            'image_size': self.img_size,
            'results': str(results) if results else None
        }
        
        summary_path = self.logs_output / "training_summary.json"
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        logger.info(f"训练总结已保存: {summary_path}")
    
    def run_complete_training(self, epochs=200, batch_size=16, learning_rate=0.01):
        """
        运行完整的训练流程
        
        Args:
            epochs: 训练轮数
            batch_size: 批次大小
            learning_rate: 学习率
        """
        logger.info("🚀 开始完整的YOLO11x从头训练流程")
        logger.info(f"训练参数: epochs={epochs}, batch_size={batch_size}, lr={learning_rate}")
        
        try:
            # 1. 创建数据集
            config_path = self.create_dataset()
            
            # 2. 从头开始训练
            results = self.train_from_scratch(
                config_path=config_path,
                epochs=epochs,
                batch_size=batch_size,
                learning_rate=learning_rate
            )
            
            # 3. 保存训练总结
            self.save_training_summary(results, config_path)
            
            logger.info("✅ 完整训练流程成功完成!")
            logger.info(f"📁 输出目录: {self.output_root}")
            logger.info(f"🏆 训练结果: {self.training_output}")
            logger.info(f"📊 日志文件: {self.logs_output}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 训练流程失败: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description='从头开始训练YOLO11x模型')
    parser.add_argument('--dataset_root', type=str, default='./dataset', 
                       help='原始数据集根目录 (默认: ./dataset)')
    parser.add_argument('--output_root', type=str, default='./yolo11x_training_output', 
                       help='输出根目录 (默认: ./yolo11x_training_output)')
    parser.add_argument('--img_size', type=int, default=640, 
                       help='图像尺寸 (默认: 640)')
    parser.add_argument('--epochs', type=int, default=200, 
                       help='训练轮数 (默认: 200)')
    parser.add_argument('--batch_size', type=int, default=16, 
                       help='批次大小 (默认: 16)')
    parser.add_argument('--learning_rate', type=float, default=0.01, 
                       help='学习率 (默认: 0.01)')
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = YOLO11xTrainer(
        dataset_root=args.dataset_root,
        output_root=args.output_root,
        img_size=args.img_size
    )
    
    # 运行完整训练流程
    trainer.run_complete_training(
        epochs=args.epochs,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate
    )

if __name__ == "__main__":
    main()