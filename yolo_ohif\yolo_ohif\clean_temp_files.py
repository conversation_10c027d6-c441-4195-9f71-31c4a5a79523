#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理temp_images目录中不应该存在的txt文件
根据用户需求，只有包含撕裂区域的层面才应该有txt标签文件
"""

import os
import cv2
import numpy as np
import nibabel as nib
from pathlib import Path
import logging
from tqdm import tqdm

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TempFilesCleaner:
    def __init__(self, dataset_root, temp_images_dir, img_size=640):
        self.dataset_root = Path(dataset_root)
        self.temp_images_dir = Path(temp_images_dir)
        self.img_size = img_size
        
    def load_nii_image(self, nii_path):
        """加载NII图像"""
        try:
            nii_img = nib.load(str(nii_path))
            img_data = nii_img.get_fdata()
            return img_data
        except Exception as e:
            logger.error(f"加载NII文件失败 {nii_path}: {e}")
            return None
    
    def mask_to_bbox(self, mask, min_area_threshold=5):
        """检查mask是否能生成有效的边界框（适应小的冈上肌撕裂）"""
        # 找到mask中的非零像素
        coords = np.where(mask > 0)
        
        if len(coords[0]) == 0:
            return None
        
        # 检查撕裂区域面积是否足够大（降低阈值以检测小的冈上肌撕裂）
        tear_area = len(coords[0])
        if tear_area < min_area_threshold:
            return None
        
        # 计算边界框
        y_min, y_max = coords[0].min(), coords[0].max()
        x_min, x_max = coords[1].min(), coords[1].max()
        
        # 检查边界框尺寸是否合理（降低阈值以适应小的撕裂区域）
        bbox_width = x_max - x_min + 1  # 加1确保至少1像素宽度
        bbox_height = y_max - y_min + 1  # 加1确保至少1像素高度
        
        if bbox_width < 3 or bbox_height < 3:
            return None
        
        return True  # 返回True表示可以生成有效边界框
    
    def should_have_label(self, img_file_name):
        """检查指定的图像文件是否应该有对应的标签文件"""
        # 解析文件名：例如 "130.nii_slice_000.jpg"
        parts = img_file_name.replace('.jpg', '').split('_slice_')
        if len(parts) != 2:
            return False
        
        nii_name = parts[0] + '.nii.gz'
        slice_idx = int(parts[1])
        
        # 查找对应的标签文件
        tear_labels_dir = self.dataset_root / "label_T2"
        label_file = tear_labels_dir / nii_name
        
        if not label_file.exists():
            return False
        
        # 加载标签volume
        label_volume = self.load_nii_image(label_file)
        if label_volume is None:
            return False
        
        # 检查指定层面是否有标签
        if slice_idx >= label_volume.shape[2]:
            return False
        
        label_slice = label_volume[:, :, slice_idx]
        
        # 检查是否有非零像素
        if np.sum(label_slice > 0) == 0:
            return False
        
        # 调整尺寸并检查是否能生成有效边界框
        label_resized = cv2.resize(label_slice.astype(np.uint8), (self.img_size, self.img_size))
        return self.mask_to_bbox(label_resized) is not None
    
    def clean_temp_files(self):
        """清理temp_images目录中不应该存在的txt文件"""
        if not self.temp_images_dir.exists():
            logger.error(f"临时目录不存在: {self.temp_images_dir}")
            return
        
        # 获取所有txt文件
        txt_files = list(self.temp_images_dir.glob('*.txt'))
        logger.info(f"找到 {len(txt_files)} 个txt文件")
        
        files_to_remove = []
        files_to_keep = []
        
        for txt_file in tqdm(txt_files, desc="检查txt文件"):
            img_file_name = txt_file.stem + '.jpg'
            
            # 检查对应的图像文件是否存在
            img_file = self.temp_images_dir / img_file_name
            if not img_file.exists():
                files_to_remove.append(txt_file)
                logger.warning(f"对应图像文件不存在，将删除: {txt_file.name}")
                continue
            
            # 检查是否应该有标签文件
            if self.should_have_label(img_file_name):
                files_to_keep.append(txt_file)
                logger.debug(f"应该保留: {txt_file.name}")
            else:
                files_to_remove.append(txt_file)
                logger.info(f"不应该存在，将删除: {txt_file.name}")
        
        # 删除不应该存在的txt文件
        for txt_file in files_to_remove:
            try:
                txt_file.unlink()
                logger.info(f"已删除: {txt_file.name}")
            except Exception as e:
                logger.error(f"删除文件失败 {txt_file.name}: {e}")
        
        logger.info(f"清理完成:")
        logger.info(f"  - 保留的txt文件: {len(files_to_keep)} 个")
        logger.info(f"  - 删除的txt文件: {len(files_to_remove)} 个")
        
        return len(files_to_keep), len(files_to_remove)

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='清理temp_images目录中不应该存在的txt文件')
    parser.add_argument('--dataset-root', default='e:/Trae/yolo_ohif/dataset', 
                       help='数据集根目录路径')
    parser.add_argument('--temp-dir', default='e:/Trae/yolo_ohif/yolo_training_output/temp_images',
                       help='临时图像目录路径')
    parser.add_argument('--dry-run', action='store_true',
                       help='只检查不删除文件')
    
    args = parser.parse_args()
    
    # 创建清理器
    cleaner = TempFilesCleaner(args.dataset_root, args.temp_dir)
    
    if args.dry_run:
        logger.info("执行干运行模式（只检查不删除）...")
        # 这里可以添加干运行逻辑
    else:
        # 执行清理
        logger.info("开始清理temp_images目录中的txt文件...")
        kept, removed = cleaner.clean_temp_files()
        
        logger.info(f"清理完成！保留 {kept} 个，删除 {removed} 个txt文件")
        
        if removed > 0:
            logger.info("建议重新运行数据处理以确保数据一致性")
        else:
            logger.info("✅ 所有txt文件都符合要求！")

if __name__ == "__main__":
    main()