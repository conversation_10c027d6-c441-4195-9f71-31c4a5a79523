{% extends "base.html" %}

{% block title %}系统状态{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h2 class="mb-4"><i class="fas fa-heartbeat me-2"></i>系统状态检查</h2>
            
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-database me-2"></i>Orthanc DICOM服务器</h5>
                        </div>
                        <div class="card-body">
                            <div id="orthanc-status" class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">检查中...</span>
                                </div>
                                <p class="mt-2">检查中...</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-brain me-2"></i>YOLO检测服务</h5>
                        </div>
                        <div class="card-body">
                            <div id="yolo-status" class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">检查中...</span>
                                </div>
                                <p class="mt-2">检查中...</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-eye me-2"></i>OHIF查看器</h5>
                        </div>
                        <div class="card-body">
                            <div id="ohif-status" class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">检查中...</span>
                                </div>
                                <p class="mt-2">检查中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>系统配置信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Orthanc配置</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>URL:</strong> {{ config.ORTHANC.ORTHANC_URL }}</li>
                                        <li><strong>用户名:</strong> {{ config.ORTHANC.ORTHANC_USERNAME }}</li>
                                        <li><strong>DICOM Web URL:</strong> {{ config.ORTHANC.ORTHANC_DICOMWEB_URL }}</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>OHIF配置</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>查看器URL:</strong> {{ config.OHIF.OHIF_VIEWER_URL }}</li>
                                    </ul>
                                    
                                    <h6 class="mt-3">YOLO配置</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>默认模型:</strong> {{ config.YOLO.DEFAULT_MODEL }}</li>
                                        <li><strong>设备:</strong> {{ config.YOLO.DEVICE }}</li>
                                        <li><strong>置信度阈值:</strong> {{ config.YOLO.CONFIDENCE_THRESHOLD }}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="d-flex gap-2">
                        <button id="refresh-status" class="btn btn-primary">
                            <i class="fas fa-sync-alt me-1"></i>刷新状态
                        </button>
                        <a href="{{ url_for('web.dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回仪表板
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateServiceStatus(serviceId, status) {
    const container = document.getElementById(`${serviceId}-status`);
    
    if (status.status === 'ok') {
        container.innerHTML = `
            <div class="text-success">
                <i class="fas fa-check-circle fa-3x mb-2"></i>
                <p class="mb-1"><strong>服务正常</strong></p>
                ${status.version ? `<small class="text-muted">版本: ${status.version}</small>` : ''}
            </div>
        `;
    } else {
        container.innerHTML = `
            <div class="text-danger">
                <i class="fas fa-times-circle fa-3x mb-2"></i>
                <p class="mb-1"><strong>服务异常</strong></p>
                <small class="text-muted">${status.message || '未知错误'}</small>
            </div>
        `;
    }
}

function checkSystemHealth() {
    // 重置所有状态为加载中
    ['orthanc', 'yolo', 'ohif'].forEach(service => {
        const container = document.getElementById(`${service}-status`);
        container.innerHTML = `
            <div class="spinner-border" role="status">
                <span class="visually-hidden">检查中...</span>
            </div>
            <p class="mt-2">检查中...</p>
        `;
    });
    
    fetch('/api/health')
        .then(response => response.json())
        .then(data => {
            if (data.services) {
                updateServiceStatus('orthanc', data.services.orthanc);
                updateServiceStatus('yolo', data.services.yolo);
                updateServiceStatus('ohif', data.services.ohif);
            }
        })
        .catch(error => {
            console.error('检查系统健康状态时出错:', error);
            ['orthanc', 'yolo', 'ohif'].forEach(service => {
                updateServiceStatus(service, {
                    status: 'error',
                    message: '无法连接到服务器'
                });
            });
        });
}

// 页面加载时检查状态
document.addEventListener('DOMContentLoaded', checkSystemHealth);

// 刷新按钮事件
document.getElementById('refresh-status').addEventListener('click', checkSystemHealth);
</script>
{% endblock %}