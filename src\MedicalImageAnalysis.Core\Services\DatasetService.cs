using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.Core.Models;

namespace MedicalImageAnalysis.Core.Services
{
    /// <summary>
    /// 数据集管理服务
    /// </summary>
    public class DatasetService
    {
        private readonly ILogger<DatasetService> _logger;

        public DatasetService(ILogger<DatasetService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 查找数据集路径
        /// </summary>
        public string? FindDatasetPath(string datasetName)
        {
            try
            {
                // 预定义的数据集路径映射
                var predefinedPaths = new Dictionary<string, string>
                {
                    ["医学影像数据集 v1.0"] = @".\data\医学影像数据集_v1.0",
                    ["DICOM标注数据集"] = @".\data\DICOM标注数据集",
                    ["自定义数据集"] = @".\data\custom_dataset",
                    ["nnUNet医学分割数据集"] = @".\data\nnUNet_raw\Dataset001_Medical",
                    ["脑部MRI数据集"] = @".\data\nnUNet_raw\Dataset002_BrainMRI",
                    ["肺部CT数据集"] = @".\data\nnUNet_raw\Dataset003_LungCT"
                };

                // 首先检查预定义路径
                if (predefinedPaths.TryGetValue(datasetName, out var predefinedPath))
                {
                    var fullPath = Path.GetFullPath(predefinedPath);
                    if (Directory.Exists(fullPath))
                    {
                        return fullPath;
                    }
                }

                // 动态搜索路径
                var searchPaths = new[]
                {
                    Path.Combine(Environment.CurrentDirectory, "data", datasetName),
                    Path.Combine(Environment.CurrentDirectory, "data", datasetName.Replace(" ", "_")),
                    Path.Combine(Environment.CurrentDirectory, "data", datasetName.Replace(" ", "")),
                    Path.Combine(Environment.CurrentDirectory, "datasets", datasetName),
                    Path.Combine(Environment.CurrentDirectory, "datasets", datasetName.Replace(" ", "_")),
                    Path.Combine(@"C:\Data", datasetName),
                    Path.Combine(@"D:\Data", datasetName)
                };

                foreach (var searchPath in searchPaths)
                {
                    if (Directory.Exists(searchPath))
                    {
                        return Path.GetFullPath(searchPath);
                    }
                }

                _logger.LogWarning("未找到数据集路径: {DatasetName}", datasetName);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查找数据集路径时发生错误: {DatasetName}", datasetName);
                return null;
            }
        }

        /// <summary>
        /// 分析数据集统计信息
        /// </summary>
        public async Task<DatasetStatistics> AnalyzeDatasetAsync(string datasetPath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var statistics = new DatasetStatistics
                    {
                        DatasetPath = datasetPath,
                        AnalysisTime = DateTime.Now
                    };

                    if (!Directory.Exists(datasetPath))
                    {
                        _logger.LogWarning("数据集路径不存在: {DatasetPath}", datasetPath);
                        return statistics;
                    }

                    var imageExtensions = new[] { ".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".dcm", ".nii", ".nii.gz" };
                    var labelExtensions = new[] { ".txt", ".xml", ".json", ".csv" };

                    var allFiles = Directory.GetFiles(datasetPath, "*", SearchOption.AllDirectories);
                    
                    foreach (var file in allFiles)
                    {
                        var extension = Path.GetExtension(file).ToLower();
                        var fileInfo = new FileInfo(file);

                        statistics.TotalSize += fileInfo.Length;

                        if (imageExtensions.Contains(extension))
                        {
                            statistics.ImageCount++;
                            statistics.ImageFormats[extension] = statistics.ImageFormats.GetValueOrDefault(extension, 0) + 1;
                        }
                        else if (labelExtensions.Contains(extension))
                        {
                            statistics.AnnotationCount++;
                            statistics.AnnotationFormats[extension] = statistics.AnnotationFormats.GetValueOrDefault(extension, 0) + 1;
                        }
                    }

                    statistics.SubfolderCount = Directory.GetDirectories(datasetPath, "*", SearchOption.AllDirectories).Length;

                    // 分析数据集结构
                    AnalyzeDatasetStructure(datasetPath, statistics);

                    _logger.LogInformation("数据集分析完成: {DatasetPath}, 图像: {ImageCount}, 标注: {AnnotationCount}", 
                        datasetPath, statistics.ImageCount, statistics.AnnotationCount);

                    return statistics;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "分析数据集时发生错误: {DatasetPath}", datasetPath);
                    return new DatasetStatistics { DatasetPath = datasetPath, AnalysisTime = DateTime.Now };
                }
            });
        }

        /// <summary>
        /// 分析数据集结构
        /// </summary>
        private void AnalyzeDatasetStructure(string datasetPath, DatasetStatistics statistics)
        {
            try
            {
                // 检查是否为YOLO格式数据集
                var imagesDir = Path.Combine(datasetPath, "images");
                var labelsDir = Path.Combine(datasetPath, "labels");

                if (Directory.Exists(imagesDir) && Directory.Exists(labelsDir))
                {
                    statistics.DatasetType = "YOLO";
                    
                    // 分析训练/验证/测试分割
                    var splits = new[] { "train", "val", "test" };
                    foreach (var split in splits)
                    {
                        var splitImagesDir = Path.Combine(imagesDir, split);
                        var splitLabelsDir = Path.Combine(labelsDir, split);

                        if (Directory.Exists(splitImagesDir))
                        {
                            var imageFiles = Directory.GetFiles(splitImagesDir, "*.*", SearchOption.TopDirectoryOnly)
                                .Where(f => new[] { ".jpg", ".jpeg", ".png", ".bmp" }.Contains(Path.GetExtension(f).ToLower()))
                                .ToArray();

                            statistics.SplitStatistics[split] = new SplitStatistics
                            {
                                ImageCount = imageFiles.Length,
                                LabelCount = Directory.Exists(splitLabelsDir) ? 
                                    Directory.GetFiles(splitLabelsDir, "*.txt", SearchOption.TopDirectoryOnly).Length : 0
                            };
                        }
                    }
                }
                else
                {
                    statistics.DatasetType = "Custom";
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "分析数据集结构时发生错误: {DatasetPath}", datasetPath);
                statistics.DatasetType = "Unknown";
            }
        }

        /// <summary>
        /// 验证数据集
        /// </summary>
        public async Task<DatasetValidationResult> ValidateDatasetAsync(string datasetPath)
        {
            return await Task.Run(() =>
            {
                var result = new DatasetValidationResult
                {
                    DatasetPath = datasetPath,
                    ValidationTime = DateTime.Now
                };

                try
                {
                    if (!Directory.Exists(datasetPath))
                    {
                        result.Issues.Add("数据集路径不存在");
                        result.IsValid = false;
                        return result;
                    }

                    // 检查基本结构
                    var hasImages = Directory.GetFiles(datasetPath, "*.*", SearchOption.AllDirectories)
                        .Any(f => new[] { ".jpg", ".jpeg", ".png", ".bmp", ".dcm", ".nii" }.Contains(Path.GetExtension(f).ToLower()));

                    if (!hasImages)
                    {
                        result.Issues.Add("未找到图像文件");
                        result.IsValid = false;
                    }

                    // 检查YOLO格式数据集
                    var imagesDir = Path.Combine(datasetPath, "images");
                    var labelsDir = Path.Combine(datasetPath, "labels");

                    if (Directory.Exists(imagesDir) && Directory.Exists(labelsDir))
                    {
                        ValidateYoloDataset(imagesDir, labelsDir, result);
                    }

                    result.IsValid = !result.Issues.Any();

                    _logger.LogInformation("数据集验证完成: {DatasetPath}, 有效: {IsValid}, 问题数: {IssueCount}", 
                        datasetPath, result.IsValid, result.Issues.Count);

                    return result;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "验证数据集时发生错误: {DatasetPath}", datasetPath);
                    result.Issues.Add($"验证过程中发生错误: {ex.Message}");
                    result.IsValid = false;
                    return result;
                }
            });
        }

        /// <summary>
        /// 验证YOLO格式数据集
        /// </summary>
        private void ValidateYoloDataset(string imagesDir, string labelsDir, DatasetValidationResult result)
        {
            try
            {
                var splits = new[] { "train", "val", "test" };
                
                foreach (var split in splits)
                {
                    var splitImagesDir = Path.Combine(imagesDir, split);
                    var splitLabelsDir = Path.Combine(labelsDir, split);

                    if (!Directory.Exists(splitImagesDir))
                        continue;

                    var imageFiles = Directory.GetFiles(splitImagesDir, "*.*", SearchOption.TopDirectoryOnly)
                        .Where(f => new[] { ".jpg", ".jpeg", ".png", ".bmp" }.Contains(Path.GetExtension(f).ToLower()))
                        .ToArray();

                    if (imageFiles.Length == 0)
                    {
                        result.Issues.Add($"{split} 分割中没有图像文件");
                        continue;
                    }

                    if (!Directory.Exists(splitLabelsDir))
                    {
                        result.Issues.Add($"{split} 分割缺少标签目录");
                        continue;
                    }

                    // 检查图像和标签的匹配情况
                    var unmatchedImages = 0;
                    foreach (var imageFile in imageFiles)
                    {
                        var imageName = Path.GetFileNameWithoutExtension(imageFile);
                        var labelFile = Path.Combine(splitLabelsDir, imageName + ".txt");
                        
                        if (!File.Exists(labelFile))
                        {
                            unmatchedImages++;
                        }
                    }

                    if (unmatchedImages > 0)
                    {
                        result.Issues.Add($"{split} 分割中有 {unmatchedImages} 个图像文件缺少对应的标签文件");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "验证YOLO数据集时发生错误");
                result.Issues.Add("YOLO数据集验证过程中发生错误");
            }
        }

        /// <summary>
        /// 创建示例数据集结构
        /// </summary>
        public async Task<bool> CreateSampleDatasetStructureAsync(string datasetPath, string datasetName)
        {
            try
            {
                if (Directory.Exists(datasetPath))
                {
                    _logger.LogWarning("数据集目录已存在: {DatasetPath}", datasetPath);
                    return false;
                }

                // 创建YOLO格式的目录结构
                var directories = new[]
                {
                    Path.Combine(datasetPath, "images", "train"),
                    Path.Combine(datasetPath, "images", "val"),
                    Path.Combine(datasetPath, "images", "test"),
                    Path.Combine(datasetPath, "labels", "train"),
                    Path.Combine(datasetPath, "labels", "val"),
                    Path.Combine(datasetPath, "labels", "test")
                };

                foreach (var dir in directories)
                {
                    Directory.CreateDirectory(dir);
                }

                // 创建README文件
                var readmeContent = $@"# {datasetName}

## 数据集结构

```
{Path.GetFileName(datasetPath)}/
├── images/
│   ├── train/          # 训练图像
│   ├── val/            # 验证图像
│   └── test/           # 测试图像
├── labels/
│   ├── train/          # 训练标注
│   ├── val/            # 验证标注
│   └── test/           # 测试标注
└── README.md           # 本文件

## 使用说明

1. 将图像文件放入对应的 images/ 子目录中
2. 将YOLO格式的标注文件放入对应的 labels/ 子目录中
3. 标注文件名应与图像文件名相同（扩展名为.txt）

## 支持的文件格式

- 图像: .jpg, .jpeg, .png, .bmp
- 标注: .txt (YOLO格式)

## 创建时间

{DateTime.Now:yyyy-MM-dd HH:mm:ss}
";

                await File.WriteAllTextAsync(Path.Combine(datasetPath, "README.md"), readmeContent);

                _logger.LogInformation("成功创建示例数据集结构: {DatasetPath}", datasetPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建示例数据集结构时发生错误: {DatasetPath}", datasetPath);
                return false;
            }
        }
    }
}
