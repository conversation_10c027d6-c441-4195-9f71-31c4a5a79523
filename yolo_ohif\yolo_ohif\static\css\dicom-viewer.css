/* DICOM查看器专用样式 */

.viewer-container {
    height: calc(100vh - 120px);
    background: #000;
    position: relative;
    overflow: hidden;
    border: 1px solid #333;
}

.dicom-viewport {
    width: 100%;
    height: 100%;
    position: relative;
    cursor: crosshair;
    background: #000;
}

.dicom-canvas {
    position: absolute;
    top: 0;
    left: 0;
    max-width: 100%;
    max-height: 100%;
}

.detection-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.detection-box {
    position: absolute;
    border: 2px solid #ff4444;
    background: rgba(255, 68, 68, 0.1);
    pointer-events: auto;
    cursor: pointer;
    transition: all 0.2s ease;
}

.detection-box:hover {
    border-color: #ff6666;
    background: rgba(255, 68, 68, 0.2);
    box-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
}

.detection-label {
    position: absolute;
    background: rgba(255, 68, 68, 0.9);
    color: white;
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 3px;
    top: -22px;
    left: 0;
    white-space: nowrap;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.toolbar {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    padding: 12px 15px;
    border-bottom: 2px solid #34495e;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar .btn {
    margin-right: 8px;
    background: #34495e;
    border: 1px solid #4a5568;
    color: white;
    padding: 8px 12px;
    border-radius: 5px;
    transition: all 0.2s ease;
    font-size: 13px;
}

.toolbar .btn:hover {
    background: #4a6741;
    border-color: #5a7a51;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toolbar .btn.active {
    background: #27ae60;
    border-color: #2ecc71;
    box-shadow: 0 0 8px rgba(39, 174, 96, 0.3);
}

.toolbar .btn i {
    margin-right: 4px;
}

.info-panel {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
    padding: 20px;
    height: calc(100vh - 120px);
    overflow-y: auto;
    border-left: 1px solid #4a5568;
}

.info-panel h5 {
    color: #ecf0f1;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #4a5568;
    font-weight: 600;
}

.series-list, .detection-results {
    max-height: 250px;
    overflow-y: auto;
    border: 1px solid #4a5568;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 20px;
    background: rgba(52, 73, 94, 0.3);
}

.series-item, .detection-item {
    padding: 10px 12px;
    margin: 8px 0;
    background: #4a5568;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}

.series-item:hover, .detection-item:hover {
    background: #5a6578;
    transform: translateX(2px);
}

.series-item.active {
    background: #27ae60;
    border-left-color: #2ecc71;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.2);
}

.detection-item {
    border-left-color: #e74c3c;
}

.detection-item:hover {
    border-left-color: #c0392b;
}

.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 18px;
    background: rgba(0, 0, 0, 0.8);
    padding: 20px 30px;
    border-radius: 8px;
    text-align: center;
}

.loading i {
    margin-right: 10px;
    color: #3498db;
}

.image-info {
    position: absolute;
    top: 15px;
    left: 15px;
    color: white;
    background: rgba(0, 0, 0, 0.8);
    padding: 12px 15px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    z-index: 5;
    border: 1px solid #333;
    backdrop-filter: blur(5px);
}

.image-info div {
    margin: 3px 0;
}

.controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    color: white;
    background: rgba(0, 0, 0, 0.85);
    padding: 12px 15px;
    border-radius: 8px;
    z-index: 5;
    border: 1px solid #444;
    backdrop-filter: blur(8px);
    width: 200px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.slider-container {
    margin: 8px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.slider-container label {
    display: inline-block;
    width: 40px;
    font-size: 11px;
    font-weight: 500;
    color: #ecf0f1;
    text-align: right;
}

.slider-container input[type="range"] {
    flex: 1;
    height: 6px;
    background: #34495e;
    border-radius: 3px;
    outline: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.slider-container input[type="range"]:hover {
    background: #3d566e;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 50%;
    cursor: pointer;
    border: 3px solid white;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
    transition: all 0.2s ease;
}

.slider-container input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

.slider-container input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 50%;
    cursor: pointer;
    border: 3px solid white;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
    transition: all 0.2s ease;
}

.slider-container input[type="range"]::-moz-range-thumb:hover {
    transform: scale(1.1);
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

.slider-container span {
    min-width: 40px;
    text-align: center;
    font-size: 10px;
    color: #ecf0f1;
    font-weight: 600;
    background: rgba(52, 152, 219, 0.2);
    padding: 3px 6px;
    border-radius: 3px;
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.detection-report {
    border: 1px solid #4a5568;
    border-radius: 8px;
    padding: 12px;
    background: rgba(52, 73, 94, 0.3);
    max-height: 200px;
    overflow-y: auto;
}

.text-muted {
    color: #7f8c8d !important;
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .info-panel {
        height: auto;
        max-height: 300px;
    }
    
    .controls {
        bottom: 10px;
        right: 10px;
        width: 180px;
        padding: 10px 12px;
    }
    
    .slider-container {
        margin: 10px 0;
    }
    
    .slider-container label {
        width: 45px;
        font-size: 11px;
    }
    
    .slider-container span {
        min-width: 45px;
        font-size: 11px;
        padding: 3px 6px;
    }
}

@media (max-width: 480px) {
    .controls {
        bottom: 5px;
        right: 5px;
        left: auto;
        width: 160px;
        padding: 8px 10px;
    }
    
    .slider-container {
        margin: 8px 0;
        gap: 8px;
    }
    
    .slider-container label {
        width: 40px;
        font-size: 10px;
    }
    
    .slider-container span {
        min-width: 40px;
        font-size: 10px;
        padding: 2px 4px;
    }
    
    .slider-container input[type="range"]::-webkit-slider-thumb {
        width: 18px;
        height: 18px;
    }
    
    .slider-container input[type="range"]::-moz-range-thumb {
        width: 18px;
        height: 18px;
    }
    
    .viewer-container {
        height: calc(100vh - 200px);
    }
    
    .toolbar {
        padding: 8px 10px;
    }
    
    .toolbar .btn {
        padding: 6px 8px;
        font-size: 12px;
        margin-right: 4px;
    }
    
    .image-info, .controls {
        font-size: 10px;
        padding: 8px 10px;
    }
    
    .controls {
        min-width: 200px;
    }
}

/* 滚动条样式 */
.info-panel::-webkit-scrollbar,
.series-list::-webkit-scrollbar,
.detection-results::-webkit-scrollbar,
.detection-report::-webkit-scrollbar {
    width: 6px;
}

.info-panel::-webkit-scrollbar-track,
.series-list::-webkit-scrollbar-track,
.detection-results::-webkit-scrollbar-track,
.detection-report::-webkit-scrollbar-track {
    background: #2c3e50;
}

.info-panel::-webkit-scrollbar-thumb,
.series-list::-webkit-scrollbar-thumb,
.detection-results::-webkit-scrollbar-thumb,
.detection-report::-webkit-scrollbar-thumb {
    background: #4a5568;
    border-radius: 3px;
}

.info-panel::-webkit-scrollbar-thumb:hover,
.series-list::-webkit-scrollbar-thumb:hover,
.detection-results::-webkit-scrollbar-thumb:hover,
.detection-report::-webkit-scrollbar-thumb:hover {
    background: #5a6578;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.series-item, .detection-item {
    animation: fadeIn 0.3s ease;
}

/* 全屏模式样式 */
.viewer-container:fullscreen {
    height: 100vh;
}

.viewer-container:fullscreen .image-info,
.viewer-container:fullscreen .controls {
    background: rgba(0, 0, 0, 0.9);
}

/* 错误状态样式 */
.error-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #e74c3c;
    background: rgba(0, 0, 0, 0.8);
    padding: 20px 30px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e74c3c;
}

.error-message i {
    margin-right: 10px;
    font-size: 20px;
}

/* 成功状态样式 */
.success-message {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(39, 174, 96, 0.9);
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

/* 工具提示样式 */
.tooltip {
    position: relative;
}

.tooltip:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 5px 8px;
    border-radius: 3px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 5px;
}

.tooltip:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
}