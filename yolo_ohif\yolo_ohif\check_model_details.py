#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型详细信息检查脚本
用于诊断YOLO模型的训练状态和配置问题
"""

import os
import sys
import torch
from ultralytics import YOLO
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config

def check_model_details():
    """检查模型详细信息"""
    print("="*60)
    print("YOLO模型详细信息检查")
    print("="*60)
    
    # 模型路径
    model_dir = Config.YOLO.MODEL_PATH
    model_name = Config.YOLO.DEFAULT_MODEL
    model_path = os.path.join(model_dir, model_name)
    
    print(f"\n模型配置:")
    print(f"  模型目录: {model_dir}")
    print(f"  模型文件: {model_name}")
    print(f"  完整路径: {model_path}")
    print(f"  置信度阈值: {Config.YOLO.CONFIDENCE_THRESHOLD}")
    print(f"  IOU阈值: {Config.YOLO.IOU_THRESHOLD}")
    print(f"  设备: {Config.YOLO.DEVICE}")
    
    # 检查文件是否存在
    if not os.path.exists(model_path):
        print(f"\n❌ 错误: 模型文件不存在: {model_path}")
        return
    
    file_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
    print(f"  文件大小: {file_size:.2f} MB")
    
    try:
        # 加载模型
        print(f"\n正在加载模型...")
        model = YOLO(model_path)
        
        # 基本信息
        print(f"\n✅ 模型加载成功")
        print(f"\n模型基本信息:")
        
        # 检查模型属性
        if hasattr(model, 'model'):
            print(f"  模型类型: {type(model.model).__name__}")
            
        if hasattr(model, 'task'):
            print(f"  任务类型: {model.task}")
            
        # 检查类别信息
        if hasattr(model, 'names'):
            class_names = model.names
            print(f"  类别数量: {len(class_names)}")
            print(f"  类别名称: {class_names}")
        else:
            print(f"  ⚠️ 警告: 无法获取类别信息")
            
        # 检查模型参数
        if hasattr(model.model, 'parameters'):
            total_params = sum(p.numel() for p in model.model.parameters())
            trainable_params = sum(p.numel() for p in model.model.parameters() if p.requires_grad)
            print(f"  总参数数: {total_params:,}")
            print(f"  可训练参数: {trainable_params:,}")
            
        # 检查输入尺寸
        if hasattr(model.model, 'yaml'):
            yaml_info = model.model.yaml
            if 'imgsz' in yaml_info:
                print(f"  输入图像尺寸: {yaml_info['imgsz']}")
                
        # 尝试获取训练信息
        try:
            # 检查是否有训练历史
            if hasattr(model, 'trainer') and model.trainer:
                print(f"\n训练信息:")
                if hasattr(model.trainer, 'epochs'):
                    print(f"  训练轮数: {model.trainer.epochs}")
                if hasattr(model.trainer, 'best_fitness'):
                    print(f"  最佳适应度: {model.trainer.best_fitness}")
        except:
            print(f"  ⚠️ 无法获取训练信息")
            
        # 测试模型推理
        print(f"\n测试模型推理能力...")
        
        # 创建测试图像
        import numpy as np
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # 进行推理
        results = model(test_image, conf=Config.YOLO.CONFIDENCE_THRESHOLD, verbose=False)
        
        if results:
            result = results[0]
            detections = result.boxes
            
            if detections is not None and len(detections) > 0:
                print(f"  ✅ 模型推理正常，检测到 {len(detections)} 个目标")
                for i, detection in enumerate(detections):
                    conf = detection.conf.item()
                    cls = int(detection.cls.item())
                    class_name = model.names.get(cls, f'class_{cls}')
                    print(f"    目标 {i+1}: {class_name} (置信度: {conf:.3f})")
            else:
                print(f"  ⚠️ 模型推理正常，但未检测到目标（随机图像）")
        else:
            print(f"  ❌ 模型推理失败")
            
    except Exception as e:
        print(f"\n❌ 模型加载失败: {str(e)}")
        print(f"\n可能的原因:")
        print(f"  1. 模型文件损坏")
        print(f"  2. 模型版本不兼容")
        print(f"  3. 缺少必要的依赖")
        print(f"  4. 模型未正确训练")
        
    # 检查其他可用模型
    print(f"\n检查模型目录中的其他文件:")
    if os.path.exists(model_dir):
        for file in os.listdir(model_dir):
            if file.endswith('.pt'):
                file_path = os.path.join(model_dir, file)
                file_size = os.path.getsize(file_path) / (1024 * 1024)
                status = "✅ 当前使用" if file == model_name else "⭕ 可选"
                print(f"  {status} {file} ({file_size:.2f} MB)")
    
    print(f"\n建议:")
    print(f"  1. 如果模型文件很小(<10MB)，可能训练不充分")
    print(f"  2. 尝试降低置信度阈值到0.01")
    print(f"  3. 检查训练数据是否正确标注")
    print(f"  4. 考虑使用预训练模型进行测试")
    
if __name__ == "__main__":
    check_model_details()