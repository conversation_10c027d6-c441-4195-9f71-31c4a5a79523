{"Name": "YOLO-OHIF Orthanc Server", "StorageDirectory": "/var/lib/orthanc/db", "IndexDirectory": "/var/lib/orthanc/db", "ServerIndex": {"MaximumPatientCount": 50000, "MaximumStudyCount": 100000, "MaximumSeriesCount": 500000, "MaximumInstanceCount": 10000000}, "Plugins": ["/usr/share/orthanc/plugins"], "HttpPort": 8042, "HttpServerEnabled": true, "HttpsPort": 8043, "HttpsServerEnabled": false, "RemoteAccessAllowed": true, "AuthenticationEnabled": false, "DicomServerEnabled": true, "DicomAet": "ORTHANC", "DicomPort": 4242, "DicomModalities": {}, "OrthancPeers": {}, "DicomWeb": {"Enable": true, "Root": "/dicom-web/", "EnableWado": true, "WadoRoot": "/wado", "Ssl": false, "StudiesMetadata": "MainDicomTags", "SeriesMetadata": "MainDicomTags"}, "CaseSensitivePN": false, "UnknownSopClassAccepted": false, "CallingAet": "ORTHANC", "DicomScpTimeout": 30, "DefaultEncoding": "UTF-8", "DeflatedTransferSyntaxAccepted": true, "JpegTransferSyntaxAccepted": true, "Jpeg2000TransferSyntaxAccepted": true, "JpegLosslessTransferSyntaxAccepted": true, "JpipTransferSyntaxAccepted": true, "RleTransferSyntaxAccepted": true, "TranscodeDicomProtocol": true, "OverwriteInstances": false, "StoreMD5ForAttachments": true, "LimitFindResults": 0, "LimitFindInstances": 0, "LogExportedResources": false, "KeepAlive": true, "TcpNoDelay": true, "HttpThreadsCount": 50, "StoreDicom": true, "StoreCompressedSpaceC": true, "MaximumStorageSize": 0, "MaximumPatientCount": 0, "StrictAetComparison": false, "StorageCompression": true, "PatientsAdditionalMetadata": ["SpecificCharacterSet"], "StudiesAdditionalMetadata": ["SpecificCharacterSet"], "SeriesAdditionalMetadata": ["SpecificCharacterSet"], "InstancesAdditionalMetadata": ["SpecificCharacterSet"], "SynchronousCMove": true, "JobsHistorySize": 10, "ConcurrentJobs": 10, "DicomAssociationCloseDelay": 5, "QueryRetrieveSize": 100, "LogLevel": "Info", "LogDirectory": "/var/log/orthanc", "Locale": "", "HttpProxy": "", "HttpTimeout": 60, "HttpRequestTimeout": 30, "SaveJobs": true, "MetricsEnabled": true, "AllowedOrigins": ["*"], "RegisteredUsers": {"admin": "admin"}}