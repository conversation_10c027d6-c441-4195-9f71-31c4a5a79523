"""自定义异常类

定义扩展中使用的异常类型，提供更好的错误处理和调试信息
"""

from typing import Optional, Dict, Any, List


class ExtensionError(Exception):
    """扩展基础异常类"""
    
    def __init__(self, 
                 message: str, 
                 error_code: Optional[str] = None,
                 details: Optional[Dict[str, Any]] = None,
                 cause: Optional[Exception] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.cause = cause
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error_type': self.__class__.__name__,
            'message': self.message,
            'error_code': self.error_code,
            'details': self.details,
            'cause': str(self.cause) if self.cause else None
        }
    
    def __str__(self) -> str:
        base_msg = f"{self.__class__.__name__}: {self.message}"
        if self.error_code:
            base_msg += f" (Code: {self.error_code})"
        if self.details:
            base_msg += f" Details: {self.details}"
        if self.cause:
            base_msg += f" Caused by: {self.cause}"
        return base_msg


class ConfigurationError(ExtensionError):
    """配置错误异常"""
    
    def __init__(self, 
                 message: str, 
                 config_field: Optional[str] = None,
                 expected_type: Optional[str] = None,
                 actual_value: Optional[Any] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.config_field = config_field
        self.expected_type = expected_type
        self.actual_value = actual_value
        
        if config_field:
            self.details.update({
                'config_field': config_field,
                'expected_type': expected_type,
                'actual_value': str(actual_value)
            })


class ValidationError(ExtensionError):
    """验证错误异常"""
    
    def __init__(self, 
                 message: str, 
                 validation_errors: Optional[List[str]] = None,
                 field_name: Optional[str] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.validation_errors = validation_errors or []
        self.field_name = field_name
        
        self.details.update({
            'validation_errors': self.validation_errors,
            'field_name': field_name
        })


class ModelNotFoundError(ExtensionError):
    """模型未找到异常"""
    
    def __init__(self, 
                 model_id: str, 
                 available_models: Optional[List[str]] = None,
                 **kwargs):
        message = f"Model not found: {model_id}"
        super().__init__(message, **kwargs)
        self.model_id = model_id
        self.available_models = available_models or []
        
        self.details.update({
            'model_id': model_id,
            'available_models': self.available_models
        })


class ModelLoadError(ExtensionError):
    """模型加载错误异常"""
    
    def __init__(self, 
                 model_id: str, 
                 load_path: Optional[str] = None,
                 **kwargs):
        message = f"Failed to load model: {model_id}"
        super().__init__(message, **kwargs)
        self.model_id = model_id
        self.load_path = load_path
        
        self.details.update({
            'model_id': model_id,
            'load_path': load_path
        })


class PredictionError(ExtensionError):
    """预测错误异常"""
    
    def __init__(self, 
                 message: str, 
                 model_id: Optional[str] = None,
                 input_shape: Optional[tuple] = None,
                 prediction_stage: Optional[str] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.model_id = model_id
        self.input_shape = input_shape
        self.prediction_stage = prediction_stage
        
        self.details.update({
            'model_id': model_id,
            'input_shape': str(input_shape) if input_shape else None,
            'prediction_stage': prediction_stage
        })


class APIError(ExtensionError):
    """API错误异常"""
    
    def __init__(self, 
                 message: str, 
                 status_code: Optional[int] = None,
                 endpoint: Optional[str] = None,
                 response_data: Optional[Dict] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.status_code = status_code
        self.endpoint = endpoint
        self.response_data = response_data
        
        self.details.update({
            'status_code': status_code,
            'endpoint': endpoint,
            'response_data': response_data
        })


class NetworkError(ExtensionError):
    """网络错误异常"""
    
    def __init__(self, 
                 message: str, 
                 url: Optional[str] = None,
                 timeout: Optional[float] = None,
                 retry_count: Optional[int] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.url = url
        self.timeout = timeout
        self.retry_count = retry_count
        
        self.details.update({
            'url': url,
            'timeout': timeout,
            'retry_count': retry_count
        })


class UIError(ExtensionError):
    """UI错误异常"""
    
    def __init__(self, 
                 message: str, 
                 component_name: Optional[str] = None,
                 viewer_state: Optional[str] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.component_name = component_name
        self.viewer_state = viewer_state
        
        self.details.update({
            'component_name': component_name,
            'viewer_state': viewer_state
        })


class CacheError(ExtensionError):
    """缓存错误异常"""
    
    def __init__(self, 
                 message: str, 
                 cache_key: Optional[str] = None,
                 operation: Optional[str] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.cache_key = cache_key
        self.operation = operation
        
        self.details.update({
            'cache_key': cache_key,
            'operation': operation
        })


class ResourceError(ExtensionError):
    """资源错误异常"""
    
    def __init__(self, 
                 message: str, 
                 resource_type: Optional[str] = None,
                 resource_path: Optional[str] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.resource_type = resource_type
        self.resource_path = resource_path
        
        self.details.update({
            'resource_type': resource_type,
            'resource_path': resource_path
        })


class TimeoutError(ExtensionError):
    """超时错误异常"""
    
    def __init__(self, 
                 message: str, 
                 timeout_duration: Optional[float] = None,
                 operation: Optional[str] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.timeout_duration = timeout_duration
        self.operation = operation
        
        self.details.update({
            'timeout_duration': timeout_duration,
            'operation': operation
        })


class SecurityError(ExtensionError):
    """安全错误异常"""
    
    def __init__(self, 
                 message: str, 
                 security_check: Optional[str] = None,
                 user_id: Optional[str] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.security_check = security_check
        self.user_id = user_id
        
        self.details.update({
            'security_check': security_check,
            'user_id': user_id
        })


# 异常工厂函数
def create_model_not_found_error(model_id: str, available_models: List[str]) -> ModelNotFoundError:
    """创建模型未找到异常"""
    return ModelNotFoundError(
        model_id=model_id,
        available_models=available_models,
        error_code="MODEL_NOT_FOUND"
    )


def create_validation_error(field_name: str, errors: List[str]) -> ValidationError:
    """创建验证错误异常"""
    message = f"Validation failed for field '{field_name}': {', '.join(errors)}"
    return ValidationError(
        message=message,
        field_name=field_name,
        validation_errors=errors,
        error_code="VALIDATION_FAILED"
    )


def create_api_error(endpoint: str, status_code: int, response_data: Dict) -> APIError:
    """创建API错误异常"""
    message = f"API request failed: {endpoint} (Status: {status_code})"
    return APIError(
        message=message,
        endpoint=endpoint,
        status_code=status_code,
        response_data=response_data,
        error_code="API_REQUEST_FAILED"
    )


class ContainerError(ExtensionError):
    """服务容器错误异常"""
    
    def __init__(self, 
                 message: str,
                 service_type: Optional[str] = None,
                 operation: Optional[str] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.service_type = service_type
        self.operation = operation
    
    def to_dict(self) -> Dict[str, Any]:
        result = super().to_dict()
        result.update({
            'service_type': self.service_type,
            'operation': self.operation
        })
        return result


def create_prediction_error(model_id: str, stage: str, cause: Exception) -> PredictionError:
    """创建预测错误异常"""
    message = f"Prediction failed at {stage} stage for model {model_id}"
    return PredictionError(
        message=message,
        model_id=model_id,
        prediction_stage=stage,
        cause=cause,
        error_code="PREDICTION_FAILED"
    )