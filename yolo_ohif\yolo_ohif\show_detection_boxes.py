#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测框可视化脚本
专门用于显示检测结果和检测框
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib import rcParams
import random
from pathlib import Path
import glob

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

# 导入检测服务
from services.detection_service import DetectionService
from config import Config

# 配置路径
MODEL_PATH = "E:/Trae/yolo_ohif/yolo11x_training_output/training_results/yolo11x_from_scratch_20250719_135134/weights/best.pt"
TEST_IMAGE_DIR = "E:/Trae/yolo_ohif/yolo11x_training_output/yolo_dataset/yolo_dataset/images/test"

def show_detection_boxes(num_images=5, confidence_threshold=0.1):
    """
    显示检测框
    
    Args:
        num_images: 要处理的图像数量
        confidence_threshold: 置信度阈值
    """
    print("=== 检测框可视化 ===")
    print(f"模型路径: {MODEL_PATH}")
    print(f"测试图像目录: {TEST_IMAGE_DIR}")
    print(f"置信度阈值: {confidence_threshold}")
    print(f"处理图像数量: {num_images}")
    
    # 初始化检测服务
    try:
        detection_service = DetectionService(
            model_path=MODEL_PATH,
            confidence_threshold=confidence_threshold,
            iou_threshold=Config.YOLO.IOU_THRESHOLD
        )
        print("✅ 检测服务初始化成功")
    except Exception as e:
        print(f"❌ 检测服务初始化失败: {e}")
        return
    
    # 获取测试图像
    image_files = []
    for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
        image_files.extend(glob.glob(os.path.join(TEST_IMAGE_DIR, f'*{ext}')))
        image_files.extend(glob.glob(os.path.join(TEST_IMAGE_DIR, f'*{ext.upper()}')))
    
    if not image_files:
        print("❌ 未找到测试图像")
        return
    
    print(f"📁 找到 {len(image_files)} 张图像")
    
    # 随机选择图像
    selected_images = random.sample(image_files, min(num_images, len(image_files)))
    
    # 统计信息
    total_detections = 0
    images_with_detections = 0
    
    # 处理每张图像
    for i, image_path in enumerate(selected_images):
        print(f"\n{'='*60}")
        print(f"🖼️  处理第 {i+1}/{len(selected_images)} 张图像")
        print(f"📄 文件: {os.path.basename(image_path)}")
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            continue
        
        # 转换为RGB格式
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 进行检测
        try:
            detections = detection_service._detect_image(image)
            print(f"🔍 检测到 {len(detections)} 个目标")
            
            # 过滤低置信度检测
            filtered_detections = [d for d in detections if d.get('confidence', 0) >= confidence_threshold]
            print(f"✅ 置信度 >= {confidence_threshold} 的检测: {len(filtered_detections)} 个")
            
            if filtered_detections:
                total_detections += len(filtered_detections)
                images_with_detections += 1
                
                # 创建可视化
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
                
                # 显示原图
                ax1.imshow(image_rgb)
                ax1.set_title(f'原图\n{os.path.basename(image_path)}', fontsize=12)
                ax1.axis('off')
                
                # 显示检测结果
                ax2.imshow(image_rgb)
                
                # 绘制检测框
                colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'cyan', 'magenta']
                
                for j, det in enumerate(filtered_detections):
                    x1, y1, x2, y2 = det['x1'], det['y1'], det['x2'], det['y2']
                    width = x2 - x1
                    height = y2 - y1
                    
                    # 选择颜色
                    color = colors[j % len(colors)]
                    
                    # 绘制边界框
                    rect = patches.Rectangle((x1, y1), width, height, 
                                           linewidth=3, edgecolor=color, facecolor='none')
                    ax2.add_patch(rect)
                    
                    # 添加标签
                    class_name = det.get('class', 'unknown')
                    confidence = det.get('confidence', 0)
                    label = f"{class_name}: {confidence:.2f}"
                    
                    # 绘制标签背景
                    ax2.text(x1, y1-10, label, color='white', fontsize=10, weight='bold',
                            bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.8))
                    
                    # 打印检测详情
                    print(f"  🎯 检测 {j+1}: 类别={class_name}, 置信度={confidence:.3f}, "
                          f"位置=({x1:.0f}, {y1:.0f}, {x2:.0f}, {y2:.0f}), "
                          f"大小={width:.0f}x{height:.0f}")
                
                ax2.set_title(f'检测结果 (置信度 >= {confidence_threshold})\n'
                             f'检测到 {len(filtered_detections)} 个目标', fontsize=12)
                ax2.axis('off')
                
                plt.tight_layout()
                plt.show()
                
            else:
                print("❌ 未检测到符合置信度要求的目标")
                
        except Exception as e:
            print(f"❌ 检测过程中出错: {e}")
            continue
    
    # 打印总结
    print(f"\n{'='*60}")
    print("📊 检测总结")
    print(f"📁 处理图像数量: {len(selected_images)}")
    print(f"🎯 有检测结果的图像: {images_with_detections}")
    print(f"🔍 总检测数量: {total_detections}")
    if len(selected_images) > 0:
        print(f"📈 平均每张图像检测数: {total_detections/len(selected_images):.2f}")
        print(f"📊 检测成功率: {images_with_detections/len(selected_images)*100:.1f}%")
    
    if total_detections > 0:
        print("\n✅ 检测框已显示在可视化图像中")
        print("🎨 不同颜色的框代表不同的检测目标")
        print("🏷️  每个框上的标签显示类别名称和置信度")
    else:
        print("\n❌ 未检测到任何目标，可能需要：")
        print("   1. 降低置信度阈值")
        print("   2. 检查模型是否正确加载")
        print("   3. 确认测试图像包含目标对象")

def test_different_confidence_levels():
    """
    测试不同置信度阈值下的检测框显示
    """
    print("\n=== 测试不同置信度阈值 ===")
    
    confidence_levels = [0.1, 0.2, 0.3, 0.4, 0.5]
    
    for conf in confidence_levels:
        print(f"\n🔍 测试置信度阈值: {conf}")
        show_detection_boxes(num_images=2, confidence_threshold=conf)
        print(f"\n{'='*40}")

def main():
    """
    主函数
    """
    print("🚀 启动检测框可视化程序")
    
    # 基本检测框显示
    show_detection_boxes(num_images=5, confidence_threshold=0.1)
    
    # 询问是否测试不同置信度
    print("\n❓ 是否要测试不同置信度阈值？(输入 'y' 继续，其他键跳过)")
    user_input = input().strip().lower()
    
    if user_input == 'y':
        test_different_confidence_levels()
    
    print("\n✅ 检测框可视化完成")

if __name__ == "__main__":
    main()