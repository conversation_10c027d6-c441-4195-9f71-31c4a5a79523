namespace MedicalImageAnalysis.Core.Models;

/// <summary>
/// 批量处理结果
/// </summary>
public class BatchProcessResult
{
    /// <summary>
    /// 是否处理成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 总文件数量
    /// </summary>
    public int TotalFiles { get; set; }

    /// <summary>
    /// 成功处理的文件数量
    /// </summary>
    public int SuccessfulFiles { get; set; }

    /// <summary>
    /// 失败的文件数量
    /// </summary>
    public int FailedFiles { get; set; }

    /// <summary>
    /// 跳过的文件数量
    /// </summary>
    public int SkippedFiles { get; set; }

    /// <summary>
    /// 处理开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 处理结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 总处理时间
    /// </summary>
    public TimeSpan TotalProcessingTime => EndTime - StartTime;

    /// <summary>
    /// 平均处理时间（每个文件）
    /// </summary>
    public TimeSpan AverageProcessingTime => 
        TotalFiles > 0 ? TimeSpan.FromMilliseconds(TotalProcessingTime.TotalMilliseconds / TotalFiles) : TimeSpan.Zero;

    /// <summary>
    /// 处理结果详情
    /// </summary>
    public List<BatchProcessFileResult> FileResults { get; set; } = new();

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 警告信息列表
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 处理统计信息
    /// </summary>
    public Dictionary<string, object> Statistics { get; set; } = new();

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalFiles > 0 ? (double)SuccessfulFiles / TotalFiles * 100 : 0;

    /// <summary>
    /// 总处理数量（别名）
    /// </summary>
    public int TotalProcessed
    {
        get => TotalFiles;
        set => TotalFiles = value;
    }

    /// <summary>
    /// 成功数量（别名）
    /// </summary>
    public int SuccessCount
    {
        get => SuccessfulFiles;
        set => SuccessfulFiles = value;
    }

    /// <summary>
    /// 失败数量（别名）
    /// </summary>
    public int FailureCount
    {
        get => FailedFiles;
        set => FailedFiles = value;
    }

    /// <summary>
    /// 成功文件列表（用于兼容性）
    /// </summary>
    public List<string> SuccessfulFilesList { get; set; } = new();

    /// <summary>
    /// 失败文件字典（用于兼容性）
    /// </summary>
    public Dictionary<string, string> FailedFilesDictionary { get; set; } = new();

    /// <summary>
    /// 添加文件处理结果
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="isSuccess">是否成功</param>
    /// <param name="errorMessage">错误信息</param>
    /// <param name="processingTime">处理时间</param>
    public void AddFileResult(string filePath, bool isSuccess, string? errorMessage = null, TimeSpan? processingTime = null)
    {
        FileResults.Add(new BatchProcessFileResult
        {
            FilePath = filePath,
            IsSuccess = isSuccess,
            ErrorMessage = errorMessage,
            ProcessingTime = processingTime ?? TimeSpan.Zero,
            ProcessedAt = DateTime.UtcNow
        });

        if (isSuccess)
            SuccessfulFiles++;
        else
            FailedFiles++;
    }
}

/// <summary>
/// 批量处理文件结果
/// </summary>
public class BatchProcessFileResult
{
    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 是否处理成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 处理时间
    /// </summary>
    public TimeSpan ProcessingTime { get; set; }

    /// <summary>
    /// 处理时间戳
    /// </summary>
    public DateTime ProcessedAt { get; set; }

    /// <summary>
    /// 输出文件路径
    /// </summary>
    public string? OutputPath { get; set; }

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 处理的数据量（字节）
    /// </summary>
    public long ProcessedDataSize { get; set; }

    /// <summary>
    /// 额外信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}
