namespace MedicalImageAnalysis.Core.Models;

/// <summary>
/// 纹理分析结果
/// </summary>
public class TextureAnalysisResult
{
    /// <summary>
    /// 对比度
    /// </summary>
    public double Contrast { get; set; }

    /// <summary>
    /// 相关性
    /// </summary>
    public double Correlation { get; set; }

    /// <summary>
    /// 能量
    /// </summary>
    public double Energy { get; set; }

    /// <summary>
    /// 同质性
    /// </summary>
    public double Homogeneity { get; set; }

    /// <summary>
    /// 熵
    /// </summary>
    public double Entropy { get; set; }

    /// <summary>
    /// 方差
    /// </summary>
    public double Variance { get; set; }

    /// <summary>
    /// 均值
    /// </summary>
    public double Mean { get; set; }

    /// <summary>
    /// 标准差
    /// </summary>
    public double StandardDeviation { get; set; }

    /// <summary>
    /// 偏度
    /// </summary>
    public double Skewness { get; set; }

    /// <summary>
    /// 峰度
    /// </summary>
    public double Kurtosis { get; set; }

    /// <summary>
    /// 纹理特征向量
    /// </summary>
    public double[] FeatureVector { get; set; } = Array.Empty<double>();

    /// <summary>
    /// 分析时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 分析参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// GLCM特征
    /// </summary>
    public GLCMFeatures? GLCMFeatures { get; set; }

    /// <summary>
    /// 多方向GLCM特征
    /// </summary>
    public MultiDirectionalGLCMFeatures? MultiDirectionalGLCMFeatures { get; set; }

    /// <summary>
    /// LBP特征
    /// </summary>
    public LBPFeatures? LBPFeatures { get; set; }

    /// <summary>
    /// 旋转不变LBP特征
    /// </summary>
    public RotationInvariantLBPFeatures? RotationInvariantLBPFeatures { get; set; }

    /// <summary>
    /// Gabor特征
    /// </summary>
    public GaborFeatures? GaborFeatures { get; set; }
}

/// <summary>
/// GLCM特征
/// </summary>
public class GLCMFeatures
{
    public double Contrast { get; set; }
    public double Correlation { get; set; }
    public double Energy { get; set; }
    public double Homogeneity { get; set; }
    public double Entropy { get; set; }
}

/// <summary>
/// 多方向GLCM特征
/// </summary>
public class MultiDirectionalGLCMFeatures
{
    public List<GLCMFeatures> DirectionalFeatures { get; set; } = new();
    public double MeanContrast { get; set; }
    public double MeanCorrelation { get; set; }
    public double MeanEnergy { get; set; }
    public double MeanHomogeneity { get; set; }
    public double MeanEntropy { get; set; }
    public double ContrastVariance { get; set; }
    public double CorrelationVariance { get; set; }
    public double EnergyVariance { get; set; }
    public double HomogeneityVariance { get; set; }
}

/// <summary>
/// LBP特征
/// </summary>
public class LBPFeatures
{
    public double[] Histogram { get; set; } = Array.Empty<double>();
    public double Uniformity { get; set; }
    public double Entropy { get; set; }
    public double Energy { get; set; }
    public double Contrast { get; set; }
    public double Variance { get; set; }
}

/// <summary>
/// 旋转不变LBP特征
/// </summary>
public class RotationInvariantLBPFeatures
{
    public double[] Histogram { get; set; } = Array.Empty<double>();
    public int UniformPatternCount { get; set; }
    public double NonUniformRatio { get; set; }
    public double Entropy { get; set; }
    public double Energy { get; set; }
}

/// <summary>
/// Gabor特征
/// </summary>
public class GaborFeatures
{
    public List<GaborResponse> Responses { get; set; } = new();
    public double MeanEnergy { get; set; }
    public double MeanEntropy { get; set; }
    public double EnergyVariance { get; set; }
    public double EntropyVariance { get; set; }
}

/// <summary>
/// Gabor响应
/// </summary>
public class GaborResponse
{
    public double Frequency { get; set; }
    public double Orientation { get; set; }
    public double Mean { get; set; }
    public double StandardDeviation { get; set; }
    public double Energy { get; set; }
    public double Entropy { get; set; }
}

/// <summary>
/// 纹理特征类型
/// </summary>
public enum TextureFeatures
{
    /// <summary>
    /// 灰度共生矩阵
    /// </summary>
    GLCM = 1,

    /// <summary>
    /// 局部二值模式
    /// </summary>
    LBP = 2,

    /// <summary>
    /// Gabor滤波器
    /// </summary>
    Gabor = 4,

    /// <summary>
    /// 小波变换
    /// </summary>
    Wavelet = 8,

    /// <summary>
    /// 分形维数
    /// </summary>
    Fractal = 16,

    /// <summary>
    /// 所有特征
    /// </summary>
    All = GLCM | LBP | Gabor | Wavelet | Fractal
}

/// <summary>
/// 纹理分析配置
/// </summary>
public class TextureAnalysisConfig
{
    /// <summary>
    /// 要计算的纹理特征
    /// </summary>
    public TextureFeatures Features { get; set; } = TextureFeatures.All;

    /// <summary>
    /// GLCM参数
    /// </summary>
    public GLCMConfig GLCMConfig { get; set; } = new();

    /// <summary>
    /// LBP参数
    /// </summary>
    public LBPConfig LBPConfig { get; set; } = new();

    /// <summary>
    /// Gabor参数
    /// </summary>
    public GaborConfig GaborConfig { get; set; } = new();

    /// <summary>
    /// 是否启用多尺度分析
    /// </summary>
    public bool EnableMultiScale { get; set; } = false;

    /// <summary>
    /// 尺度因子
    /// </summary>
    public double[] ScaleFactors { get; set; } = { 1.0, 0.5, 0.25 };
}

/// <summary>
/// GLCM配置
/// </summary>
public class GLCMConfig
{
    /// <summary>
    /// 距离
    /// </summary>
    public int Distance { get; set; } = 1;

    /// <summary>
    /// 角度（度）
    /// </summary>
    public double[] Angles { get; set; } = { 0, 45, 90, 135 };

    /// <summary>
    /// 灰度级数
    /// </summary>
    public int GrayLevels { get; set; } = 256;

    /// <summary>
    /// 是否对称化
    /// </summary>
    public bool Symmetric { get; set; } = true;
}

/// <summary>
/// LBP配置
/// </summary>
public class LBPConfig
{
    /// <summary>
    /// 半径
    /// </summary>
    public int Radius { get; set; } = 1;

    /// <summary>
    /// 邻居数量
    /// </summary>
    public int Neighbors { get; set; } = 8;

    /// <summary>
    /// 是否使用旋转不变
    /// </summary>
    public bool RotationInvariant { get; set; } = true;

    /// <summary>
    /// 是否使用均匀模式
    /// </summary>
    public bool UniformPattern { get; set; } = true;
}

/// <summary>
/// Gabor配置
/// </summary>
public class GaborConfig
{
    /// <summary>
    /// 频率
    /// </summary>
    public double[] Frequencies { get; set; } = { 0.1, 0.2, 0.3, 0.4 };

    /// <summary>
    /// 方向（弧度）
    /// </summary>
    public double[] Orientations { get; set; } = { 0, Math.PI / 4, Math.PI / 2, 3 * Math.PI / 4 };

    /// <summary>
    /// 标准差
    /// </summary>
    public double Sigma { get; set; } = 2.0;

    /// <summary>
    /// 长宽比
    /// </summary>
    public double AspectRatio { get; set; } = 1.0;
}

#region 多尺度分析模型

/// <summary>
/// 小波分析结果
/// </summary>
public class WaveletAnalysisResult
{
    public WaveletConfig Config { get; set; } = new();
    public DateTime AnalysisTime { get; set; }
    public List<WaveletDecomposition> Decompositions { get; set; } = new();
    public Dictionary<int, WaveletFeatures> ScaleFeatures { get; set; } = new();
    public MultiScaleFeatures MultiScaleFeatures { get; set; } = new();
    public EnergyDistribution EnergyDistribution { get; set; } = new();
    public MultiScaleTextureFeatures TextureFeatures { get; set; } = new();
}

/// <summary>
/// 小波配置
/// </summary>
public class WaveletConfig
{
    public WaveletType WaveletType { get; set; } = WaveletType.Daubechies4;
    public int MaxLevels { get; set; } = 4;
    public bool CalculateEnergy { get; set; } = true;
    public bool ExtractFeatures { get; set; } = true;
}

/// <summary>
/// 小波类型
/// </summary>
public enum WaveletType
{
    Haar,
    Daubechies4,
    Daubechies8,
    Biorthogonal,
    Coiflets,
    Morlet
}

/// <summary>
/// 小波分解
/// </summary>
public class WaveletDecomposition
{
    public int Level { get; set; }
    public float[] ApproximationCoefficients { get; set; } = Array.Empty<float>();
    public float[] HorizontalDetailCoefficients { get; set; } = Array.Empty<float>();
    public float[] VerticalDetailCoefficients { get; set; } = Array.Empty<float>();
    public float[] DiagonalDetailCoefficients { get; set; } = Array.Empty<float>();
    public int Width { get; set; }
    public int Height { get; set; }
}

/// <summary>
/// 小波特征
/// </summary>
public class WaveletFeatures
{
    public double Energy { get; set; }
    public double Entropy { get; set; }
    public double Mean { get; set; }
    public double StandardDeviation { get; set; }
    public double Variance { get; set; }
    public double Skewness { get; set; }
    public double Kurtosis { get; set; }
}

/// <summary>
/// 多尺度特征
/// </summary>
public class MultiScaleFeatures
{
    public double[] EnergyDistribution { get; set; } = Array.Empty<double>();
    public double[] EntropyDistribution { get; set; } = Array.Empty<double>();
    public double TotalEnergy { get; set; }
    public double EnergyConcentration { get; set; }
    public int DominantScale { get; set; }
}

/// <summary>
/// 能量分布
/// </summary>
public class EnergyDistribution
{
    public Dictionary<int, double> ScaleEnergies { get; set; } = new();
    public Dictionary<string, double> SubbandEnergies { get; set; } = new();
    public double TotalEnergy { get; set; }
    public double[] RelativeEnergies { get; set; } = Array.Empty<double>();
}

/// <summary>
/// 多尺度纹理特征
/// </summary>
public class MultiScaleTextureFeatures
{
    public Dictionary<int, TextureAnalysisResult> ScaleTextures { get; set; } = new();
    public double[] TextureComplexity { get; set; } = Array.Empty<double>();
    public double[] TextureRegularity { get; set; } = Array.Empty<double>();
    public double OverallComplexity { get; set; }
}

/// <summary>
/// 小波去噪配置
/// </summary>
public class WaveletDenoiseConfig
{
    public WaveletType WaveletType { get; set; } = WaveletType.Daubechies4;
    public int DecompositionLevels { get; set; } = 4;
    public ThresholdingMethod ThresholdingMethod { get; set; } = ThresholdingMethod.Soft;
    public double ThresholdValue { get; set; } = 0.1;
    public bool AdaptiveThresholding { get; set; } = true;
}

/// <summary>
/// 阈值处理方法
/// </summary>
public enum ThresholdingMethod
{
    Soft,
    Hard,
    Greater,
    Less
}

/// <summary>
/// 拉普拉斯金字塔结果
/// </summary>
public class LaplacianPyramidResult
{
    public PyramidConfig Config { get; set; } = new();
    public DateTime AnalysisTime { get; set; }
    public List<PyramidLevel> GaussianPyramid { get; set; } = new();
    public List<PyramidLevel> LaplacianPyramid { get; set; } = new();
    public Dictionary<int, PyramidLevelFeatures> LevelFeatures { get; set; } = new();
    public List<EdgeMap> MultiScaleEdges { get; set; } = new();
    public Dictionary<int, List<FeaturePoint>> FeaturePoints { get; set; } = new();
}

/// <summary>
/// 金字塔配置
/// </summary>
public class PyramidConfig
{
    public int Levels { get; set; } = 4;
    public double ScaleFactor { get; set; } = 0.5;
    public double SigmaGaussian { get; set; } = 1.0;
    public bool DetectEdges { get; set; } = true;
    public bool DetectFeatures { get; set; } = true;
}

/// <summary>
/// 金字塔层级
/// </summary>
public class PyramidLevel
{
    public int Level { get; set; }
    public float[] Data { get; set; } = Array.Empty<float>();
    public int Width { get; set; }
    public int Height { get; set; }
    public double Scale { get; set; }
}

/// <summary>
/// 金字塔层级特征
/// </summary>
public class PyramidLevelFeatures
{
    public double Mean { get; set; }
    public double StandardDeviation { get; set; }
    public double Energy { get; set; }
    public double Entropy { get; set; }
    public double EdgeDensity { get; set; }
    public int FeaturePointCount { get; set; }
}

/// <summary>
/// 边缘图
/// </summary>
public class EdgeMap
{
    public int Level { get; set; }
    public byte[] EdgeData { get; set; } = Array.Empty<byte>();
    public int Width { get; set; }
    public int Height { get; set; }
    public double EdgeDensity { get; set; }
}

/// <summary>
/// 特征点
/// </summary>
public class FeaturePoint
{
    public double X { get; set; }
    public double Y { get; set; }
    public double Scale { get; set; }
    public double Response { get; set; }
    public double Orientation { get; set; }
    public float[] Descriptor { get; set; } = Array.Empty<float>();
}

/// <summary>
/// 特征匹配结果
/// </summary>
public class FeatureMatchingResult
{
    public List<FeatureMatch> Matches { get; set; } = new();
    public double MatchingQuality { get; set; }
    public GeometricTransform? EstimatedTransform { get; set; }
    public int TotalFeatures1 { get; set; }
    public int TotalFeatures2 { get; set; }
    public double MatchingRatio { get; set; }
}

/// <summary>
/// 特征匹配
/// </summary>
public class FeatureMatch
{
    public FeaturePoint Point1 { get; set; } = new();
    public FeaturePoint Point2 { get; set; } = new();
    public double Distance { get; set; }
    public double Confidence { get; set; }
    public bool IsInlier { get; set; }
}

/// <summary>
/// 特征匹配配置
/// </summary>
public class FeatureMatchingConfig
{
    public double MaxDistance { get; set; } = 0.7;
    public double RatioThreshold { get; set; } = 0.8;
    public int MinMatches { get; set; } = 10;
    public bool UseRANSAC { get; set; } = true;
    public double RANSACThreshold { get; set; } = 3.0;
}

/// <summary>
/// 几何变换
/// </summary>
public class GeometricTransform
{
    public TransformType Type { get; set; }
    public double[,] Matrix { get; set; } = new double[3, 3];
    public double[] Parameters { get; set; } = Array.Empty<double>();
    public double RMSE { get; set; }
    public int InlierCount { get; set; }
}

/// <summary>
/// 变换类型
/// </summary>
public enum TransformType
{
    Translation,
    Rotation,
    Similarity,
    Affine,
    Homography
}

/// <summary>
/// 分形分析结果
/// </summary>
public class FractalAnalysisResult
{
    public FractalConfig Config { get; set; } = new();
    public DateTime AnalysisTime { get; set; }
    public double? BoxCountingDimension { get; set; }
    public double? DifferentialBoxCountingDimension { get; set; }
    public double? BlanketDimension { get; set; }
    public double? VariogramDimension { get; set; }
    public double AverageFractalDimension { get; set; }
    public double FractalDimensionStd { get; set; }
    public MultiScaleFractalFeatures MultiScaleFractalFeatures { get; set; } = new();
}

/// <summary>
/// 分形配置
/// </summary>
public class FractalConfig
{
    public List<FractalMethod> Methods { get; set; } = new() { FractalMethod.BoxCounting };
    public int MinBoxSize { get; set; } = 2;
    public int MaxBoxSize { get; set; } = 64;
    public int BoxSizeSteps { get; set; } = 10;
    public bool LogScale { get; set; } = true;
}

/// <summary>
/// 分形方法
/// </summary>
public enum FractalMethod
{
    BoxCounting,
    DifferentialBoxCounting,
    BlanketMethod,
    Variogram
}

/// <summary>
/// 多尺度分形特征
/// </summary>
public class MultiScaleFractalFeatures
{
    public Dictionary<int, double> ScaleFractalDimensions { get; set; } = new();
    public double[] FractalSpectrum { get; set; } = Array.Empty<double>();
    public double MultifractalDimension { get; set; }
    public double FractalComplexity { get; set; }
    public double SelfSimilarity { get; set; }
}

#endregion
