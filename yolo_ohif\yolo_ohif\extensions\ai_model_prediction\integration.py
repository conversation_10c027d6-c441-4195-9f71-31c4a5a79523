"""AI预测扩展集成模块

提供与Flask应用的集成接口
"""

import logging
from typing import Any, Dict, Optional
from flask import Flask

from .factory import ExtensionFactory
from .refactored_extension import RefactoredAIPredictionExtension
from .core.exceptions import ExtensionError

logger = logging.getLogger(__name__)


def init_ai_extension(app: Flask, config: Optional[Dict[str, Any]] = None) -> Optional[RefactoredAIPredictionExtension]:
    """初始化AI预测扩展
    
    Args:
        app: Flask应用实例
        config: 扩展配置
        
    Returns:
        扩展实例，如果初始化失败则返回None
    """
    try:
        logger.info("开始初始化AI预测扩展")
        
        # 创建扩展实例
        if config:
            extension = ExtensionFactory.create_with_config(config)
        else:
            extension = ExtensionFactory.create_default()
        
        # 初始化扩展
        extension.initialize(config)
        
        # 将扩展绑定到Flask应用
        app.ai_prediction_extension = extension
        
        logger.info("AI预测扩展初始化成功")
        return extension
        
    except ExtensionError as e:
        logger.error(f"AI预测扩展初始化失败: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"AI预测扩展初始化出现未知错误: {str(e)}")
        return None


def get_ai_extension(app: Flask) -> Optional[RefactoredAIPredictionExtension]:
    """获取AI预测扩展实例
    
    Args:
        app: Flask应用实例
        
    Returns:
        扩展实例，如果不存在则返回None
    """
    return getattr(app, 'ai_prediction_extension', None)


def cleanup_ai_extension(app: Flask) -> None:
    """清理AI预测扩展
    
    Args:
        app: Flask应用实例
    """
    try:
        extension = get_ai_extension(app)
        if extension:
            # 执行清理操作
            if hasattr(extension, 'cleanup'):
                extension.cleanup()
            
            # 从应用中移除
            if hasattr(app, 'ai_prediction_extension'):
                delattr(app, 'ai_prediction_extension')
            
            logger.info("AI预测扩展清理完成")
    except Exception as e:
        logger.error(f"AI预测扩展清理失败: {str(e)}")