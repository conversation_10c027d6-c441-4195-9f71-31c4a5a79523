<Window x:Class="MedicalImageAnalysis.Wpf.Windows.BatchAnnotationProgressWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="AI批量标注进度" 
        Height="600" 
        Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="{DynamicResource MaterialDesignPaper}" Padding="24,16">
            <StackPanel>
                <TextBlock Text="AI批量标注进度" 
                         FontSize="20" 
                         FontWeight="Medium"
                         Margin="0,0,0,8"/>
                <TextBlock x:Name="StatusText" 
                         Text="准备开始批量标注..."
                         FontSize="14"
                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>
        </Border>

        <!-- 进度信息 -->
        <materialDesign:Card Grid.Row="1" Margin="16,8,16,8" materialDesign:ElevationAssist.Elevation="Dp2">
            <StackPanel Margin="16">
                <Grid Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="ProgressText" 
                                 Text="0 / 0 文件已处理"
                                 FontSize="16"
                                 FontWeight="Medium"/>
                        <TextBlock x:Name="CurrentFileText" 
                                 Text=""
                                 FontSize="12"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                 Margin="0,4,0,0"/>
                    </StackPanel>
                    
                    <TextBlock Grid.Column="1" 
                             x:Name="PercentageText"
                             Text="0%"
                             FontSize="24"
                             FontWeight="Bold"
                             VerticalAlignment="Center"/>
                </Grid>

                <ProgressBar x:Name="MainProgressBar"
                           Height="8"
                           Minimum="0"
                           Maximum="100"
                           Value="0"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- 处理结果列表 -->
        <materialDesign:Card Grid.Row="2" Margin="16,8,16,8" materialDesign:ElevationAssist.Elevation="Dp2">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <Border Grid.Row="0" Background="{DynamicResource MaterialDesignDivider}" Padding="16,12">
                    <TextBlock Text="处理结果" FontWeight="Medium"/>
                </Border>

                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel x:Name="ResultsPanel" Margin="16"/>
                </ScrollViewer>
            </Grid>
        </materialDesign:Card>

        <!-- 按钮栏 -->
        <Border Grid.Row="3" Background="{DynamicResource MaterialDesignPaper}" Padding="16">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button x:Name="CancelButton"
                      Content="取消"
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Margin="0,0,8,0"
                      Click="CancelButton_Click"/>
                <Button x:Name="CloseButton"
                      Content="关闭"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      IsEnabled="False"
                      Click="CloseButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
