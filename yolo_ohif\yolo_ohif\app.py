from flask import Flask, render_template, request, jsonify, redirect, url_for, send_from_directory
from flask_cors import CORS
from flask_wtf.csrf import CSRFProtect
from werkzeug.utils import secure_filename
import os
import logging
import json
import time
from datetime import datetime

# 导入配置
from config import Config, BASE_DIR

# 导入工具模块
from utils.error_handler import setup_error_handlers, APIError
from utils.config_validator import validate_and_report_config
from utils.performance_monitor import (
    performance_metrics, start_monitoring, timing_decorator, 
    get_performance_report
)

# 导入性能监控工具
try:
    from utils.performance_monitor import timing_decorator, get_performance_report, start_monitoring
except ImportError:
    # 如果导入失败，提供兼容的空实现
    def timing_decorator(func):
        return func
    def get_performance_report():
        return {'error': 'Performance monitoring not available'}
    def start_monitoring():
        pass

# 导入数据库优化工具
try:
    from utils.database_optimizer import initialize_connection_pool, get_connection_pool, close_connection_pool
except ImportError:
    def initialize_connection_pool(*args, **kwargs):
        pass
    def get_connection_pool():
        return None
    def close_connection_pool():
        pass

# 导入配置管理器
try:
    from utils.config_manager import get_config_manager, validate_environment
except ImportError:
    def get_config_manager():
        return None
    def validate_environment():
        return True

# 导入API文档生成器
try:
    from utils.api_documentation import get_documentation_generator, generate_api_documentation
except ImportError:
    def get_documentation_generator():
        return None
    def generate_api_documentation(*args, **kwargs):
        pass

# 导入服务模块
from services.orthanc_service import OrthancService
from services.detection_service import DetectionService
from services.ohif_service import OHIFService
from services.auth_service import AuthService
from services.database_service import DatabaseService

# 导入路由
from routes.web import web_bp
from routes.api import api_bp

# 设置日志
logging.basicConfig(
    level=getattr(logging, Config.LOG.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(Config.LOG.LOG_DIR, 'app.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 导入AI预测扩展
try:
    from extensions.ai_model_prediction.integration import init_ai_extension
except ImportError:
    logger.warning("AI预测扩展导入失败，将跳过AI功能")
    def init_ai_extension(app, config=None):
        return None

# 验证配置
validate_and_report_config(Config)

# 初始化Flask应用
app = Flask(__name__)
app.config.from_object(Config.FLASK)
app.secret_key = Config.FLASK.SECRET_KEY  # 设置密钥用于会话加密
CORS(app)  # 启用CORS支持跨域请求

# 验证环境配置
if not validate_environment():
    logger.warning("环境配置验证失败，请检查配置")

# 设置错误处理器
setup_error_handlers(app)

# 初始化CSRF保护
csrf = CSRFProtect(app)

# 为API蓝图禁用CSRF保护
csrf.exempt(api_bp)

# 注册Blueprint
app.register_blueprint(web_bp, url_prefix='')
app.register_blueprint(api_bp)

# 启动性能监控
start_monitoring()

# 初始化数据库连接池
try:
    db_path = os.path.join(BASE_DIR, 'data', 'app.db')
    initialize_connection_pool(db_path, min_connections=2, max_connections=10)
    logger.info("数据库连接池初始化成功")
except Exception as e:
    logger.error(f"数据库连接池初始化失败: {str(e)}")

# 生成API文档
try:
    generate_api_documentation("docs")
    logger.info("API文档生成成功")
except Exception as e:
    logger.warning(f"API文档生成失败: {str(e)}")

# 初始化服务
orthanc_service = OrthancService(
    Config.ORTHANC.ORTHANC_URL,
    Config.ORTHANC.ORTHANC_USERNAME,
    Config.ORTHANC.ORTHANC_PASSWORD
)
detection_service = DetectionService(
    model_path=os.path.join(Config.YOLO.MODEL_PATH, Config.YOLO.DEFAULT_MODEL),
    confidence_threshold=Config.YOLO.CONFIDENCE_THRESHOLD,
    iou_threshold=Config.YOLO.IOU_THRESHOLD,
    device=Config.YOLO.DEVICE
)
ohif_service = OHIFService(
    Config.OHIF.OHIF_VIEWER_URL,
    Config.ORTHANC.ORTHANC_DICOMWEB_URL
)

# 初始化数据库服务
database_service = DatabaseService(os.path.join(BASE_DIR, 'data', 'app.db'))

# 初始化认证服务
auth_service = AuthService(
    db_service=database_service,
    secret_key=Config.FLASK.SECRET_KEY,
    token_expiry=24
)

# 将服务实例添加到应用上下文
app.orthanc_service = orthanc_service
app.detection_service = detection_service
app.ohif_service = ohif_service
app.database_service = database_service
app.auth_service = auth_service

# 初始化AI预测扩展
try:
    ai_extension_config = {
        'models': {
            'yolo_chest_xray': {
                'id': 'yolo_chest_xray',
                'name': 'YOLO胸部X光检测',
                'description': '基于YOLOv11的胸部X光疾病检测模型',
                'modality': ['CR', 'DX', 'CT', 'MR'],  # 支持多种模态
                'api_endpoint': '/api/yolo/detect',
                'info_api': '/api/yolo/model/info',
                'is_active': True,
                'confidence_threshold': Config.YOLO.CONFIDENCE_THRESHOLD,
                'iou_threshold': Config.YOLO.IOU_THRESHOLD
            }
        },
        'models_details': {
            'yolo_chest_xray': {
                'version': '1.0.0',
                'author': 'YOLO Team',
                'created_date': '2025-01-01',
                'model_size': 'Medium',
                'supported_formats': ['DICOM'],
                'performance_metrics': {
                    'accuracy': 0.85,
                    'precision': 0.82,
                    'recall': 0.88
                }
            }
        },
        'options': {
            'auto_detection': False,
            'show_confidence': True,
            'min_confidence': Config.YOLO.CONFIDENCE_THRESHOLD,
            'max_detections': 10,
            'enableBoundingBoxes': True,
            'enableReports': True,
            'enableAutoDetection': False,
            'defaultConfidenceThreshold': Config.YOLO.CONFIDENCE_THRESHOLD,
            'supportedModalities': ['CR', 'DX', 'CT', 'MR']
        }
    }
    
    app.ai_extension = init_ai_extension(app, ai_extension_config)
    if app.ai_extension:
        logger.info("AI预测扩展初始化成功")
    else:
        logger.warning("AI预测扩展初始化失败")
except Exception as e:
    logger.error(f"AI预测扩展初始化错误: {str(e)}")
    app.ai_extension = None

# 添加模板上下文处理器
@app.context_processor
def inject_now():
    return {'now': datetime.now()}

# 添加自定义Jinja2过滤器
@app.template_filter('safe_strftime')
def safe_strftime(date_value, format_string='%Y-%m-%d'):
    """安全的日期格式化过滤器，处理字符串和datetime对象"""
    if not date_value:
        return '未知'
    
    # 如果已经是字符串，直接返回
    if isinstance(date_value, str):
        return date_value
    
    # 如果是datetime对象，格式化后返回
    try:
        return date_value.strftime(format_string)
    except (AttributeError, ValueError):
        return str(date_value)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'dcm', 'dicom', 'zip'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# 注意：主要路由已移至 routes/web.py 和 routes/api.py 中的 Blueprint

# 保留静态文件服务路由
@app.route('/static/<path:filename>')
def serve_static(filename):
    return send_from_directory('static', filename)

# 添加favicon.ico路由
@app.route('/favicon.ico')
def favicon():
    return send_from_directory('static/images', 'favicon.svg', mimetype='image/svg+xml')

@app.route('/health')
def health_check():
    """系统健康检查端点"""
    try:
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'version': '1.0.0',
            'services': {
                'orthanc': orthanc_service.check_health() if orthanc_service else False,
                'detection': detection_service.get_model_info() is not None if detection_service else False,
                'database': database_service.check_connection() if database_service else False
            }
        })
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return jsonify({
            'status': 'unhealthy',
            'timestamp': datetime.now().isoformat(),
            'error': str(e)
        }), 500

# API路由：健康检查
@app.route('/api/health')
def api_health_check():
    """API健康检查端点（兼容性）"""
    return health_check()

@app.route('/api/metrics')
@timing_decorator('metrics_request')
def get_metrics():
    """获取系统性能指标"""
    try:
        metrics = get_performance_report()
        
        # 添加数据库连接池状态
        pool = get_connection_pool()
        if pool:
            metrics['database_pool'] = pool.get_stats()
        
        return jsonify(metrics)
    except Exception as e:
        logger.error(f"获取性能指标失败: {str(e)}")
        raise APIError("获取性能指标失败", 500)

@app.route('/api/config/validate')
def api_config_validate():
    """验证配置"""
    try:
        config_manager = get_config_manager()
        if config_manager:
            report = config_manager.get_validation_report()
            return jsonify(report)
        else:
            return jsonify({'error': 'Config manager not available'}), 500
    except Exception as e:
        logger.error(f"配置验证失败: {str(e)}")
        return jsonify({'error': 'Config validation failed'}), 500

@app.route('/api/database/optimize', methods=['POST'])
def api_database_optimize():
    """优化数据库"""
    try:
        from utils.database_optimizer import DatabaseOptimizer
        
        db_path = os.path.join(BASE_DIR, 'data', 'app.db')
        optimizer = DatabaseOptimizer(db_path)
        
        # 执行优化
        results = optimizer.optimize_database()
        
        # 创建索引
        created_indexes = optimizer.create_indexes()
        results['created_indexes'] = len(created_indexes)
        
        return jsonify(results)
    except Exception as e:
        logger.error(f"数据库优化失败: {str(e)}")
        return jsonify({'error': 'Database optimization failed'}), 500

@app.route('/api/database/stats')
def api_database_stats():
    """获取数据库统计信息"""
    try:
        from utils.database_optimizer import DatabaseOptimizer
        
        db_path = os.path.join(BASE_DIR, 'data', 'app.db')
        optimizer = DatabaseOptimizer(db_path)
        
        stats = optimizer.get_database_stats()
        
        # 添加连接池状态
        pool = get_connection_pool()
        if pool:
            stats['connection_pool'] = pool.get_stats()
        
        return jsonify(stats)
    except Exception as e:
        logger.error(f"获取数据库统计失败: {str(e)}")
        return jsonify({'error': 'Failed to get database stats'}), 500

@app.route('/api/docs')
def api_docs():
    """API文档"""
    try:
        doc_generator = get_documentation_generator()
        if doc_generator:
            openapi_spec = doc_generator.generate_openapi_spec()
            return jsonify(openapi_spec)
        else:
            return jsonify({'error': 'Documentation generator not available'}), 500
    except Exception as e:
        logger.error(f"获取API文档失败: {str(e)}")
        return jsonify({'error': 'Failed to get API documentation'}), 500

@app.route('/docs')
def docs_html():
    """HTML格式的API文档"""
    try:
        doc_generator = get_documentation_generator()
        if doc_generator:
            html_doc = doc_generator.generate_html_documentation()
            return html_doc
        else:
            return "<h1>API文档不可用</h1>", 500
    except Exception as e:
        logger.error(f"生成HTML文档失败: {str(e)}")
        return "<h1>文档生成失败</h1>", 500

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def server_error(error):
    return jsonify({'error': 'Internal server error'}), 500

# 注册应用关闭处理
@app.teardown_appcontext
def close_db_connections(error):
    """关闭数据库连接"""
    try:
        close_connection_pool()
    except Exception as e:
        logger.error(f"关闭数据库连接池失败: {str(e)}")

# 启动应用
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=Config.DEBUG)