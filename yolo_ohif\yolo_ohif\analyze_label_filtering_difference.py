#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析标签过滤差异：为什么convert_label_to_bbox.py生成2076个标签，
但训练代码只使用了410个标签
"""

import os
import cv2
import numpy as np
from pathlib import Path
import sys

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from train_supraspinatus_yolo import SupraspinatusYOLOTrainer
from convert_label_to_bbox import mask_to_bbox as convert_mask_to_bbox

def analyze_filtering_difference():
    """
    分析两种mask_to_bbox函数的过滤差异
    """
    print("=== 分析标签过滤差异 ===")
    
    # 创建训练器实例
    trainer = SupraspinatusYOLOTrainer(
        dataset_root="./dataset",
        output_root="./test_output"
    )
    
    # 路径设置
    sliced_labels_dir = Path("sliced_output/sliced_images/label_T2")
    bbox_labels_dir = Path("sliced_output/bbox_labels")
    
    if not sliced_labels_dir.exists():
        print(f"❌ 切片标签目录不存在: {sliced_labels_dir}")
        return
    
    # 统计信息
    stats = {
        'total_images': 0,
        'convert_script_valid': 0,  # convert_label_to_bbox.py认为有效的
        'training_code_valid': 0,   # 训练代码认为有效的
        'both_valid': 0,            # 两者都认为有效的
        'only_convert_valid': 0,    # 只有convert脚本认为有效的
        'only_training_valid': 0,   # 只有训练代码认为有效的
        'neither_valid': 0          # 两者都认为无效的
    }
    
    # 获取所有标签图像文件
    label_files = list(sliced_labels_dir.glob("*.jpg"))
    print(f"找到 {len(label_files)} 个标签图像文件")
    
    for i, label_file in enumerate(label_files[:100]):  # 只分析前100个文件作为样本
        if i % 20 == 0:
            print(f"处理进度: {i}/{min(100, len(label_files))}")
        
        stats['total_images'] += 1
        
        # 加载标签图像
        label_img = cv2.imread(str(label_file), cv2.IMREAD_GRAYSCALE)
        if label_img is None:
            continue
        
        # 调整到640x640尺寸（与训练代码一致）
        label_resized = cv2.resize(label_img, (640, 640))
        
        # 二值化处理
        _, binary_mask = cv2.threshold(label_resized, 127, 255, cv2.THRESH_BINARY)
        
        # 检查convert_label_to_bbox.py的判断
        convert_result = convert_mask_to_bbox(binary_mask)
        convert_valid = convert_result is not None and len(convert_result) > 0
        
        # 检查训练代码的判断
        training_result = trainer.mask_to_bbox(binary_mask)
        training_valid = training_result is not None
        
        # 统计结果
        if convert_valid:
            stats['convert_script_valid'] += 1
        if training_valid:
            stats['training_code_valid'] += 1
        
        if convert_valid and training_valid:
            stats['both_valid'] += 1
        elif convert_valid and not training_valid:
            stats['only_convert_valid'] += 1
            # 分析为什么训练代码拒绝了这个样本
            if i < 10:  # 只打印前10个差异样本的详细信息
                print(f"\n差异样本: {label_file.name}")
                print(f"  convert脚本结果: {convert_result}")
                print(f"  训练代码结果: {training_result}")
                print(f"  标签像素数: {np.sum(binary_mask > 0)}")
        elif not convert_valid and training_valid:
            stats['only_training_valid'] += 1
        else:
            stats['neither_valid'] += 1
    
    # 打印统计结果
    print("\n=== 过滤差异分析结果 ===")
    print(f"分析样本数: {stats['total_images']}")
    print(f"convert脚本认为有效: {stats['convert_script_valid']} ({stats['convert_script_valid']/stats['total_images']*100:.1f}%)")
    print(f"训练代码认为有效: {stats['training_code_valid']} ({stats['training_code_valid']/stats['total_images']*100:.1f}%)")
    print(f"两者都认为有效: {stats['both_valid']} ({stats['both_valid']/stats['total_images']*100:.1f}%)")
    print(f"只有convert脚本认为有效: {stats['only_convert_valid']} ({stats['only_convert_valid']/stats['total_images']*100:.1f}%)")
    print(f"只有训练代码认为有效: {stats['only_training_valid']} ({stats['only_training_valid']/stats['total_images']*100:.1f}%)")
    print(f"两者都认为无效: {stats['neither_valid']} ({stats['neither_valid']/stats['total_images']*100:.1f}%)")
    
    # 分析参数差异
    print("\n=== 参数差异分析 ===")
    print("convert_label_to_bbox.py 参数:")
    print("  - min_area_threshold: 10")
    print("  - min_bbox_size: 5")
    print("  - max_bbox_ratio: 0.95")
    print("  - min_norm_size: 0.01")
    print("\ntrain_supraspinatus_yolo.py 参数:")
    print("  - min_area_threshold: 10")
    print("  - min_bbox_size: 5")
    print("  - max_bbox_ratio: 0.95")
    print("  - min_norm_size: 0.01")
    
    # 推测原因
    print("\n=== 可能的差异原因 ===")
    print("1. 图像预处理差异：训练代码可能有额外的预处理步骤")
    print("2. 边界框合并逻辑：训练代码使用了边界框合并，可能影响最终结果")
    print("3. 连通组件分析：两个函数可能使用了不同的连通性参数")
    print("4. 数据源差异：训练代码直接从nii.gz文件处理，convert脚本从切片图像处理")
    
    # 检查实际的bbox_labels目录
    if bbox_labels_dir.exists():
        bbox_files = list(bbox_labels_dir.glob("*.txt"))
        print(f"\nbbox_labels目录中实际有 {len(bbox_files)} 个标签文件")
        
        # 检查训练数据集中的标签文件
        train_labels_dir = Path("yolo_training_output/yolo_dataset/labels/train")
        val_labels_dir = Path("yolo_training_output/yolo_dataset/labels/val")
        test_labels_dir = Path("yolo_training_output/yolo_dataset/labels/test")
        
        train_count = len(list(train_labels_dir.glob("*.txt"))) if train_labels_dir.exists() else 0
        val_count = len(list(val_labels_dir.glob("*.txt"))) if val_labels_dir.exists() else 0
        test_count = len(list(test_labels_dir.glob("*.txt"))) if test_labels_dir.exists() else 0
        
        print(f"训练数据集标签文件: {train_count + val_count + test_count} 个")
        print(f"  - 训练集: {train_count}")
        print(f"  - 验证集: {val_count}")
        print(f"  - 测试集: {test_count}")
        
        print(f"\n差异: bbox_labels有{len(bbox_files)}个，训练数据集只用了{train_count + val_count + test_count}个")
        print("这说明训练代码在处理过程中应用了更严格的过滤条件")

if __name__ == "__main__":
    analyze_filtering_difference()