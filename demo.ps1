# 医学影像解析系统演示脚本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "医学影像解析系统 - 项目演示" -ForegroundColor Cyan
Write-Host "Medical Image Analysis System - Demo" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查.NET环境
Write-Host "检查系统环境..." -ForegroundColor Yellow
$dotnetVersion = dotnet --version 2>$null
if ($dotnetVersion) {
    Write-Host "✓ .NET SDK 版本: $dotnetVersion" -ForegroundColor Green
} else {
    Write-Host "✗ 未找到 .NET SDK，请先安装 .NET 8 SDK" -ForegroundColor Red
    exit 1
}

# 显示项目结构
Write-Host ""
Write-Host "项目结构概览:" -ForegroundColor Yellow
Write-Host "src/" -ForegroundColor White
Write-Host "├── MedicalImageAnalysis.Core/          # 核心业务逻辑" -ForegroundColor Gray
Write-Host "├── MedicalImageAnalysis.Application/   # 应用服务层" -ForegroundColor Gray
Write-Host "├── MedicalImageAnalysis.Infrastructure/ # 基础设施层" -ForegroundColor Gray
Write-Host "├── MedicalImageAnalysis.Wpf/           # WPF 桌面应用" -ForegroundColor Gray
Write-Host "└── MedicalImageAnalysis.WpfClient/     # WPF 客户端应用" -ForegroundColor Gray

# 显示主要功能
Write-Host ""
Write-Host "主要功能模块:" -ForegroundColor Yellow
Write-Host "✓ DICOM 文件处理和解析" -ForegroundColor Green
Write-Host "✓ 医学影像显示和操作" -ForegroundColor Green
Write-Host "✓ 智能标注系统" -ForegroundColor Green
Write-Host "✓ YOLOv11 AI 模型集成" -ForegroundColor Green
Write-Host "✓ 数据管理和导出" -ForegroundColor Green
Write-Host "✓ 系统监控和日志" -ForegroundColor Green

# 显示示例数据
Write-Host ""
Write-Host "示例数据:" -ForegroundColor Yellow
$brainFiles = Get-ChildItem -Path "Brain" -Filter "*.dcm" -ErrorAction SilentlyContinue
if ($brainFiles) {
    Write-Host "✓ 发现 $($brainFiles.Count) 个 DICOM 示例文件:" -ForegroundColor Green
    $brainFiles | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor Gray }
} else {
    Write-Host "⚠ 未找到示例 DICOM 文件" -ForegroundColor Yellow
}

# 构建项目
Write-Host ""
Write-Host "构建项目..." -ForegroundColor Yellow

Write-Host "正在构建核心模块..." -ForegroundColor Gray
dotnet build src/MedicalImageAnalysis.Core/MedicalImageAnalysis.Core.csproj --verbosity quiet 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 核心模块构建成功" -ForegroundColor Green
} else {
    Write-Host "⚠ 核心模块构建有问题" -ForegroundColor Yellow
}

Write-Host "正在构建客户端应用..." -ForegroundColor Gray
dotnet build src/MedicalImageAnalysis.WpfClient/MedicalImageAnalysis.WpfClient.csproj --verbosity quiet 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 客户端应用构建成功" -ForegroundColor Green

    Write-Host ""
    Write-Host "启动桌面应用程序..." -ForegroundColor Yellow
    Write-Host "注意: 应用程序需要连接到 API 服务器 (localhost:5000)" -ForegroundColor Gray

    $appPath = "src\MedicalImageAnalysis.WpfClient\bin\Debug\net8.0-windows\MedicalImageAnalysis.WpfClient.exe"
    if (Test-Path $appPath) {
        Write-Host "正在启动应用程序..." -ForegroundColor Green
        Start-Process -FilePath $appPath -WorkingDirectory (Get-Location)
        Start-Sleep -Seconds 3

        $process = Get-Process -Name "MedicalImageAnalysis.WpfClient" -ErrorAction SilentlyContinue
        if ($process) {
            Write-Host "✓ 应用程序已启动 (PID: $($process.Id))" -ForegroundColor Green
        } else {
            Write-Host "⚠ 应用程序启动后立即退出，可能需要API服务器" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "✗ 客户端应用构建失败" -ForegroundColor Red
}

# 显示日志位置
Write-Host ""
Write-Host "日志文件位置:" -ForegroundColor Yellow
Write-Host "- 应用程序日志: logs/" -ForegroundColor Gray
Write-Host "- 构建日志: 控制台输出" -ForegroundColor Gray

# 显示下一步操作
Write-Host ""
Write-Host "下一步操作建议:" -ForegroundColor Yellow
Write-Host "1. 查看应用程序界面和功能" -ForegroundColor White
Write-Host "2. 加载示例 DICOM 文件进行测试" -ForegroundColor White
Write-Host "3. 探索标注和分析功能" -ForegroundColor White
Write-Host "4. 查看系统监控和日志" -ForegroundColor White

Write-Host ""
Write-Host "演示完成！" -ForegroundColor Green
Write-Host "如有问题，请查看 README.md 或日志文件" -ForegroundColor Gray
Write-Host ""
