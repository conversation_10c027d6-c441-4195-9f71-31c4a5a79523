using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Media.Imaging;
using Microsoft.Extensions.Logging;
using FellowOakDicom;
using FellowOakDicom.Imaging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;

namespace MedicalImageAnalysis.Wpf.Services
{
    /// <summary>
    /// 增强的DICOM图像处理服务
    /// 基于fo-dicom提供DICOM图像的窗宽窗位调整、格式转换等功能
    /// </summary>
    public class GdcmImageProcessor
    {
        private readonly ILogger<GdcmImageProcessor> _logger;

        public GdcmImageProcessor(ILogger<GdcmImageProcessor> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 应用窗宽窗位调整
        /// </summary>
        public async Task<BitmapSource?> ApplyWindowLevelAsync(string dicomFilePath, double windowCenter, double windowWidth)
        {
            return await Task.Run(() =>
            {
                try
                {
                    _logger.LogInformation("应用窗宽窗位调整: WC={WindowCenter}, WW={WindowWidth}", windowCenter, windowWidth);

                    var dicomFile = DicomFile.Open(dicomFilePath);
                    var dataset = dicomFile.Dataset;

                    // 获取图像尺寸和像素数据
                    var rows = dataset.GetSingleValue<ushort>(DicomTag.Rows);
                    var columns = dataset.GetSingleValue<ushort>(DicomTag.Columns);
                    var bitsAllocated = dataset.GetSingleValue<ushort>(DicomTag.BitsAllocated);

                    // 应用窗宽窗位并转换为BitmapSource
                    return ApplyWindowLevelToBitmapSource(dataset, columns, rows, bitsAllocated, windowCenter, windowWidth);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "应用窗宽窗位调整失败");
                    return null;
                }
            });
        }

        /// <summary>
        /// 应用窗宽窗位调整和曝光值调整
        /// </summary>
        public async Task<BitmapSource?> ApplyWindowLevelWithExposureAsync(string dicomFilePath, double windowCenter, double windowWidth, double exposure)
        {
            return await Task.Run(() =>
            {
                try
                {
                    _logger.LogInformation("应用窗宽窗位和曝光值调整: WC={WindowCenter}, WW={WindowWidth}, Exposure={Exposure}",
                                         windowCenter, windowWidth, exposure);

                    var dicomFile = DicomFile.Open(dicomFilePath);
                    var dataset = dicomFile.Dataset;

                    // 获取图像尺寸和像素数据
                    var rows = dataset.GetSingleValue<ushort>(DicomTag.Rows);
                    var columns = dataset.GetSingleValue<ushort>(DicomTag.Columns);
                    var bitsAllocated = dataset.GetSingleValue<ushort>(DicomTag.BitsAllocated);

                    // 应用窗宽窗位和曝光值调整并转换为BitmapSource
                    return ApplyWindowLevelToBitmapSource(dataset, columns, rows, bitsAllocated, windowCenter, windowWidth, exposure);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "应用窗宽窗位和曝光值调整失败");
                    return null;
                }
            });
        }

        /// <summary>
        /// 应用窗宽窗位并转换为BitmapSource
        /// </summary>
        private BitmapSource ApplyWindowLevelToBitmapSource(DicomDataset dataset, int width, int height, int bitsAllocated, double windowCenter, double windowWidth)
        {
            return ApplyWindowLevelToBitmapSource(dataset, width, height, bitsAllocated, windowCenter, windowWidth, 0.0);
        }

        /// <summary>
        /// 应用窗宽窗位和曝光值调整并转换为BitmapSource
        /// </summary>
        private BitmapSource ApplyWindowLevelToBitmapSource(DicomDataset dataset, int width, int height, int bitsAllocated, double windowCenter, double windowWidth, double exposure)
        {
            try
            {
                _logger.LogInformation("应用窗宽窗位和曝光值: WC={WindowCenter}, WW={WindowWidth}, Exposure={Exposure}",
                                     windowCenter, windowWidth, exposure);

                // 获取像素数据
                var pixelData = DicomPixelData.Create(dataset);
                var frame = pixelData.GetFrame(0);
                var rawData = frame.Data;

                // 获取重缩放参数
                var rescaleSlope = dataset.GetSingleValueOrDefault(DicomTag.RescaleSlope, 1.0);
                var rescaleIntercept = dataset.GetSingleValueOrDefault(DicomTag.RescaleIntercept, 0.0);

                // 应用窗宽窗位和曝光值变换
                byte[] processedData;

                if (bitsAllocated == 16)
                {
                    processedData = ApplyWindowLevelTo16BitWithExposure(rawData, windowCenter, windowWidth, exposure, rescaleSlope, rescaleIntercept);
                }
                else if (bitsAllocated == 8)
                {
                    processedData = ApplyWindowLevelTo8BitWithExposure(rawData, windowCenter, windowWidth, exposure, rescaleSlope, rescaleIntercept);
                }
                else
                {
                    _logger.LogWarning("不支持的位深度: {BitsAllocated}", bitsAllocated);
                    return CreateTestImage(width, height);
                }

                // 创建BitmapSource
                var bitmapSource = BitmapSource.Create(
                    width, height,
                    96, 96,
                    System.Windows.Media.PixelFormats.Gray8,
                    null,
                    processedData,
                    width);

                bitmapSource.Freeze();
                return bitmapSource;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用窗宽窗位失败");
                return CreateTestImage(width, height);
            }
        }

        /// <summary>
        /// 对16位数据应用窗宽窗位和亮度调整
        /// </summary>
        private byte[] ApplyWindowLevelTo16BitWithExposure(byte[] data16Bit, double windowCenter, double windowWidth, double exposure, double rescaleSlope, double rescaleIntercept)
        {
            var result = new byte[data16Bit.Length / 2];

            // 计算窗宽窗位范围
            var minValue = windowCenter - windowWidth / 2.0;
            var maxValue = windowCenter + windowWidth / 2.0;
            var range = maxValue - minValue;

            if (range <= 0) range = 1; // 避免除零

            for (int i = 0; i < result.Length; i++)
            {
                // 读取16位值（小端序）
                var rawValue = BitConverter.ToInt16(data16Bit, i * 2);

                // 转换为Hounsfield单位 (HU)
                var huValue = rescaleSlope * rawValue + rescaleIntercept;

                // 确保HU值在医学影像的合理范围内 (-1024 到 3071)
                huValue = Math.Clamp(huValue, -1024, 3071);

                // 应用窗宽窗位
                double normalizedValue;
                if (huValue <= minValue)
                    normalizedValue = 0.0;
                else if (huValue >= maxValue)
                    normalizedValue = 1.0;
                else
                    normalizedValue = (huValue - minValue) / range;

                // 应用亮度调整（exposure范围: -100 到 100，表示亮度百分比调整）
                // 将exposure转换为亮度调整因子：0表示无调整，正值增亮，负值变暗
                var brightnessAdjustment = exposure / 100.0; // 转换为-1.0到1.0的范围
                normalizedValue = Math.Clamp(normalizedValue + brightnessAdjustment, 0.0, 1.0);

                // 转换为8位
                result[i] = (byte)(normalizedValue * 255);
            }

            return result;
        }

        /// <summary>
        /// 对16位数据应用窗宽窗位（兼容性方法）
        /// </summary>
        private byte[] ApplyWindowLevelTo16Bit(byte[] data16Bit, double windowCenter, double windowWidth)
        {
            return ApplyWindowLevelTo16BitWithExposure(data16Bit, windowCenter, windowWidth, 0.0, 1.0, 0.0);
        }

        /// <summary>
        /// 对8位数据应用窗宽窗位和曝光值调整
        /// </summary>
        private byte[] ApplyWindowLevelTo8BitWithExposure(byte[] data8Bit, double windowCenter, double windowWidth, double exposure, double rescaleSlope, double rescaleIntercept)
        {
            var result = new byte[data8Bit.Length];

            // 计算窗宽窗位范围
            var minValue = windowCenter - windowWidth / 2.0;
            var maxValue = windowCenter + windowWidth / 2.0;
            var range = maxValue - minValue;

            if (range <= 0) range = 1; // 避免除零

            for (int i = 0; i < result.Length; i++)
            {
                var rawValue = data8Bit[i];

                // 转换为Hounsfield单位 (HU)
                var huValue = rescaleSlope * rawValue + rescaleIntercept;

                // 确保HU值在医学影像的合理范围内
                huValue = Math.Clamp(huValue, -1024, 3071);

                // 应用窗宽窗位
                double normalizedValue;
                if (huValue <= minValue)
                    normalizedValue = 0.0;
                else if (huValue >= maxValue)
                    normalizedValue = 1.0;
                else
                    normalizedValue = (huValue - minValue) / range;

                // 应用亮度调整（exposure范围: -100 到 100，表示亮度百分比调整）
                var brightnessAdjustment = exposure / 100.0; // 转换为-1.0到1.0的范围
                normalizedValue = Math.Clamp(normalizedValue + brightnessAdjustment, 0.0, 1.0);

                // 转换为8位
                result[i] = (byte)(normalizedValue * 255);
            }

            return result;
        }

        /// <summary>
        /// 对8位数据应用窗宽窗位（兼容性方法）
        /// </summary>
        private byte[] ApplyWindowLevelTo8Bit(byte[] data8Bit, double windowCenter, double windowWidth)
        {
            return ApplyWindowLevelTo8BitWithExposure(data8Bit, windowCenter, windowWidth, 0.0, 1.0, 0.0);
        }

        /// <summary>
        /// 创建测试图像
        /// </summary>
        private BitmapSource CreateTestImage(int width, int height)
        {
            var testData = new byte[width * height];

            // 创建渐变图案
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    var index = y * width + x;

                    // 创建径向渐变
                    var centerX = width / 2.0;
                    var centerY = height / 2.0;
                    var distance = Math.Sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                    var maxDistance = Math.Sqrt(centerX * centerX + centerY * centerY);
                    var intensity = 1.0 - (distance / maxDistance);

                    testData[index] = (byte)(intensity * 255);
                }
            }

            return BitmapSource.Create(
                width, height,
                96, 96,
                System.Windows.Media.PixelFormats.Gray8,
                null,
                testData,
                width);
        }

        /// <summary>
        /// 应用窗宽窗位算法（保留用于兼容性）
        /// </summary>
        private byte[] ApplyWindowLevel(byte[] originalBuffer, ushort bitsAllocated, double windowCenter, double windowWidth)
        {
            var processedBuffer = new byte[originalBuffer.Length];
            
            if (bitsAllocated == 16)
            {
                // 16位图像处理
                for (int i = 0; i < originalBuffer.Length; i += 2)
                {
                    // 读取16位像素值
                    short pixelValue = BitConverter.ToInt16(originalBuffer, i);
                    
                    // 应用窗宽窗位
                    double normalizedValue = ApplyWindowLevelToPixel(pixelValue, windowCenter, windowWidth);
                    
                    // 转换回16位
                    short processedValue = (short)(normalizedValue * short.MaxValue);
                    byte[] processedBytes = BitConverter.GetBytes(processedValue);
                    
                    processedBuffer[i] = processedBytes[0];
                    processedBuffer[i + 1] = processedBytes[1];
                }
            }
            else if (bitsAllocated == 8)
            {
                // 8位图像处理
                for (int i = 0; i < originalBuffer.Length; i++)
                {
                    byte pixelValue = originalBuffer[i];
                    double normalizedValue = ApplyWindowLevelToPixel(pixelValue, windowCenter, windowWidth);
                    processedBuffer[i] = (byte)(normalizedValue * 255);
                }
            }
            else
            {
                // 不支持的位深度，返回原始数据
                Array.Copy(originalBuffer, processedBuffer, originalBuffer.Length);
            }

            return processedBuffer;
        }

        /// <summary>
        /// 对单个像素应用窗宽窗位
        /// </summary>
        private double ApplyWindowLevelToPixel(double pixelValue, double windowCenter, double windowWidth)
        {
            double minValue = windowCenter - windowWidth / 2.0;
            double maxValue = windowCenter + windowWidth / 2.0;

            if (pixelValue <= minValue)
                return 0.0;
            else if (pixelValue >= maxValue)
                return 1.0;
            else
                return (pixelValue - minValue) / windowWidth;
        }

        /// <summary>
        /// 将高位深度图像转换为8位用于显示
        /// </summary>
        private byte[] ConvertTo8Bit(byte[] buffer, ushort bitsAllocated)
        {
            if (bitsAllocated == 8)
            {
                return buffer;
            }

            if (bitsAllocated == 16)
            {
                var result = new byte[buffer.Length / 2];
                for (int i = 0; i < result.Length; i++)
                {
                    // 取16位值的高8位
                    ushort value = BitConverter.ToUInt16(buffer, i * 2);
                    result[i] = (byte)(value >> 8);
                }
                return result;
            }

            throw new NotSupportedException($"不支持的位深度: {bitsAllocated}");
        }

        /// <summary>
        /// 转换DICOM为常见图像格式
        /// </summary>
        public async Task<bool> ConvertDicomToImageAsync(string dicomFilePath, string outputPath, string format = "PNG")
        {
            return await Task.Run(() =>
            {
                try
                {
                    _logger.LogInformation("转换DICOM为图像格式: {Format}", format);

                    var dicomFile = DicomFile.Open(dicomFilePath);
                    var dataset = dicomFile.Dataset;

                    // 获取图像信息
                    var rows = dataset.GetSingleValue<ushort>(DicomTag.Rows);
                    var columns = dataset.GetSingleValue<ushort>(DicomTag.Columns);
                    var bitsAllocated = dataset.GetSingleValue<ushort>(DicomTag.BitsAllocated);

                    // 使用我们的像素数据提取方法
                    var bitmapSource = ApplyWindowLevelToBitmapSource(dataset, columns, rows, bitsAllocated, 40, 400);

                    // 保存BitmapSource为指定格式
                    SaveBitmapSourceAsImage(bitmapSource, outputPath, format);

                    _logger.LogInformation("DICOM转换完成: {OutputPath}", outputPath);
                    return true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "DICOM转换失败");
                    return false;
                }
            });
        }

        /// <summary>
        /// 转换DICOM为常见图像格式（使用指定的窗宽窗位）
        /// </summary>
        public async Task<bool> ConvertDicomToImageWithWindowLevelAsync(string dicomFilePath, string outputPath, string format, double windowCenter, double windowWidth)
        {
            return await Task.Run(() =>
            {
                try
                {
                    _logger.LogInformation("转换DICOM为图像格式: {Format}, WC={WindowCenter}, WW={WindowWidth}", format, windowCenter, windowWidth);

                    var dicomFile = DicomFile.Open(dicomFilePath);
                    var dataset = dicomFile.Dataset;

                    // 获取图像信息
                    var rows = dataset.GetSingleValue<ushort>(DicomTag.Rows);
                    var columns = dataset.GetSingleValue<ushort>(DicomTag.Columns);
                    var bitsAllocated = dataset.GetSingleValue<ushort>(DicomTag.BitsAllocated);

                    // 使用指定的窗宽窗位值
                    var bitmapSource = ApplyWindowLevelToBitmapSource(dataset, columns, rows, bitsAllocated, windowCenter, windowWidth);

                    // 保存BitmapSource为指定格式
                    SaveBitmapSourceAsImage(bitmapSource, outputPath, format);

                    _logger.LogInformation("DICOM转换完成: {OutputPath}, 窗宽/窗位: {WindowWidth}/{WindowCenter}", outputPath, windowWidth, windowCenter);
                    return true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "DICOM转换失败");
                    return false;
                }
            });
        }

        /// <summary>
        /// 保存BitmapSource为图像文件
        /// </summary>
        private void SaveBitmapSourceAsImage(BitmapSource bitmapSource, string outputPath, string format)
        {
            BitmapEncoder encoder = format.ToUpper() switch
            {
                "PNG" => new PngBitmapEncoder(),
                "JPEG" or "JPG" => new JpegBitmapEncoder(),
                "BMP" => new BmpBitmapEncoder(),
                "TIFF" or "TIF" => new TiffBitmapEncoder(),
                _ => new PngBitmapEncoder()
            };

            encoder.Frames.Add(BitmapFrame.Create(bitmapSource));

            using var fileStream = new FileStream(outputPath, FileMode.Create);
            encoder.Save(fileStream);
        }

        /// <summary>
        /// 获取DICOM图像的统计信息
        /// </summary>
        public async Task<ImageStatistics> GetImageStatisticsAsync(string dicomFilePath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var dicomFile = DicomFile.Open(dicomFilePath);
                    var dataset = dicomFile.Dataset;

                    // 获取像素数据
                    var pixelData = DicomPixelData.Create(dataset);
                    var frame = pixelData.GetFrame(0);

                    // 获取图像属性
                    var bitsAllocated = dataset.GetSingleValue<ushort>(DicomTag.BitsAllocated);

                    return CalculateStatistics(frame.Data, bitsAllocated);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "计算图像统计信息失败");
                    throw;
                }
            });
        }

        /// <summary>
        /// 计算图像统计信息
        /// </summary>
        private ImageStatistics CalculateStatistics(byte[] buffer, ushort bitsAllocated)
        {
            var stats = new ImageStatistics();
            
            if (bitsAllocated == 16)
            {
                var values = new double[buffer.Length / 2];
                double sum = 0;
                double min = double.MaxValue;
                double max = double.MinValue;

                for (int i = 0; i < values.Length; i++)
                {
                    short value = BitConverter.ToInt16(buffer, i * 2);
                    values[i] = value;
                    sum += value;
                    min = Math.Min(min, value);
                    max = Math.Max(max, value);
                }

                stats.Mean = sum / values.Length;
                stats.Min = min;
                stats.Max = max;

                // 计算标准差
                double sumSquaredDiff = 0;
                foreach (var value in values)
                {
                    sumSquaredDiff += Math.Pow(value - stats.Mean, 2);
                }
                stats.StandardDeviation = Math.Sqrt(sumSquaredDiff / values.Length);
            }
            else if (bitsAllocated == 8)
            {
                double sum = 0;
                double min = double.MaxValue;
                double max = double.MinValue;

                foreach (byte value in buffer)
                {
                    sum += value;
                    min = Math.Min(min, value);
                    max = Math.Max(max, value);
                }

                stats.Mean = sum / buffer.Length;
                stats.Min = min;
                stats.Max = max;

                // 计算标准差
                double sumSquaredDiff = 0;
                foreach (byte value in buffer)
                {
                    sumSquaredDiff += Math.Pow(value - stats.Mean, 2);
                }
                stats.StandardDeviation = Math.Sqrt(sumSquaredDiff / buffer.Length);
            }

            return stats;
        }
    }

    /// <summary>
    /// 图像统计信息
    /// </summary>
    public class ImageStatistics
    {
        public double Mean { get; set; }
        public double Min { get; set; }
        public double Max { get; set; }
        public double StandardDeviation { get; set; }
    }
}
