#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CUDA设备检测测试脚本
用于验证训练环境中的CUDA可用性
"""

import os
import sys
from pathlib import Path

def test_cuda_detection():
    """
    测试CUDA设备检测
    """
    print("🔍 CUDA设备检测测试")
    print("=" * 50)
    
    try:
        import torch
        
        # 打印基本信息
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA编译版本: {torch.version.cuda}")
        print(f"Python版本: {sys.version}")
        print(f"当前工作目录: {Path.cwd()}")
        
        # 检查环境变量
        print("\n📋 环境变量检查:")
        cuda_visible = os.environ.get('CUDA_VISIBLE_DEVICES')
        print(f"CUDA_VISIBLE_DEVICES: {cuda_visible}")
        
        # 尝试修复空的CUDA_VISIBLE_DEVICES
        if cuda_visible == '':
            print("🔧 检测到空的CUDA_VISIBLE_DEVICES，正在清除...")
            del os.environ['CUDA_VISIBLE_DEVICES']
            print("✅ 已清除空的CUDA_VISIBLE_DEVICES")
        
        # CUDA可用性检查
        print("\n🔍 CUDA可用性检查:")
        cuda_available = torch.cuda.is_available()
        device_count = torch.cuda.device_count()
        
        print(f"torch.cuda.is_available(): {cuda_available}")
        print(f"torch.cuda.device_count(): {device_count}")
        
        if cuda_available and device_count > 0:
            print("\n✅ CUDA设备信息:")
            for i in range(device_count):
                device_name = torch.cuda.get_device_name(i)
                memory_total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"  GPU {i}: {device_name} ({memory_total:.1f}GB)")
            
            # 测试GPU操作
            print("\n🧪 GPU操作测试:")
            try:
                x = torch.randn(100, 100).cuda()
                y = torch.randn(100, 100).cuda()
                z = torch.matmul(x, y)
                print("✅ GPU矩阵运算测试成功")
                print(f"   结果张量形状: {z.shape}")
                print(f"   结果张量设备: {z.device}")
            except Exception as e:
                print(f"❌ GPU操作测试失败: {e}")
        else:
            print("\n⚠️ 未检测到可用的CUDA设备")
            print("可能的原因:")
            print("1. NVIDIA驱动未正确安装")
            print("2. PyTorch版本与CUDA版本不匹配")
            print("3. 环境变量配置问题")
            print("4. conda环境问题")
            
            # 提供解决建议
            print("\n💡 建议解决方案:")
            print("1. 检查nvidia-smi命令是否可用")
            print("2. 重新安装匹配的PyTorch版本")
            print("3. 检查conda环境激活状态")
            print("4. 重启终端或IDE")
        
        return cuda_available
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装PyTorch")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_training_environment():
    """
    测试训练环境
    """
    print("\n🚀 训练环境测试")
    print("=" * 50)
    
    try:
        # 测试训练器的设备检测
        from train_yolo11x_from_scratch import YOLO11xTrainer
        
        trainer = YOLO11xTrainer(
            dataset_root='./dataset',
            output_root='./test_output'
        )
        
        device = trainer._detect_device()
        print(f"\n🎯 训练器检测到的设备: {device}")
        
        if device != 'cpu':
            print("✅ 训练环境CUDA检测成功")
        else:
            print("⚠️ 训练环境将使用CPU")
            
        return device != 'cpu'
        
    except Exception as e:
        print(f"❌ 训练环境测试失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("🔧 CUDA设备检测与训练环境测试")
    print("=" * 60)
    
    # 基础CUDA检测
    cuda_ok = test_cuda_detection()
    
    # 训练环境检测
    training_ok = test_training_environment()
    
    # 总结
    print("\n📊 测试总结")
    print("=" * 50)
    print(f"基础CUDA检测: {'✅ 通过' if cuda_ok else '❌ 失败'}")
    print(f"训练环境检测: {'✅ 通过' if training_ok else '❌ 失败'}")
    
    if cuda_ok and training_ok:
        print("\n🎉 恭喜！您的环境已准备就绪，可以开始GPU训练")
        print("💡 建议: 运行 python start_yolo11x_training.py 开始训练")
    elif cuda_ok and not training_ok:
        print("\n⚠️ CUDA可用但训练环境有问题")
        print("💡 建议: 检查train_yolo11x_from_scratch.py文件")
    else:
        print("\n❌ CUDA不可用，将使用CPU训练（速度较慢）")
        print("💡 建议: 检查CUDA安装和PyTorch版本")

if __name__ == "__main__":
    main()