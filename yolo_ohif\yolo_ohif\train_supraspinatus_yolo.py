#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
冈上肌肌腱撕裂YOLO检测模型训练脚本

功能:
1. 将nii.gz格式的医学图像转换为YOLO训练格式
2. 从mask生成YOLO标注文件
3. 数据增强和数据集划分
4. 训练YOLO模型

数据集结构:
- image_T2/: 撕裂图像 (130-395.nii.gz)
- label_T2/: 撕裂图像对应的冈上肌mask (130-395.nii.gz)
- image_T2_normal/: 正常图像 (1-129.nii.gz)
"""

import os
import cv2
import numpy as np
import nibabel as nib
from pathlib import Path
import yaml
import shutil
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
from ultralytics import YOLO
import logging
from tqdm import tqdm
import argparse
from scipy import ndimage

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SupraspinatusYOLOTrainer:
    def __init__(self, dataset_root, output_root, img_size=640):
        """
        初始化训练器
        
        Args:
            dataset_root: 原始数据集根目录
            output_root: 输出目录
            img_size: YOLO训练图像尺寸
        """
        self.dataset_root = Path(dataset_root)
        self.output_root = Path(output_root)
        self.img_size = img_size
        
        # 创建输出目录结构
        self.yolo_dataset_root = self.output_root / "yolo_dataset"
        self.train_images = self.yolo_dataset_root / "images" / "train"
        self.val_images = self.yolo_dataset_root / "images" / "val"
        self.test_images = self.yolo_dataset_root / "images" / "test"
        self.train_labels = self.yolo_dataset_root / "labels" / "train"
        self.val_labels = self.yolo_dataset_root / "labels" / "val"
        self.test_labels = self.yolo_dataset_root / "labels" / "test"
        
        # 创建所有必要的目录
        for dir_path in [self.train_images, self.val_images, self.test_images,
                        self.train_labels, self.val_labels, self.test_labels]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 类别定义 - 只有一个类别：冈上肌撕裂
        self.classes = {
            'supraspinatus_tear': 0  # 冈上肌撕裂
        }
        
        logger.info(f"初始化完成，输出目录: {self.output_root}")
    
    def load_nii_image(self, nii_path):
        """
        加载nii.gz图像
        
        Args:
            nii_path: nii.gz文件路径
            
        Returns:
            numpy array: 图像数据
        """
        try:
            nii_img = nib.load(str(nii_path))
            img_data = nii_img.get_fdata()
            
            # 标准化到0-255范围
            img_data = np.clip(img_data, np.percentile(img_data, 1), np.percentile(img_data, 99))
            img_data = ((img_data - img_data.min()) / (img_data.max() - img_data.min()) * 255).astype(np.uint8)
            
            return img_data
        except Exception as e:
            logger.error(f"加载图像失败 {nii_path}: {e}")
            return None
    
    def extract_slices_from_volume(self, volume, max_slices=None):
        """
        从3D体积中提取2D切片
        
        Args:
            volume: 3D numpy array
            max_slices: 最大切片数量
            
        Returns:
            list: 2D切片列表
        """
        slices = []
        
        # 通常取轴向切片 (axis=2)
        for i in range(volume.shape[2]):
            slice_img = volume[:, :, i]
            
            # 跳过空白切片
            if np.sum(slice_img) > 0:
                slices.append(slice_img)
        
        # 限制切片数量
        if max_slices and len(slices) > max_slices:
            # 均匀采样
            indices = np.linspace(0, len(slices)-1, max_slices, dtype=int)
            slices = [slices[i] for i in indices]
        
        return slices
    
    def mask_to_bbox(self, mask):
        """
        将二值mask转换为YOLO格式的边界框
        使用连通组件分析来检测独立的区域，并合并多个边界框为一个
        不进行任何过滤，保留所有从mask转换而来的边界框
        
        Args:
            mask: 二值mask图像 (numpy array)
        
        Returns:
            list: [x_center, y_center, width, height] (归一化坐标) 或 None
        """
        if mask is None or mask.size == 0:
            return None
        
        # 确保mask是二值的
        binary_mask = (mask > 0).astype(np.uint8)
        
        if np.sum(binary_mask) == 0:
            return None
        
        img_height, img_width = mask.shape
        
        # 使用连通组件分析找到独立的区域
        labeled_mask, num_features = ndimage.label(binary_mask)
        
        if num_features == 0:
            return None
        
        bboxes = []
        
        # 为每个连通组件生成边界框
        for i in range(1, num_features + 1):
            component_mask = (labeled_mask == i)
            
            # 找到组件的边界框
            coords = np.where(component_mask)
            y_min, y_max = coords[0].min(), coords[0].max()
            x_min, x_max = coords[1].min(), coords[1].max()
            
            # 计算边界框尺寸
            bbox_width = x_max - x_min + 1
            bbox_height = y_max - y_min + 1
            
            # 转换为归一化坐标
            x_center = (x_min + x_max) / 2 / img_width
            y_center = (y_min + y_max) / 2 / img_height
            norm_width = bbox_width / img_width
            norm_height = bbox_height / img_height
            
            bboxes.append((x_center, y_center, norm_width, norm_height))
        
        if not bboxes:
            return None
        
        # 如果有多个边界框，合并成一个大边界框
        if len(bboxes) > 1:
            # 计算所有边界框的最小外接矩形
            min_x = min(bbox[0] - bbox[2]/2 for bbox in bboxes)  # 最左边
            max_x = max(bbox[0] + bbox[2]/2 for bbox in bboxes)  # 最右边
            min_y = min(bbox[1] - bbox[3]/2 for bbox in bboxes)  # 最上边
            max_y = max(bbox[1] + bbox[3]/2 for bbox in bboxes)  # 最下边
            
            # 计算合并后的边界框
            merged_x_center = (min_x + max_x) / 2
            merged_y_center = (min_y + max_y) / 2
            merged_width = max_x - min_x
            merged_height = max_y - min_y
            
            logger.debug(f"合并 {len(bboxes)} 个边界框为一个: center=({merged_x_center:.3f}, {merged_y_center:.3f}), size=({merged_width:.3f}, {merged_height:.3f})")
            
            return [merged_x_center, merged_y_center, merged_width, merged_height]
        
        # 只有一个边界框，直接返回
        bbox = bboxes[0]
        return [bbox[0], bbox[1], bbox[2], bbox[3]]
    
    def process_tear_images(self):
        """
        处理包含撕裂的图像，精确匹配图像层面和标签层面
        - 所有层面的图像都会被保存
        - 只有当label_T2的某个层面存在mask=1时，才为对应的image_T2层面生成YOLO标签文件
        - 无撕裂区域的层面只保存图像，不生成标签文件
        - 图像保持原样，不做任何变换（旋转、镜像等）
        
        Returns:
            list: 处理后的数据列表 [(image_path, label_path, class_id)]
                  注意：label_path可能为None（表示该层面无撕裂标注）
        """
        logger.info("开始处理撕裂图像（精确匹配图像层面和标签层面）...")
        
        tear_images_dir = self.dataset_root / "image_T2"
        tear_labels_dir = self.dataset_root / "label_T2"
        
        processed_data = []
        
        # 获取所有撕裂图像文件
        tear_files = sorted(list(tear_images_dir.glob("*.nii.gz")))
        
        for img_file in tqdm(tear_files, desc="处理撕裂图像"):
            # 对应的标签文件
            label_file = tear_labels_dir / img_file.name
            
            if not label_file.exists():
                logger.warning(f"标签文件不存在: {label_file}")
                continue
            
            # 加载图像和标签的完整volume
            img_volume = self.load_nii_image(img_file)
            label_volume = self.load_nii_image(label_file)
            
            if img_volume is None or label_volume is None:
                continue
            
            # 确保图像和标签的层数一致
            if img_volume.shape != label_volume.shape:
                logger.warning(f"图像和标签尺寸不匹配: {img_file.name}")
                continue
            
            # 遍历每个层面（轴向切片）
            num_slices = img_volume.shape[2]  # 假设第三个维度是层面
            
            for slice_idx in range(num_slices):
                # 获取当前层面的图像和标签
                img_slice = img_volume[:, :, slice_idx]
                label_slice = label_volume[:, :, slice_idx]
                
                # 处理图像：调整尺寸但不做其他变换
                img_resized = cv2.resize(img_slice, (self.img_size, self.img_size))
                
                # 归一化到0-255范围
                img_normalized = ((img_resized - img_resized.min()) / (img_resized.max() - img_resized.min()) * 255).astype(np.uint8)
                
                # 转换为3通道
                if len(img_normalized.shape) == 2:
                    img_normalized = cv2.cvtColor(img_normalized, cv2.COLOR_GRAY2RGB)
                
                # 生成文件名：使用原文件名_层面索引
                base_name = f"{img_file.stem}_slice_{slice_idx:03d}"
                img_path = self.output_root / "temp_images" / f"{base_name}.jpg"
                
                # 创建临时目录
                img_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 保存图像
                cv2.imwrite(str(img_path), img_normalized)
                
                # 检查当前层面的标签是否包含撕裂区域（mask=1）
                # 首先检查原始标签是否有非零像素
                if np.sum(label_slice > 0) > 0:
                    # 调整标签尺寸用于生成YOLO标注
                    label_resized = cv2.resize(label_slice.astype(np.uint8), (self.img_size, self.img_size))
                    
                    # 生成YOLO边界框（只有能生成有效边界框才创建txt文件）
                    bbox = self.mask_to_bbox(label_resized)
                    
                    if bbox is not None:
                        # 创建YOLO标注文件
                        label_path = img_path.with_suffix('.txt')
                        with open(label_path, 'w') as f:
                            f.write(f"{self.classes['supraspinatus_tear']} {' '.join(map(str, bbox))}\n")
                        
                        processed_data.append((str(img_path), str(label_path), self.classes['supraspinatus_tear']))
                        logger.debug(f"处理文件 {img_file.name} 第 {slice_idx} 层：发现撕裂区域，生成标注")
                    else:
                        # 虽然有标签像素，但无法生成有效边界框，只保存图像不生成标注
                        processed_data.append((str(img_path), None, self.classes['supraspinatus_tear']))
                        logger.debug(f"处理文件 {img_file.name} 第 {slice_idx} 层：无法生成边界框，只保存图像")
                else:
                    # 当前层面没有撕裂区域，只保存图像，不生成标注文件
                    processed_data.append((str(img_path), None, self.classes['supraspinatus_tear']))
                    logger.debug(f"处理文件 {img_file.name} 第 {slice_idx} 层：无撕裂区域，只保存图像")
        
        # 统计有标注和无标注的样本数量
        samples_with_labels = sum(1 for _, label_path, _ in processed_data if label_path is not None)
        samples_without_labels = len(processed_data) - samples_with_labels
        
        logger.info(f"撕裂图像处理完成，共处理 {len(processed_data)} 个层面")
        logger.info(f"  - 生成txt标注的层面: {samples_with_labels} 个（包含撕裂区域）")
        logger.info(f"  - 只保存图像的层面: {samples_without_labels} 个（无撕裂区域）")
        logger.info(f"  - 检测标准：使用连通组件分析，保留所有从mask转换的边界框，自动合并多个边界框")
        logger.info(f"  - 精确匹配图像层面和标签层面，图像保持原样无额外变换")
        return processed_data
    
    def process_normal_images(self):
        """
        处理正常图像，保持原样不做任何变换
        遍历所有层面，为每个层面生成正常样本
        
        Returns:
            list: 处理后的数据列表 [(image_path, label_path, class_id)]
        """
        logger.info("开始处理正常图像（保持原样，无变换）...")
        
        normal_images_dir = self.dataset_root / "image_T2_normal"
        processed_data = []
        
        # 获取所有正常图像文件
        normal_files = sorted(list(normal_images_dir.glob("*.nii.gz")))
        
        for img_file in tqdm(normal_files, desc="处理正常图像"):
            # 加载图像volume
            img_volume = self.load_nii_image(img_file)
            
            if img_volume is None:
                continue
            
            # 遍历每个层面（轴向切片）
            num_slices = img_volume.shape[2]  # 假设第三个维度是层面
            
            for slice_idx in range(num_slices):
                # 获取当前层面的图像
                img_slice = img_volume[:, :, slice_idx]
                
                # 处理图像：调整尺寸但不做其他变换
                img_resized = cv2.resize(img_slice, (self.img_size, self.img_size))
                
                # 归一化到0-255范围
                img_normalized = ((img_resized - img_resized.min()) / (img_resized.max() - img_resized.min()) * 255).astype(np.uint8)
                
                # 转换为3通道
                if len(img_normalized.shape) == 2:
                    img_normalized = cv2.cvtColor(img_normalized, cv2.COLOR_GRAY2RGB)
                
                # 生成文件名：使用原文件名_层面索引
                base_name = f"normal_{img_file.stem}_slice_{slice_idx:03d}"
                img_path = self.output_root / "temp_images" / f"{base_name}.jpg"
                
                # 创建临时目录
                img_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 保存图像
                cv2.imwrite(str(img_path), img_normalized)
                
                # 正常图像不生成标注文件，只保存图像（作为负样本）
                processed_data.append((str(img_path), None, -1))  # -1表示负样本（无目标）
        
        logger.info(f"正常图像处理完成，共 {len(processed_data)} 个样本")
        logger.info(f"  - 遍历所有层面，保持图像原样")
        logger.info(f"  - 正常图像不生成txt标注文件（符合YOLO训练要求）")
        logger.info(f"  - 无额外变换（旋转、镜像等）")
        return processed_data
    
    def split_dataset(self, all_data, train_ratio=0.7, val_ratio=0.2, test_ratio=0.1):
        """
        划分数据集
        
        Args:
            all_data: 所有数据列表
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            test_ratio: 测试集比例
        """
        logger.info("开始划分数据集...")
        
        # 按类别分组
        tear_data = [item for item in all_data if item[2] == self.classes['supraspinatus_tear']]
        normal_data = [item for item in all_data if item[2] == -1]  # 负样本（正常图像）
        
        logger.info(f"撕裂样本: {len(tear_data)}, 正常样本（负样本）: {len(normal_data)}")
        
        # 分别划分每个类别
        def split_class_data(class_data):
            # 先分出测试集
            train_val, test = train_test_split(class_data, test_size=test_ratio, random_state=42)
            # 再从训练验证集中分出验证集
            val_size = val_ratio / (train_ratio + val_ratio)
            train, val = train_test_split(train_val, test_size=val_size, random_state=42)
            return train, val, test
        
        tear_train, tear_val, tear_test = split_class_data(tear_data)
        normal_train, normal_val, normal_test = split_class_data(normal_data)
        
        # 合并各类别数据
        train_data = tear_train + normal_train
        val_data = tear_val + normal_val
        test_data = tear_test + normal_test
        
        logger.info(f"数据集划分完成:")
        logger.info(f"训练集: {len(train_data)} (撕裂: {len(tear_train)}, 负样本: {len(normal_train)})")
        logger.info(f"验证集: {len(val_data)} (撕裂: {len(tear_val)}, 负样本: {len(normal_val)})")
        logger.info(f"测试集: {len(test_data)} (撕裂: {len(tear_test)}, 负样本: {len(normal_test)})")
        
        # 复制文件到对应目录
        self._copy_files_to_split(train_data, 'train')
        self._copy_files_to_split(val_data, 'val')
        self._copy_files_to_split(test_data, 'test')
    
    def _copy_files_to_split(self, data_list, split_name):
        """
        复制文件到对应的数据集分割目录
        
        Args:
            data_list: 数据列表
            split_name: 分割名称 ('train', 'val', 'test')
        """
        images_dir = self.yolo_dataset_root / "images" / split_name
        labels_dir = self.yolo_dataset_root / "labels" / split_name
        
        for img_path, label_path, class_id in tqdm(data_list, desc=f"复制{split_name}数据"):
            # 复制图像
            img_name = Path(img_path).name
            shutil.copy2(img_path, images_dir / img_name)
            
            # 复制标签（如果存在）
            if label_path is not None:
                label_name = Path(label_path).name
                shutil.copy2(label_path, labels_dir / label_name)
            # 如果label_path为None，说明该图像没有对应的标注文件，只复制图像即可
    
    def create_yaml_config(self):
        """
        创建YOLO训练配置文件
        """
        config = {
            'path': str(self.yolo_dataset_root.absolute()),
            'train': 'images/train',
            'val': 'images/val',
            'test': 'images/test',
            'nc': len(self.classes),
            'names': list(self.classes.keys())
        }
        
        config_path = self.yolo_dataset_root / "dataset.yaml"
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"YOLO配置文件已创建: {config_path}")
        return config_path
    
    def train_model(self, config_path, epochs=100, batch_size=16, model_name='yolo11x.pt'):
        """
        训练YOLO11模型
        
        Args:
            config_path: 数据集配置文件路径
            epochs: 训练轮数
            batch_size: 批次大小
            model_name: 预训练模型名称 (默认: yolo11x.pt，高精度版本)
            
        Returns:
            训练结果
        """
        logger.info("开始训练YOLO模型...")
        
        # 创建模型
        model = YOLO(model_name)
        
        # 训练参数
        train_args = {
            'data': str(config_path),
            'epochs': epochs,
            'batch': batch_size,
            'imgsz': self.img_size,
            'project': str(self.output_root),
            'name': 'supraspinatus_detection',
            'save_period': 10,
            'patience': 20,
            'device': 'auto',
            'workers': 4,
            'optimizer': 'AdamW',
            'lr0': 0.01,
            'weight_decay': 0.0005,
            'warmup_epochs': 3,
            'box': 7.5,
            'cls': 0.5,
            'dfl': 1.5,
            'pose': 12.0,
            'kobj': 1.0,
            'label_smoothing': 0.0,
            'nbs': 64,
            'hsv_h': 0.015,
            'hsv_s': 0.7,
            'hsv_v': 0.4,
            'degrees': 0.0,
            'translate': 0.1,
            'scale': 0.5,
            'shear': 0.0,
            'perspective': 0.0,
            'flipud': 0.0,
            'fliplr': 0.5,
            'mosaic': 1.0,
            'mixup': 0.0,
            'copy_paste': 0.0
        }
        
        # 开始训练
        results = model.train(**train_args)
        
        logger.info("模型训练完成!")
        return results
    
    def process_sliced_data(self):
        """
        处理sliced_output目录中的预处理数据
        直接使用已经切片和标注好的数据，包含2076个标签
        """
        logger.info("开始处理预切片数据...")
        
        # 定义路径
        sliced_images_dir = Path("e:/Trae/yolo_ohif/sliced_output/sliced_images/image_T2")
        bbox_labels_dir = Path("e:/Trae/yolo_ohif/sliced_output/bbox_labels")
        
        if not sliced_images_dir.exists():
            raise FileNotFoundError(f"切片图像目录不存在: {sliced_images_dir}")
        if not bbox_labels_dir.exists():
            raise FileNotFoundError(f"标签目录不存在: {bbox_labels_dir}")
        
        # 创建输出目录
        self.output_root.mkdir(parents=True, exist_ok=True)
        temp_images_dir = self.output_root / "temp_images"
        temp_images_dir.mkdir(exist_ok=True)
        
        all_data = []
        
        # 获取所有图像文件
        image_files = list(sliced_images_dir.glob("*.jpg"))
        logger.info(f"找到 {len(image_files)} 个图像文件")
        
        positive_samples = 0
        negative_samples = 0
        
        for img_file in tqdm(image_files, desc="处理切片数据"):
            # 获取对应的标签文件
            label_file = bbox_labels_dir / f"{img_file.stem}.txt"
            
            # 复制图像到临时目录
            temp_img_path = temp_images_dir / img_file.name
            shutil.copy2(img_file, temp_img_path)
            
            # 检查是否有对应的标签文件
            if label_file.exists():
                # 有标签的正样本
                temp_label_path = temp_images_dir / f"{img_file.stem}.txt"
                shutil.copy2(label_file, temp_label_path)
                all_data.append({
                    'image_path': str(temp_img_path),
                    'label_path': str(temp_label_path),
                    'has_annotation': True
                })
                positive_samples += 1
            else:
                # 无标签的负样本
                all_data.append({
                    'image_path': str(temp_img_path),
                    'label_path': None,
                    'has_annotation': False
                })
                negative_samples += 1
        
        logger.info(f"处理完成: 正样本 {positive_samples} 个, 负样本 {negative_samples} 个")
        logger.info(f"总样本数: {len(all_data)} 个")
        
        return all_data
    
    def run_full_pipeline(self, epochs=100, batch_size=16, use_sliced_data=False):
        """
        运行完整的训练流程
        
        Args:
            epochs: 训练轮数
            batch_size: 批次大小
            use_sliced_data: 是否使用预切片数据，默认False使用原始nii.gz处理流程
        """
        logger.info("开始完整的YOLO训练流程...")
        
        try:
            if use_sliced_data:
                # 使用预处理的切片数据(2076个标签)
                logger.info("使用预处理的切片数据(包含2076个标签)")
                all_data = self.process_sliced_data()
            else:
                # 使用原始的处理方式：从nii.gz转jpg再转bbox
                logger.info("使用原始数据处理方式：从nii.gz图像和mask开始完整流程")
                tear_data = self.process_tear_images()
                normal_data = self.process_normal_images()
                all_data = tear_data + normal_data
            
            logger.info(f"总共处理了 {len(all_data)} 个样本")
            
            # 2. 划分数据集
            self.split_dataset(all_data)
            
            # 3. 创建配置文件
            config_path = self.create_yaml_config()
            
            # 4. 训练模型
            results = self.train_model(config_path, epochs=epochs, batch_size=batch_size)
            
            # 5. 清理临时文件
            temp_dir = self.output_root / "temp_images"
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
                logger.info("临时文件已清理")
            
            logger.info("训练流程完成!")
            return results
            
        except Exception as e:
            logger.error(f"训练流程出错: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description='冈上肌肌腱撕裂YOLO检测模型训练')
    parser.add_argument('--dataset_root', type=str, default='./dataset', help='数据集根目录')
    parser.add_argument('--output_root', type=str, default='./yolo_training_output', help='输出目录')
    parser.add_argument('--img_size', type=int, default=640, help='图像尺寸')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=16, help='批次大小')
    parser.add_argument('--use_sliced_data', action='store_true', default=False, help='使用预处理的切片数据(包含2076个标签)')
    parser.add_argument('--use_original_data', action='store_true', help='使用原始数据处理方式')
    
    args = parser.parse_args()
    
    # 如果指定了use_original_data，则不使用切片数据
    use_sliced = args.use_sliced_data and not args.use_original_data
    
    # 创建训练器
    trainer = SupraspinatusYOLOTrainer(
        dataset_root=args.dataset_root,
        output_root=args.output_root,
        img_size=args.img_size
    )
    
    # 运行训练流程
    trainer.run_full_pipeline(epochs=args.epochs, batch_size=args.batch_size, use_sliced_data=use_sliced)

if __name__ == "__main__":
    main()