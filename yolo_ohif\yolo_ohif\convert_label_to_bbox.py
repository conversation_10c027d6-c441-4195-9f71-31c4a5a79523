#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将 label_T2 目录中的切片图像转换为YOLO格式的边界框标注
"""

import os
import cv2
import numpy as np
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def mask_to_bbox(mask, min_area_threshold=10, min_bbox_size=5, max_bbox_ratio=0.95, min_norm_size=0.01):
    """
    将二值mask转换为YOLO格式的边界框
    使用连通组件分析来检测独立的区域
    
    Args:
        mask: 二值mask图像 (numpy array)
        min_area_threshold: 最小面积阈值
        min_bbox_size: 最小边界框尺寸
        max_bbox_ratio: 边界框相对于图像的最大尺寸比例（0.95表示不超过图像95%的尺寸）
        min_norm_size: 归一化后的最小边界框尺寸（0.01表示不小于图像1%的尺寸）
    
    Returns:
        list: 包含多个边界框的列表，每个边界框为(x_center, y_center, width, height) 归一化坐标
    """
    # 使用连通组件分析检测独立区域
    retval, labels, stats, centroids = cv2.connectedComponentsWithStats(mask, connectivity=8)
    
    # stats格式: [x, y, width, height, area]
    # 排除背景（第一个组件）
    valid_components = stats[1:]
    
    if len(valid_components) == 0:
        return []
    
    # 按面积排序，保留较大的组件
    valid_components = valid_components[valid_components[:, 4].argsort()]
    
    bboxes = []
    img_height, img_width = mask.shape
    
    for component in valid_components:
        x, y, w, h, area = component
        
        # 检查面积阈值
        if area < min_area_threshold:
            continue
            
        # 检查边界框尺寸
        if w < min_bbox_size or h < min_bbox_size:
            continue
        
        # 计算YOLO格式的归一化坐标
        x_center = (x + w / 2) / img_width
        y_center = (y + h / 2) / img_height
        norm_width = w / img_width
        norm_height = h / img_height
        
        # 过滤掉覆盖整张图像或接近整张图像的边界框
        if norm_width > max_bbox_ratio or norm_height > max_bbox_ratio:
            logger.debug(f"跳过过大的边界框: width={norm_width:.3f}, height={norm_height:.3f}")
            continue
        
        # 过滤掉面积占比过大的边界框（超过图像面积的90%）
        bbox_area_ratio = norm_width * norm_height
        if bbox_area_ratio > 0.9:
            logger.debug(f"跳过面积过大的边界框: area_ratio={bbox_area_ratio:.3f}")
            continue
        
        # 过滤掉过小的边界框（可能是噪声）
        if norm_width < min_norm_size or norm_height < min_norm_size:
            logger.debug(f"跳过过小的边界框: width={norm_width:.3f}, height={norm_height:.3f}")
            continue
        
        bboxes.append((x_center, y_center, norm_width, norm_height))
    
    # 如果有多个边界框，合并成一个大边界框
    if len(bboxes) > 1:
        # 计算所有边界框的最小外接矩形
        min_x = min(bbox[0] - bbox[2]/2 for bbox in bboxes)  # 最左边
        max_x = max(bbox[0] + bbox[2]/2 for bbox in bboxes)  # 最右边
        min_y = min(bbox[1] - bbox[3]/2 for bbox in bboxes)  # 最上边
        max_y = max(bbox[1] + bbox[3]/2 for bbox in bboxes)  # 最下边
        
        # 计算合并后的边界框
        merged_x_center = (min_x + max_x) / 2
        merged_y_center = (min_y + max_y) / 2
        merged_width = max_x - min_x
        merged_height = max_y - min_y
        
        logger.debug(f"合并 {len(bboxes)} 个边界框为一个: center=({merged_x_center:.3f}, {merged_y_center:.3f}), size=({merged_width:.3f}, {merged_height:.3f})")
        
        return [(merged_x_center, merged_y_center, merged_width, merged_height)]
    
    return bboxes

def merge_nearby_bboxes(bboxes, merge_threshold=0.1):
    """
    合并相近的边界框
    
    Args:
        bboxes: 边界框列表，每个边界框为(x_center, y_center, width, height)
        merge_threshold: 合并阈值，两个边界框中心距离小于此值时合并
    
    Returns:
        list: 合并后的边界框列表
    """
    if len(bboxes) <= 1:
        return bboxes
    
    merged = []
    used = set()
    
    for i, bbox1 in enumerate(bboxes):
        if i in used:
            continue
            
        x1, y1, w1, h1 = bbox1
        group = [bbox1]
        used.add(i)
        
        # 查找需要合并的边界框
        for j, bbox2 in enumerate(bboxes):
            if j in used or i == j:
                continue
                
            x2, y2, w2, h2 = bbox2
            
            # 计算中心距离
            distance = ((x1 - x2) ** 2 + (y1 - y2) ** 2) ** 0.5
            
            # 如果距离小于阈值，则合并
            if distance < merge_threshold:
                group.append(bbox2)
                used.add(j)
        
        # 合并组内的所有边界框
        if len(group) == 1:
            merged.append(group[0])
        else:
            # 计算合并后的边界框
            min_x = min(x - w/2 for x, y, w, h in group)
            max_x = max(x + w/2 for x, y, w, h in group)
            min_y = min(y - h/2 for x, y, w, h in group)
            max_y = max(y + h/2 for x, y, w, h in group)
            
            merged_x = (min_x + max_x) / 2
            merged_y = (min_y + max_y) / 2
            merged_w = max_x - min_x
            merged_h = max_y - min_y
            
            merged.append((merged_x, merged_y, merged_w, merged_h))
            
            logger.debug(f"合并了 {len(group)} 个边界框")
    
    return merged

def process_label_images(input_dir, output_dir):
    """
    处理label_T2目录中的图像，生成YOLO格式的边界框标注
    
    Args:
        input_dir: 输入目录路径
        output_dir: 输出目录路径
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # 创建输出目录
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 统计信息
    total_images = 0
    generated_labels = 0
    skipped_images = 0
    
    logger.info(f"开始处理 {input_path} 中的图像...")
    
    # 遍历所有jpg文件
    for img_file in sorted(input_path.glob("*.jpg")):
        total_images += 1
        
        # 读取图像
        img = cv2.imread(str(img_file), cv2.IMREAD_GRAYSCALE)
        if img is None:
            logger.warning(f"无法读取图像: {img_file}")
            skipped_images += 1
            continue
        
        # 检查图像中是否有白色像素（值为255，表示标签区域）
        white_pixels = np.sum(img == 255)
        
        if white_pixels == 0:
            # 如果没有白色像素，跳过此图像
            skipped_images += 1
            logger.debug(f"跳过图像（无白色标签区域）: {img_file.name}")
            continue
        
        # 将图像转换为二值mask（只检测白色像素，值为255）
        mask = (img == 255).astype(np.uint8)
        
        # 转换为边界框
        bboxes = mask_to_bbox(mask)
        
        if len(bboxes) > 0:
            # 生成对应的txt文件名
            txt_filename = img_file.stem + ".txt"
            txt_filepath = output_path / txt_filename
            
            # 写入YOLO格式标注（类别0表示冈上肌撕裂）
            with open(txt_filepath, 'w') as f:
                for bbox in bboxes:
                    x_center, y_center, width, height = bbox
                    f.write(f"0 {x_center:.8f} {y_center:.8f} {width:.8f} {height:.8f}\n")
            
            generated_labels += 1
            logger.info(f"生成标注: {txt_filename} (白色像素数: {white_pixels}, 检测到 {len(bboxes)} 个区域)")
        else:
            skipped_images += 1
            logger.debug(f"跳过图像（白色区域过小）: {img_file.name} (白色像素数: {white_pixels})")
    
    # 输出统计信息
    logger.info("\n=== 处理完成 ===")
    logger.info(f"总图像数: {total_images}")
    logger.info(f"生成标注文件: {generated_labels}")
    logger.info(f"跳过图像: {skipped_images}")
    logger.info(f"标注生成率: {generated_labels/total_images*100:.1f}%")
    logger.info(f"输出目录: {output_path}")

def main():
    # 设置路径
    input_dir = r"E:\Trae\yolo_ohif\sliced_output\sliced_images\label_T2"
    output_dir = r"E:\Trae\yolo_ohif\sliced_output\bbox_labels"
    
    # 检查输入目录是否存在
    if not os.path.exists(input_dir):
        logger.error(f"输入目录不存在: {input_dir}")
        return
    
    # 处理图像
    process_label_images(input_dir, output_dir)
    
    logger.info("\n标注转换完成！")
    logger.info(f"生成的YOLO标注文件保存在: {output_dir}")
    logger.info("\n使用说明:")
    logger.info("- 只有包含撕裂区域的图像才会生成对应的txt标注文件")
    logger.info("- 标注格式: 类别 x_center y_center width height（归一化坐标）")
    logger.info("- 类别0表示冈上肌撕裂")
    logger.info("- 最小面积阈值: 5像素")
    logger.info("- 最小边界框尺寸: 3x3像素")

if __name__ == "__main__":
    main()