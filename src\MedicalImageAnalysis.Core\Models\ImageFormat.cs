namespace MedicalImageAnalysis.Core.Models;

/// <summary>
/// 图像格式枚举
/// </summary>
public enum ImageFormat
{
    /// <summary>
    /// PNG格式
    /// </summary>
    Png = 0,

    /// <summary>
    /// JPEG格式
    /// </summary>
    Jpeg = 1,

    /// <summary>
    /// BMP格式
    /// </summary>
    Bmp = 2,

    /// <summary>
    /// TIFF格式
    /// </summary>
    Tiff = 3,

    /// <summary>
    /// WebP格式
    /// </summary>
    WebP = 4,

    /// <summary>
    /// GIF格式
    /// </summary>
    Gif = 5,

    /// <summary>
    /// TGA格式
    /// </summary>
    Tga = 6
}

/// <summary>
/// 图像格式扩展方法
/// </summary>
public static class ImageFormatExtensions
{
    /// <summary>
    /// 获取文件扩展名
    /// </summary>
    /// <param name="format">图像格式</param>
    /// <returns>文件扩展名</returns>
    public static string GetFileExtension(this ImageFormat format)
    {
        return format switch
        {
            ImageFormat.Png => ".png",
            ImageFormat.Jpeg => ".jpg",
            ImageFormat.Bmp => ".bmp",
            ImageFormat.Tiff => ".tiff",
            ImageFormat.WebP => ".webp",
            ImageFormat.Gif => ".gif",
            ImageFormat.Tga => ".tga",
            _ => ".png"
        };
    }

    /// <summary>
    /// 获取MIME类型
    /// </summary>
    /// <param name="format">图像格式</param>
    /// <returns>MIME类型</returns>
    public static string GetMimeType(this ImageFormat format)
    {
        return format switch
        {
            ImageFormat.Png => "image/png",
            ImageFormat.Jpeg => "image/jpeg",
            ImageFormat.Bmp => "image/bmp",
            ImageFormat.Tiff => "image/tiff",
            ImageFormat.WebP => "image/webp",
            ImageFormat.Gif => "image/gif",
            ImageFormat.Tga => "image/x-tga",
            _ => "image/png"
        };
    }

    /// <summary>
    /// 从文件扩展名解析图像格式
    /// </summary>
    /// <param name="extension">文件扩展名</param>
    /// <returns>图像格式</returns>
    public static ImageFormat FromExtension(string extension)
    {
        return extension.ToLowerInvariant() switch
        {
            ".png" => ImageFormat.Png,
            ".jpg" or ".jpeg" => ImageFormat.Jpeg,
            ".bmp" => ImageFormat.Bmp,
            ".tiff" or ".tif" => ImageFormat.Tiff,
            ".webp" => ImageFormat.WebP,
            ".gif" => ImageFormat.Gif,
            ".tga" => ImageFormat.Tga,
            _ => ImageFormat.Png
        };
    }
}
