<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化DICOM测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        #dicomViewport {
            width: 512px;
            height: 512px;
            border: 1px solid #ccc;
            margin: 20px 0;
            background-color: #000;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>简化DICOM图像加载测试</h1>
        
        <div class="test-section">
            <h3>1. JavaScript库加载状态</h3>
            <div id="library-status">检查中...</div>
        </div>
        
        <div class="test-section">
            <h3>2. API连接测试</h3>
            <button onclick="testAPI()">测试API连接</button>
            <div id="api-status">点击按钮开始测试</div>
        </div>
        
        <div class="test-section">
            <h3>3. DICOM图像加载测试</h3>
            <button onclick="testDICOMLoad()" id="dicom-test-btn">测试DICOM加载</button>
            <div id="dicomViewport"></div>
            <div id="dicom-status">点击按钮开始测试</div>
        </div>
        
        <div class="test-section">
            <h3>4. 详细日志</h3>
            <button onclick="clearLog()">清除日志</button>
            <div id="log"></div>
        </div>
    </div>

    <!-- 加载Cornerstone库 -->
    <script src="https://unpkg.com/cornerstone-core@2.6.1/dist/cornerstone.min.js"></script>
    <script src="https://unpkg.com/dicom-parser@1.8.21/dist/dicomParser.min.js"></script>
    <script src="https://unpkg.com/cornerstone-wado-image-loader@4.1.3/dist/cornerstoneWADOImageLoader.bundle.min.js"></script>

    <script>
        let logElement = null;
        
        function log(message, type = 'info') {
            if (!logElement) {
                logElement = document.getElementById('log');
            }
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logMessage);
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        // 检查库加载状态
        function checkLibraries() {
            const statusDiv = document.getElementById('library-status');
            let statusHtml = '';
            
            // 检查Cornerstone
            if (typeof cornerstone !== 'undefined') {
                statusHtml += '<div class="status success">✓ Cornerstone Core: 已加载</div>';
                log('Cornerstone Core 加载成功');
            } else {
                statusHtml += '<div class="status error">✗ Cornerstone Core: 未加载</div>';
                log('Cornerstone Core 加载失败', 'error');
            }
            
            // 检查DICOM Parser
            if (typeof dicomParser !== 'undefined') {
                statusHtml += '<div class="status success">✓ DICOM Parser: 已加载</div>';
                log('DICOM Parser 加载成功');
            } else {
                statusHtml += '<div class="status error">✗ DICOM Parser: 未加载</div>';
                log('DICOM Parser 加载失败', 'error');
            }
            
            // 检查WADO Image Loader
            if (typeof cornerstoneWADOImageLoader !== 'undefined') {
                statusHtml += '<div class="status success">✓ WADO Image Loader: 已加载</div>';
                log('WADO Image Loader 加载成功');
            } else {
                statusHtml += '<div class="status error">✗ WADO Image Loader: 未加载</div>';
                log('WADO Image Loader 加载失败', 'error');
            }
            
            statusDiv.innerHTML = statusHtml;
        }
        
        // 测试API连接
        async function testAPI() {
            const statusDiv = document.getElementById('api-status');
            statusDiv.innerHTML = '<div class="status warning">测试中...</div>';
            
            const endpoints = [
                { name: 'Health Check', url: '/api/health' },
                { name: 'Studies', url: '/api/studies' },
                { name: 'Models', url: '/api/models' }
            ];
            
            let statusHtml = '';
            
            for (const endpoint of endpoints) {
                try {
                    log(`测试 ${endpoint.name} API: ${endpoint.url}`);
                    const response = await fetch(endpoint.url);
                    if (response.ok) {
                        statusHtml += `<div class="status success">✓ ${endpoint.name}: 连接正常 (${response.status})</div>`;
                        log(`${endpoint.name} API 测试成功: ${response.status}`);
                    } else {
                        statusHtml += `<div class="status error">✗ ${endpoint.name}: ${response.status} ${response.statusText}</div>`;
                        log(`${endpoint.name} API 测试失败: ${response.status}`, 'error');
                    }
                } catch (error) {
                    statusHtml += `<div class="status error">✗ ${endpoint.name}: ${error.message}</div>`;
                    log(`${endpoint.name} API 错误: ${error.message}`, 'error');
                }
            }
            
            statusDiv.innerHTML = statusHtml;
        }
        
        // 测试DICOM图像加载
        async function testDICOMLoad() {
            const statusDiv = document.getElementById('dicom-status');
            const viewport = document.getElementById('dicomViewport');
            const button = document.getElementById('dicom-test-btn');
            
            button.disabled = true;
            statusDiv.innerHTML = '<div class="status warning">测试中...</div>';
            
            try {
                // 检查库是否加载
                if (typeof cornerstone === 'undefined') {
                    throw new Error('Cornerstone库未加载');
                }
                
                log('开始DICOM加载测试');
                
                // 启用Cornerstone
                cornerstone.enable(viewport);
                log('Cornerstone 已启用');
                
                // 配置WADO图像加载器
                if (typeof cornerstoneWADOImageLoader !== 'undefined') {
                    cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
                    cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
                    
                    // 配置WADO加载器
                    cornerstoneWADOImageLoader.configure({
                        useWebWorkers: true,
                        decodeConfig: {
                            convertFloatPixelDataToInt: false
                        },
                        beforeSend: function(xhr) {
                            xhr.timeout = 30000;
                            log('设置DICOM请求超时: 30秒');
                        }
                    });
                    
                    // 注册图像加载器
                    cornerstone.registerImageLoader('wadouri', cornerstoneWADOImageLoader.wadouri.loadImage);
                    log('WADO图像加载器已配置');
                }
                
                // 首先测试一个简单的API调用
                log('测试基本API连接...');
                const healthResponse = await fetch('/api/health');
                if (!healthResponse.ok) {
                    throw new Error(`API健康检查失败: ${healthResponse.status}`);
                }
                log('API连接正常');
                
                // 获取可用的研究列表
                log('获取可用的研究列表...');
                const studiesResponse = await fetch('/api/studies');
                if (!studiesResponse.ok) {
                    throw new Error(`无法获取研究列表: ${studiesResponse.status} ${studiesResponse.statusText}`);
                }
                
                const studiesData = await studiesResponse.json();
                log(`研究API响应: ${JSON.stringify(studiesData)}`);
                
                // API直接返回研究数组，不是包装在success对象中
                if (!Array.isArray(studiesData) || studiesData.length === 0) {
                    throw new Error('没有可用的研究数据');
                }
                
                const firstStudy = studiesData[0];
                log(`使用研究: ${firstStudy.id}`);
                
                // 获取研究的序列
                log('获取研究序列...');
                const studyResponse = await fetch(`/api/studies/${firstStudy.id}`);
                if (!studyResponse.ok) {
                    throw new Error(`无法获取研究详情: ${studyResponse.status} ${studyResponse.statusText}`);
                }
                
                const studyData = await studyResponse.json();
                log(`研究详情API响应: ${JSON.stringify(studyData)}`);
                
                if (!studyData.success || !studyData.series || studyData.series.length === 0) {
                    throw new Error('研究中没有可用的序列');
                }
                
                const firstSeries = studyData.series[0];
                log(`使用序列: ${firstSeries.id}`);
                
                // 获取序列的图像
                log('获取序列图像...');
                const imagesResponse = await fetch(`/api/series/${firstSeries.id}/images`);
                if (!imagesResponse.ok) {
                    throw new Error(`无法获取图像列表: ${imagesResponse.status} ${imagesResponse.statusText}`);
                }
                
                const imagesData = await imagesResponse.json();
                log(`图像列表API响应: ${JSON.stringify(imagesData)}`);
                
                if (!imagesData.success || !imagesData.images || imagesData.images.length === 0) {
                    throw new Error('序列中没有可用的图像');
                }
                
                const firstImage = imagesData.images[0];
                log(`使用图像: ${firstImage.id}`);
                
                // 测试DICOM端点是否可访问
                const dicomEndpoint = `/api/images/${firstImage.id}/dicom`;
                log(`测试DICOM端点: ${dicomEndpoint}`);
                
                const dicomTestResponse = await fetch(dicomEndpoint, { method: 'HEAD' });
                if (!dicomTestResponse.ok) {
                    throw new Error(`DICOM端点不可访问: ${dicomTestResponse.status} ${dicomTestResponse.statusText}`);
                }
                log('DICOM端点可访问');
                
                // 构建图像ID
                const imageId = `wadouri:${dicomEndpoint}`;
                log(`加载图像: ${imageId}`);
                
                // 加载并显示图像
                const image = await cornerstone.loadImage(imageId);
                log('图像加载成功');
                log(`图像信息: 宽度=${image.width}, 高度=${image.height}, 像素类型=${image.color ? '彩色' : '灰度'}`);
                
                cornerstone.displayImage(viewport, image);
                log('图像显示成功');
                
                statusDiv.innerHTML = '<div class="status success">✓ DICOM图像加载成功</div>';
                
            } catch (error) {
                log(`DICOM 加载失败: ${error.message}`, 'error');
                log(`错误堆栈: ${error.stack}`, 'error');
                statusDiv.innerHTML = `<div class="status error">✗ DICOM加载失败: ${error.message}</div>`;
            } finally {
                button.disabled = false;
            }
        }
        
        // 页面加载完成后自动检查库状态
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('页面加载完成，开始检查库状态...');
                checkLibraries();
            }, 1000);
        });
    </script>
</body>
</html>