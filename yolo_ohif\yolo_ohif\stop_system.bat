@echo off
echo 正在停止YOLO-OHIF医学影像检测系统...

:: 停止Flask应用进程
echo 正在停止Flask应用...
for /f "tokens=2" %%a in ('tasklist /fi "windowtitle eq YOLO-OHIF Flask应用" /fo list ^| find "PID:"') do (
    taskkill /pid %%a /f > nul 2>&1
)

:: 停止Orthanc服务器进程
echo 正在停止Orthanc DICOM服务器...
for /f "tokens=2" %%a in ('tasklist /fi "windowtitle eq Orthanc DICOM服务器" /fo list ^| find "PID:"') do (
    taskkill /pid %%a /f > nul 2>&1
)

:: 停止Python相关进程
echo 正在停止所有相关Python进程...
for /f "tokens=2" %%a in ('tasklist /fi "imagename eq python.exe" /fo list ^| find "PID:"') do (
    wmic process where "ProcessID=%%a" get CommandLine | find "run.py" > nul
    if not errorlevel 1 (
        taskkill /pid %%a /f > nul 2>&1
    )
)

echo.
echo YOLO-OHIF医学影像检测系统已停止！
echo.

timeout /t 3 > nul