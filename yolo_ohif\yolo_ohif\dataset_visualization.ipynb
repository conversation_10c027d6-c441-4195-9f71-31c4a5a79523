{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# YOLO数据集可视化\n", "\n", "本notebook用于可视化和验证冈上肌撕裂检测的YOLO数据集，确保图像和标签正确匹配。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import os\n", "import cv2\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as patches\n", "from pathlib import Path\n", "import random\n", "from IPython.display import display, HTML\n", "\n", "# 设置matplotlib中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 设置图像显示大小\n", "plt.rcParams['figure.figsize'] = [12, 8]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入自定义可视化工具\n", "from visualize_dataset import YOLODatasetVisualizer\n", "\n", "# 设置数据集路径\n", "DATASET_ROOT = r'E:\\Trae\\yolo_ohif\\yolo_training_output\\temp_images'\n", "\n", "# 创建可视化器\n", "visualizer = YOLODatasetVisualizer(DATASET_ROOT)\n", "\n", "print(f'数据集路径: {DATASET_ROOT}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 数据集完整性检查\n", "\n", "首先检查数据集的完整性，确保图像和标签文件正确匹配。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 检查数据集完整性\n", "visualizer.check_dataset_integrity()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 训练集样本可视化\n", "\n", "随机选择一些训练集样本进行可视化，查看图像和标注是否正确匹配。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 获取训练集图像文件\n", "train_images_dir = Path(DATASET_ROOT) / 'images' / 'train'\n", "train_labels_dir = Path(DATASET_ROOT) / 'labels' / 'train'\n", "\n", "if train_images_dir.exists():\n", "    image_files = list(train_images_dir.glob('*.jpg')) + list(train_images_dir.glob('*.png'))\n", "    print(f'训练集图像数量: {len(image_files)}')\n", "    \n", "    # 随机选择5个样本\n", "    if image_files:\n", "        sample_files = random.sample(image_files, min(5, len(image_files)))\n", "        print(f'选择 {len(sample_files)} 个样本进行可视化')\n", "    else:\n", "        print('没有找到图像文件')\n", "else:\n", "    print(f'训练集目录不存在: {train_images_dir}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 可视化训练集样本\n", "if 'sample_files' in locals() and sample_files:\n", "    for i, img_path in enumerate(sample_files):\n", "        label_path = train_labels_dir / (img_path.stem + '.txt')\n", "        \n", "        print(f'\\n=== 样本 {i+1}: {img_path.name} ===')\n", "        \n", "        # 加载图像\n", "        img = cv2.imread(str(img_path))\n", "        if img is None:\n", "            print(f'无法加载图像: {img_path}')\n", "            continue\n", "        \n", "        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\n", "        img_height, img_width = img.shape[:2]\n", "        \n", "        # 加载标注\n", "        annotations = visualizer.load_yolo_annotations(label_path)\n", "        \n", "        # 创建图像显示\n", "        fig, ax = plt.subplots(1, 1, figsize=(12, 8))\n", "        ax.imshow(img)\n", "        \n", "        # 绘制边界框\n", "        for class_id, x_center, y_center, width, height in annotations:\n", "            x_min, y_min, x_max, y_max = visualizer.yolo_to_bbox(\n", "                x_center, y_center, width, height, img_width, img_height\n", "            )\n", "            \n", "            # 创建矩形框\n", "            color = 'red' if class_id == 0 else 'green'\n", "            rect = patches.Rectangle(\n", "                (x_min, y_min), x_max - x_min, y_max - y_min,\n", "                linewidth=3, edgecolor=color, facecolor='none'\n", "            )\n", "            ax.add_patch(rect)\n", "            \n", "            # 添加类别标签\n", "            class_name = '冈上肌撕裂' if class_id == 0 else '正常'\n", "            ax.text(x_min, y_min - 5, class_name, \n", "                   color=color, fontsize=14, fontweight='bold',\n", "                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))\n", "        \n", "        # 设置标题\n", "        title = f'图像: {img_path.name} | 尺寸: {img_width}x{img_height}'\n", "        if annotations:\n", "            title += f' | 检测目标: {len(annotations)} 个'\n", "        else:\n", "            title += ' | 无检测目标'\n", "        \n", "        if label_path.exists():\n", "            title += f' | 标签文件: {label_path.name}'\n", "        else:\n", "            title += ' | 无标签文件'\n", "        \n", "        ax.set_title(title, fontsize=12, pad=15)\n", "        ax.axis('off')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # 显示标注详情\n", "        if annotations:\n", "            print(f'标注详情:')\n", "            for j, (class_id, x_center, y_center, width, height) in enumerate(annotations):\n", "                class_name = '冈上肌撕裂' if class_id == 0 else '正常'\n", "                print(f'  目标 {j+1}: {class_name} | 中心: ({x_center:.3f}, {y_center:.3f}) | 尺寸: {width:.3f}x{height:.3f}')\n", "        else:\n", "            print('无标注信息')\n", "else:\n", "    print('没有可用的样本文件')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 验证集样本可视化\n", "\n", "查看验证集的样本，确保验证数据的质量。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 获取验证集图像文件\n", "val_images_dir = Path(DATASET_ROOT) / 'images' / 'val'\n", "val_labels_dir = Path(DATASET_ROOT) / 'labels' / 'val'\n", "\n", "if val_images_dir.exists():\n", "    val_image_files = list(val_images_dir.glob('*.jpg')) + list(val_images_dir.glob('*.png'))\n", "    print(f'验证集图像数量: {len(val_image_files)}')\n", "    \n", "    # 随机选择3个样本\n", "    if val_image_files:\n", "        val_sample_files = random.sample(val_image_files, min(3, len(val_image_files)))\n", "        print(f'选择 {len(val_sample_files)} 个验证样本进行可视化')\n", "        \n", "        for i, img_path in enumerate(val_sample_files):\n", "            label_path = val_labels_dir / (img_path.stem + '.txt')\n", "            print(f'\\n=== 验证样本 {i+1}: {img_path.name} ===')\n", "            visualizer.visualize_single_image(img_path, label_path)\n", "    else:\n", "        print('验证集中没有找到图像文件')\n", "else:\n", "    print(f'验证集目录不存在: {val_images_dir}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 数据集统计分析\n", "\n", "分析数据集的类别分布和标注统计。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 统计数据集信息\n", "def analyze_dataset_statistics(dataset_root):\n", "    stats = {}\n", "    \n", "    for split in ['train', 'val', 'test']:\n", "        images_dir = Path(dataset_root) / 'images' / split\n", "        labels_dir = Path(dataset_root) / 'labels' / split\n", "        \n", "        if not images_dir.exists():\n", "            continue\n", "        \n", "        # 统计图像和标签\n", "        image_files = list(images_dir.glob('*.jpg')) + list(images_dir.glob('*.png'))\n", "        label_files = list(labels_dir.glob('*.txt'))\n", "        \n", "        # 统计类别\n", "        class_counts = {0: 0, 1: 0}  # 0: 撕裂, 1: 正常\n", "        total_objects = 0\n", "        images_with_objects = 0\n", "        \n", "        for label_file in label_files:\n", "            if label_file.stat().st_size > 0:  # 非空标签文件\n", "                images_with_objects += 1\n", "                with open(label_file, 'r') as f:\n", "                    for line in f.readlines():\n", "                        line = line.strip()\n", "                        if line:\n", "                            parts = line.split()\n", "                            if len(parts) >= 5:\n", "                                class_id = int(parts[0])\n", "                                class_counts[class_id] = class_counts.get(class_id, 0) + 1\n", "                                total_objects += 1\n", "        \n", "        stats[split] = {\n", "            'images': len(image_files),\n", "            'labels': len(label_files),\n", "            'images_with_objects': images_with_objects,\n", "            'total_objects': total_objects,\n", "            'tear_objects': class_counts.get(0, 0),\n", "            'normal_objects': class_counts.get(1, 0)\n", "        }\n", "    \n", "    return stats\n", "\n", "# 分析数据集\n", "stats = analyze_dataset_statistics(DATASET_ROOT)\n", "\n", "# 显示统计结果\n", "print('=== 数据集统计分析 ===\\n')\n", "\n", "for split, data in stats.items():\n", "    print(f'{split.upper()} 数据集:')\n", "    print(f'  图像文件: {data[\"images\"]} 个')\n", "    print(f'  标签文件: {data[\"labels\"]} 个')\n", "    print(f'  包含目标的图像: {data[\"images_with_objects\"]} 个')\n", "    print(f'  总目标数: {data[\"total_objects\"]} 个')\n", "    print(f'  撕裂目标: {data[\"tear_objects\"]} 个')\n", "    print(f'  正常目标: {data[\"normal_objects\"]} 个')\n", "    if data['total_objects'] > 0:\n", "        tear_ratio = data['tear_objects'] / data['total_objects'] * 100\n", "        print(f'  撕裂比例: {tear_ratio:.1f}%')\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 绘制数据集分布图\n", "if stats:\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    # 1. 各分割的图像数量\n", "    splits = list(stats.keys())\n", "    image_counts = [stats[split]['images'] for split in splits]\n", "    \n", "    axes[0, 0].bar(splits, image_counts, color=['skyblue', 'lightgreen', 'lightcoral'])\n", "    axes[0, 0].set_title('各数据集分割的图像数量')\n", "    axes[0, 0].set_ylabel('图像数量')\n", "    for i, v in enumerate(image_counts):\n", "        axes[0, 0].text(i, v + max(image_counts) * 0.01, str(v), ha='center')\n", "    \n", "    # 2. 目标类别分布\n", "    total_tear = sum(stats[split]['tear_objects'] for split in splits)\n", "    total_normal = sum(stats[split]['normal_objects'] for split in splits)\n", "    \n", "    if total_tear + total_normal > 0:\n", "        axes[0, 1].pie([total_tear, total_normal], \n", "                      labels=['冈上肌撕裂', '正常'], \n", "                      colors=['red', 'green'],\n", "                      autopct='%1.1f%%')\n", "        axes[0, 1].set_title('目标类别分布')\n", "    \n", "    # 3. 各分割的目标数量\n", "    tear_counts = [stats[split]['tear_objects'] for split in splits]\n", "    normal_counts = [stats[split]['normal_objects'] for split in splits]\n", "    \n", "    x = np.arange(len(splits))\n", "    width = 0.35\n", "    \n", "    axes[1, 0].bar(x - width/2, tear_counts, width, label='撕裂', color='red', alpha=0.7)\n", "    axes[1, 0].bar(x + width/2, normal_counts, width, label='正常', color='green', alpha=0.7)\n", "    axes[1, 0].set_title('各数据集分割的目标数量')\n", "    axes[1, 0].set_ylabel('目标数量')\n", "    axes[1, 0].set_xticks(x)\n", "    axes[1, 0].set_xticklabels(splits)\n", "    axes[1, 0].legend()\n", "    \n", "    # 4. 包含目标的图像比例\n", "    ratios = []\n", "    for split in splits:\n", "        if stats[split]['images'] > 0:\n", "            ratio = stats[split]['images_with_objects'] / stats[split]['images'] * 100\n", "            ratios.append(ratio)\n", "        else:\n", "            ratios.append(0)\n", "    \n", "    axes[1, 1].bar(splits, ratios, color=['orange', 'purple', 'brown'])\n", "    axes[1, 1].set_title('包含目标的图像比例')\n", "    axes[1, 1].set_ylabel('比例 (%)')\n", "    axes[1, 1].set_ylim(0, 100)\n", "    for i, v in enumerate(ratios):\n", "        axes[1, 1].text(i, v + 2, f'{v:.1f}%', ha='center')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print('没有可用的统计数据')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 总结\n", "\n", "通过以上可视化和分析，我们可以验证：\n", "\n", "1. **数据集完整性**: 图像和标签文件是否正确匹配\n", "2. **标注质量**: 边界框是否准确标注了撕裂区域\n", "3. **数据分布**: 各类别和各数据集分割的样本分布\n", "4. **图像质量**: 切出的图像是否清晰可用\n", "\n", "如果发现问题，可以回到数据处理脚本进行调整。"]}], "metadata": {"kernelspec": {"display_name": "torch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 4}