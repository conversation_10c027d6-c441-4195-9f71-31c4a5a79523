﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC944}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MedicalImageAnalysis.Core", "src\MedicalImageAnalysis.Core\MedicalImageAnalysis.Core.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MedicalImageAnalysis.Infrastructure", "src\MedicalImageAnalysis.Infrastructure\MedicalImageAnalysis.Infrastructure.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567891}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MedicalImageAnalysis.Application", "src\MedicalImageAnalysis.Application\MedicalImageAnalysis.Application.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}"
EndProject


Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MedicalImageAnalysis.Wpf", "src\MedicalImageAnalysis.Wpf\MedicalImageAnalysis.Wpf.csproj", "{5D7410A1-4E51-4D51-987D-90B8F6DDDC33}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x86.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x86.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x86.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567891}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567891}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567891}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567891}.Debug|x64.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567891}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567891}.Debug|x86.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567891}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567891}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567891}.Release|x64.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567891}.Release|x64.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567891}.Release|x86.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567891}.Release|x86.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}.Debug|x64.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}.Debug|x86.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}.Release|x64.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}.Release|x64.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}.Release|x86.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}.Release|x86.Build.0 = Release|Any CPU


		{5D7410A1-4E51-4D51-987D-90B8F6DDDC33}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5D7410A1-4E51-4D51-987D-90B8F6DDDC33}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5D7410A1-4E51-4D51-987D-90B8F6DDDC33}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5D7410A1-4E51-4D51-987D-90B8F6DDDC33}.Debug|x64.Build.0 = Debug|Any CPU
		{5D7410A1-4E51-4D51-987D-90B8F6DDDC33}.Debug|x86.ActiveCfg = Debug|Any CPU
		{5D7410A1-4E51-4D51-987D-90B8F6DDDC33}.Debug|x86.Build.0 = Debug|Any CPU
		{5D7410A1-4E51-4D51-987D-90B8F6DDDC33}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5D7410A1-4E51-4D51-987D-90B8F6DDDC33}.Release|Any CPU.Build.0 = Release|Any CPU
		{5D7410A1-4E51-4D51-987D-90B8F6DDDC33}.Release|x64.ActiveCfg = Release|Any CPU
		{5D7410A1-4E51-4D51-987D-90B8F6DDDC33}.Release|x64.Build.0 = Release|Any CPU
		{5D7410A1-4E51-4D51-987D-90B8F6DDDC33}.Release|x86.ActiveCfg = Release|Any CPU
		{5D7410A1-4E51-4D51-987D-90B8F6DDDC33}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567891} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567892} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}

		{5D7410A1-4E51-4D51-987D-90B8F6DDDC33} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789012}
	EndGlobalSection
EndGlobal
