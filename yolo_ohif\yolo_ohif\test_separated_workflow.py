#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分离式工作流程
验证数据准备和训练分离后的功能是否正常
"""

import os
import sys
import logging
import tempfile
import shutil
from pathlib import Path
import subprocess

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_data_preparation():
    """
    测试数据准备步骤
    """
    logger.info("🧪 测试数据准备步骤")
    logger.info("=" * 50)
    
    # 检查create_yolo_dataset.py是否存在
    script_path = Path('./create_yolo_dataset.py')
    if not script_path.exists():
        logger.error("❌ create_yolo_dataset.py 不存在")
        return False
    
    # 检查原始数据集是否存在
    dataset_dir = Path('./dataset')
    if not dataset_dir.exists():
        logger.warning("⚠️ 原始数据集不存在，跳过数据准备测试")
        return True
    
    try:
        # 导入数据集创建器
        from create_yolo_dataset import YOLODatasetCreator
        
        # 创建测试输出目录
        test_output = Path('./test_separated_workflow_output')
        if test_output.exists():
            shutil.rmtree(test_output)
        
        logger.info("开始测试数据准备...")
        
        # 创建数据集
        creator = YOLODatasetCreator(
            dataset_root=str(dataset_dir),
            output_root=str(test_output),
            img_size=640
        )
        
        config_path = creator.create_dataset()
        
        # 验证输出
        if Path(config_path).exists():
            logger.info("✅ 数据准备测试通过")
            
            # 检查数据集结构
            yolo_dataset = test_output / "yolo_dataset"
            required_dirs = [
                yolo_dataset / "images" / "train",
                yolo_dataset / "images" / "val",
                yolo_dataset / "images" / "test",
                yolo_dataset / "labels" / "train",
                yolo_dataset / "labels" / "val",
                yolo_dataset / "labels" / "test"
            ]
            
            all_exist = all(d.exists() for d in required_dirs)
            if all_exist:
                logger.info("✅ 数据集结构验证通过")
                return True
            else:
                logger.error("❌ 数据集结构不完整")
                return False
        else:
            logger.error("❌ 配置文件未创建")
            return False
            
    except Exception as e:
        logger.error(f"❌ 数据准备测试失败: {e}")
        return False
    
    finally:
        # 清理测试输出
        try:
            if test_output.exists():
                shutil.rmtree(test_output)
                logger.info("🧹 测试输出已清理")
        except Exception as e:
            logger.warning(f"清理测试输出失败: {e}")

def test_training_script():
    """
    测试训练脚本的数据集检测功能
    """
    logger.info("\n🧪 测试训练脚本")
    logger.info("=" * 50)
    
    # 检查start_yolo11x_training.py是否存在
    script_path = Path('./start_yolo11x_training.py')
    if not script_path.exists():
        logger.error("❌ start_yolo11x_training.py 不存在")
        return False
    
    try:
        # 测试导入
        import start_yolo11x_training
        logger.info("✅ 训练脚本导入成功")
        
        # 检查是否有现有的数据集
        current_dir = Path.cwd()
        possible_dataset_paths = [
            current_dir / "yolo_dataset_output" / "yolo_dataset",
            current_dir / "yolo11x_training_output" / "yolo_dataset",
            current_dir / "yolo_dataset"
        ]
        
        dataset_found = False
        for path in possible_dataset_paths:
            if path.exists():
                for config_name in ["dataset.yaml", "data.yaml"]:
                    config_file = path / config_name
                    if config_file.exists():
                        train_dir = path / "images" / "train"
                        val_dir = path / "images" / "val"
                        if train_dir.exists() and val_dir.exists():
                            dataset_found = True
                            logger.info(f"✅ 找到有效数据集: {path}")
                            break
                if dataset_found:
                    break
        
        if dataset_found:
            logger.info("✅ 训练脚本数据集检测功能正常")
            return True
        else:
            logger.info("ℹ️ 未找到现有数据集，这是正常的（需要先运行数据准备）")
            return True
            
    except Exception as e:
        logger.error(f"❌ 训练脚本测试失败: {e}")
        return False

def test_workflow_integration():
    """
    测试完整工作流程集成
    """
    logger.info("\n🧪 测试工作流程集成")
    logger.info("=" * 50)
    
    # 检查必要的脚本文件
    required_files = [
        'create_yolo_dataset.py',
        'start_yolo11x_training.py',
        'train_yolo11x_from_scratch.py'
    ]
    
    missing_files = []
    for file_name in required_files:
        if not Path(file_name).exists():
            missing_files.append(file_name)
    
    if missing_files:
        logger.error(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    logger.info("✅ 所有必要文件存在")
    
    # 检查文档文件
    doc_files = [
        'TRAINING_WORKFLOW_GUIDE.md',
        'FILE_SAFETY_FIX_GUIDE.md',
        'YOLO11X_FROM_SCRATCH_GUIDE.md'
    ]
    
    existing_docs = []
    for doc_file in doc_files:
        if Path(doc_file).exists():
            existing_docs.append(doc_file)
    
    logger.info(f"✅ 文档文件: {len(existing_docs)}/{len(doc_files)} 个存在")
    
    # 检查导入兼容性
    try:
        from create_yolo_dataset import YOLODatasetCreator
        from train_yolo11x_from_scratch import YOLO11xTrainer
        logger.info("✅ 核心类导入成功")
    except Exception as e:
        logger.error(f"❌ 导入失败: {e}")
        return False
    
    return True

def test_file_safety_features():
    """
    测试文件安全功能
    """
    logger.info("\n🧪 测试文件安全功能")
    logger.info("=" * 50)
    
    try:
        from create_yolo_dataset import YOLODatasetCreator
        
        # 创建测试实例
        creator = YOLODatasetCreator(
            dataset_root='./test',
            output_root='./test_output',
            img_size=640
        )
        
        # 检查安全文件写入方法是否存在
        if hasattr(creator, 'safe_write_file'):
            logger.info("✅ 安全文件写入方法存在")
        else:
            logger.warning("⚠️ 安全文件写入方法不存在")
            return False
        
        if hasattr(creator, 'safe_file_operation'):
            logger.info("✅ 安全文件操作上下文管理器存在")
        else:
            logger.warning("⚠️ 安全文件操作上下文管理器不存在")
            return False
        
        # 测试安全写入功能
        test_dir = Path('./test_safety')
        test_dir.mkdir(exist_ok=True)
        
        try:
            test_file = test_dir / 'test.txt'
            creator.safe_write_file(str(test_file), 'test content')
            
            if test_file.exists():
                logger.info("✅ 安全文件写入功能正常")
                return True
            else:
                logger.error("❌ 安全文件写入失败")
                return False
        finally:
            if test_dir.exists():
                shutil.rmtree(test_dir)
                
    except Exception as e:
        logger.error(f"❌ 文件安全功能测试失败: {e}")
        return False

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("分离式工作流程测试")
    print("=" * 60)
    
    # 测试列表
    tests = [
        ("工作流程集成", test_workflow_integration),
        ("文件安全功能", test_file_safety_features),
        ("数据准备步骤", test_data_preparation),
        ("训练脚本", test_training_script)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{len(results)+1}. 测试{test_name}...")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            logger.error(f"测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    
    passed = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！")
        print("\n📋 分离式工作流程已就绪:")
        print("1. 数据准备: python create_yolo_dataset.py")
        print("2. 模型训练: python start_yolo11x_training.py")
        print("\n📖 详细说明: TRAINING_WORKFLOW_GUIDE.md")
        return True
    else:
        print(f"\n❌ {len(results) - passed} 个测试失败")
        print("请检查错误信息并修复问题")
        return False

if __name__ == "__main__":
    main()