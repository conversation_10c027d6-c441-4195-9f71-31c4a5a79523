Write-Host "========================================" -ForegroundColor Cyan
Write-Host "医学影像解析系统 - 项目演示" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查.NET环境
Write-Host "检查系统环境..." -ForegroundColor Yellow
$dotnetVersion = dotnet --version 2>$null
if ($dotnetVersion) {
    Write-Host "✓ .NET SDK 版本: $dotnetVersion" -ForegroundColor Green
} else {
    Write-Host "✗ 未找到 .NET SDK" -ForegroundColor Red
    exit 1
}

# 显示项目结构
Write-Host ""
Write-Host "项目结构:" -ForegroundColor Yellow
Write-Host "✓ MedicalImageAnalysis.Core - 核心业务逻辑" -ForegroundColor Green
Write-Host "✓ MedicalImageAnalysis.WpfClient - 桌面客户端" -ForegroundColor Green

# 显示示例数据
Write-Host ""
Write-Host "示例数据:" -ForegroundColor Yellow
$brainFiles = Get-ChildItem -Path "Brain" -Filter "*.dcm" -ErrorAction SilentlyContinue
if ($brainFiles) {
    Write-Host "✓ 发现 $($brainFiles.Count) 个 DICOM 示例文件" -ForegroundColor Green
} else {
    Write-Host "⚠ 未找到示例 DICOM 文件" -ForegroundColor Yellow
}

# 构建项目
Write-Host ""
Write-Host "构建客户端应用..." -ForegroundColor Yellow
dotnet build src/MedicalImageAnalysis.WpfClient/MedicalImageAnalysis.WpfClient.csproj --verbosity quiet 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 客户端应用构建成功" -ForegroundColor Green

    Write-Host ""
    Write-Host "启动应用程序..." -ForegroundColor Yellow
    $appPath = "src\MedicalImageAnalysis.WpfClient\bin\Debug\net8.0-windows\MedicalImageAnalysis.WpfClient.exe"
    if (Test-Path $appPath) {
        Start-Process -FilePath $appPath
        Write-Host "✓ 应用程序已启动" -ForegroundColor Green
    } else {
        Write-Host "✗ 应用程序文件不存在" -ForegroundColor Red
    }
} else {
    Write-Host "✗ 构建失败" -ForegroundColor Red
}

Write-Host ""
Write-Host "演示完成！" -ForegroundColor Green
