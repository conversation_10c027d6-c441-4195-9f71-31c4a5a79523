{% extends "base.html" %}

{% block title %}智能疾病检测 - YOLO-OHIF医学图像疾病检测系统{% endblock %}

{% block extra_css %}
<style>
    .smart-detect-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem 0;
    }
    
    .upload-zone {
        border: 3px dashed #667eea;
        border-radius: 15px;
        padding: 3rem 2rem;
        text-align: center;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    
    .upload-zone:hover, .upload-zone.dragover {
        border-color: #764ba2;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
    }
    
    .upload-icon {
        font-size: 4rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1rem;
    }
    
    .upload-title {
        font-size: 1.8rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }
    
    .upload-subtitle {
        color: #666;
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
    }
    
    .btn-upload {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 12px 30px;
        font-size: 1.1rem;
        border-radius: 25px;
        color: white;
        transition: all 0.3s ease;
    }
    
    .btn-upload:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        color: white;
    }
    
    .detection-progress {
        display: none;
        text-align: center;
        padding: 2rem;
    }
    
    .progress-circle {
        width: 80px;
        height: 80px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .progress-text {
        font-size: 1.2rem;
        color: #333;
        margin-bottom: 0.5rem;
    }
    
    .progress-detail {
        color: #666;
        font-size: 0.9rem;
    }
    
    .detection-complete {
        display: none;
        text-align: center;
        padding: 2rem;
    }
    
    .success-icon {
        font-size: 4rem;
        color: #28a745;
        margin-bottom: 1rem;
    }
    
    .result-summary {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1rem 0;
    }
    
    .result-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .result-item:last-child {
        border-bottom: none;
    }
    
    .btn-view-results {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        padding: 12px 30px;
        font-size: 1.1rem;
        border-radius: 25px;
        color: white;
        transition: all 0.3s ease;
        margin: 0.5rem;
    }
    
    .btn-view-results:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        color: white;
    }
    
    .feature-list {
        text-align: left;
        max-width: 500px;
        margin: 2rem auto;
    }
    
    .feature-item {
        display: flex;
        align-items: center;
        padding: 0.5rem 0;
        color: #666;
    }
    
    .feature-icon {
        color: #667eea;
        margin-right: 0.8rem;
        width: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="smart-detect-container">
    <div class="text-center mb-4">
        <h1 class="display-5 fw-bold mb-3">
            <i class="fas fa-brain me-2" style="color: #667eea;"></i>
            智能疾病检测
        </h1>
        <p class="lead text-muted">上传医学图像，AI自动检测疾病并在OHIF查看器中展示结果</p>
    </div>
    
    <!-- 上传区域 -->
    <div id="uploadSection">
        <!-- 隐藏的CSRF令牌 -->
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        
        <div class="upload-zone" id="uploadZone">
            <i class="fas fa-cloud-upload-alt upload-icon"></i>
            <h3 class="upload-title">拖拽或点击上传DICOM文件</h3>
            <p class="upload-subtitle">支持单个文件、多个文件或ZIP压缩包</p>
            
            <input type="file" id="fileInput" multiple accept=".dcm,.dicom,.zip" style="display: none;">
            <button type="button" class="btn btn-upload" id="browseBtn">
                <i class="fas fa-folder-open me-2"></i>
                选择文件
            </button>
            
            <div class="feature-list mt-4">
                <div class="feature-item">
                    <i class="fas fa-check feature-icon"></i>
                    <span>自动DICOM格式识别和处理</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check feature-icon"></i>
                    <span>AI智能疾病检测和病灶定位</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check feature-icon"></i>
                    <span>检测结果自动叠加到OHIF查看器</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check feature-icon"></i>
                    <span>专业医学图像浏览和标注工具</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 检测进度 -->
    <div id="progressSection" class="detection-progress">
        <div class="progress-circle"></div>
        <div class="progress-text" id="progressText">正在上传文件...</div>
        <div class="progress-detail" id="progressDetail">请稍候，系统正在处理您的文件</div>
    </div>
    
    <!-- 检测完成 -->
    <div id="completeSection" class="detection-complete">
        <i class="fas fa-check-circle success-icon"></i>
        <h3 class="mb-3">检测完成！</h3>
        
        <div class="result-summary" id="resultSummary">
            <!-- 结果摘要将在这里动态生成 -->
        </div>
        
        <div class="mt-4">
            <button type="button" class="btn btn-view-results" id="viewInOHIF">
                <i class="fas fa-eye me-2"></i>
                在OHIF查看器中查看结果
            </button>
            <button type="button" class="btn btn-outline-primary" id="newDetection">
                <i class="fas fa-plus me-2"></i>
                开始新的检测
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    let currentStudyId = null;
    let viewerUrl = null;
    
    // 上传区域事件
    const uploadZone = $('#uploadZone');
    const fileInput = $('#fileInput');
    
    // 点击上传区域
    uploadZone.on('click', function() {
        fileInput.click();
    });
    
    // 浏览按钮
    $('#browseBtn').on('click', function(e) {
        e.stopPropagation();
        fileInput.click();
    });
    
    // 文件选择事件
    fileInput.on('change', function() {
        const files = this.files;
        if (files.length > 0) {
            startDetection(files);
        }
    });
    
    // 拖拽事件
    uploadZone.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });
    
    uploadZone.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });
    
    uploadZone.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            startDetection(files);
        }
    });
    
    // 开始检测流程
    function startDetection(files) {
        // 切换到进度界面
        $('#uploadSection').hide();
        $('#progressSection').show();
        
        // 上传文件
        uploadFiles(files);
    }
    
    // 上传文件
    function uploadFiles(files) {
        updateProgress('正在上传文件...', '文件上传中，请稍候');
        
        const formData = new FormData();
        
        // 添加CSRF令牌
        const csrfToken = $('input[name="csrf_token"]').val() || $('meta[name=csrf-token]').attr('content');
        if (csrfToken) {
            formData.append('csrf_token', csrfToken);
        }
        
        // 添加文件
        for (let i = 0; i < files.length; i++) {
            if (files.length === 1) {
                formData.append('file', files[i]);
            } else {
                formData.append('files[]', files[i]);
            }
        }
        
        $.ajax({
            url: '/api/upload-and-detect',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.status === 'success') {
                    currentStudyId = response.study_id;
                    viewerUrl = response.viewer_url;
                    showResults(response);
                } else {
                    showError(response.message || '上传失败');
                }
            },
            error: function(xhr, status, error) {
                let errorMsg = '上传失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                showError(errorMsg);
            }
        });
    }
    
    // 更新进度
    function updateProgress(text, detail) {
        $('#progressText').text(text);
        $('#progressDetail').text(detail);
    }
    
    // 显示结果
    function showResults(response) {
        $('#progressSection').hide();
        
        // 生成结果摘要
        const results = response.detection_results || [];
        let totalDetections = 0;
        let processedImages = results.length;
        
        results.forEach(result => {
            totalDetections += (result.detections || []).length;
        });
        
        const summaryHtml = `
            <div class="result-item">
                <span><i class="fas fa-images me-2"></i>处理图像数量</span>
                <strong>${processedImages}</strong>
            </div>
            <div class="result-item">
                <span><i class="fas fa-crosshairs me-2"></i>检测到的病灶</span>
                <strong>${totalDetections}</strong>
            </div>
            <div class="result-item">
                <span><i class="fas fa-clock me-2"></i>检测时间</span>
                <strong>${new Date().toLocaleTimeString()}</strong>
            </div>
        `;
        
        $('#resultSummary').html(summaryHtml);
        $('#completeSection').show();
    }
    
    // 显示错误
    function showError(message) {
        $('#progressSection').hide();
        
        const errorHtml = `
            <div class="alert alert-danger text-center" role="alert">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h5>检测失败</h5>
                <p class="mb-0">${message}</p>
                <button type="button" class="btn btn-outline-danger mt-3" onclick="location.reload()">
                    <i class="fas fa-redo me-2"></i>重新开始
                </button>
            </div>
        `;
        
        $('#uploadSection').html(errorHtml).show();
    }
    
    // 在OHIF中查看结果
    $('#viewInOHIF').on('click', function() {
        if (viewerUrl) {
            window.open(viewerUrl, '_blank');
        } else if (currentStudyId) {
            window.open(`/viewer/${currentStudyId}`, '_blank');
        }
    });
    
    // 开始新的检测
    $('#newDetection').on('click', function() {
        location.reload();
    });
});
</script>
{% endblock %}