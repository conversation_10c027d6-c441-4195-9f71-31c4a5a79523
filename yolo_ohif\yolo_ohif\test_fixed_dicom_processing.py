#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的DICOM处理
验证统一归一化方法后模型是否能检测到病灶
"""

import os
import sys
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services.detection_service import DetectionService
from config import Config

def test_fixed_dicom_processing():
    """
    测试修复后的DICOM处理能力
    """
    print("=== 测试修复后的DICOM处理 ===")
    
    # 初始化检测服务
    try:
        detection_service = DetectionService(
            model_path="E:/Trae/yolo_ohif/yolo11x_training_output/training_results/weights/best.pt",
            confidence_threshold=0.25
        )
        print("✓ DetectionService 初始化成功")
    except Exception as e:
        print(f"✗ DetectionService 初始化失败: {e}")
        return
    
    # 测试DICOM文件路径（如果有的话）
    test_dicom_paths = [
        "E:/Trae/yolo_ohif/uploads",  # 上传目录
        "E:/Trae/yolo_ohif/dataset",   # 数据集目录
    ]
    
    dicom_files = []
    for test_path in test_dicom_paths:
        if os.path.exists(test_path):
            for root, dirs, files in os.walk(test_path):
                for file in files:
                    if file.lower().endswith(('.dcm', '.dicom')):
                        dicom_files.append(os.path.join(root, file))
    
    if not dicom_files:
        print("未找到DICOM文件，将创建模拟测试")
        test_with_simulated_data(detection_service)
    else:
        print(f"找到 {len(dicom_files)} 个DICOM文件")
        for i, dicom_file in enumerate(dicom_files[:3]):  # 测试前3个文件
            print(f"\n--- 测试DICOM文件 {i+1}: {os.path.basename(dicom_file)} ---")
            test_single_dicom(detection_service, dicom_file)

def test_with_simulated_data(detection_service):
    """
    使用模拟数据测试（当没有DICOM文件时）
    """
    print("\n=== 使用模拟数据测试 ===")
    
    # 创建模拟的医学图像数据
    # 模拟16位医学图像
    img_array = np.random.randint(0, 4096, (512, 512), dtype=np.uint16)
    
    # 添加一些"病灶"区域（高亮区域）
    img_array[200:250, 200:250] = 3500  # 模拟病灶区域
    
    print(f"模拟图像形状: {img_array.shape}")
    print(f"模拟图像数据类型: {img_array.dtype}")
    print(f"模拟图像值范围: {img_array.min()} - {img_array.max()}")
    
    # 测试归一化方法
    normalized_img = detection_service._normalize_to_uint8(img_array)
    print(f"归一化后形状: {normalized_img.shape}")
    print(f"归一化后数据类型: {normalized_img.dtype}")
    print(f"归一化后值范围: {normalized_img.min()} - {normalized_img.max()}")
    
    # 转换为RGB格式
    if len(normalized_img.shape) == 2:
        rgb_img = np.stack([normalized_img] * 3, axis=-1)
    else:
        rgb_img = normalized_img
    
    print(f"RGB图像形状: {rgb_img.shape}")
    
    # 进行检测
    try:
        results = detection_service._detect_image(rgb_img)
        print(f"\n检测结果: {len(results)} 个目标")
        
        if results:
            for i, result in enumerate(results):
                print(f"目标 {i+1}: 置信度={result.get('confidence', 'N/A'):.3f}, "
                      f"类别={result.get('class', 'N/A')}, "
                      f"边界框={result.get('bbox', 'N/A')}")
        else:
            print("未检测到任何目标")
            
            # 尝试更低的置信度
            print("\n尝试使用更低的置信度阈值 (0.01)...")
            detection_service.confidence_threshold = 0.01
            results_low = detection_service._detect_image(rgb_img)
            
            if results_low:
                print(f"低置信度检测结果: {len(results_low)} 个目标")
                for i, result in enumerate(results_low):
                    print(f"目标 {i+1}: 置信度={result.get('confidence', 'N/A'):.3f}, "
                          f"类别={result.get('class', 'N/A')}, "
                          f"边界框={result.get('bbox', 'N/A')}")
            else:
                print("即使使用低置信度也未检测到目标")
                
    except Exception as e:
        print(f"检测过程中出错: {e}")
        import traceback
        traceback.print_exc()

def test_single_dicom(detection_service, dicom_path):
    """
    测试单个DICOM文件
    """
    try:
        # 使用修复后的方法检测DICOM
        results = detection_service.detect_single_dicom(
            dicom_path=dicom_path,
            save_result=False
        )
        
        print(f"检测结果: {len(results)} 个目标")
        
        if results:
            for i, result in enumerate(results):
                print(f"目标 {i+1}: 置信度={result.get('confidence', 'N/A'):.3f}, "
                      f"类别={result.get('class', 'N/A')}, "
                      f"边界框={result.get('bbox', 'N/A')}")
        else:
            print("未检测到任何目标")
            
            # 尝试更低的置信度
            print("尝试使用更低的置信度阈值 (0.01)...")
            original_threshold = detection_service.confidence_threshold
            detection_service.confidence_threshold = 0.01
            
            results_low = detection_service.detect_single_dicom(
                dicom_path=dicom_path,
                save_result=False
            )
            
            if results_low:
                print(f"低置信度检测结果: {len(results_low)} 个目标")
                for i, result in enumerate(results_low):
                    print(f"目标 {i+1}: 置信度={result.get('confidence', 'N/A'):.3f}, "
                          f"类别={result.get('class', 'N/A')}, "
                          f"边界框={result.get('bbox', 'N/A')}")
            else:
                print("即使使用低置信度也未检测到目标")
            
            # 恢复原始置信度
            detection_service.confidence_threshold = original_threshold
            
    except Exception as e:
        print(f"DICOM检测过程中出错: {e}")
        import traceback
        traceback.print_exc()

def compare_normalization_methods():
    """
    比较修复前后的归一化方法差异
    """
    print("\n=== 比较归一化方法 ===")
    
    # 创建测试图像
    test_img = np.random.randint(0, 4096, (100, 100), dtype=np.uint16)
    
    # 修复后的方法（当前使用的）
    def new_normalize(img_array):
        img_float = img_array.astype(np.float32)
        if img_float.max() > img_float.min():
            return ((img_float - img_float.min()) / 
                   (img_float.max() - img_float.min()) * 255).astype(np.uint8)
        else:
            return np.zeros_like(img_float, dtype=np.uint8)
    
    # 修复前的方法（带百分位数窗口化）
    def old_normalize(img_array):
        p1, p99 = np.percentile(img_array, (1, 99))
        img_clipped = np.clip(img_array, p1, p99)
        return ((img_clipped - np.min(img_clipped)) / 
               (np.max(img_clipped) - np.min(img_clipped)) * 255).astype(np.uint8)
    
    new_result = new_normalize(test_img)
    old_result = old_normalize(test_img)
    
    print(f"原始图像范围: {test_img.min()} - {test_img.max()}")
    print(f"新方法结果范围: {new_result.min()} - {new_result.max()}")
    print(f"旧方法结果范围: {old_result.min()} - {old_result.max()}")
    
    # 计算差异
    diff = np.abs(new_result.astype(np.float32) - old_result.astype(np.float32))
    print(f"两种方法的平均差异: {diff.mean():.2f}")
    print(f"两种方法的最大差异: {diff.max():.2f}")
    
    if diff.mean() > 10:
        print("⚠️  两种归一化方法差异较大，这可能是导致检测问题的原因")
    else:
        print("✓ 两种归一化方法差异较小")

if __name__ == "__main__":
    print("DICOM处理修复验证脚本")
    print("=" * 50)
    
    # 比较归一化方法
    compare_normalization_methods()
    
    # 测试修复后的处理
    test_fixed_dicom_processing()
    
    print("\n=== 测试完成 ===")
    print("如果仍然检测不到目标，可能需要进一步检查:")
    print("1. 模型是否正确加载")
    print("2. 图像预处理是否与训练时完全一致")
    print("3. 模型训练质量是否足够好")
    print("4. 测试图像是否包含训练时见过的病灶类型")