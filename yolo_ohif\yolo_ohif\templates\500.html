{% extends "base.html" %}

{% block title %}500 - 服务器错误{% endblock %}

{% block extra_css %}
<style>
  .error-container {
    text-align: center;
    padding: 100px 0;
  }
  
  .error-code {
    font-size: 120px;
    font-weight: bold;
    color: #dc3545;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .error-message {
    font-size: 24px;
    margin-bottom: 30px;
    color: #495057;
  }
  
  .error-details {
    font-size: 16px;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto 40px;
  }
  
  .error-image {
    max-width: 300px;
    margin-bottom: 30px;
  }
  
  .btn-home {
    padding: 10px 30px;
    font-size: 18px;
    border-radius: 30px;
  }
</style>
{% endblock %}

{% block content %}
<div class="container error-container">
  <div class="row">
    <div class="col-md-12">
      <svg class="error-image" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
        <!-- 医学十字 -->
        <path d="M100,40 L100,160 M40,100 L160,100" stroke="#dc3545" stroke-width="10" stroke-linecap="round"/>
        
        <!-- 警告符号 -->
        <path d="M100,60 L100,120" stroke="#dc3545" stroke-width="8" stroke-linecap="round"/>
        <circle cx="100" cy="140" r="6" fill="#dc3545"/>
        
        <!-- 500文字 -->
        <text x="100" y="100" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#dc3545" dominant-baseline="middle">500</text>
      </svg>
      
      <h1 class="error-code">500</h1>
      <h2 class="error-message">服务器内部错误</h2>
      <p class="error-details">
        抱歉，服务器遇到了意外错误，无法完成您的请求。我们的技术团队已经收到此错误通知，并正在努力解决问题。
      </p>
      <div class="d-flex justify-content-center gap-3">
        <a href="{{ url_for('index') }}" class="btn btn-primary btn-home">
          <i class="fas fa-home me-2"></i>返回首页
        </a>
        <button onclick="window.location.reload()" class="btn btn-outline-secondary btn-home">
          <i class="fas fa-sync-alt me-2"></i>刷新页面
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %}