#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查YOLO模型信息脚本
用于检查best.pt模型的类别信息和详细配置
"""

import os
import sys
from ultralytics import YOLO

def check_model_info(model_path):
    """检查模型信息"""
    try:
        print(f"正在加载模型: {model_path}")
        
        # 检查文件是否存在
        if not os.path.exists(model_path):
            print(f"错误: 模型文件不存在 - {model_path}")
            return False
        
        # 加载模型
        model = YOLO(model_path)
        
        print("\n=== 模型基本信息 ===")
        print(f"模型路径: {model_path}")
        print(f"模型类型: {type(model).__name__}")
        
        # 检查类别信息
        print("\n=== 类别信息 ===")
        if hasattr(model, 'names') and model.names:
            print(f"类别数量: {len(model.names)}")
            print("类别列表:")
            for class_id, class_name in model.names.items():
                print(f"  {class_id}: {class_name}")
        else:
            print("警告: 无法获取模型类别信息")
        
        # 模型详细信息
        print("\n=== 模型详细信息 ===")
        try:
            model_info = model.info()
            print(f"模型信息: {model_info}")
        except Exception as e:
            print(f"获取模型详细信息时出错: {e}")
        
        # 检查模型配置
        print("\n=== 模型配置 ===")
        if hasattr(model, 'model'):
            if hasattr(model.model, 'yaml'):
                print(f"模型配置文件: {model.model.yaml}")
            if hasattr(model.model, 'nc'):
                print(f"类别数 (nc): {model.model.nc}")
        
        # 测试推理
        print("\n=== 推理测试 ===")
        print("正在进行推理测试...")
        
        # 创建测试图像
        import numpy as np
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # 进行推理
        results = model(test_image, conf=0.25, iou=0.45, verbose=False)
        print(f"推理成功，结果数量: {len(results)}")
        
        if results:
            result = results[0]
            if hasattr(result, 'boxes') and result.boxes is not None:
                print(f"检测到的目标数量: {len(result.boxes)}")
            else:
                print("未检测到任何目标")
        
        return True
        
    except Exception as e:
        print(f"检查模型时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    model_path = "e:/Trae/yolo_ohif/models/weights/best.pt"
    
    print("YOLO模型信息检查工具")
    print("=" * 50)
    
    success = check_model_info(model_path)
    
    if success:
        print("\n模型检查完成！")
    else:
        print("\n模型检查失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()