# DICOM 阅读器中检测结果显示指南

## 概述

本系统提供了一个专业的 DICOM 阅读器，集成了 YOLO 检测功能，能够在医学影像上自动检测并显示病灶。当您运行 `python app.py` 后，系统会启动一个完整的 Web 应用，包含专业的 DICOM 阅读器界面。

## 系统架构

### 后端服务
- **Flask Web 应用** (`app.py`): 主应用入口
- **检测服务** (`DetectionService`): 负责 YOLO 模型推理
- **Orthanc 服务** (`OrthancService`): 管理 DICOM 文件
- **数据库服务** (`DatabaseService`): 存储检测结果和研究信息
- **OHIF 服务** (`OHIFService`): 提供医学影像查看功能

### 前端界面
- **自定义 DICOM 查看器** (`/dicom-viewer`): 专业医学影像阅读界面
- **检测结果面板**: 实时显示 AI 检测结果
- **交互式标注工具**: 支持手动标注和检测结果可视化

## 启动和使用流程

### 1. 启动系统

```bash
cd E:\Trae\yolo_ohif
python app.py
```

系统将在 `http://localhost:5000` 启动。

### 2. 访问 DICOM 阅读器

有以下几种方式访问 DICOM 阅读器：

#### 方式一：通过仪表板
1. 访问 `http://localhost:5000`
2. 登录系统（如果需要）
3. 在仪表板中选择研究
4. 点击「查看」按钮进入 DICOM 阅读器

#### 方式二：直接访问
- 通用查看器：`http://localhost:5000/dicom-viewer`
- 指定研究：`http://localhost:5000/dicom-viewer/<study_id>`
- 专业版：`http://localhost:5000/professional-viewer/<study_id>`

### 3. 检测结果显示机制

#### 自动检测模式
当通过仪表板访问研究时，系统会：
1. **自动执行检测**：系统自动对 DICOM 图像进行 YOLO 检测
2. **保存结果**：检测结果保存到数据库
3. **实时显示**：在查看器中实时显示检测框和标签

#### 手动检测模式
在 DICOM 查看器中：
1. 点击左侧工具栏的 **AI 检测按钮**（橙色按钮）
2. 系统对当前图像执行检测
3. 检测结果立即显示在图像上

## DICOM 查看器功能详解

### 界面布局

```
┌─────────────────────────────────────────────────────────┐
│                    顶部工具栏                           │
├─────┬───────────────────────────────────────────┬─────┤
│左侧 │                                         │右侧 │
│工具 │            主显示区域                     │面板 │
│栏   │         (DICOM 图像)                    │     │
│     │                                         │     │
│     │                                         │     │
├─────┴───────────────────────────────────────────┴─────┤
│                    底部状态栏                           │
└─────────────────────────────────────────────────────────┘
```

### 左侧工具栏功能

- **🔍 缩放工具**：图像缩放
- **📐 测量工具**：距离和角度测量
- **✏️ 标注工具**：手动标注
- **🤖 AI 检测**：执行 YOLO 检测（橙色按钮）
- **👁️ 检测切换**：显示/隐藏检测结果
- **🗑️ 清除检测**：清除所有检测结果

### 右侧面板功能

#### 检测结果面板
- **检测统计**：显示检测数量、置信度等
- **结果列表**：列出所有检测项目
- **风险分级**：按置信度分为高、中、低风险
- **详细信息**：每个检测的具体位置和置信度

#### 图像信息面板
- **DICOM 元数据**：患者信息、检查参数等
- **图像属性**：尺寸、像素间距等
- **窗宽窗位**：调整图像对比度

## 检测结果显示特性

### 1. 实时检测框
- **颜色编码**：不同类别使用不同颜色
- **置信度标签**：显示检测类别和置信度百分比
- **交互式**：点击检测框可查看详细信息

### 2. 检测结果持久化
- **数据库存储**：所有检测结果保存到 SQLite 数据库
- **历史记录**：可查看之前的检测结果
- **结果导出**：支持导出为 JSON 格式

### 3. 多图像支持
- **序列浏览**：支持多帧 DICOM 序列
- **批量检测**：一次性检测整个研究
- **结果同步**：检测结果与图像序列同步显示

## API 接口说明

### 核心 API 端点

#### 1. 获取研究检测结果
```http
GET /api/studies/<study_id>/detections
```

#### 2. 执行 AI 检测
```http
POST /api/ai/predict
```

#### 3. 获取 DICOM 图像
```http
GET /api/images/<image_id>/dicom
```

#### 4. 删除检测结果
```http
DELETE /api/studies/<study_id>/detections
```

### JavaScript 前端集成

查看器使用 Cornerstone.js 进行 DICOM 图像渲染，检测结果通过覆盖层显示：

```javascript
// 加载检测结果
fetch(`/api/studies/${studyId}/detections`)
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      this.displayDetections(data.detections);
    }
  });

// 显示检测框
displayDetections(detections) {
  detections.forEach(detection => {
    this.createDetectionBox(detection);
  });
}
```

## 配置和自定义

### 1. 模型配置

在 `config.py` 中配置 YOLO 模型：

```python
class YOLOConfig:
    MODEL_PATH = "E:/Trae/yolo_ohif/yolo11x_training_output/training_results/yolo11x_from_scratch_20250719_135134/weights"
    DEFAULT_MODEL = "best.pt"
    CONFIDENCE_THRESHOLD = 0.25
    IOU_THRESHOLD = 0.45
    DEVICE = "cuda"  # 或 "cpu"
```

### 2. 检测参数调整

可以在查看器中实时调整：
- **置信度阈值**：过滤低置信度检测
- **显示选项**：选择显示的检测类别
- **颜色方案**：自定义检测框颜色

### 3. 数据库配置

检测结果存储在 SQLite 数据库中：
- 位置：`E:/Trae/yolo_ohif/data/app.db`
- 表结构：包含研究信息、检测结果、用户信息等

## 故障排除

### 常见问题

1. **检测结果不显示**
   - 检查模型文件是否存在
   - 确认 CUDA 环境配置正确
   - 查看浏览器控制台错误信息

2. **DICOM 图像无法加载**
   - 确认 Orthanc 服务正常运行
   - 检查 DICOM 文件格式是否正确
   - 验证网络连接

3. **性能问题**
   - 使用 GPU 加速（CUDA）
   - 调整图像分辨率
   - 优化置信度阈值

### 日志查看

系统日志位置：
- 应用日志：`E:/Trae/yolo_ohif/logs/app.log`
- 检测日志：控制台输出
- 浏览器日志：F12 开发者工具

## 总结

通过运行 `python app.py`，您将获得一个功能完整的专业 DICOM 阅读器，具备：

- ✅ **自动 AI 检测**：YOLO 模型自动识别病灶
- ✅ **实时结果显示**：检测框和标签实时覆盖在图像上
- ✅ **交互式界面**：专业的医学影像查看工具
- ✅ **结果管理**：检测结果的保存、查看和导出
- ✅ **多格式支持**：支持各种 DICOM 格式
- ✅ **性能优化**：GPU 加速和实时渲染

这个系统为医学影像诊断提供了强大的 AI 辅助功能，大大提高了诊断效率和准确性。