# Python字节码文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
env/
ENV/
.env

# IDE配置文件
.idea/
.vscode/
*.swp
*.swo

# 日志文件
logs/
*.log

# 本地配置文件
instance/
.webassets-cache

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 上传和结果文件
uploads/*
!uploads/.gitkeep
results/*
!results/.gitkeep

# YOLO模型文件（大文件）
models/*.pt
models/*.pth
models/*.onnx
!models/.gitkeep

# 系统文件
.DS_Store
Thumbs.db

# 临时文件
*.tmp
*.bak
*.swp
*~

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 证书文件
*.pem
*.key
*.crt

# 压缩文件
*.zip
*.tar.gz
*.rar

# DICOM文件
*.dcm
*.dicom