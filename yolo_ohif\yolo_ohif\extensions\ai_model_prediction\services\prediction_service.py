"""预测服务

重构后的预测服务，支持异步处理、重试机制和性能监控
"""

import logging
import asyncio
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import aiohttp
import requests
from dataclasses import replace

from ..core.interfaces import PredictionServiceInterface, CacheManagerInterface
from ..core.types import (
    ModelConfig, 
    PredictionResult, 
    DetectionResult, 
    BoundingBox,
    PredictionMetrics,
    PredictionStatus,
    SeverityLevel,
    ImageData
)
from ..core.exceptions import (
    PredictionError,
    APIError,
    NetworkError,
    ValidationError,
    TimeoutError,
    create_prediction_error,
    create_api_error
)

logger = logging.getLogger(__name__)


class PredictionService(PredictionServiceInterface):
    """预测服务
    
    提供AI模型预测功能，支持同步和异步调用
    包含重试机制、缓存和性能监控
    """
    
    def __init__(self, 
                 cache_manager: Optional[CacheManagerInterface] = None,
                 max_retries: int = 3,
                 timeout: float = 30.0,
                 enable_cache: bool = True):
        """初始化预测服务
        
        Args:
            cache_manager: 缓存管理器
            max_retries: 最大重试次数
            timeout: 请求超时时间（秒）
            enable_cache: 是否启用缓存
        """
        self._cache_manager = cache_manager
        self._max_retries = max_retries
        self._timeout = timeout
        self._enable_cache = enable_cache
        self._session: Optional[aiohttp.ClientSession] = None
        
        logger.info(f"预测服务初始化完成 (重试: {max_retries}, 超时: {timeout}s, 缓存: {enable_cache})")
    
    async def predict_async(self, 
                           model_config: ModelConfig, 
                           image_data: ImageData, 
                           options: Optional[Dict] = None) -> PredictionResult:
        """异步预测"""
        start_time = time.time()
        options = options or {}
        
        try:
            # 验证输入
            if not self.validate_input(image_data, model_config):
                raise ValidationError("Invalid input data for prediction")
            
            # 检查缓存
            cache_key = self._generate_cache_key(model_config.id, image_data, options)
            if self._enable_cache and self._cache_manager:
                cached_result = self._cache_manager.get(cache_key)
                if cached_result:
                    logger.debug(f"从缓存获取预测结果: {model_config.id}")
                    return cached_result
            
            # 预处理
            preprocess_start = time.time()
            processed_data = self.preprocess_image(image_data, model_config)
            preprocess_time = time.time() - preprocess_start
            
            # 执行预测
            inference_start = time.time()
            raw_result = await self._call_prediction_api_async(model_config, processed_data, options)
            inference_time = time.time() - inference_start
            
            # 后处理
            postprocess_start = time.time()
            result = self.postprocess_result(raw_result, model_config)
            postprocess_time = time.time() - postprocess_start
            
            # 添加性能指标
            total_time = time.time() - start_time
            metrics = PredictionMetrics(
                inference_time=inference_time,
                preprocessing_time=preprocess_time,
                postprocessing_time=postprocess_time,
                total_time=total_time
            )
            
            result = replace(result, metrics=metrics)
            
            # 缓存结果
            if self._enable_cache and self._cache_manager and result.success:
                self._cache_manager.set(cache_key, result, ttl=3600)  # 缓存1小时
            
            logger.info(f"异步预测完成: {model_config.id}, 耗时: {total_time:.2f}s")
            return result
            
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"异步预测失败: {model_config.id}, 错误: {e}")
            
            return PredictionResult(
                success=False,
                detections=[],
                model_id=model_config.id,
                model_name=model_config.name,
                timestamp=datetime.now(),
                error_message=str(e),
                status=PredictionStatus.FAILED,
                metrics=PredictionMetrics(
                    inference_time=0,
                    preprocessing_time=0,
                    postprocessing_time=0,
                    total_time=total_time
                )
            )
    
    def predict(self, 
               model_config: ModelConfig, 
               image_data: ImageData, 
               options: Optional[Dict] = None) -> PredictionResult:
        """同步预测"""
        start_time = time.time()
        options = options or {}
        
        try:
            # 验证输入
            if not self.validate_input(image_data, model_config):
                raise ValidationError("Invalid input data for prediction")
            
            # 检查缓存
            cache_key = self._generate_cache_key(model_config.id, image_data, options)
            if self._enable_cache and self._cache_manager:
                cached_result = self._cache_manager.get(cache_key)
                if cached_result:
                    logger.debug(f"从缓存获取预测结果: {model_config.id}")
                    return cached_result
            
            # 预处理
            preprocess_start = time.time()
            processed_data = self.preprocess_image(image_data, model_config)
            preprocess_time = time.time() - preprocess_start
            
            # 执行预测
            inference_start = time.time()
            raw_result = self._call_prediction_api_sync(model_config, processed_data, options)
            inference_time = time.time() - inference_start
            
            # 后处理
            postprocess_start = time.time()
            result = self.postprocess_result(raw_result, model_config)
            postprocess_time = time.time() - postprocess_start
            
            # 添加性能指标
            total_time = time.time() - start_time
            metrics = PredictionMetrics(
                inference_time=inference_time,
                preprocessing_time=preprocess_time,
                postprocessing_time=postprocess_time,
                total_time=total_time
            )
            
            result = replace(result, metrics=metrics)
            
            # 缓存结果
            if self._enable_cache and self._cache_manager and result.success:
                self._cache_manager.set(cache_key, result, ttl=3600)  # 缓存1小时
            
            logger.info(f"同步预测完成: {model_config.id}, 耗时: {total_time:.2f}s")
            return result
            
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"同步预测失败: {model_config.id}, 错误: {e}")
            
            return PredictionResult(
                success=False,
                detections=[],
                model_id=model_config.id,
                model_name=model_config.name,
                timestamp=datetime.now(),
                error_message=str(e),
                status=PredictionStatus.FAILED,
                metrics=PredictionMetrics(
                    inference_time=0,
                    preprocessing_time=0,
                    postprocessing_time=0,
                    total_time=total_time
                )
            )
    
    def validate_input(self, image_data: ImageData, model_config: ModelConfig) -> bool:
        """验证输入数据"""
        try:
            # 检查图像数据是否为空
            if image_data is None:
                logger.error("图像数据为空")
                return False
            
            # 检查数据类型
            if isinstance(image_data, bytes):
                if len(image_data) == 0:
                    logger.error("图像数据长度为0")
                    return False
            elif isinstance(image_data, str):
                if not image_data.strip():
                    logger.error("图像数据字符串为空")
                    return False
            
            # 检查模型配置
            if not model_config.prediction_api:
                logger.error("模型预测API未配置")
                return False
            
            logger.debug(f"输入验证通过: {model_config.id}")
            return True
            
        except Exception as e:
            logger.error(f"输入验证失败: {e}")
            return False
    
    def preprocess_image(self, image_data: ImageData, model_config: ModelConfig) -> Any:
        """预处理图像"""
        try:
            # 基础预处理逻辑
            processed_data = image_data
            
            # 应用模型特定的预处理配置
            preprocessing_config = model_config.preprocessing_config
            if preprocessing_config:
                # 这里可以添加具体的预处理逻辑
                # 例如：调整大小、归一化等
                logger.debug(f"应用预处理配置: {preprocessing_config}")
            
            return processed_data
            
        except Exception as e:
            raise create_prediction_error(
                model_config.id, 
                "preprocessing", 
                e
            )
    
    def postprocess_result(self, raw_result: Any, model_config: ModelConfig) -> PredictionResult:
        """后处理结果"""
        try:
            detections = []
            
            # 解析原始结果
            if isinstance(raw_result, dict):
                predictions = raw_result.get('predictions', [])
                
                for pred in predictions:
                    # 创建边界框
                    bbox_data = pred.get('bbox', [])
                    if len(bbox_data) >= 4:
                        bbox = BoundingBox(
                            x=float(bbox_data[0]),
                            y=float(bbox_data[1]),
                            width=float(bbox_data[2]),
                            height=float(bbox_data[3])
                        )
                        
                        # 评估严重程度
                        confidence = float(pred.get('confidence', 0))
                        severity = self._assess_severity(confidence)
                        
                        detection = DetectionResult(
                            class_name=pred.get('class_name', 'Unknown'),
                            confidence=confidence,
                            bbox=bbox,
                            class_id=pred.get('class_id'),
                            severity=severity,
                            attributes=pred.get('attributes', {})
                        )
                        
                        detections.append(detection)
            
            # 应用置信度阈值过滤
            threshold = model_config.confidence_threshold
            filtered_detections = [
                det for det in detections 
                if det.confidence >= threshold
            ]
            
            # 限制检测数量
            max_detections = model_config.max_detections
            if len(filtered_detections) > max_detections:
                # 按置信度排序并取前N个
                filtered_detections.sort(key=lambda x: x.confidence, reverse=True)
                filtered_detections = filtered_detections[:max_detections]
            
            return PredictionResult(
                success=True,
                detections=filtered_detections,
                model_id=model_config.id,
                model_name=model_config.name,
                timestamp=datetime.now(),
                status=PredictionStatus.COMPLETED
            )
            
        except Exception as e:
            raise create_prediction_error(
                model_config.id, 
                "postprocessing", 
                e
            )
    
    async def _call_prediction_api_async(self, 
                                        model_config: ModelConfig, 
                                        image_data: Any, 
                                        options: Dict) -> Dict:
        """异步调用预测API"""
        if not self._session:
            self._session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self._timeout))
        
        for attempt in range(self._max_retries + 1):
            try:
                # 准备请求数据
                request_data = self._prepare_request_data(image_data, options)
                
                async with self._session.post(
                    model_config.prediction_api,
                    json=request_data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result
                    else:
                        error_text = await response.text()
                        raise create_api_error(
                            model_config.prediction_api,
                            response.status,
                            {'error': error_text}
                        )
                        
            except asyncio.TimeoutError:
                if attempt == self._max_retries:
                    raise TimeoutError(
                        f"API request timeout after {self._max_retries} retries",
                        timeout_duration=self._timeout,
                        operation="prediction_api_call"
                    )
                logger.warning(f"API请求超时，重试 {attempt + 1}/{self._max_retries}")
                await asyncio.sleep(2 ** attempt)  # 指数退避
                
            except Exception as e:
                if attempt == self._max_retries:
                    raise NetworkError(
                        f"API request failed after {self._max_retries} retries: {e}",
                        url=model_config.prediction_api,
                        retry_count=attempt
                    )
                logger.warning(f"API请求失败，重试 {attempt + 1}/{self._max_retries}: {e}")
                await asyncio.sleep(2 ** attempt)
    
    def _call_prediction_api_sync(self, 
                                 model_config: ModelConfig, 
                                 image_data: Any, 
                                 options: Dict) -> Dict:
        """同步调用预测API"""
        for attempt in range(self._max_retries + 1):
            try:
                # 准备请求数据
                request_data = self._prepare_request_data(image_data, options)
                
                response = requests.post(
                    model_config.prediction_api,
                    json=request_data,
                    timeout=self._timeout
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    raise create_api_error(
                        model_config.prediction_api,
                        response.status_code,
                        {'error': response.text}
                    )
                    
            except requests.exceptions.Timeout:
                if attempt == self._max_retries:
                    raise TimeoutError(
                        f"API request timeout after {self._max_retries} retries",
                        timeout_duration=self._timeout,
                        operation="prediction_api_call"
                    )
                logger.warning(f"API请求超时，重试 {attempt + 1}/{self._max_retries}")
                time.sleep(2 ** attempt)
                
            except Exception as e:
                if attempt == self._max_retries:
                    raise NetworkError(
                        f"API request failed after {self._max_retries} retries: {e}",
                        url=model_config.prediction_api,
                        retry_count=attempt
                    )
                logger.warning(f"API请求失败，重试 {attempt + 1}/{self._max_retries}: {e}")
                time.sleep(2 ** attempt)
    
    def _prepare_request_data(self, image_data: Any, options: Dict) -> Dict:
        """准备请求数据"""
        request_data = {
            'image_data': image_data,
            'options': options
        }
        
        # 添加其他必要的请求参数
        if 'confidence_threshold' in options:
            request_data['confidence_threshold'] = options['confidence_threshold']
        
        return request_data
    
    def _generate_cache_key(self, model_id: str, image_data: Any, options: Dict) -> str:
        """生成缓存键"""
        # 简化的缓存键生成逻辑
        # 在实际应用中，可能需要更复杂的哈希算法
        import hashlib
        
        key_parts = [model_id, str(hash(str(image_data))), str(sorted(options.items()))]
        key_string = "|".join(key_parts)
        
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _assess_severity(self, confidence: float) -> SeverityLevel:
        """评估检测结果的严重程度"""
        if confidence >= 0.9:
            return SeverityLevel.CRITICAL
        elif confidence >= 0.7:
            return SeverityLevel.HIGH
        elif confidence >= 0.5:
            return SeverityLevel.MEDIUM
        else:
            return SeverityLevel.LOW
    
    async def cleanup(self) -> None:
        """清理资源"""
        if self._session:
            await self._session.close()
            self._session = None
        
        logger.info("预测服务已清理")