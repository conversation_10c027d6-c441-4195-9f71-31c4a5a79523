namespace MedicalImageAnalysis.Core.Models;

/// <summary>
/// 图像坐标信息
/// </summary>
public class ImageCoordinateInfo
{
    /// <summary>
    /// 图像位置（患者坐标系）
    /// </summary>
    public Vector3D ImagePosition { get; set; } = new();

    /// <summary>
    /// 图像方向（患者坐标系）
    /// </summary>
    public ImageOrientation ImageOrientation { get; set; } = new();

    /// <summary>
    /// 像素间距
    /// </summary>
    public PixelSpacing PixelSpacing { get; set; } = new();

    /// <summary>
    /// 层厚
    /// </summary>
    public double SliceThickness { get; set; }

    /// <summary>
    /// 层间距
    /// </summary>
    public double? SpacingBetweenSlices { get; set; }

    /// <summary>
    /// 层位置
    /// </summary>
    public double? SliceLocation { get; set; }

    /// <summary>
    /// 图像尺寸
    /// </summary>
    public ImageDimensions Dimensions { get; set; } = new();

    /// <summary>
    /// 坐标系类型
    /// </summary>
    public CoordinateSystemType CoordinateSystem { get; set; } = CoordinateSystemType.Patient;

    /// <summary>
    /// 解剖方向
    /// </summary>
    public AnatomicalOrientation AnatomicalOrientation { get; set; } = new();

    /// <summary>
    /// 将像素坐标转换为患者坐标
    /// </summary>
    /// <param name="pixelX">像素X坐标</param>
    /// <param name="pixelY">像素Y坐标</param>
    /// <returns>患者坐标</returns>
    public Vector3D PixelToPatientCoordinate(double pixelX, double pixelY)
    {
        // 使用DICOM标准的坐标变换公式
        var x = ImageOrientation.RowDirection.X * pixelX * PixelSpacing.Row +
                ImageOrientation.ColumnDirection.X * pixelY * PixelSpacing.Column +
                ImagePosition.X;

        var y = ImageOrientation.RowDirection.Y * pixelX * PixelSpacing.Row +
                ImageOrientation.ColumnDirection.Y * pixelY * PixelSpacing.Column +
                ImagePosition.Y;

        var z = ImageOrientation.RowDirection.Z * pixelX * PixelSpacing.Row +
                ImageOrientation.ColumnDirection.Z * pixelY * PixelSpacing.Column +
                ImagePosition.Z;

        return new Vector3D(x, y, z);
    }

    /// <summary>
    /// 将患者坐标转换为像素坐标
    /// </summary>
    /// <param name="patientCoordinate">患者坐标</param>
    /// <returns>像素坐标</returns>
    public (double X, double Y) PatientToPixelCoordinate(Vector3D patientCoordinate)
    {
        // 计算相对于图像位置的偏移
        var offset = patientCoordinate - ImagePosition;

        // 使用逆变换矩阵计算像素坐标
        var det = ImageOrientation.RowDirection.X * ImageOrientation.ColumnDirection.Y -
                  ImageOrientation.RowDirection.Y * ImageOrientation.ColumnDirection.X;

        if (Math.Abs(det) < 1e-10)
            throw new InvalidOperationException("图像方向矩阵不可逆");

        var pixelX = (ImageOrientation.ColumnDirection.Y * offset.X - ImageOrientation.ColumnDirection.X * offset.Y) / 
                     (det * PixelSpacing.Row);

        var pixelY = (ImageOrientation.RowDirection.X * offset.Y - ImageOrientation.RowDirection.Y * offset.X) / 
                     (det * PixelSpacing.Column);

        return (pixelX, pixelY);
    }

    /// <summary>
    /// 获取体素大小
    /// </summary>
    public Vector3D VoxelSize => new(PixelSpacing.Row, PixelSpacing.Column, SliceThickness);

    /// <summary>
    /// 获取图像边界框（患者坐标系）
    /// </summary>
    public BoundingBox3D GetImageBounds()
    {
        var corners = new[]
        {
            PixelToPatientCoordinate(0, 0),
            PixelToPatientCoordinate(Dimensions.Width - 1, 0),
            PixelToPatientCoordinate(0, Dimensions.Height - 1),
            PixelToPatientCoordinate(Dimensions.Width - 1, Dimensions.Height - 1)
        };

        var minX = corners.Min(c => c.X);
        var maxX = corners.Max(c => c.X);
        var minY = corners.Min(c => c.Y);
        var maxY = corners.Max(c => c.Y);
        var minZ = corners.Min(c => c.Z);
        var maxZ = corners.Max(c => c.Z);

        return new BoundingBox3D(
            new Vector3D(minX, minY, minZ),
            new Vector3D(maxX, maxY, maxZ)
        );
    }
}

/// <summary>
/// 3D向量
/// </summary>
public class Vector3D
{
    public double X { get; set; }
    public double Y { get; set; }
    public double Z { get; set; }

    public Vector3D() { }

    public Vector3D(double x, double y, double z)
    {
        X = x;
        Y = y;
        Z = z;
    }

    public static Vector3D operator +(Vector3D a, Vector3D b) => new(a.X + b.X, a.Y + b.Y, a.Z + b.Z);
    public static Vector3D operator -(Vector3D a, Vector3D b) => new(a.X - b.X, a.Y - b.Y, a.Z - b.Z);
    public static Vector3D operator *(Vector3D a, double scalar) => new(a.X * scalar, a.Y * scalar, a.Z * scalar);

    public double Length => Math.Sqrt(X * X + Y * Y + Z * Z);
    public Vector3D Normalize() => Length > 0 ? this * (1.0 / Length) : new Vector3D();

    public override string ToString() => $"({X:F3}, {Y:F3}, {Z:F3})";
}

/// <summary>
/// 图像方向
/// </summary>
public class ImageOrientation
{
    /// <summary>
    /// 行方向向量
    /// </summary>
    public Vector3D RowDirection { get; set; } = new(1, 0, 0);

    /// <summary>
    /// 列方向向量
    /// </summary>
    public Vector3D ColumnDirection { get; set; } = new(0, 1, 0);

    /// <summary>
    /// 法向量（垂直于图像平面）
    /// </summary>
    public Vector3D NormalDirection
    {
        get
        {
            // 计算叉积得到法向量
            return new Vector3D(
                RowDirection.Y * ColumnDirection.Z - RowDirection.Z * ColumnDirection.Y,
                RowDirection.Z * ColumnDirection.X - RowDirection.X * ColumnDirection.Z,
                RowDirection.X * ColumnDirection.Y - RowDirection.Y * ColumnDirection.X
            ).Normalize();
        }
    }
}

/// <summary>
/// 像素间距
/// </summary>
public class PixelSpacing
{
    /// <summary>
    /// 行间距（mm）
    /// </summary>
    public double Row { get; set; } = 1.0;

    /// <summary>
    /// 列间距（mm）
    /// </summary>
    public double Column { get; set; } = 1.0;

    public override string ToString() => $"Row: {Row:F3}mm, Column: {Column:F3}mm";
}

/// <summary>
/// 图像尺寸
/// </summary>
public class ImageDimensions
{
    /// <summary>
    /// 宽度（像素）
    /// </summary>
    public int Width { get; set; }

    /// <summary>
    /// 高度（像素）
    /// </summary>
    public int Height { get; set; }

    /// <summary>
    /// 深度（层数）
    /// </summary>
    public int Depth { get; set; } = 1;

    public override string ToString() => $"{Width} × {Height} × {Depth}";
}

/// <summary>
/// 坐标系类型
/// </summary>
public enum CoordinateSystemType
{
    /// <summary>
    /// 患者坐标系
    /// </summary>
    Patient,

    /// <summary>
    /// 设备坐标系
    /// </summary>
    Device,

    /// <summary>
    /// 图像坐标系
    /// </summary>
    Image
}

/// <summary>
/// 解剖方向
/// </summary>
public class AnatomicalOrientation
{
    /// <summary>
    /// 主要方向（如：轴位、矢状位、冠状位）
    /// </summary>
    public string PrimaryOrientation { get; set; } = string.Empty;

    /// <summary>
    /// 行方向标签（如：L-R, A-P, S-I）
    /// </summary>
    public string RowOrientationLabel { get; set; } = string.Empty;

    /// <summary>
    /// 列方向标签
    /// </summary>
    public string ColumnOrientationLabel { get; set; } = string.Empty;

    /// <summary>
    /// 是否为轴位
    /// </summary>
    public bool IsAxial => PrimaryOrientation.Contains("AXIAL", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// 是否为矢状位
    /// </summary>
    public bool IsSagittal => PrimaryOrientation.Contains("SAGITTAL", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// 是否为冠状位
    /// </summary>
    public bool IsCoronal => PrimaryOrientation.Contains("CORONAL", StringComparison.OrdinalIgnoreCase);
}

/// <summary>
/// 3D边界框
/// </summary>
public class BoundingBox3D
{
    public Vector3D Min { get; set; }
    public Vector3D Max { get; set; }

    public BoundingBox3D(Vector3D min, Vector3D max)
    {
        Min = min;
        Max = max;
    }

    public Vector3D Size => Max - Min;
    public Vector3D Center => (Min + Max) * 0.5;

    public override string ToString() => $"Min: {Min}, Max: {Max}";
}
