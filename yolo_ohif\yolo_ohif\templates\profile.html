{% extends "base.html" %}

{% block title %}个人资料 - YOLO-OHIF医学图像疾病检测系统{% endblock %}

{% block extra_css %}
<style>
    .profile-header {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
    }
    .profile-avatar {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        object-fit: cover;
        border: 5px solid #fff;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .profile-stats {
        background-color: #fff;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
        margin-bottom: 20px;
    }
    .stat-item {
        text-align: center;
        padding: 10px;
    }
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: #0d6efd;
    }
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    .profile-card {
        height: 100%;
        transition: transform 0.3s;
    }
    .profile-card:hover {
        transform: translateY(-5px);
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- 个人资料头部 -->
    <div class="profile-header shadow-sm">
        <div class="row align-items-center">
            <div class="col-md-3 text-center">
                <img src="{{ url_for('static', filename='images/default_avatar.svg') }}" alt="用户头像" class="profile-avatar">
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#changeAvatarModal">
                        <i class="fas fa-camera me-1"></i>更换头像
                    </button>
                </div>
            </div>
            <div class="col-md-9">
                <h2>{{ user.username }}</h2>
                <p class="text-muted">{{ user.role }}</p>
                <p><i class="fas fa-envelope me-2"></i>{{ user.email }}</p>
                <p><i class="fas fa-calendar-alt me-2"></i>注册时间: {{ user.created_at | safe_strftime }}</p>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                        <i class="fas fa-edit me-1"></i>编辑资料
                    </button>
                    <button class="btn btn-outline-secondary ms-2" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                        <i class="fas fa-key me-1"></i>修改密码
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="profile-stats mb-4">
        <div class="row">
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <div class="stat-value">{{ stats.total_studies }}</div>
                    <div class="stat-label">上传研究</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <div class="stat-value">{{ stats.detected_studies }}</div>
                    <div class="stat-label">已检测研究</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <div class="stat-value">{{ stats.abnormal_findings }}</div>
                    <div class="stat-label">异常发现</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <div class="stat-value">{{ stats.annotations }}</div>
                    <div class="stat-label">创建标注</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 个人资料卡片 -->
    <div class="row">
        <!-- 最近活动 -->
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm profile-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>最近活动</h5>
                </div>
                <div class="card-body">
                    {% if activities %}
                        <ul class="list-group list-group-flush">
                            {% for activity in activities %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="{{ activity.icon }} me-2"></i>
                                        {{ activity.description }}
                                    </div>
                                    <small class="text-muted">{{ activity.timestamp }}</small>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p>暂无活动记录</p>
                        </div>
                    {% endif %}
                </div>
                {% if activities %}
                    <div class="card-footer text-center">
                        <a href="#" class="btn btn-sm btn-outline-primary">查看所有活动</a>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- 最近上传 -->
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm profile-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-upload me-2"></i>最近上传</h5>
                </div>
                <div class="card-body">
                    {% if recent_uploads %}
                        <div class="list-group list-group-flush">
                            {% for upload in recent_uploads %}
                                <a href="{{ url_for('web.viewer', study_id=upload.id) }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">{{ upload.patient_name }}</h6>
                                        <small class="text-muted">{{ upload.upload_date }}</small>
                                    </div>
                                    <p class="mb-1">{{ upload.description }}</p>
                                    <small>
                                        <span class="badge bg-secondary">{{ upload.modality }}</span>
                                        <span class="badge {% if upload.is_detected %}bg-success{% else %}bg-warning{% endif %}">
                                            {% if upload.is_detected %}已检测{% else %}未检测{% endif %}
                                        </span>
                                    </small>
                                </a>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <p>暂无上传记录</p>
                            <a href="{{ url_for('web.upload') }}" class="btn btn-primary">
                                <i class="fas fa-upload me-1"></i>上传医学图像
                            </a>
                        </div>
                    {% endif %}
                </div>
                {% if recent_uploads %}
                    <div class="card-footer text-center">
                        <a href="{{ url_for('web.dashboard') }}" class="btn btn-sm btn-outline-primary">查看所有研究</a>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- 系统使用情况 -->
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm profile-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>系统使用情况</h5>
                </div>
                <div class="card-body">
                    <canvas id="usageChart" width="400" height="250"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 账户设置 -->
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm profile-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-cog me-2"></i>账户设置</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action" data-bs-toggle="modal" data-bs-target="#notificationSettingsModal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">通知设置</h6>
                                <i class="fas fa-chevron-right"></i>
                            </div>
                            <p class="mb-1 text-muted">管理电子邮件和系统通知</p>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" data-bs-toggle="modal" data-bs-target="#privacySettingsModal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">隐私设置</h6>
                                <i class="fas fa-chevron-right"></i>
                            </div>
                            <p class="mb-1 text-muted">管理数据共享和隐私选项</p>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" data-bs-toggle="modal" data-bs-target="#exportDataModal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">导出数据</h6>
                                <i class="fas fa-chevron-right"></i>
                            </div>
                            <p class="mb-1 text-muted">下载您的数据副本</p>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action text-danger" data-bs-toggle="modal" data-bs-target="#deleteAccountModal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">删除账户</h6>
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <p class="mb-1 text-muted">永久删除您的账户和所有数据</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑资料模态框 -->
<div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editProfileModalLabel">编辑个人资料</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('web.update_profile') }}" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="username" name="username" value="{{ user.username }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">电子邮箱</label>
                        <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="fullname" class="form-label">姓名</label>
                        <input type="text" class="form-control" id="fullname" name="fullname" value="{{ user.fullname }}">
                    </div>
                    <div class="mb-3">
                        <label for="organization" class="form-label">组织/机构</label>
                        <input type="text" class="form-control" id="organization" name="organization" value="{{ user.organization }}">
                    </div>
                    <div class="mb-3">
                        <label for="bio" class="form-label">个人简介</label>
                        <textarea class="form-control" id="bio" name="bio" rows="3">{{ user.bio }}</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存更改</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 修改密码模态框 -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changePasswordModalLabel">修改密码</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('web.change_password') }}" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">当前密码</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">新密码</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                        <div class="form-text">密码长度至少为8个字符，包含大小写字母、数字和特殊字符。</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">确认新密码</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">更改密码</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 更换头像模态框 -->
<div class="modal fade" id="changeAvatarModal" tabindex="-1" aria-labelledby="changeAvatarModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changeAvatarModalLabel">更换头像</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('web.update_avatar') }}" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="avatar" class="form-label">选择新头像</label>
                        <input class="form-control" type="file" id="avatar" name="avatar" accept="image/*" required>
                        <div class="form-text">支持JPG、PNG格式，文件大小不超过2MB。</div>
                    </div>
                    <div class="text-center mt-3">
                        <img id="avatar-preview" src="#" alt="头像预览" class="img-thumbnail" style="max-width: 200px; max-height: 200px; display: none;">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">上传头像</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 通知设置模态框 -->
<div class="modal fade" id="notificationSettingsModal" tabindex="-1" aria-labelledby="notificationSettingsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notificationSettingsModalLabel">通知设置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('web.update_notification_settings') }}" method="post">
                <div class="modal-body">
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="email_detection" name="email_detection" {% if user.notification_settings.email_detection %}checked{% endif %}>
                        <label class="form-check-label" for="email_detection">检测完成电子邮件通知</label>
                    </div>
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="email_system" name="email_system" {% if user.notification_settings.email_system %}checked{% endif %}>
                        <label class="form-check-label" for="email_system">系统更新电子邮件通知</label>
                    </div>
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="browser_notifications" name="browser_notifications" {% if user.notification_settings.browser_notifications %}checked{% endif %}>
                        <label class="form-check-label" for="browser_notifications">浏览器推送通知</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存设置</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 隐私设置模态框 -->
<div class="modal fade" id="privacySettingsModal" tabindex="-1" aria-labelledby="privacySettingsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="privacySettingsModalLabel">隐私设置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('web.update_privacy_settings') }}" method="post">
                <div class="modal-body">
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="share_anonymized_data" name="share_anonymized_data" {% if user.privacy_settings.share_anonymized_data %}checked{% endif %}>
                        <label class="form-check-label" for="share_anonymized_data">允许共享匿名化数据用于改进AI模型</label>
                        <div class="form-text">我们只会共享完全匿名化的数据，不包含任何可识别的个人信息。</div>
                    </div>
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="usage_analytics" name="usage_analytics" {% if user.privacy_settings.usage_analytics %}checked{% endif %}>
                        <label class="form-check-label" for="usage_analytics">允许收集使用分析数据</label>
                        <div class="form-text">帮助我们了解系统的使用情况，改进用户体验。</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存设置</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 导出数据模态框 -->
<div class="modal fade" id="exportDataModal" tabindex="-1" aria-labelledby="exportDataModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportDataModalLabel">导出数据</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您可以导出以下数据：</p>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="export_profile" checked>
                    <label class="form-check-label" for="export_profile">个人资料信息</label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="export_studies" checked>
                    <label class="form-check-label" for="export_studies">研究元数据</label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="export_detection_results" checked>
                    <label class="form-check-label" for="export_detection_results">检测结果</label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="export_annotations" checked>
                    <label class="form-check-label" for="export_annotations">标注数据</label>
                </div>
                <div class="form-check mb-4">
                    <input class="form-check-input" type="checkbox" id="export_dicom_files">
                    <label class="form-check-label" for="export_dicom_files">DICOM文件（可能较大）</label>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>数据导出可能需要几分钟时间。导出完成后，我们会通过电子邮件通知您。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startExport">开始导出</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除账户模态框 -->
<div class="modal fade" id="deleteAccountModal" tabindex="-1" aria-labelledby="deleteAccountModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteAccountModalLabel">删除账户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('web.delete_account') }}" method="post">
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>警告：账户删除是永久性的，无法恢复！
                    </div>
                    <p>删除账户将会：</p>
                    <ul>
                        <li>永久删除您的个人资料和账户信息</li>
                        <li>删除您上传的所有研究和DICOM文件</li>
                        <li>删除所有检测结果和标注数据</li>
                        <li>取消您对系统的访问权限</li>
                    </ul>
                    <div class="mb-3">
                        <label for="delete_confirmation" class="form-label">请输入"DELETE"确认删除：</label>
                        <input type="text" class="form-control" id="delete_confirmation" name="delete_confirmation" required>
                    </div>
                    <div class="mb-3">
                        <label for="password_confirmation" class="form-label">请输入您的密码：</label>
                        <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger" id="confirmDelete" disabled>删除账户</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // 头像预览
    document.getElementById('avatar').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('avatar-preview');
                preview.src = e.target.result;
                preview.style.display = 'block';
            }
            reader.readAsDataURL(file);
        }
    });
    
    // 删除账户确认
    document.getElementById('delete_confirmation').addEventListener('input', function() {
        const confirmButton = document.getElementById('confirmDelete');
        if (this.value === 'DELETE') {
            confirmButton.disabled = false;
        } else {
            confirmButton.disabled = true;
        }
    });
    
    // 使用情况图表
    const ctx = document.getElementById('usageChart').getContext('2d');
    const usageChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: {{ usage_data.dates|tojson }},
            datasets: [{
                label: '上传研究',
                data: {{ usage_data.uploads|tojson }},
                borderColor: 'rgba(13, 110, 253, 1)',
                backgroundColor: 'rgba(13, 110, 253, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'YOLO检测',
                data: {{ usage_data.detections|tojson }},
                borderColor: 'rgba(25, 135, 84, 1)',
                backgroundColor: 'rgba(25, 135, 84, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: '过去30天的系统使用情况'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
    
    // 导出数据
    document.getElementById('startExport').addEventListener('click', function() {
        const exportOptions = {
            profile: document.getElementById('export_profile').checked,
            studies: document.getElementById('export_studies').checked,
            detection_results: document.getElementById('export_detection_results').checked,
            annotations: document.getElementById('export_annotations').checked,
            dicom_files: document.getElementById('export_dicom_files').checked
        };
        
        // 发送AJAX请求
        fetch('{{ url_for("web.export_data") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify(exportOptions)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('数据导出请求已提交，完成后我们将通过电子邮件通知您。');
                $('#exportDataModal').modal('hide');
            } else {
                alert('导出请求失败：' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('发生错误，请稍后重试。');
        });
    });
</script>
{% endblock %}
{% endblock %}