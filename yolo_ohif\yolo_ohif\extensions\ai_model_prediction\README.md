# AI模型预测扩展 (重构版)

这是一个为OHIF Viewer设计的AI模型预测扩展的重构版本，采用了现代化的架构设计，包括依赖注入、分层架构和强类型支持。

## 🚀 新特性

### 架构改进
- **依赖注入**: 使用服务容器管理依赖关系
- **分层架构**: 清晰的核心层、服务层和接口层
- **强类型支持**: 完整的类型定义和接口抽象
- **异步支持**: 原生支持异步预测和处理
- **事件驱动**: 基于事件的松耦合架构

### 功能增强
- **缓存系统**: 智能缓存预测结果，提升性能
- **错误处理**: 精细化的异常处理和错误恢复
- **配置验证**: 严格的配置验证和类型检查
- **性能监控**: 内置性能指标和统计信息
- **可扩展性**: 易于扩展和自定义的插件架构

## 📁 项目结构

```
ai_model_prediction/
├── core/                          # 核心模块
│   ├── __init__.py               # 核心接口导出
│   ├── interfaces.py             # 服务接口定义
│   ├── types.py                  # 数据类型定义
│   ├── exceptions.py             # 异常类定义
│   └── container.py              # 依赖注入容器
├── services/                      # 服务层
│   ├── __init__.py               # 服务导出
│   ├── model_manager_service.py  # 模型管理服务
│   ├── prediction_service.py     # 预测服务
│   ├── ui_service.py             # UI组件服务
│   ├── cache_service.py          # 缓存服务
│   ├── event_service.py          # 事件服务
│   ├── config_service.py         # 配置服务
│   ├── validation_service.py     # 验证服务
│   └── null_services.py          # 空实现服务
├── refactored_extension.py       # 重构后的主扩展类
├── factory.py                    # 扩展工厂和构建器
├── test_refactored_extension.py  # 重构后的测试套件
├── README.md                     # 项目文档
└── 原有文件...                   # 保留的原有文件
```

## 🛠 快速开始

### 基本使用

```python
from ai_model_prediction.factory import create_extension

# 创建默认扩展
extension = create_extension()

# 获取可用模型
models = extension.get_available_models()
print(f"可用模型: {len(models)}个")

# 运行预测
result = extension.run_prediction(image_data)
if result:
    print(f"检测到 {len(result.detections)} 个目标")

# 清理资源
extension.cleanup()
```

### 使用配置文件

```python
from ai_model_prediction.factory import create_extension

# 从JSON配置文件创建
extension = create_extension(config_file='config.json')

# 从字典配置创建
config = {
    'name': '自定义AI扩展',
    'version': '1.0.0',
    'models': [
        {
            'id': 'chest_xray_model',
            'name': '胸部X光检测模型',
            'type': 'detection',
            'modality': 'CR',
            'endpoint': 'http://localhost:8000/predict',
            'classes': ['pneumonia', 'normal'],
            'confidence_threshold': 0.7,
            'enabled': True
        }
    ],
    'options': {
        'auto_detection': True,
        'cache_enabled': True,
        'cache_ttl': 300
    }
}

extension = create_extension(config=config)
```

### 使用构建器模式

```python
from ai_model_prediction.factory import create_builder

# 使用流式API构建扩展
extension = (create_builder()
    .with_name('高级AI扩展')
    .with_version('2.0.0')
    .add_model({
        'id': 'brain_mri_model',
        'name': '脑部MRI分析',
        'type': 'segmentation',
        'modality': 'MR',
        'endpoint': 'http://localhost:8001/predict'
    })
    .enable_auto_detection()
    .enable_cache(ttl=600)
    .enable_debug()
    .build())
```

### 异步预测

```python
import asyncio

async def run_async_prediction():
    extension = create_extension()
    
    # 异步运行预测
    result = await extension.run_prediction_async(image_data)
    
    if result:
        print(f"异步预测完成: {len(result.detections)}个检测结果")
        print(f"处理时间: {result.processing_time:.2f}秒")
    
    extension.cleanup()

# 运行异步预测
asyncio.run(run_async_prediction())
```

## 🔧 高级配置

### 自定义服务

```python
from ai_model_prediction.factory import ExtensionFactory
from ai_model_prediction.core.interfaces import CacheManagerInterface
from ai_model_prediction.services import CacheService

# 自定义缓存服务
class CustomCacheService(CacheService):
    def __init__(self):
        super().__init__(max_size=1000, default_ttl=600)
    
    def get(self, key: str):
        # 自定义缓存逻辑
        return super().get(key)

# 使用自定义服务创建扩展
custom_services = {
    CacheManagerInterface: CustomCacheService
}

extension = ExtensionFactory.create_with_custom_services(
    custom_services=custom_services
)
```

### 事件监听

```python
# 获取事件服务
event_service = extension.container.get_service(EventManagerInterface)

# 监听预测完成事件
def on_prediction_completed(event_data):
    print(f"预测完成: {event_data.data}")

event_service.subscribe('prediction_completed', on_prediction_completed)

# 监听模型切换事件
def on_model_changed(event_data):
    model_id = event_data.data.get('model_id')
    print(f"模型已切换到: {model_id}")

event_service.subscribe('model_changed', on_model_changed)
```

### 性能监控

```python
# 获取扩展信息和统计
info = extension.get_extension_info()
print(f"扩展信息: {info}")

# 获取各服务的统计信息
cache_service = extension.container.get_service(CacheManagerInterface)
cache_stats = cache_service.get_stats()
print(f"缓存统计: {cache_stats}")

event_service = extension.container.get_service(EventManagerInterface)
event_stats = event_service.get_stats()
print(f"事件统计: {event_stats}")
```

## 🧪 测试

### 运行测试套件

```bash
# 运行所有测试
python test_refactored_extension.py

# 或者使用unittest
python -m unittest test_refactored_extension.py -v
```

### 测试覆盖范围

- ✅ 服务容器和依赖注入
- ✅ 空服务实现
- ✅ 扩展工厂和构建器
- ✅ 重构后的扩展功能
- ✅ 异步功能
- ✅ 集成测试
- ✅ 错误处理

### 创建测试实例

```python
from ai_model_prediction.factory import ExtensionFactory

# 创建用于测试的扩展实例
test_extension = ExtensionFactory.create_test_instance()

# 使用Mock服务进行测试
from unittest.mock import Mock
from ai_model_prediction.core.interfaces import PredictionServiceInterface

mock_prediction_service = Mock(spec=PredictionServiceInterface)
mock_services = {
    PredictionServiceInterface: mock_prediction_service
}

test_extension = ExtensionFactory.create_test_instance(mock_services)
```

## 📊 性能优化

### 缓存策略

```python
# 启用缓存并配置TTL
extension = (create_builder()
    .enable_cache(ttl=300)  # 5分钟TTL
    .with_option('cache_max_size', 100)
    .build())

# 手动管理缓存
cache_service = extension.container.get_service(CacheManagerInterface)
cache_service.set('custom_key', 'custom_value', ttl=600)
value = cache_service.get('custom_key')
```

### 异步处理

```python
# 并发处理多个预测
import asyncio

async def batch_prediction(image_list):
    tasks = []
    for image_data in image_list:
        task = extension.run_prediction_async(image_data)
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    return results

# 运行批量预测
results = asyncio.run(batch_prediction(image_list))
```

## 🔒 安全性

### 输入验证

```python
# 获取验证服务
validation_service = extension.container.get_service(ValidationService)

# 验证图像数据
is_valid = validation_service.validate_image_data(image_data)
if not is_valid:
    raise ValueError("无效的图像数据")

# 验证模型配置
is_valid = validation_service.validate_model_config(model_config)
if not is_valid:
    raise ValueError("无效的模型配置")
```

### 错误处理

```python
from ai_model_prediction.core.exceptions import (
    ExtensionError,
    ModelNotFoundError,
    PredictionError,
    ValidationError
)

try:
    result = extension.run_prediction(image_data)
except ModelNotFoundError as e:
    print(f"模型未找到: {e}")
except PredictionError as e:
    print(f"预测失败: {e}")
except ValidationError as e:
    print(f"验证失败: {e}")
except ExtensionError as e:
    print(f"扩展错误: {e}")
```

## 🔄 迁移指南

### 从原版本迁移

1. **更新导入**:
   ```python
   # 旧版本
   from extension import AIPredictionExtension
   
   # 新版本
   from ai_model_prediction.factory import create_extension
   ```

2. **更新初始化**:
   ```python
   # 旧版本
   extension = AIPredictionExtension(config)
   extension.initialize()
   
   # 新版本
   extension = create_extension(config=config)
   ```

3. **更新方法调用**:
   ```python
   # 旧版本
   models = extension.get_models_list()  # 已修复
   
   # 新版本
   models = extension.get_available_models()
   ```

### 兼容性

- ✅ 保持原有API的向后兼容性
- ✅ 支持原有配置格式
- ✅ 渐进式迁移支持
- ✅ 详细的迁移文档

## 📈 监控和调试

### 启用调试模式

```python
# 启用调试模式
extension = (create_builder()
    .enable_debug()
    .build())

# 或通过配置
config = {
    'options': {
        'debug_mode': True
    }
}
extension = create_extension(config=config)
```

### 日志配置

```python
import logging

# 配置日志级别
logging.basicConfig(level=logging.DEBUG)

# 或者针对特定模块
logger = logging.getLogger('ai_model_prediction')
logger.setLevel(logging.DEBUG)
```

### 性能分析

```python
# 获取详细的性能统计
info = extension.get_extension_info()
print(f"容器服务数: {info['container_services']}")
print(f"模型数量: {info['models_count']}")
print(f"缓存统计: {info['cache_stats']}")
print(f"事件统计: {info['event_stats']}")
print(f"UI统计: {info['ui_stats']}")
```

## 🤝 贡献指南

### 开发环境设置

1. 克隆项目
2. 安装依赖
3. 运行测试确保环境正常

### 代码规范

- 遵循PEP 8代码风格
- 添加类型注解
- 编写单元测试
- 更新文档

### 提交流程

1. 创建功能分支
2. 实现功能并添加测试
3. 确保所有测试通过
4. 提交Pull Request

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🆘 支持

如果您遇到问题或有建议，请：

1. 查看文档和FAQ
2. 搜索已有的Issues
3. 创建新的Issue描述问题
4. 联系维护团队

---

**重构版本亮点**:
- 🏗️ 现代化架构设计
- 🚀 更好的性能和可扩展性
- 🛡️ 增强的错误处理和验证
- 🧪 完整的测试覆盖
- 📚 详细的文档和示例
- 🔄 平滑的迁移路径