namespace MedicalImageAnalysis.Core.Models;

/// <summary>
/// DICOM验证结果
/// </summary>
public class DicomValidationResult
{
    /// <summary>
    /// 是否验证通过
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 警告信息列表
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 是否包含像素数据
    /// </summary>
    public bool HasPixelData { get; set; }

    /// <summary>
    /// SOP类UID
    /// </summary>
    public string? SopClassUid { get; set; }

    /// <summary>
    /// 传输语法UID
    /// </summary>
    public string? TransferSyntaxUid { get; set; }

    /// <summary>
    /// 文件格式版本
    /// </summary>
    public string? FileFormatVersion { get; set; }

    /// <summary>
    /// 字符集
    /// </summary>
    public string? CharacterSet { get; set; }

    /// <summary>
    /// 验证的规则数量
    /// </summary>
    public int ValidatedRulesCount { get; set; }

    /// <summary>
    /// 通过的规则数量
    /// </summary>
    public int PassedRulesCount { get; set; }

    /// <summary>
    /// 验证时间戳
    /// </summary>
    public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 验证耗时（毫秒）
    /// </summary>
    public long ValidationTimeMs { get; set; }
}
