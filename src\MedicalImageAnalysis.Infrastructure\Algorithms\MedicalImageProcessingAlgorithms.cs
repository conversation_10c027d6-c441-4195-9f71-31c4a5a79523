using MedicalImageAnalysis.Core.Models;
using Microsoft.Extensions.Logging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using System.Numerics;

namespace MedicalImageAnalysis.Infrastructure.Algorithms;

/// <summary>
/// 医学影像处理核心算法库
/// 提供专业级的医学影像处理算法实现
/// </summary>
public class MedicalImageProcessingAlgorithms
{
    private readonly ILogger<MedicalImageProcessingAlgorithms> _logger;

    public MedicalImageProcessingAlgorithms(ILogger<MedicalImageProcessingAlgorithms> logger)
    {
        _logger = logger;
    }

    #region 高级滤波算法

    /// <summary>
    /// 自适应双边滤波
    /// 根据图像局部特征自动调整滤波参数
    /// </summary>
    public async Task<PixelData> AdaptiveBilateralFilterAsync(PixelData pixelData, double sigmaColor = 75, double sigmaSpace = 75)
    {
        _logger.LogInformation("执行自适应双边滤波");
        
        var width = pixelData.Width;
        var height = pixelData.Height;
        var result = new PixelData
        {
            Width = width,
            Height = height,
            DataType = pixelData.DataType,
            Data = new float[width * height]
        };

        var inputData = ConvertToFloatArray(pixelData);
        var outputData = (float[])result.Data;

        await Task.Run(() =>
        {
            // 计算局部方差用于自适应参数调整
            var localVariance = ComputeLocalVariance(inputData, width, height, 5);
            
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    var centerIdx = y * width + x;
                    var centerValue = inputData[centerIdx];
                    var variance = localVariance[centerIdx];
                    
                    // 根据局部方差自适应调整参数
                    var adaptiveSigmaColor = sigmaColor * (1 + variance * 0.1);
                    var adaptiveSigmaSpace = sigmaSpace * (1 + variance * 0.05);
                    
                    float weightSum = 0;
                    float valueSum = 0;
                    
                    // 双边滤波核
                    int kernelRadius = (int)(adaptiveSigmaSpace * 2);
                    for (int dy = -kernelRadius; dy <= kernelRadius; dy++)
                    {
                        for (int dx = -kernelRadius; dx <= kernelRadius; dx++)
                        {
                            int nx = x + dx;
                            int ny = y + dy;
                            
                            if (nx >= 0 && nx < width && ny >= 0 && ny < height)
                            {
                                var neighborIdx = ny * width + nx;
                                var neighborValue = inputData[neighborIdx];
                                
                                // 空间权重
                                var spatialDist = Math.Sqrt(dx * dx + dy * dy);
                                var spatialWeight = Math.Exp(-(spatialDist * spatialDist) / (2 * adaptiveSigmaSpace * adaptiveSigmaSpace));
                                
                                // 颜色权重
                                var colorDist = Math.Abs(centerValue - neighborValue);
                                var colorWeight = Math.Exp(-(colorDist * colorDist) / (2 * adaptiveSigmaColor * adaptiveSigmaColor));
                                
                                var weight = (float)(spatialWeight * colorWeight);
                                weightSum += weight;
                                valueSum += weight * neighborValue;
                            }
                        }
                    }
                    
                    outputData[centerIdx] = weightSum > 0 ? valueSum / weightSum : centerValue;
                }
            }
        });

        return result;
    }

    /// <summary>
    /// 各向异性扩散滤波（Perona-Malik）
    /// 保边去噪，适用于医学影像
    /// </summary>
    public async Task<PixelData> AnisotropicDiffusionFilterAsync(PixelData pixelData, int iterations = 10, double kappa = 30, double lambda = 0.25)
    {
        _logger.LogInformation("执行各向异性扩散滤波");
        
        var width = pixelData.Width;
        var height = pixelData.Height;
        var inputData = ConvertToFloatArray(pixelData);
        var outputData = new float[width * height];
        Array.Copy(inputData, outputData, inputData.Length);

        await Task.Run(() =>
        {
            for (int iter = 0; iter < iterations; iter++)
            {
                var tempData = new float[width * height];
                Array.Copy(outputData, tempData, outputData.Length);

                for (int y = 1; y < height - 1; y++)
                {
                    for (int x = 1; x < width - 1; x++)
                    {
                        var idx = y * width + x;
                        var center = tempData[idx];
                        
                        // 计算梯度
                        var gradN = tempData[(y - 1) * width + x] - center;
                        var gradS = tempData[(y + 1) * width + x] - center;
                        var gradE = tempData[y * width + (x + 1)] - center;
                        var gradW = tempData[y * width + (x - 1)] - center;
                        
                        // 计算扩散系数
                        var cN = DiffusionCoefficient(gradN, kappa);
                        var cS = DiffusionCoefficient(gradS, kappa);
                        var cE = DiffusionCoefficient(gradE, kappa);
                        var cW = DiffusionCoefficient(gradW, kappa);
                        
                        // 更新像素值
                        outputData[idx] = (float)(center + lambda * (cN * gradN + cS * gradS + cE * gradE + cW * gradW));
                    }
                }
            }
        });

        return new PixelData
        {
            Width = width,
            Height = height,
            DataType = typeof(float),
            Data = outputData
        };
    }

    /// <summary>
    /// 非局部均值滤波
    /// 基于图像自相似性的高级去噪算法
    /// </summary>
    public async Task<PixelData> NonLocalMeansFilterAsync(PixelData pixelData, int searchWindow = 21, int templateWindow = 7, double h = 10)
    {
        _logger.LogInformation("执行非局部均值滤波");
        
        var width = pixelData.Width;
        var height = pixelData.Height;
        var inputData = ConvertToFloatArray(pixelData);
        var outputData = new float[width * height];

        await Task.Run(() =>
        {
            int searchRadius = searchWindow / 2;
            int templateRadius = templateWindow / 2;
            double h2 = h * h;

            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    var centerIdx = y * width + x;
                    float weightSum = 0;
                    float valueSum = 0;

                    // 搜索窗口
                    for (int sy = Math.Max(0, y - searchRadius); sy <= Math.Min(height - 1, y + searchRadius); sy++)
                    {
                        for (int sx = Math.Max(0, x - searchRadius); sx <= Math.Min(width - 1, x + searchRadius); sx++)
                        {
                            var searchIdx = sy * width + sx;
                            
                            // 计算模板相似度
                            double similarity = ComputeTemplateSimilarity(inputData, width, height, x, y, sx, sy, templateRadius);
                            
                            // 计算权重
                            var weight = (float)Math.Exp(-Math.Max(similarity - 2 * h2, 0) / h2);
                            
                            weightSum += weight;
                            valueSum += weight * inputData[searchIdx];
                        }
                    }

                    outputData[centerIdx] = weightSum > 0 ? valueSum / weightSum : inputData[centerIdx];
                }
            }
        });

        return new PixelData
        {
            Width = width,
            Height = height,
            DataType = typeof(float),
            Data = outputData
        };
    }

    #endregion

    #region 高级分割算法

    /// <summary>
    /// 自适应阈值分割
    /// 基于局部统计特征的自适应阈值选择
    /// </summary>
    public async Task<PixelData> AdaptiveThresholdSegmentationAsync(PixelData pixelData, int blockSize = 11, double c = 2)
    {
        _logger.LogInformation("执行自适应阈值分割");
        
        var width = pixelData.Width;
        var height = pixelData.Height;
        var inputData = ConvertToFloatArray(pixelData);
        var outputData = new byte[width * height];

        await Task.Run(() =>
        {
            int radius = blockSize / 2;

            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    var centerIdx = y * width + x;
                    var centerValue = inputData[centerIdx];
                    
                    // 计算局部均值
                    float sum = 0;
                    int count = 0;
                    
                    for (int dy = -radius; dy <= radius; dy++)
                    {
                        for (int dx = -radius; dx <= radius; dx++)
                        {
                            int nx = x + dx;
                            int ny = y + dy;
                            
                            if (nx >= 0 && nx < width && ny >= 0 && ny < height)
                            {
                                sum += inputData[ny * width + nx];
                                count++;
                            }
                        }
                    }
                    
                    var localMean = sum / count;
                    var threshold = localMean - c;
                    
                    outputData[centerIdx] = centerValue > threshold ? (byte)255 : (byte)0;
                }
            }
        });

        return new PixelData
        {
            Width = width,
            Height = height,
            DataType = typeof(byte),
            Data = outputData
        };
    }

    /// <summary>
    /// 区域生长分割
    /// 基于种子点的区域生长算法
    /// </summary>
    public async Task<PixelData> RegionGrowingSegmentationAsync(PixelData pixelData, List<(int x, int y)> seedPoints, double threshold = 10)
    {
        _logger.LogInformation("执行区域生长分割，种子点数量: {Count}", seedPoints.Count);
        
        var width = pixelData.Width;
        var height = pixelData.Height;
        var inputData = ConvertToFloatArray(pixelData);
        var outputData = new byte[width * height];
        var visited = new bool[width * height];

        await Task.Run(() =>
        {
            var queue = new Queue<(int x, int y)>();
            
            // 初始化种子点
            foreach (var seed in seedPoints)
            {
                if (seed.x >= 0 && seed.x < width && seed.y >= 0 && seed.y < height)
                {
                    queue.Enqueue(seed);
                    var idx = seed.y * width + seed.x;
                    visited[idx] = true;
                    outputData[idx] = 255;
                }
            }

            // 8连通区域生长
            int[] dx = { -1, -1, -1, 0, 0, 1, 1, 1 };
            int[] dy = { -1, 0, 1, -1, 1, -1, 0, 1 };

            while (queue.Count > 0)
            {
                var current = queue.Dequeue();
                var currentIdx = current.y * width + current.x;
                var currentValue = inputData[currentIdx];

                for (int i = 0; i < 8; i++)
                {
                    int nx = current.x + dx[i];
                    int ny = current.y + dy[i];
                    
                    if (nx >= 0 && nx < width && ny >= 0 && ny < height)
                    {
                        var neighborIdx = ny * width + nx;
                        
                        if (!visited[neighborIdx])
                        {
                            var neighborValue = inputData[neighborIdx];
                            
                            if (Math.Abs(neighborValue - currentValue) <= threshold)
                            {
                                visited[neighborIdx] = true;
                                outputData[neighborIdx] = 255;
                                queue.Enqueue((nx, ny));
                            }
                        }
                    }
                }
            }
        });

        return new PixelData
        {
            Width = width,
            Height = height,
            DataType = typeof(byte),
            Data = outputData
        };
    }

    #endregion

    #region 辅助方法

    private float[] ConvertToFloatArray(PixelData pixelData)
    {
        var data = pixelData.Data;
        var length = pixelData.Width * pixelData.Height;
        var result = new float[length];

        if (data is float[] floatArray)
        {
            Array.Copy(floatArray, result, length);
        }
        else if (data is byte[] byteArray)
        {
            for (int i = 0; i < length; i++)
                result[i] = byteArray[i];
        }
        else if (data is ushort[] ushortArray)
        {
            for (int i = 0; i < length; i++)
                result[i] = ushortArray[i];
        }

        return result;
    }

    private float[] ComputeLocalVariance(float[] data, int width, int height, int kernelSize)
    {
        var variance = new float[width * height];
        int radius = kernelSize / 2;

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                var centerIdx = y * width + x;
                float sum = 0, sumSq = 0;
                int count = 0;

                for (int dy = -radius; dy <= radius; dy++)
                {
                    for (int dx = -radius; dx <= radius; dx++)
                    {
                        int nx = x + dx;
                        int ny = y + dy;
                        
                        if (nx >= 0 && nx < width && ny >= 0 && ny < height)
                        {
                            var value = data[ny * width + nx];
                            sum += value;
                            sumSq += value * value;
                            count++;
                        }
                    }
                }

                var mean = sum / count;
                variance[centerIdx] = (sumSq / count) - (mean * mean);
            }
        }

        return variance;
    }

    private double DiffusionCoefficient(double gradient, double kappa)
    {
        return Math.Exp(-(gradient * gradient) / (kappa * kappa));
    }

    private double ComputeTemplateSimilarity(float[] data, int width, int height, int x1, int y1, int x2, int y2, int templateRadius)
    {
        double similarity = 0;
        int count = 0;

        for (int dy = -templateRadius; dy <= templateRadius; dy++)
        {
            for (int dx = -templateRadius; dx <= templateRadius; dx++)
            {
                int nx1 = x1 + dx, ny1 = y1 + dy;
                int nx2 = x2 + dx, ny2 = y2 + dy;

                if (nx1 >= 0 && nx1 < width && ny1 >= 0 && ny1 < height &&
                    nx2 >= 0 && nx2 < width && ny2 >= 0 && ny2 < height)
                {
                    var diff = data[ny1 * width + nx1] - data[ny2 * width + nx2];
                    similarity += diff * diff;
                    count++;
                }
            }
        }

        return count > 0 ? similarity / count : 0;
    }

    #endregion
}
