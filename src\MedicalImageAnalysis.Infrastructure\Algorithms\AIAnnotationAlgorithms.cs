using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Models;
using Microsoft.Extensions.Logging;
using System.Numerics;
using SystemPoint = System.Drawing.Point;
using SystemRectangle = System.Drawing.Rectangle;
using Detection = MedicalImageAnalysis.Core.Models.Detection;

namespace MedicalImageAnalysis.Infrastructure.Algorithms;

/// <summary>
/// AI标注算法库
/// 提供基于人工智能的高级标注算法
/// </summary>
public class AIAnnotationAlgorithms
{
    private readonly ILogger<AIAnnotationAlgorithms> _logger;

    public AIAnnotationAlgorithms(ILogger<AIAnnotationAlgorithms> logger)
    {
        _logger = logger;
    }

    #region 深度学习目标检测

    /// <summary>
    /// 基于深度学习的目标检测
    /// </summary>
    public async Task<ObjectDetectionResult> DetectObjectsAsync(
        PixelData pixelData, 
        ObjectDetectionConfig config,
        IProgress<DetectionProgress>? progressCallback = null)
    {
        _logger.LogInformation("执行深度学习目标检测");

        var result = new ObjectDetectionResult
        {
            Config = config,
            DetectionTime = DateTime.UtcNow
        };

        await Task.Run(() =>
        {
            progressCallback?.Report(new DetectionProgress { Stage = "预处理图像", Progress = 0.1 });

            // 1. 图像预处理
            var preprocessedData = PreprocessImageForDetection(pixelData, config);

            progressCallback?.Report(new DetectionProgress { Stage = "加载模型", Progress = 0.2 });

            // 2. 加载预训练模型
            var model = LoadDetectionModel(config.ModelPath);

            progressCallback?.Report(new DetectionProgress { Stage = "执行推理", Progress = 0.3 });

            // 3. 执行推理
            var rawDetections = PerformInference(model, preprocessedData, config);

            progressCallback?.Report(new DetectionProgress { Stage = "后处理结果", Progress = 0.7 });

            // 4. 后处理
            result.Detections = PostprocessDetections(rawDetections, config);

            progressCallback?.Report(new DetectionProgress { Stage = "质量评估", Progress = 0.9 });

            // 5. 质量评估
            result.QualityMetrics = EvaluateDetectionQuality(result.Detections, pixelData, config);

            // 6. 置信度校准
            result.Detections = CalibrateConfidences(result.Detections, config);

            progressCallback?.Report(new DetectionProgress { Stage = "完成", Progress = 1.0 });
        });

        _logger.LogInformation("目标检测完成，检测到 {Count} 个对象", result.Detections.Count);
        return result;
    }

    /// <summary>
    /// 多模型集成检测
    /// </summary>
    public async Task<EnsembleDetectionResult> EnsembleDetectionAsync(
        PixelData pixelData,
        List<ObjectDetectionConfig> modelConfigs,
        EnsembleConfig ensembleConfig)
    {
        _logger.LogInformation("执行多模型集成检测，模型数量: {Count}", modelConfigs.Count);

        var result = new EnsembleDetectionResult
        {
            ModelConfigs = modelConfigs,
            EnsembleConfig = ensembleConfig
        };

        await Task.Run(async () =>
        {
            // 1. 并行执行多个模型
            var detectionTasks = modelConfigs.Select(config => 
                DetectObjectsAsync(pixelData, config)).ToArray();

            var detectionResults = await Task.WhenAll(detectionTasks);
            result.IndividualResults = detectionResults.ToList();

            // 2. 融合检测结果
            result.FusedDetections = FuseDetections(detectionResults, ensembleConfig);

            // 3. 计算集成置信度
            result.FusedDetections = CalculateEnsembleConfidence(result.FusedDetections, detectionResults, ensembleConfig);

            // 4. 评估集成质量
            result.EnsembleQuality = EvaluateEnsembleQuality(result.IndividualResults, result.FusedDetections);
        });

        _logger.LogInformation("集成检测完成，融合后检测到 {Count} 个对象", result.FusedDetections.Count);
        return result;
    }

    #endregion

    #region 语义分割

    /// <summary>
    /// 深度学习语义分割
    /// </summary>
    public async Task<SemanticSegmentationResult> PerformSemanticSegmentationAsync(
        PixelData pixelData,
        SegmentationConfig config,
        IProgress<SegmentationProgress>? progressCallback = null)
    {
        _logger.LogInformation("执行语义分割");

        var result = new SemanticSegmentationResult
        {
            Config = config,
            SegmentationTime = DateTime.UtcNow
        };

        await Task.Run(() =>
        {
            progressCallback?.Report(new SegmentationProgress { Stage = "预处理", Progress = 0.1 });

            // 1. 图像预处理
            var preprocessedData = PreprocessImageForSegmentation(pixelData, config);

            progressCallback?.Report(new SegmentationProgress { Stage = "加载模型", Progress = 0.2 });

            // 2. 加载分割模型
            var model = LoadSegmentationModel(config.ModelPath);

            progressCallback?.Report(new SegmentationProgress { Stage = "执行分割", Progress = 0.3 });

            // 3. 执行分割
            var rawSegmentation = PerformSegmentationInference(model, preprocessedData, config);

            progressCallback?.Report(new SegmentationProgress { Stage = "后处理", Progress = 0.7 });

            // 4. 后处理
            result.SegmentationMask = PostprocessSegmentation(rawSegmentation, config);

            // 5. 提取区域
            result.Regions = ExtractSegmentationRegions(result.SegmentationMask, config);

            progressCallback?.Report(new SegmentationProgress { Stage = "质量评估", Progress = 0.9 });

            // 6. 质量评估
            result.QualityMetrics = EvaluateSegmentationQuality(result.SegmentationMask, pixelData, config);

            progressCallback?.Report(new SegmentationProgress { Stage = "完成", Progress = 1.0 });
        });

        _logger.LogInformation("语义分割完成，分割出 {Count} 个区域", result.Regions.Count);
        return result;
    }

    /// <summary>
    /// 实例分割
    /// </summary>
    public async Task<InstanceSegmentationResult> PerformInstanceSegmentationAsync(
        PixelData pixelData,
        InstanceSegmentationConfig config)
    {
        _logger.LogInformation("执行实例分割");

        var result = new InstanceSegmentationResult
        {
            Config = config,
            SegmentationTime = DateTime.UtcNow
        };

        await Task.Run(() =>
        {
            // 1. 预处理
            var preprocessedData = PreprocessImageForInstanceSegmentation(pixelData, config);

            // 2. 加载模型
            var model = LoadInstanceSegmentationModel(config.ModelPath);

            // 3. 执行推理
            var rawResults = PerformInstanceSegmentationInference(model, preprocessedData, config);

            // 4. 后处理
            result.Instances = PostprocessInstanceSegmentation(rawResults, config);

            // 5. 质量评估
            result.QualityMetrics = EvaluateInstanceSegmentationQuality(result.Instances, pixelData, config);
        });

        _logger.LogInformation("实例分割完成，分割出 {Count} 个实例", result.Instances.Count);
        return result;
    }

    #endregion

    #region 主动学习

    /// <summary>
    /// 主动学习样本选择
    /// </summary>
    public async Task<ActiveLearningResult> SelectActiveLearningsamplesAsync(
        List<PixelData> unlabeledData,
        ActiveLearningConfig config)
    {
        _logger.LogInformation("执行主动学习样本选择，候选样本数: {Count}", unlabeledData.Count);

        var result = new ActiveLearningResult
        {
            Config = config,
            SelectionTime = DateTime.UtcNow
        };

        await Task.Run(() =>
        {
            // 1. 计算不确定性
            var uncertaintyScores = CalculateUncertaintyScores(unlabeledData, config);
            result.UncertaintyScores = uncertaintyScores;

            // 2. 计算多样性
            var diversityScores = CalculateDiversityScores(unlabeledData, config);
            result.DiversityScores = diversityScores;

            // 3. 综合评分
            var combinedScores = CombineScores(uncertaintyScores, diversityScores, config);

            // 4. 选择样本
            result.SelectedSamples = SelectTopSamples(unlabeledData, combinedScores, config.SampleCount);

            // 5. 评估选择质量
            result.SelectionQuality = EvaluateSelectionQuality(result.SelectedSamples, unlabeledData, config);
        });

        _logger.LogInformation("主动学习样本选择完成，选择了 {Count} 个样本", result.SelectedSamples.Count);
        return result;
    }

    /// <summary>
    /// 伪标签生成
    /// </summary>
    public async Task<PseudoLabelingResult> GeneratePseudoLabelsAsync(
        List<PixelData> unlabeledData,
        PseudoLabelingConfig config)
    {
        _logger.LogInformation("生成伪标签，数据数量: {Count}", unlabeledData.Count);

        var result = new PseudoLabelingResult
        {
            Config = config,
            GenerationTime = DateTime.UtcNow
        };

        await Task.Run(async () =>
        {
            result.PseudoLabels = new List<PseudoLabel>();

            foreach (var data in unlabeledData)
            {
                // 1. 执行推理
                var detectionResult = await DetectObjectsAsync(data, config.DetectionConfig);

                // 2. 过滤高置信度检测
                var highConfidenceDetections = detectionResult.Detections
                    .Where(d => d.Confidence >= config.ConfidenceThreshold)
                    .ToList();

                // 3. 生成伪标签
                if (highConfidenceDetections.Any())
                {
                    var pseudoLabel = new PseudoLabel
                    {
                        ImageData = data,
                        Detections = highConfidenceDetections,
                        Confidence = highConfidenceDetections.Average(d => d.Confidence),
                        GenerationMethod = "HighConfidenceFiltering"
                    };

                    result.PseudoLabels.Add(pseudoLabel);
                }
            }

            // 4. 质量评估
            result.QualityMetrics = EvaluatePseudoLabelQuality(result.PseudoLabels, config);
        });

        _logger.LogInformation("伪标签生成完成，生成了 {Count} 个伪标签", result.PseudoLabels.Count);
        return result;
    }

    #endregion

    #region 辅助方法

    private PixelData PreprocessImageForDetection(PixelData pixelData, ObjectDetectionConfig config)
    {
        try
        {
            var processedData = ClonePixelData(pixelData);

            // 1. 尺寸调整到模型输入尺寸
            if (pixelData.Width != config.InputSize || pixelData.Height != config.InputSize)
            {
                processedData = ResizeImage(processedData, config.InputSize, config.InputSize);
            }

            // 2. 归一化到[0,1]范围
            NormalizePixelData(processedData);

            // 3. 应用数据增强（如果在训练模式）
            if (config.UseGPU) // 使用GPU标志作为训练模式的简化判断
            {
                processedData = ApplyDataAugmentation(processedData);
            }

            return processedData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "图像预处理失败");
            return pixelData;
        }
    }

    private object LoadDetectionModel(string modelPath)
    {
        try
        {
            if (!File.Exists(modelPath))
            {
                _logger.LogWarning("模型文件不存在: {ModelPath}", modelPath);
                return new ModelPlaceholder { ModelPath = modelPath, IsLoaded = false };
            }

            // 模拟模型加载
            _logger.LogInformation("加载检测模型: {ModelPath}", modelPath);
            return new ModelPlaceholder { ModelPath = modelPath, IsLoaded = true };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "模型加载失败: {ModelPath}", modelPath);
            return new ModelPlaceholder { ModelPath = modelPath, IsLoaded = false };
        }
    }

    private List<Core.Models.Detection> PerformInference(object model, PixelData data, ObjectDetectionConfig config)
    {
        var detections = new List<Core.Models.Detection>();

        try
        {
            var modelPlaceholder = model as ModelPlaceholder;
            if (modelPlaceholder?.IsLoaded != true)
            {
                _logger.LogWarning("模型未正确加载，返回空检测结果");
                return detections;
            }

            // 模拟推理过程
            _logger.LogDebug("执行模型推理，输入尺寸: {Width}x{Height}", data.Width, data.Height);

            // 生成模拟检测结果（实际实现中会调用真实的AI模型）
            detections = GenerateSimulatedDetections(data, config);

            _logger.LogInformation("推理完成，检测到 {Count} 个对象", detections.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "模型推理失败");
        }

        return detections;
    }

    private List<Core.Models.Detection> PostprocessDetections(List<Core.Models.Detection> rawDetections, ObjectDetectionConfig config)
    {
        try
        {
            var processedDetections = new List<Core.Models.Detection>();

            // 1. 置信度过滤
            var filteredDetections = rawDetections
                .Where(d => d.Confidence >= config.ConfidenceThreshold)
                .ToList();

            _logger.LogDebug("置信度过滤后剩余 {Count} 个检测", filteredDetections.Count);

            // 2. 非极大值抑制 (NMS)
            var nmsDetections = ApplyNonMaximumSuppression(filteredDetections, config.IoUThreshold);

            _logger.LogDebug("NMS后剩余 {Count} 个检测", nmsDetections.Count);

            // 3. 限制最大检测数量
            processedDetections = nmsDetections
                .OrderByDescending(d => d.Confidence)
                .Take(config.MaxDetections)
                .ToList();

            _logger.LogInformation("后处理完成，最终检测数量: {Count}", processedDetections.Count);

            return processedDetections;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检测后处理失败");
            return rawDetections.Where(d => d.Confidence >= config.ConfidenceThreshold).ToList();
        }
    }

    private DetectionQualityMetrics EvaluateDetectionQuality(List<Core.Models.Detection> detections, PixelData pixelData, ObjectDetectionConfig config)
    {
        // 评估检测质量
        return new DetectionQualityMetrics
        {
            AverageConfidence = detections.Any() ? detections.Average(d => d.Confidence) : 0,
            DetectionCount = detections.Count,
            QualityScore = 0.8
        };
    }

    private List<Detection> CalibrateConfidences(List<Detection> detections, ObjectDetectionConfig config)
    {
        // 置信度校准
        return detections; // 简化实现
    }

    private List<Detection> FuseDetections(ObjectDetectionResult[] results, EnsembleConfig config)
    {
        // 融合多个模型的检测结果
        var allDetections = results.SelectMany(r => r.Detections).ToList();
        return allDetections; // 简化实现，实际需要NMS等处理
    }

    private List<Detection> CalculateEnsembleConfidence(List<Detection> detections, ObjectDetectionResult[] results, EnsembleConfig config)
    {
        // 计算集成置信度
        return detections; // 简化实现
    }

    private EnsembleQualityMetrics EvaluateEnsembleQuality(List<ObjectDetectionResult> individualResults, List<Detection> fusedDetections)
    {
        // 评估集成质量
        return new EnsembleQualityMetrics
        {
            ModelAgreement = 0.8,
            FusionQuality = 0.85,
            OverallQuality = 0.82
        };
    }

    // 其他辅助方法的占位符实现
    private PixelData PreprocessImageForSegmentation(PixelData pixelData, SegmentationConfig config) => pixelData;
    private object LoadSegmentationModel(string modelPath) => new object();
    private byte[] PerformSegmentationInference(object model, PixelData data, SegmentationConfig config) => new byte[0];
    private byte[] PostprocessSegmentation(byte[] rawSegmentation, SegmentationConfig config) => rawSegmentation;
    private List<SegmentationRegion> ExtractSegmentationRegions(byte[] mask, SegmentationConfig config) => new();
    private SegmentationQualityMetrics EvaluateSegmentationQuality(byte[] mask, PixelData pixelData, SegmentationConfig config) => new();
    private PixelData PreprocessImageForInstanceSegmentation(PixelData pixelData, InstanceSegmentationConfig config) => pixelData;
    private object LoadInstanceSegmentationModel(string modelPath) => new object();
    private List<InstanceMask> PerformInstanceSegmentationInference(object model, PixelData data, InstanceSegmentationConfig config) => new();
    private List<InstanceMask> PostprocessInstanceSegmentation(List<InstanceMask> rawResults, InstanceSegmentationConfig config) => rawResults;
    private InstanceSegmentationQualityMetrics EvaluateInstanceSegmentationQuality(List<InstanceMask> instances, PixelData pixelData, InstanceSegmentationConfig config) => new();
    private Dictionary<int, double> CalculateUncertaintyScores(List<PixelData> data, ActiveLearningConfig config) => new();
    private Dictionary<int, double> CalculateDiversityScores(List<PixelData> data, ActiveLearningConfig config) => new();
    private Dictionary<int, double> CombineScores(Dictionary<int, double> uncertainty, Dictionary<int, double> diversity, ActiveLearningConfig config) => uncertainty;
    private List<PixelData> SelectTopSamples(List<PixelData> data, Dictionary<int, double> scores, int count) => data.Take(count).ToList();
    private ActiveLearningQualityMetrics EvaluateSelectionQuality(List<PixelData> selected, List<PixelData> all, ActiveLearningConfig config) => new();
    private PseudoLabelQualityMetrics EvaluatePseudoLabelQuality(List<PseudoLabel> labels, PseudoLabelingConfig config) => new();

    #endregion

    #region 图像处理辅助方法

    private Core.Models.PixelData ClonePixelData(Core.Models.PixelData original)
    {
        return new Core.Models.PixelData
        {
            Width = original.Width,
            Height = original.Height,
            BitsPerPixel = original.BitsPerPixel,
            Data = original.Data != null ? CloneArray(original.Data) : null
        };
    }

    private Core.Models.PixelData ResizeImage(Core.Models.PixelData pixelData, int newWidth, int newHeight)
    {
        // 简化的双线性插值缩放
        var resized = new Core.Models.PixelData
        {
            Width = newWidth,
            Height = newHeight,
            BitsPerPixel = pixelData.BitsPerPixel,
            Data = new double[newWidth * newHeight]
        };

        var scaleX = (double)pixelData.Width / newWidth;
        var scaleY = (double)pixelData.Height / newHeight;

        for (int y = 0; y < newHeight; y++)
        {
            for (int x = 0; x < newWidth; x++)
            {
                var srcX = x * scaleX;
                var srcY = y * scaleY;

                var x1 = (int)Math.Floor(srcX);
                var y1 = (int)Math.Floor(srcY);
                var x2 = Math.Min(x1 + 1, pixelData.Width - 1);
                var y2 = Math.Min(y1 + 1, pixelData.Height - 1);

                // 双线性插值
                var value = BilinearInterpolation(pixelData, x1, y1, x2, y2, srcX - x1, srcY - y1);
                resized.Data.SetValue(value, y * newWidth + x);
            }
        }

        return resized;
    }

    private double BilinearInterpolation(Core.Models.PixelData pixelData, int x1, int y1, int x2, int y2, double dx, double dy)
    {
        var v11 = GetPixelValue(pixelData, x1, y1);
        var v12 = GetPixelValue(pixelData, x1, y2);
        var v21 = GetPixelValue(pixelData, x2, y1);
        var v22 = GetPixelValue(pixelData, x2, y2);

        var v1 = v11 * (1 - dx) + v21 * dx;
        var v2 = v12 * (1 - dx) + v22 * dx;

        return v1 * (1 - dy) + v2 * dy;
    }

    private double GetPixelValue(Core.Models.PixelData pixelData, int x, int y)
    {
        if (x < 0 || x >= pixelData.Width || y < 0 || y >= pixelData.Height)
            return 0;

        var index = y * pixelData.Width + x;
        return pixelData.Data != null ? Convert.ToDouble(pixelData.Data.GetValue(index)) : 0;
    }

    private void NormalizePixelData(Core.Models.PixelData pixelData)
    {
        if (pixelData.Data == null) return;

        // 转换为double数组以便进行计算
        var values = new double[pixelData.Data.Length];
        for (int i = 0; i < pixelData.Data.Length; i++)
        {
            values[i] = Convert.ToDouble(pixelData.Data.GetValue(i));
        }

        var min = values.Min();
        var max = values.Max();
        var range = max - min;

        if (range > 0)
        {
            for (int i = 0; i < pixelData.Data.Length; i++)
            {
                var normalizedValue = (values[i] - min) / range;
                pixelData.Data.SetValue(normalizedValue, i);
            }
        }
    }

    private Core.Models.PixelData ApplyDataAugmentation(Core.Models.PixelData pixelData)
    {
        // 简化的数据增强：随机亮度调整
        var augmented = ClonePixelData(pixelData);
        var random = new Random();
        var brightnessFactor = 0.8 + random.NextDouble() * 0.4; // 0.8-1.2

        if (augmented.Data != null)
        {
            for (int i = 0; i < augmented.Data.Length; i++)
            {
                var currentValue = Convert.ToDouble(augmented.Data.GetValue(i));
                var newValue = Math.Max(0, Math.Min(1, currentValue * brightnessFactor));
                augmented.Data.SetValue(newValue, i);
            }
        }

        return augmented;
    }

    private List<Core.Models.Detection> GenerateSimulatedDetections(Core.Models.PixelData data, ObjectDetectionConfig config)
    {
        var detections = new List<Core.Models.Detection>();
        var random = new Random();

        // 生成1-3个模拟检测
        var numDetections = random.Next(1, 4);

        for (int i = 0; i < numDetections; i++)
        {
            // 生成归一化坐标
            var centerX = 0.2 + random.NextDouble() * 0.6; // 0.2-0.8
            var centerY = 0.2 + random.NextDouble() * 0.6;
            var width = 0.1 + random.NextDouble() * 0.3;   // 0.1-0.4
            var height = 0.1 + random.NextDouble() * 0.3;

            // 转换为绝对坐标
            var absoluteX = (int)((centerX - width / 2) * data.Width);
            var absoluteY = (int)((centerY - height / 2) * data.Height);
            var absoluteWidth = (int)(width * data.Width);
            var absoluteHeight = (int)(height * data.Height);

            var detection = new Core.Models.Detection
            {
                Label = config.ClassNames.Count > 0 ? config.ClassNames[random.Next(config.ClassNames.Count)] : "Object",
                Confidence = 0.5 + random.NextDouble() * 0.5, // 0.5-1.0
                ClassId = random.Next(Math.Max(1, config.ClassNames.Count)),
                BoundingBox = new SystemRectangle(absoluteX, absoluteY, absoluteWidth, absoluteHeight)
            };

            detections.Add(detection);
        }

        return detections;
    }

    private List<Core.Models.Detection> ApplyNonMaximumSuppression(List<Core.Models.Detection> detections, double iouThreshold)
    {
        var result = new List<Core.Models.Detection>();
        var sortedDetections = detections.OrderByDescending(d => d.Confidence).ToList();

        while (sortedDetections.Any())
        {
            var best = sortedDetections.First();
            result.Add(best);
            sortedDetections.RemoveAt(0);

            // 移除与当前最佳检测重叠度过高的检测
            sortedDetections = sortedDetections
                .Where(d => CalculateIoU(best.BoundingBox, d.BoundingBox) < iouThreshold)
                .ToList();
        }

        return result;
    }

    private double CalculateIoU(SystemRectangle box1, SystemRectangle box2)
    {
        // 计算交集
        var x1 = Math.Max(box1.X, box2.X);
        var y1 = Math.Max(box1.Y, box2.Y);
        var x2 = Math.Min(box1.X + box1.Width, box2.X + box2.Width);
        var y2 = Math.Min(box1.Y + box1.Height, box2.Y + box2.Height);

        if (x2 <= x1 || y2 <= y1)
            return 0.0;

        var intersectionArea = (x2 - x1) * (y2 - y1);
        var box1Area = box1.Width * box1.Height;
        var box2Area = box2.Width * box2.Height;
        var unionArea = box1Area + box2Area - intersectionArea;

        return unionArea > 0 ? intersectionArea / unionArea : 0.0;
    }

    private double CalculateIoU(BoundingBox box1, BoundingBox box2)
    {
        // 转换为左上角和右下角坐标
        var x1_1 = box1.CenterX - box1.Width / 2;
        var y1_1 = box1.CenterY - box1.Height / 2;
        var x2_1 = box1.CenterX + box1.Width / 2;
        var y2_1 = box1.CenterY + box1.Height / 2;

        var x1_2 = box2.CenterX - box2.Width / 2;
        var y1_2 = box2.CenterY - box2.Height / 2;
        var x2_2 = box2.CenterX + box2.Width / 2;
        var y2_2 = box2.CenterY + box2.Height / 2;

        // 计算交集
        var intersectionX1 = Math.Max(x1_1, x1_2);
        var intersectionY1 = Math.Max(y1_1, y1_2);
        var intersectionX2 = Math.Min(x2_1, x2_2);
        var intersectionY2 = Math.Min(y2_1, y2_2);

        var intersectionArea = Math.Max(0, intersectionX2 - intersectionX1) *
                              Math.Max(0, intersectionY2 - intersectionY1);

        // 计算并集
        var area1 = box1.Width * box1.Height;
        var area2 = box2.Width * box2.Height;
        var unionArea = area1 + area2 - intersectionArea;

        return unionArea > 0 ? intersectionArea / unionArea : 0;
    }

    /// <summary>
    /// 克隆数组
    /// </summary>
    private Array CloneArray(Array original)
    {
        var cloned = Array.CreateInstance(original.GetType().GetElementType()!, original.Length);
        Array.Copy(original, cloned, original.Length);
        return cloned;
    }

    #endregion

    /// <summary>
    /// 模型占位符类
    /// </summary>
    private class ModelPlaceholder
    {
        public string ModelPath { get; set; } = string.Empty;
        public bool IsLoaded { get; set; }
    }
}
