#!/usr/bin/env python3
"""
比较训练数据处理和DICOM数据处理的差异
验证NII.gz转JPG和DICOM处理的归一化方法是否一致
"""

import os
import sys
import cv2
import numpy as np
import pydicom
import nibabel as nib
from pathlib import Path
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.detection_service import DetectionService

def normalize_nii_to_jpg_style(img_slice):
    """
    使用训练数据的归一化方法（NII.gz转JPG）
    """
    # 调整尺寸到640x640
    img_resized = cv2.resize(img_slice.astype(np.float32), (640, 640))
    
    # 归一化到0-255范围（训练时使用的方法）
    if img_resized.max() > img_resized.min():
        img_normalized = ((img_resized - img_resized.min()) / 
                        (img_resized.max() - img_resized.min()) * 255).astype(np.uint8)
    else:
        img_normalized = np.zeros_like(img_resized, dtype=np.uint8)
    
    # 转换为3通道
    if len(img_normalized.shape) == 2:
        img_normalized = cv2.cvtColor(img_normalized, cv2.COLOR_GRAY2RGB)
    
    return img_normalized

def normalize_dicom_style(pixel_array):
    """
    使用DICOM处理的归一化方法（DetectionService中的方法）
    """
    # 处理不同的图像格式
    # 这里模拟DICOM处理中的归一化逻辑
    
    # 应用窗位窗宽，增强对比度
    # 使用简单的百分比窗口化方法
    p1, p99 = np.percentile(pixel_array, (1, 99))
    pixel_array = np.clip(pixel_array, p1, p99)
    
    # 归一化到0-255
    pixel_array = ((pixel_array - np.min(pixel_array)) / (np.max(pixel_array) - np.min(pixel_array)) * 255).astype(np.uint8)
    
    # 如果是灰度图像，转换为RGB
    if len(pixel_array.shape) == 2:
        pixel_array = cv2.cvtColor(pixel_array, cv2.COLOR_GRAY2RGB)
    elif len(pixel_array.shape) == 3 and pixel_array.shape[2] == 1:
        pixel_array = cv2.cvtColor(pixel_array, cv2.COLOR_GRAY2RGB)
    
    return pixel_array

def load_sample_nii_image():
    """
    加载一个样本NII图像进行测试
    """
    # 使用训练数据集中的一个图像
    nii_path = "E:/Trae/yolo_ohif/yolo_dataset_output/yolo_dataset/images/test/133_slice_005.jpg"
    
    # 实际上我们需要找到原始的NII文件
    # 这里我们模拟一个NII图像的处理
    
    # 查找原始数据集
    dataset_paths = [
        "E:/Trae/yolo_ohif/dataset/image_T2",
        "E:/Trae/yolo_ohif/yolo_dataset_output/dataset/image_T2"
    ]
    
    for dataset_path in dataset_paths:
        if os.path.exists(dataset_path):
            nii_files = list(Path(dataset_path).glob("*.nii.gz"))
            if nii_files:
                return nii_files[0]
    
    return None

def create_synthetic_dicom_from_nii(nii_slice):
    """
    从NII切片创建一个合成的DICOM数据，用于测试
    """
    # 创建一个简单的DICOM数据结构
    # 这里我们只关注像素数据部分
    
    # 将NII数据转换为DICOM风格的数据
    # NII数据通常是float类型，DICOM可能是uint16
    dicom_data = (nii_slice * 4095).astype(np.uint16)  # 模拟12位DICOM数据
    
    return dicom_data

def compare_processing_methods():
    """
    比较两种处理方法的差异
    """
    print("开始比较训练数据处理和DICOM数据处理的差异...")
    print("="*80)
    
    # 1. 加载样本NII图像
    nii_file = load_sample_nii_image()
    if nii_file is None:
        print("❌ 未找到NII文件进行测试")
        return
    
    print(f"使用NII文件: {nii_file}")
    
    try:
        # 加载NII图像
        nii_img = nib.load(str(nii_file))
        img_data = nii_img.get_fdata()
        
        # 取第一个切片进行测试
        if len(img_data.shape) == 3:
            test_slice = img_data[:, :, img_data.shape[2]//2]  # 取中间切片
        else:
            test_slice = img_data
        
        print(f"原始NII切片形状: {test_slice.shape}")
        print(f"原始数据范围: [{test_slice.min():.3f}, {test_slice.max():.3f}]")
        print(f"原始数据类型: {test_slice.dtype}")
        
    except Exception as e:
        print(f"❌ 加载NII文件失败: {e}")
        return
    
    # 2. 使用训练数据的处理方法
    print("\n🔄 使用训练数据处理方法 (NII.gz -> JPG)...")
    processed_nii = normalize_nii_to_jpg_style(test_slice)
    print(f"训练方法处理后形状: {processed_nii.shape}")
    print(f"训练方法处理后范围: [{processed_nii.min()}, {processed_nii.max()}]")
    print(f"训练方法处理后类型: {processed_nii.dtype}")
    
    # 3. 创建合成DICOM数据
    print("\n🔄 创建合成DICOM数据...")
    synthetic_dicom = create_synthetic_dicom_from_nii(test_slice)
    print(f"合成DICOM形状: {synthetic_dicom.shape}")
    print(f"合成DICOM范围: [{synthetic_dicom.min()}, {synthetic_dicom.max()}]")
    print(f"合成DICOM类型: {synthetic_dicom.dtype}")
    
    # 4. 使用DICOM处理方法
    print("\n🔄 使用DICOM处理方法...")
    processed_dicom = normalize_dicom_style(synthetic_dicom)
    print(f"DICOM方法处理后形状: {processed_dicom.shape}")
    print(f"DICOM方法处理后范围: [{processed_dicom.min()}, {processed_dicom.max()}]")
    print(f"DICOM方法处理后类型: {processed_dicom.dtype}")
    
    # 5. 比较两种方法的差异
    print("\n📊 比较两种处理方法的差异...")
    
    # 转换为灰度进行比较
    nii_gray = cv2.cvtColor(processed_nii, cv2.COLOR_RGB2GRAY)
    dicom_gray = cv2.cvtColor(processed_dicom, cv2.COLOR_RGB2GRAY)
    
    # 计算统计差异
    diff = np.abs(nii_gray.astype(np.float32) - dicom_gray.astype(np.float32))
    mean_diff = np.mean(diff)
    max_diff = np.max(diff)
    std_diff = np.std(diff)
    
    print(f"平均像素差异: {mean_diff:.3f}")
    print(f"最大像素差异: {max_diff:.3f}")
    print(f"像素差异标准差: {std_diff:.3f}")
    
    # 计算相关性
    correlation = np.corrcoef(nii_gray.flatten(), dicom_gray.flatten())[0, 1]
    print(f"像素相关性: {correlation:.6f}")
    
    # 6. 保存比较图像
    output_dir = Path("E:/Trae/yolo_ohif/processing_comparison")
    output_dir.mkdir(exist_ok=True)
    
    # 保存原始切片
    original_normalized = ((test_slice - test_slice.min()) / (test_slice.max() - test_slice.min()) * 255).astype(np.uint8)
    cv2.imwrite(str(output_dir / "original_slice.jpg"), original_normalized)
    
    # 保存处理后的图像
    cv2.imwrite(str(output_dir / "nii_processed.jpg"), processed_nii)
    cv2.imwrite(str(output_dir / "dicom_processed.jpg"), processed_dicom)
    
    # 保存差异图
    diff_normalized = (diff / max_diff * 255).astype(np.uint8)
    cv2.imwrite(str(output_dir / "difference_map.jpg"), diff_normalized)
    
    print(f"\n💾 比较图像已保存到: {output_dir}")
    print("   - original_slice.jpg: 原始NII切片")
    print("   - nii_processed.jpg: 训练数据处理方法")
    print("   - dicom_processed.jpg: DICOM处理方法")
    print("   - difference_map.jpg: 差异图")
    
    # 7. 分析结果
    print("\n🔍 分析结果:")
    
    if mean_diff < 10:
        print("✅ 两种处理方法差异较小，可能不是检测失败的主要原因")
    elif mean_diff < 50:
        print("⚠️  两种处理方法存在中等差异，可能影响检测效果")
    else:
        print("❌ 两种处理方法差异较大，很可能是检测失败的主要原因")
    
    if correlation > 0.9:
        print("✅ 两种方法处理后的图像高度相关")
    elif correlation > 0.7:
        print("⚠️  两种方法处理后的图像中度相关")
    else:
        print("❌ 两种方法处理后的图像相关性较低")
    
    # 8. 提供解决建议
    print("\n💡 解决建议:")
    
    if mean_diff > 20 or correlation < 0.8:
        print("1. 🔧 统一图像预处理方法:")
        print("   - 在DetectionService中使用与训练时相同的归一化方法")
        print("   - 移除DICOM处理中的百分位数窗口化")
        print("   - 使用简单的min-max归一化")
        
        print("\n2. 🔄 重新训练模型:")
        print("   - 使用DICOM数据进行训练")
        print("   - 或者在训练时模拟DICOM的处理方式")
        
        print("\n3. 🎯 数据增强:")
        print("   - 在训练时添加不同的归一化方法作为数据增强")
        print("   - 提高模型对不同预处理方法的鲁棒性")
    else:
        print("1. ✅ 图像处理差异不大，检测失败可能由其他原因导致")
        print("2. 🔍 建议检查模型路径、置信度阈值等其他配置")
        print("3. 📊 建议使用更多样本进行测试")

def test_detection_with_both_methods():
    """
    使用两种处理方法测试检测效果
    """
    print("\n🧪 测试两种处理方法的检测效果...")
    
    # 初始化检测服务
    model_path = "E:/Trae/yolo_ohif/yolo11x_training_output/training_results/yolo11x_from_scratch_20250719_135134/weights/best.pt"
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    try:
        detection_service = DetectionService(
            model_path=model_path,
            confidence_threshold=0.1,
            iou_threshold=0.45
        )
        print("✅ 检测服务初始化成功")
    except Exception as e:
        print(f"❌ 检测服务初始化失败: {e}")
        return
    
    # 加载测试图像
    test_images = [
        "E:/Trae/yolo_ohif/processing_comparison/nii_processed.jpg",
        "E:/Trae/yolo_ohif/processing_comparison/dicom_processed.jpg"
    ]
    
    for i, img_path in enumerate(test_images):
        if not os.path.exists(img_path):
            print(f"⚠️  测试图像不存在: {img_path}")
            continue
        
        method_name = "训练数据方法" if "nii" in img_path else "DICOM方法"
        print(f"\n🔍 测试 {method_name}: {os.path.basename(img_path)}")
        
        try:
            results = detection_service.detect_objects(img_path)
            detections = results.get('detections', [])
            
            print(f"   检测到 {len(detections)} 个目标")
            
            for j, detection in enumerate(detections):
                conf = detection.get('confidence', 0)
                cls = detection.get('class', 'unknown')
                bbox = detection.get('bbox', {})
                print(f"   检测 {j+1}: 类别={cls}, 置信度={conf:.3f}")
                
        except Exception as e:
            print(f"   ❌ 检测失败: {e}")

if __name__ == "__main__":
    compare_processing_methods()
    test_detection_with_both_methods()