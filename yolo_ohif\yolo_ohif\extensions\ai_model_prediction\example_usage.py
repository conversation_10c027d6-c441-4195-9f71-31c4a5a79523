"""重构后AI预测扩展使用示例

展示如何使用新架构的AI预测扩展
"""

import asyncio
import logging
from typing import Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    try:
        from extensions.ai_model_prediction.factory import create_extension
        
        # 创建默认扩展
        extension = create_extension()
        
        # 获取扩展信息
        info = extension.get_extension_info()
        print(f"扩展名称: {info['name']}")
        print(f"扩展版本: {info['version']}")
        print(f"初始化状态: {info['initialized']}")
        
        # 获取可用模型
        models = extension.get_available_models()
        print(f"可用模型数量: {len(models)}")
        
        # 清理资源
        extension.cleanup()
        print("基本使用示例完成")
        
    except Exception as e:
        print(f"基本使用示例失败: {e}")
        logger.error(f"基本使用示例失败: {e}")


def example_config_usage():
    """配置使用示例"""
    print("\n=== 配置使用示例 ===")
    
    try:
        from extensions.ai_model_prediction.factory import create_extension
        
        # 自定义配置
        config = {
            'name': '示例AI扩展',
            'version': '1.0.0',
            'models': [
                {
                    'id': 'example_model_1',
                    'name': '示例检测模型',
                    'type': 'detection',
                    'modality': 'CT',
                    'endpoint': 'http://localhost:8000/predict',
                    'description': '用于演示的检测模型',
                    'classes': ['normal', 'abnormal'],
                    'confidence_threshold': 0.6,
                    'enabled': True
                },
                {
                    'id': 'example_model_2',
                    'name': '示例分割模型',
                    'type': 'segmentation',
                    'modality': 'MR',
                    'endpoint': 'http://localhost:8001/predict',
                    'description': '用于演示的分割模型',
                    'classes': ['background', 'tumor'],
                    'confidence_threshold': 0.7,
                    'enabled': False
                }
            ],
            'options': {
                'auto_detection': True,
                'cache_enabled': True,
                'cache_ttl': 300,
                'ui_enabled': True,
                'debug_mode': True,
                'max_concurrent_predictions': 2
            }
        }
        
        # 使用配置创建扩展
        extension = create_extension(config=config)
        
        # 获取配置信息
        info = extension.get_extension_info()
        print(f"扩展名称: {info['name']}")
        print(f"模型数量: {info['models_count']}")
        
        # 获取可用模型
        models = extension.get_available_models()
        for model in models:
            print(f"模型: {model.get('name', 'Unknown')} (ID: {model.get('id', 'Unknown')})")
        
        # 设置活动模型
        if models:
            first_model_id = models[0].get('id')
            if first_model_id:
                success = extension.set_active_model(first_model_id)
                print(f"设置活动模型 {first_model_id}: {'成功' if success else '失败'}")
                
                # 获取活动模型信息
                active_model = extension.get_active_model()
                if active_model:
                    print(f"当前活动模型: {active_model.get('name', 'Unknown')}")
        
        # 清理资源
        extension.cleanup()
        print("配置使用示例完成")
        
    except Exception as e:
        print(f"配置使用示例失败: {e}")
        logger.error(f"配置使用示例失败: {e}")


def example_builder_usage():
    """构建器使用示例"""
    print("\n=== 构建器使用示例 ===")
    
    try:
        from .factory import create_builder
        
        # 使用构建器模式
        extension = (create_builder()
                    .with_name('构建器示例扩展')
                    .with_version('2.0.0')
                    .add_model({
                        'id': 'builder_model',
                        'name': '构建器模型',
                        'type': 'detection',
                        'modality': 'CR',
                        'endpoint': 'http://localhost:8002/predict',
                        'classes': ['class1', 'class2'],
                        'confidence_threshold': 0.5,
                        'enabled': True
                    })
                    .with_option('auto_detection', False)
                    .enable_cache(ttl=600)
                    .enable_debug()
                    .build())
        
        # 获取扩展信息
        info = extension.get_extension_info()
        print(f"构建器扩展: {info['name']} v{info['version']}")
        print(f"模型数量: {info['models_count']}")
        
        # 清理资源
        extension.cleanup()
        print("构建器使用示例完成")
        
    except Exception as e:
        print(f"构建器使用示例失败: {e}")
        logger.error(f"构建器使用示例失败: {e}")


async def example_async_usage():
    """异步使用示例"""
    print("\n=== 异步使用示例 ===")
    
    try:
        from .factory import create_extension
        
        # 创建扩展
        extension = create_extension()
        
        # 模拟图像数据
        mock_image_data = {
            'width': 512,
            'height': 512,
            'data': 'mock_image_data',
            'format': 'DICOM'
        }
        
        # 异步预测（注意：这里会失败因为没有真实的模型服务）
        print("尝试异步预测...")
        result = await extension.run_prediction_async(mock_image_data)
        
        if result:
            print(f"异步预测成功: {len(result.detections)}个检测结果")
            print(f"处理时间: {result.processing_time:.2f}秒")
        else:
            print("异步预测返回空结果（预期行为，因为没有真实模型服务）")
        
        # 清理资源
        extension.cleanup()
        print("异步使用示例完成")
        
    except Exception as e:
        print(f"异步使用示例失败: {e}")
        logger.error(f"异步使用示例失败: {e}")


def example_event_usage():
    """事件使用示例"""
    print("\n=== 事件使用示例 ===")
    
    try:
        from extensions.ai_model_prediction.factory import create_extension
        from extensions.ai_model_prediction.core.interfaces import EventManagerInterface
        
        # 创建扩展
        extension = create_extension()
        
        # 获取事件服务
        event_service = extension.container.get_service(EventManagerInterface)
        
        # 定义事件处理器
        def on_model_changed(event_data):
            model_id = event_data.data.get('model_id')
            print(f"事件处理器: 模型已切换到 {model_id}")
        
        def on_prediction_completed(event_data):
            detections_count = event_data.data.get('detections_count', 0)
            processing_time = event_data.data.get('processing_time', 0)
            print(f"事件处理器: 预测完成，检测到{detections_count}个目标，耗时{processing_time:.2f}秒")
        
        # 订阅事件
        sub1 = event_service.subscribe('model_changed', on_model_changed)
        sub2 = event_service.subscribe('prediction_completed', on_prediction_completed)
        
        print(f"已订阅事件: {sub1}, {sub2}")
        
        # 模拟触发事件
        event_service.publish('model_changed', {
            'model_id': 'test_model',
            'timestamp': '2025-01-01T00:00:00'
        })
        
        event_service.publish('prediction_completed', {
            'model_id': 'test_model',
            'detections_count': 3,
            'processing_time': 1.5,
            'timestamp': '2025-01-01T00:00:01'
        })
        
        # 获取事件统计
        stats = event_service.get_stats()
        print(f"事件统计: {stats}")
        
        # 取消订阅
        event_service.unsubscribe(sub1)
        event_service.unsubscribe(sub2)
        
        # 清理资源
        extension.cleanup()
        print("事件使用示例完成")
        
    except Exception as e:
        print(f"事件使用示例失败: {e}")
        logger.error(f"事件使用示例失败: {e}")


def example_cache_usage():
    """缓存使用示例"""
    print("\n=== 缓存使用示例 ===")
    
    try:
        from extensions.ai_model_prediction.factory import create_builder
        from extensions.ai_model_prediction.core.interfaces import CacheManagerInterface
        
        # 创建启用缓存的扩展
        extension = (create_builder()
                    .enable_cache(ttl=300)
                    .build())
        
        # 获取缓存服务
        cache_service = extension.container.get_service(CacheManagerInterface)
        
        # 缓存操作
        print("测试缓存操作...")
        
        # 设置缓存
        cache_service.set('test_key', 'test_value', ttl=60)
        print("已设置缓存: test_key = test_value")
        
        # 获取缓存
        value = cache_service.get('test_key')
        print(f"获取缓存: test_key = {value}")
        
        # 检查键是否存在
        exists = cache_service.exists('test_key')
        print(f"键存在: {exists}")
        
        # 删除缓存
        cache_service.delete('test_key')
        print("已删除缓存: test_key")
        
        # 再次检查
        value = cache_service.get('test_key')
        print(f"删除后获取缓存: test_key = {value}")
        
        # 获取缓存统计
        stats = cache_service.get_stats()
        print(f"缓存统计: {stats}")
        
        # 清理资源
        extension.cleanup()
        print("缓存使用示例完成")
        
    except Exception as e:
        print(f"缓存使用示例失败: {e}")
        logger.error(f"缓存使用示例失败: {e}")


def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    try:
        from extensions.ai_model_prediction.factory import ExtensionFactory
        from extensions.ai_model_prediction.core.exceptions import ExtensionError, ConfigurationError
        
        # 测试无效配置
        print("测试无效配置...")
        try:
            ExtensionFactory.create_from_file('nonexistent_file.json')
        except ExtensionError as e:
            print(f"捕获到扩展错误: {e}")
        except Exception as e:
            print(f"捕获到其他错误: {e}")
        
        # 测试无效模型操作
        print("测试无效模型操作...")
        try:
            extension = ExtensionFactory.create_minimal()
            
            # 尝试在没有模型的情况下运行预测
            result = extension.run_prediction('mock_data')
            if result is None:
                print("预测返回None（预期行为）")
            
            # 尝试设置不存在的模型
            success = extension.set_active_model('nonexistent_model')
            print(f"设置不存在的模型: {'成功' if success else '失败（预期）'}")
            
            extension.cleanup()
            
        except Exception as e:
            print(f"捕获到模型操作错误: {e}")
        
        print("错误处理示例完成")
        
    except Exception as e:
        print(f"错误处理示例失败: {e}")
        logger.error(f"错误处理示例失败: {e}")


def example_performance_monitoring():
    """性能监控示例"""
    print("\n=== 性能监控示例 ===")
    
    try:
        from extensions.ai_model_prediction.factory import create_extension
        from extensions.ai_model_prediction.core.interfaces import (
            CacheManagerInterface,
            EventManagerInterface,
            UIComponentsInterface
        )
        
        # 创建扩展
        extension = create_extension()
        
        # 获取各种统计信息
        print("收集性能统计信息...")
        
        # 扩展整体信息
        info = extension.get_extension_info()
        print(f"扩展信息:")
        print(f"  - 名称: {info.get('name')}")
        print(f"  - 版本: {info.get('version')}")
        print(f"  - 初始化状态: {info.get('initialized')}")
        print(f"  - 容器服务数: {info.get('container_services')}")
        print(f"  - 模型数量: {info.get('models_count')}")
        
        # 缓存统计
        try:
            cache_service = extension.container.get_service(CacheManagerInterface)
            cache_stats = cache_service.get_stats()
            print(f"缓存统计: {cache_stats}")
        except Exception as e:
            print(f"获取缓存统计失败: {e}")
        
        # 事件统计
        try:
            event_service = extension.container.get_service(EventManagerInterface)
            event_stats = event_service.get_stats()
            print(f"事件统计: {event_stats}")
        except Exception as e:
            print(f"获取事件统计失败: {e}")
        
        # UI统计
        try:
            ui_service = extension.container.get_service(UIComponentsInterface)
            ui_stats = ui_service.get_stats()
            print(f"UI统计: {ui_stats}")
        except Exception as e:
            print(f"获取UI统计失败: {e}")
        
        # 清理资源
        extension.cleanup()
        print("性能监控示例完成")
        
    except Exception as e:
        print(f"性能监控示例失败: {e}")
        logger.error(f"性能监控示例失败: {e}")


def main():
    """主函数，运行所有示例"""
    print("重构后AI预测扩展使用示例")
    print("=" * 50)
    
    # 运行同步示例
    example_basic_usage()
    example_config_usage()
    example_builder_usage()
    example_event_usage()
    example_cache_usage()
    example_error_handling()
    example_performance_monitoring()
    
    # 运行异步示例
    print("\n运行异步示例...")
    try:
        asyncio.run(example_async_usage())
    except Exception as e:
        print(f"异步示例运行失败: {e}")
        logger.error(f"异步示例运行失败: {e}")
    
    print("\n" + "=" * 50)
    print("所有示例运行完成")


if __name__ == '__main__':
    main()