#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的YOLO数据集生成脚本
合并了slice_nii_to_jpg.py和convert_label_to_bbox.py的功能
从nii.gz文件开始，生成完整的YOLO训练数据集

功能:
1. 将nii.gz文件切片转换为JPG格式
2. 将mask转换为YOLO格式的边界框标注
3. 生成完整的YOLO数据集结构
4. 数据集划分（训练/验证/测试）
5. 生成YOLO配置文件
"""

import os
import cv2
import numpy as np
import nibabel as nib
from pathlib import Path
import yaml
import shutil
from sklearn.model_selection import train_test_split
import logging
from tqdm import tqdm
import argparse
from scipy import ndimage
import random
import time
import tempfile
from contextlib import contextmanager

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class YOLODatasetCreator:
    def __init__(self, dataset_root, output_root, img_size=640):
        """
        初始化YOLO数据集创建器
        
        Args:
            dataset_root: 原始数据集根目录（包含image_T2, label_T2, image_T2_normal）
            output_root: 输出目录
            img_size: 图像尺寸
        """
        self.dataset_root = Path(dataset_root)
        self.output_root = Path(output_root)
        self.img_size = img_size
        
        # 输入目录
        self.image_t2_dir = self.dataset_root / "image_T2"
        self.image_t2_normal_dir = self.dataset_root / "image_T2_normal"
        self.label_t2_dir = self.dataset_root / "label_T2"
        
        # 临时处理目录
        self.temp_root = self.output_root / "temp_processing"
        self.temp_images = self.temp_root / "images"
        self.temp_labels = self.temp_root / "labels"
        
        # YOLO数据集目录
        self.yolo_dataset_root = self.output_root / "yolo_dataset"
        self.train_images = self.yolo_dataset_root / "images" / "train"
        self.val_images = self.yolo_dataset_root / "images" / "val"
        self.test_images = self.yolo_dataset_root / "images" / "test"
        self.train_labels = self.yolo_dataset_root / "labels" / "train"
        self.val_labels = self.yolo_dataset_root / "labels" / "val"
        self.test_labels = self.yolo_dataset_root / "labels" / "test"
        
        # 创建所有必要的目录
        for dir_path in [self.temp_images, self.temp_labels,
                        self.train_images, self.val_images, self.test_images,
                        self.train_labels, self.val_labels, self.test_labels]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 类别定义
        self.classes = {
            'supraspinatus_tear': 0  # 冈上肌撕裂
        }
        
        logger.info(f"初始化完成，数据集根目录: {self.dataset_root}")
        logger.info(f"输出根目录: {self.output_root}")
        
        # 文件操作重试配置
        self.file_retry_count = 3
        self.file_retry_delay = 0.5
    
    @contextmanager
    def safe_file_operation(self, operation_name="文件操作"):
        """
        安全文件操作上下文管理器，提供重试机制
        """
        for attempt in range(self.file_retry_count):
            try:
                yield
                break
            except (PermissionError, OSError) as e:
                if attempt < self.file_retry_count - 1:
                    logger.warning(f"{operation_name}失败 (尝试 {attempt + 1}/{self.file_retry_count}): {e}")
                    time.sleep(self.file_retry_delay)
                else:
                    logger.error(f"{operation_name}最终失败: {e}")
                    raise
    
    def safe_write_file(self, file_path, content):
        """
        安全写入文件，使用原子性操作和重试机制
        """
        file_path = Path(file_path)
        
        with self.safe_file_operation(f"写入文件 {file_path.name}"):
            # 使用临时文件进行原子性写入
            with tempfile.NamedTemporaryFile(mode='w', delete=False, 
                                           dir=file_path.parent, 
                                           suffix='.tmp') as tmp_file:
                tmp_file.write(content)
                tmp_file.flush()
                os.fsync(tmp_file.fileno())
                tmp_path = tmp_file.name
            
            # 原子性移动临时文件到目标位置
            if file_path.exists():
                file_path.unlink()
            Path(tmp_path).rename(file_path)
    
    def load_nii_image(self, nii_path):
        """
        加载nii.gz图像（保持原始数据，不进行预处理）
        
        Args:
            nii_path: nii.gz文件路径
            
        Returns:
            numpy array: 原始图像数据
        """
        try:
            nii_img = nib.load(str(nii_path))
            img_data = nii_img.get_fdata()
            logger.debug(f"成功加载 {nii_path}，形状: {img_data.shape}")
            return img_data
        except Exception as e:
            logger.error(f"加载图像失败 {nii_path}: {e}")
            return None
    
    def mask_to_bbox(self, mask, min_area_threshold=10, min_bbox_size=5, max_bbox_ratio=0.95, min_norm_size=0.01):
        """
        将二值mask转换为YOLO格式的边界框
        使用连通组件分析来检测独立的区域
        
        Args:
            mask: 二值mask图像 (numpy array)
            min_area_threshold: 最小面积阈值
            min_bbox_size: 最小边界框尺寸
            max_bbox_ratio: 边界框相对于图像的最大尺寸比例（0.95表示不超过图像95%的尺寸）
            min_norm_size: 归一化后的最小边界框尺寸（0.01表示不小于图像1%的尺寸）
        
        Returns:
            list: [x_center, y_center, width, height] (归一化坐标) 或 None
        """
        # 使用连通组件分析检测独立区域
        retval, labels, stats, centroids = cv2.connectedComponentsWithStats(mask, connectivity=8)
        
        # stats格式: [x, y, width, height, area]
        # 排除背景（第一个组件）
        valid_components = stats[1:]
        
        if len(valid_components) == 0:
            return None
        
        # 按面积排序，保留较大的组件
        valid_components = valid_components[valid_components[:, 4].argsort()]
        
        bboxes = []
        img_height, img_width = mask.shape
        
        for component in valid_components:
            x, y, w, h, area = component
            
            # 检查面积阈值
            if area < min_area_threshold:
                continue
                
            # 检查边界框尺寸
            if w < min_bbox_size or h < min_bbox_size:
                continue
            
            # 计算YOLO格式的归一化坐标
            x_center = (x + w / 2) / img_width
            y_center = (y + h / 2) / img_height
            norm_width = w / img_width
            norm_height = h / img_height
            
            # 过滤掉覆盖整张图像或接近整张图像的边界框
            if norm_width > max_bbox_ratio or norm_height > max_bbox_ratio:
                logger.debug(f"跳过过大的边界框: width={norm_width:.3f}, height={norm_height:.3f}")
                continue
            
            # 过滤掉面积占比过大的边界框（超过图像面积的90%）
            bbox_area_ratio = norm_width * norm_height
            if bbox_area_ratio > 0.9:
                logger.debug(f"跳过面积过大的边界框: area_ratio={bbox_area_ratio:.3f}")
                continue
            
            # 过滤掉过小的边界框（可能是噪声）
            if norm_width < min_norm_size or norm_height < min_norm_size:
                logger.debug(f"跳过过小的边界框: width={norm_width:.3f}, height={norm_height:.3f}")
                continue
            
            bboxes.append((x_center, y_center, norm_width, norm_height))
        
        # 如果有多个边界框，合并成一个大边界框
        if len(bboxes) > 1:
            # 计算所有边界框的最小外接矩形
            min_x = min(bbox[0] - bbox[2]/2 for bbox in bboxes)  # 最左边
            max_x = max(bbox[0] + bbox[2]/2 for bbox in bboxes)  # 最右边
            min_y = min(bbox[1] - bbox[3]/2 for bbox in bboxes)  # 最上边
            max_y = max(bbox[1] + bbox[3]/2 for bbox in bboxes)  # 最下边
            
            # 计算合并后的边界框
            merged_x_center = (min_x + max_x) / 2
            merged_y_center = (min_y + max_y) / 2
            merged_width = max_x - min_x
            merged_height = max_y - min_y
            
            logger.debug(f"合并 {len(bboxes)} 个边界框为一个: center=({merged_x_center:.3f}, {merged_y_center:.3f}), size=({merged_width:.3f}, {merged_height:.3f})")
            
            return [merged_x_center, merged_y_center, merged_width, merged_height]
        
        if len(bboxes) == 1:
            bbox = bboxes[0]
            return [bbox[0], bbox[1], bbox[2], bbox[3]]
        
        return None
    
    def process_tear_images(self):
        """
        处理包含撕裂的图像
        从nii.gz文件开始，生成JPG图像和对应的YOLO标注
        
        Returns:
            list: 处理后的数据列表 [(image_path, label_path, volume_id)]
        """
        logger.info("开始处理撕裂图像...")
        
        processed_data = []
        
        # 获取所有撕裂图像文件
        tear_files = sorted(list(self.image_t2_dir.glob("*.nii.gz")))
        
        for img_file in tqdm(tear_files, desc="处理撕裂图像"):
            # 对应的标签文件
            label_file = self.label_t2_dir / img_file.name
            
            if not label_file.exists():
                logger.warning(f"标签文件不存在: {label_file}")
                continue
            
            # 加载图像和标签的完整volume
            img_volume = self.load_nii_image(img_file)
            label_volume = self.load_nii_image(label_file)
            
            if img_volume is None or label_volume is None:
                continue
            
            # 确保图像和标签的层数一致
            if img_volume.shape != label_volume.shape:
                logger.warning(f"图像和标签尺寸不匹配: {img_file.name}")
                continue
            
            # 提取volume ID（用于数据集划分）
            volume_id = img_file.stem.replace('.nii', '')
            
            # 遍历每个层面（轴向切片）
            num_slices = img_volume.shape[2] if len(img_volume.shape) == 3 else 1
            
            for slice_idx in range(num_slices):
                # 获取当前层面的图像和标签
                if len(img_volume.shape) == 3:
                    img_slice = img_volume[:, :, slice_idx]
                    label_slice = label_volume[:, :, slice_idx]
                else:
                    img_slice = img_volume
                    label_slice = label_volume
                
                # 检查切片是否为空（全零）
                if np.all(img_slice == 0):
                    logger.debug(f"跳过空切片: {volume_id}_slice_{slice_idx:03d}")
                    continue
                
                # 处理图像：调整尺寸
                img_resized = cv2.resize(img_slice.astype(np.float32), (self.img_size, self.img_size))
                
                # 归一化到0-255范围（与原始版本保持一致）
                if img_resized.max() > img_resized.min():
                    img_normalized = ((img_resized - img_resized.min()) / 
                                    (img_resized.max() - img_resized.min()) * 255).astype(np.uint8)
                else:
                    img_normalized = np.zeros_like(img_resized, dtype=np.uint8)
                
                # 转换为3通道
                if len(img_normalized.shape) == 2:
                    img_normalized = cv2.cvtColor(img_normalized, cv2.COLOR_GRAY2RGB)
                
                # 生成文件名
                base_name = f"{volume_id}_slice_{slice_idx:03d}"
                img_path = self.temp_images / f"{base_name}.jpg"
                
                # 保存图像
                cv2.imwrite(str(img_path), img_normalized)
                
                # 处理标签：调整尺寸
                label_resized = cv2.resize(label_slice.astype(np.float32), (self.img_size, self.img_size))
                
                # 标签图像：保持二值化（与slice_nii_to_jpg.py保持一致）
                label_processed = (label_resized > 0.5).astype(np.uint8) * 255
                
                # 检查图像中是否有白色像素（值为255，表示标签区域）
                white_pixels = np.sum(label_processed == 255)
                
                if white_pixels > 0:
                    # 将图像转换为二值mask（只检测白色像素，值为255，与convert_label_to_bbox.py保持一致）
                    binary_mask = (label_processed == 255).astype(np.uint8)
                    
                    # 生成YOLO边界框
                    bbox = self.mask_to_bbox(binary_mask)
                    
                    if bbox is not None:
                        # 创建YOLO标注文件（使用安全写入方法）
                        label_path = self.temp_labels / f"{base_name}.txt"
                        x_center, y_center, width, height = bbox
                        label_content = f"{self.classes['supraspinatus_tear']} {x_center:.8f} {y_center:.8f} {width:.8f} {height:.8f}\n"
                        
                        # 使用安全文件写入方法
                        try:
                            self.safe_write_file(str(label_path), label_content)
                        except Exception as e:
                            logger.warning(f"安全写入失败，使用传统方法: {e}")
                            with open(label_path, 'w') as f:
                                f.write(label_content)
                        
                        processed_data.append((str(img_path), str(label_path), volume_id))
                        logger.info(f"生成标注: {base_name}.txt (白色像素数: {white_pixels}, 检测到 1 个区域)")
                    else:
                        # 无法生成有效边界框，只保存图像
                        processed_data.append((str(img_path), None, volume_id))
                        logger.debug(f"跳过图像（白色区域过小）: {base_name}.jpg (白色像素数: {white_pixels})")
                else:
                    # 当前层面没有撕裂区域，只保存图像
                    processed_data.append((str(img_path), None, volume_id))
                    logger.debug(f"跳过图像（无白色标签区域）: {base_name}.jpg")
        
        # 统计信息（与原始版本保持一致的格式）
        total_images = len(processed_data)
        generated_labels = sum(1 for _, label_path, _ in processed_data if label_path is not None)
        skipped_images = total_images - generated_labels
        
        logger.info("\n=== 撕裂图像处理完成 ===")
        logger.info(f"总图像数: {total_images}")
        logger.info(f"生成标注文件: {generated_labels}")
        logger.info(f"跳过图像: {skipped_images}")
        if total_images > 0:
            logger.info(f"标注生成率: {generated_labels/total_images*100:.1f}%")
        logger.info(f"输出目录: {self.temp_labels}")
        
        return processed_data
    
    def process_normal_images(self):
        """
        处理正常图像（无撕裂）
        
        Returns:
            list: 处理后的数据列表 [(image_path, None, volume_id)]
        """
        logger.info("开始处理正常图像...")
        
        processed_data = []
        
        # 获取所有正常图像文件
        normal_files = sorted(list(self.image_t2_normal_dir.glob("*.nii.gz")))
        
        for img_file in tqdm(normal_files, desc="处理正常图像"):
            # 加载图像volume
            img_volume = self.load_nii_image(img_file)
            
            if img_volume is None:
                continue
            
            # 提取volume ID
            volume_id = img_file.stem.replace('.nii', '')
            
            # 遍历每个层面
            num_slices = img_volume.shape[2] if len(img_volume.shape) == 3 else 1
            
            for slice_idx in range(num_slices):
                # 获取当前层面的图像
                if len(img_volume.shape) == 3:
                    img_slice = img_volume[:, :, slice_idx]
                else:
                    img_slice = img_volume
                
                # 检查切片是否为空（全零）
                if np.all(img_slice == 0):
                    logger.debug(f"跳过空切片: {volume_id}_slice_{slice_idx:03d}")
                    continue
                
                # 处理图像：调整尺寸
                img_resized = cv2.resize(img_slice.astype(np.float32), (self.img_size, self.img_size))
                
                # 归一化到0-255范围（与原始版本保持一致）
                if img_resized.max() > img_resized.min():
                    img_normalized = ((img_resized - img_resized.min()) / 
                                    (img_resized.max() - img_resized.min()) * 255).astype(np.uint8)
                else:
                    img_normalized = np.zeros_like(img_resized, dtype=np.uint8)
                
                # 转换为3通道
                if len(img_normalized.shape) == 2:
                    img_normalized = cv2.cvtColor(img_normalized, cv2.COLOR_GRAY2RGB)
                
                # 生成文件名
                base_name = f"{volume_id}_slice_{slice_idx:03d}"
                img_path = self.temp_images / f"{base_name}.jpg"
                
                # 保存图像
                cv2.imwrite(str(img_path), img_normalized)
                
                # 正常图像没有标注
                processed_data.append((str(img_path), None, volume_id))
        
        # 统计信息（与原始版本保持一致的格式）
        total_images = len(processed_data)
        
        logger.info("\n=== 正常图像处理完成 ===")
        logger.info(f"总图像数: {total_images}")
        logger.info(f"输出目录: {self.temp_images}")
        
        return processed_data
    
    def split_dataset(self, all_data, train_ratio=0.7, val_ratio=0.2, test_ratio=0.1):
        """
        按volume ID划分数据集，确保同一个volume的所有切片都在同一个数据集中
        
        Args:
            all_data: 所有数据列表 [(image_path, label_path, volume_id)]
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            test_ratio: 测试集比例
        """
        logger.info("开始划分数据集...")
        
        # 按volume_id分组
        volume_groups = {}
        for img_path, label_path, volume_id in all_data:
            if volume_id not in volume_groups:
                volume_groups[volume_id] = []
            volume_groups[volume_id].append((img_path, label_path))
        
        # 获取所有volume ID
        volume_ids = list(volume_groups.keys())
        random.shuffle(volume_ids)
        
        # 计算划分点
        total_volumes = len(volume_ids)
        train_end = int(total_volumes * train_ratio)
        val_end = train_end + int(total_volumes * val_ratio)
        
        # 划分volume ID
        train_volumes = volume_ids[:train_end]
        val_volumes = volume_ids[train_end:val_end]
        test_volumes = volume_ids[val_end:]
        
        logger.info(f"数据集划分: 训练集 {len(train_volumes)} 个volume, 验证集 {len(val_volumes)} 个volume, 测试集 {len(test_volumes)} 个volume")
        
        # 复制文件到对应的数据集目录
        self._copy_volume_files(volume_groups, train_volumes, "train")
        self._copy_volume_files(volume_groups, val_volumes, "val")
        self._copy_volume_files(volume_groups, test_volumes, "test")
    
    def _copy_volume_files(self, volume_groups, volume_ids, split_name):
        """
        复制指定volume的文件到对应的数据集目录
        
        Args:
            volume_groups: volume分组字典
            volume_ids: 要复制的volume ID列表
            split_name: 数据集名称 (train/val/test)
        """
        target_img_dir = self.yolo_dataset_root / "images" / split_name
        target_label_dir = self.yolo_dataset_root / "labels" / split_name
        
        total_images = 0
        total_labels = 0
        
        for volume_id in tqdm(volume_ids, desc=f"复制{split_name}数据"):
            for img_path, label_path in volume_groups[volume_id]:
                # 复制图像文件
                img_src = Path(img_path)
                img_dst = target_img_dir / img_src.name
                shutil.copy2(img_src, img_dst)
                total_images += 1
                
                # 复制标注文件（如果存在）
                if label_path is not None:
                    label_src = Path(label_path)
                    label_dst = target_label_dir / label_src.name
                    shutil.copy2(label_src, label_dst)
                    total_labels += 1
        
        logger.info(f"{split_name}集: {total_images} 张图像, {total_labels} 个标注文件")
    
    def create_yaml_config(self):
        """
        创建YOLO配置文件
        
        Returns:
            str: 配置文件路径
        """
        config = {
            'path': str(self.yolo_dataset_root.absolute()),
            'train': 'images/train',
            'val': 'images/val',
            'test': 'images/test',
            'nc': 1,  # 类别数量
            'names': ['supraspinatus_tear']  # 类别名称
        }
        
        config_path = self.yolo_dataset_root / "dataset.yaml"
        
        # 使用安全文件写入方法
        try:
            yaml_content = yaml.dump(config, default_flow_style=False, allow_unicode=True)
            self.safe_write_file(str(config_path), yaml_content)
        except Exception as e:
            logger.warning(f"安全写入YAML失败，使用传统方法: {e}")
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"YOLO配置文件已创建: {config_path}")
        return str(config_path)
    
    def cleanup_temp_files(self):
        """
        清理临时文件
        """
        if self.temp_root.exists():
            shutil.rmtree(self.temp_root)
            logger.info("临时文件已清理")
    
    def create_dataset(self):
        """
        创建完整的YOLO数据集
        
        Returns:
            str: 配置文件路径
        """
        logger.info("开始创建YOLO数据集...")
        
        try:
            # 1. 处理撕裂图像
            tear_data = self.process_tear_images()
            
            # 2. 处理正常图像
            normal_data = self.process_normal_images()
            
            # 3. 合并所有数据
            all_data = tear_data + normal_data
            logger.info(f"总共处理了 {len(all_data)} 个样本")
            
            # 4. 划分数据集
            self.split_dataset(all_data)
            
            # 5. 创建配置文件
            config_path = self.create_yaml_config()
            
            # 6. 清理临时文件
            self.cleanup_temp_files()
            
            logger.info("YOLO数据集创建完成!")
            logger.info(f"数据集路径: {self.yolo_dataset_root}")
            logger.info(f"配置文件: {config_path}")
            
            return config_path
            
        except Exception as e:
            logger.error(f"创建数据集时出错: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description='创建完整的YOLO数据集')
    parser.add_argument('--dataset_root', type=str, default='./dataset', help='原始数据集根目录')
    parser.add_argument('--output_root', type=str, default='./yolo_dataset_output', help='输出目录')
    parser.add_argument('--img_size', type=int, default=640, help='图像尺寸')
    
    args = parser.parse_args()
    
    # 创建数据集
    creator = YOLODatasetCreator(
        dataset_root=args.dataset_root,
        output_root=args.output_root,
        img_size=args.img_size
    )
    
    config_path = creator.create_dataset()
    
    print(f"\n✅ YOLO数据集创建成功!")
    print(f"📁 数据集路径: {creator.yolo_dataset_root}")
    print(f"⚙️ 配置文件: {config_path}")
    print(f"\n🚀 现在可以使用以下命令训练模型:")
    print(f"   yolo train data={config_path} model=yolo11x.pt epochs=100 imgsz=640")

if __name__ == "__main__":
    main()