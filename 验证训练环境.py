#!/usr/bin/env python3
"""
医学影像AI模型训练环境验证脚本
用于验证训练环境是否正确配置，确保教程能够正常使用
"""

import sys
import os
import subprocess
import importlib
from pathlib import Path
import yaml
import json

class TrainingEnvironmentValidator:
    """训练环境验证器"""
    
    def __init__(self):
        self.results = {
            'python_version': False,
            'required_packages': {},
            'gpu_support': False,
            'dataset_structure': False,
            'yolo_scripts': False,
            'dotnet_environment': False,
            'overall_status': False
        }
        
    def print_header(self, title):
        """打印标题"""
        print(f"\n{'='*60}")
        print(f" {title}")
        print(f"{'='*60}")
    
    def print_status(self, item, status, details=""):
        """打印状态"""
        status_symbol = "✅" if status else "❌"
        print(f"{status_symbol} {item}")
        if details:
            print(f"   {details}")
    
    def check_python_version(self):
        """检查Python版本"""
        self.print_header("Python环境检查")
        
        version = sys.version_info
        required_major, required_minor = 3, 8
        
        if version.major >= required_major and version.minor >= required_minor:
            self.results['python_version'] = True
            self.print_status(
                f"Python版本: {version.major}.{version.minor}.{version.micro}", 
                True,
                "满足要求 (>= 3.8)"
            )
        else:
            self.print_status(
                f"Python版本: {version.major}.{version.minor}.{version.micro}", 
                False,
                f"需要 >= {required_major}.{required_minor}"
            )
    
    def check_required_packages(self):
        """检查必需的Python包"""
        self.print_header("Python包依赖检查")
        
        required_packages = {
            'torch': '检查PyTorch深度学习框架',
            'torchvision': '检查PyTorch视觉库',
            'ultralytics': '检查YOLO训练库',
            'opencv-python': '检查OpenCV图像处理库',
            'pillow': '检查PIL图像库',
            'pyyaml': '检查YAML配置文件支持',
            'tqdm': '检查进度条库',
            'matplotlib': '检查绘图库',
            'numpy': '检查数值计算库'
        }
        
        for package, description in required_packages.items():
            try:
                importlib.import_module(package.replace('-', '_'))
                self.results['required_packages'][package] = True
                self.print_status(f"{package}: {description}", True)
            except ImportError:
                self.results['required_packages'][package] = False
                self.print_status(f"{package}: {description}", False, "需要安装")
    
    def check_gpu_support(self):
        """检查GPU支持"""
        self.print_header("GPU支持检查")
        
        try:
            import torch
            
            # 检查CUDA可用性
            cuda_available = torch.cuda.is_available()
            self.print_status("CUDA支持", cuda_available)
            
            if cuda_available:
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0)
                self.print_status(f"GPU数量: {gpu_count}", True)
                self.print_status(f"GPU型号: {gpu_name}", True)
                
                # 检查GPU内存
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
                self.print_status(f"GPU内存: {gpu_memory:.1f}GB", gpu_memory >= 4)
                
                self.results['gpu_support'] = True
            else:
                self.print_status("GPU支持", False, "将使用CPU训练（速度较慢）")
                
        except ImportError:
            self.print_status("PyTorch未安装", False, "无法检查GPU支持")
    
    def check_dataset_structure(self):
        """检查数据集结构"""
        self.print_header("数据集结构检查")
        
        # 检查示例数据集
        brain_dir = Path("Brain")
        if brain_dir.exists():
            dcm_files = list(brain_dir.glob("*.dcm"))
            self.print_status(f"示例DICOM文件: {len(dcm_files)}个", len(dcm_files) > 0)
        else:
            self.print_status("示例数据目录 (Brain/)", False, "未找到示例数据")
        
        # 检查YOLO数据集目录结构
        yolo_dataset_dirs = [
            "yolo_ohif/yolo_ohif/dataset",
            "dataset"
        ]
        
        dataset_found = False
        for dataset_dir in yolo_dataset_dirs:
            if Path(dataset_dir).exists():
                self.print_status(f"YOLO数据集目录: {dataset_dir}", True)
                dataset_found = True
                break
        
        if not dataset_found:
            self.print_status("YOLO数据集目录", False, "需要创建数据集")
        
        self.results['dataset_structure'] = dataset_found or brain_dir.exists()
    
    def check_yolo_scripts(self):
        """检查YOLO训练脚本"""
        self.print_header("YOLO训练脚本检查")
        
        yolo_scripts = [
            "yolo_ohif/yolo_ohif/start_yolo11x_training.py",
            "yolo_ohif/yolo_ohif/start_yolo11x_pretrained_training.py",
            "yolo_ohif/yolo_ohif/train_yolo11x_from_scratch.py",
            "yolo_ohif/yolo_ohif/create_yolo_dataset.py"
        ]
        
        script_count = 0
        for script in yolo_scripts:
            if Path(script).exists():
                self.print_status(f"训练脚本: {Path(script).name}", True)
                script_count += 1
            else:
                self.print_status(f"训练脚本: {Path(script).name}", False, "文件不存在")
        
        self.results['yolo_scripts'] = script_count >= 2
    
    def check_dotnet_environment(self):
        """检查.NET环境"""
        self.print_header(".NET环境检查")
        
        try:
            # 检查dotnet命令
            result = subprocess.run(['dotnet', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                version = result.stdout.strip()
                major_version = int(version.split('.')[0])
                
                if major_version >= 8:
                    self.results['dotnet_environment'] = True
                    self.print_status(f".NET版本: {version}", True, "满足要求 (>= 8.0)")
                else:
                    self.print_status(f".NET版本: {version}", False, "需要 >= 8.0")
            else:
                self.print_status(".NET SDK", False, "未安装或配置错误")
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            self.print_status(".NET SDK", False, "未找到dotnet命令")
        
        # 检查WPF项目
        wpf_project = Path("src/MedicalImageAnalysis.Wpf/MedicalImageAnalysis.Wpf.csproj")
        if wpf_project.exists():
            self.print_status("WPF项目文件", True)
        else:
            self.print_status("WPF项目文件", False, "项目文件不存在")
    
    def generate_setup_instructions(self):
        """生成安装指导"""
        self.print_header("环境配置指导")
        
        if not self.results['python_version']:
            print("📥 安装Python 3.8+:")
            print("   https://www.python.org/downloads/")
        
        missing_packages = [pkg for pkg, installed in self.results['required_packages'].items() if not installed]
        if missing_packages:
            print("\n📦 安装缺失的Python包:")
            print("   pip install " + " ".join(missing_packages))
            
            if 'torch' in missing_packages:
                print("\n🔥 PyTorch安装 (GPU版本):")
                print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
        
        if not self.results['dotnet_environment']:
            print("\n🔧 安装.NET 8 SDK:")
            print("   https://dotnet.microsoft.com/download/dotnet/8.0")
        
        if not self.results['gpu_support']:
            print("\n⚡ GPU加速 (可选):")
            print("   - 安装NVIDIA驱动")
            print("   - 安装CUDA Toolkit 11.8+")
            print("   - 重新安装PyTorch GPU版本")
    
    def create_test_script(self):
        """创建测试脚本"""
        self.print_header("创建测试脚本")
        
        test_script = """#!/usr/bin/env python3
# 快速训练测试脚本
from ultralytics import YOLO
import torch

print("🚀 开始快速训练测试...")

# 检查设备
device = 'cuda' if torch.cuda.is_available() else 'cpu'
print(f"使用设备: {device}")

# 创建小型测试模型
model = YOLO('yolo11n.pt')  # 使用最小的模型进行测试

# 快速训练测试（仅1个epoch）
try:
    results = model.train(
        data='coco8.yaml',  # 使用内置的小型数据集
        epochs=1,           # 仅训练1个epoch进行测试
        batch=2,            # 小批次
        imgsz=320,          # 小图像尺寸
        device=device,
        project='test_runs',
        name='quick_test',
        verbose=True
    )
    print("✅ 训练测试成功!")
    print(f"训练结果保存在: test_runs/quick_test/")
    
except Exception as e:
    print(f"❌ 训练测试失败: {e}")
"""
        
        test_file = Path("quick_training_test.py")
        test_file.write_text(test_script, encoding='utf-8')
        self.print_status("测试脚本创建", True, f"已创建: {test_file}")
    
    def run_validation(self):
        """运行完整验证"""
        print("🔍 医学影像AI模型训练环境验证")
        print("=" * 60)
        
        # 执行各项检查
        self.check_python_version()
        self.check_required_packages()
        self.check_gpu_support()
        self.check_dataset_structure()
        self.check_yolo_scripts()
        self.check_dotnet_environment()
        
        # 计算总体状态
        checks = [
            self.results['python_version'],
            len([v for v in self.results['required_packages'].values() if v]) >= 6,  # 至少6个包
            self.results['dataset_structure'],
            self.results['yolo_scripts'],
            self.results['dotnet_environment']
        ]
        
        self.results['overall_status'] = sum(checks) >= 4  # 至少4项通过
        
        # 显示总体结果
        self.print_header("验证结果总结")
        
        if self.results['overall_status']:
            print("🎉 环境验证通过！可以开始使用训练教程。")
            self.create_test_script()
        else:
            print("⚠️  环境配置不完整，请按照以下指导完成配置：")
            self.generate_setup_instructions()
        
        # 显示详细状态
        print(f"\n📊 详细检查结果:")
        print(f"   Python版本: {'✅' if self.results['python_version'] else '❌'}")
        print(f"   Python包: {sum(self.results['required_packages'].values())}/{len(self.results['required_packages'])}")
        print(f"   GPU支持: {'✅' if self.results['gpu_support'] else '❌'}")
        print(f"   数据集: {'✅' if self.results['dataset_structure'] else '❌'}")
        print(f"   训练脚本: {'✅' if self.results['yolo_scripts'] else '❌'}")
        print(f"   .NET环境: {'✅' if self.results['dotnet_environment'] else '❌'}")
        
        return self.results['overall_status']

def main():
    """主函数"""
    validator = TrainingEnvironmentValidator()
    success = validator.run_validation()
    
    if success:
        print(f"\n🚀 下一步操作:")
        print(f"   1. 运行快速测试: python quick_training_test.py")
        print(f"   2. 查看智能打标教程: 智能打标教程.md")
        print(f"   3. 查看模型训练教程: 模型训练教程.md")
        print(f"   4. 查看快速入门指南: 快速入门指南.md")
        return 0
    else:
        print(f"\n❌ 请先完成环境配置，然后重新运行此脚本验证。")
        return 1

if __name__ == "__main__":
    exit(main())
