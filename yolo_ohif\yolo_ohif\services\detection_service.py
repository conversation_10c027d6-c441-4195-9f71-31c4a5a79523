import os
import logging
import numpy as np
import cv2
import torch
from ultralytics import <PERSON>OL<PERSON>
import pydicom
from PIL import Image
import tempfile
import json
import time
from pathlib import Path

# 导入性能监控
try:
    from utils.performance_monitor import timing_decorator, performance_metrics
except ImportError:
    # 如果导入失败，创建空装饰器
    def timing_decorator(name):
        def decorator(func):
            return func
        return decorator
    performance_metrics = None

logger = logging.getLogger(__name__)

class DetectionService:
    """YOLO医学图像疾病检测服务"""
    
    def __init__(self, model_path, confidence_threshold=0.1, iou_threshold=0.45, device=None):
        """初始化YOLO检测服务
        
        Args:
            model_path: YOLO模型路径
            confidence_threshold: 检测置信度阈值
            iou_threshold: IOU阈值，用于非极大值抑制
            device: 运行设备 ('cpu', 'cuda', 'mps'等)
        """
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        self.iou_threshold = iou_threshold
        self.model = None
        self.device = device
        self.class_names = []
        
        # 加载模型，如果失败则抛出异常
        if not self.load_model():
            raise RuntimeError(f"无法加载YOLO模型: {model_path}")
    
    @timing_decorator('load_model')
    def load_model(self):
        """加载YOLO模型"""
        try:
            logger.info(f"正在加载YOLO模型: {self.model_path}")
            if not os.path.exists(self.model_path):
                logger.error(f"模型文件不存在: {self.model_path}")
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
            
            # 加载模型
            self.model = YOLO(self.model_path)
            
            # 获取类别名称
            if hasattr(self.model, 'names') and self.model.names:
                self.class_names = self.model.names
                logger.info(f"从模型获取类别名称: {self.class_names}")
            else:
                # 如果模型没有类别名称，尝试从数据集配置获取
                logger.warning("模型没有类别名称，尝试从数据集配置获取")
                dataset_yaml_path = "E:/Trae/yolo_ohif/yolo_dataset_output/yolo_dataset/dataset.yaml"
                try:
                    import yaml
                    with open(dataset_yaml_path, 'r', encoding='utf-8') as f:
                        dataset_config = yaml.safe_load(f)
                    
                    if 'names' in dataset_config:
                        names = dataset_config['names']
                        if isinstance(names, list):
                            # 转换为字典格式 {0: 'supraspinatus_tear'}
                            self.class_names = {i: name for i, name in enumerate(names)}
                        else:
                            self.class_names = names
                        logger.info(f"从数据集配置获取类别名称: {self.class_names}")
                    else:
                        logger.warning("数据集配置中没有类别名称")
                        self.class_names = {0: 'supraspinatus_tear'}  # 默认类别名称
                        logger.info(f"使用默认类别名称: {self.class_names}")
                except Exception as e:
                    logger.error(f"读取数据集配置失败: {e}")
                    self.class_names = {0: 'supraspinatus_tear'}  # 默认类别名称
                    logger.info(f"使用默认类别名称: {self.class_names}")
            
            logger.info(f"YOLO模型加载成功，类别数: {len(self.class_names)}")
            return True
        except Exception as e:
            logger.error(f"YOLO模型加载失败: {str(e)}")
            self.model = None
            return False
    
    @timing_decorator('switch_model')
    def switch_model(self, new_model_path):
        """切换到新的YOLO模型
        
        Args:
            new_model_path: 新模型的路径
            
        Returns:
            bool: 切换是否成功
        """
        try:
            logger.info(f"正在切换模型: {self.model_path} -> {new_model_path}")
            
            if not os.path.exists(new_model_path):
                raise FileNotFoundError(f"新模型文件不存在: {new_model_path}")
            
            # 保存旧模型路径以备回滚
            old_model_path = self.model_path
            old_model = self.model
            old_class_names = self.class_names
            
            # 更新模型路径
            self.model_path = new_model_path
            
            # 尝试加载新模型
            if self.load_model():
                logger.info(f"模型切换成功: {new_model_path}")
                return True
            else:
                # 加载失败，回滚到旧模型
                logger.warning(f"新模型加载失败，回滚到旧模型: {old_model_path}")
                self.model_path = old_model_path
                self.model = old_model
                self.class_names = old_class_names
                return False
                
        except Exception as e:
            logger.error(f"模型切换失败: {str(e)}")
            raise e
    
    def get_current_model_info(self):
        """获取当前模型信息
        
        Returns:
            dict: 模型信息字典
        """
        try:
            if self.model is None:
                return {
                    'status': 'not_loaded',
                    'message': '模型未加载'
                }
            
            model_info = {
                'status': 'loaded',
                'model_path': self.model_path,
                'model_name': os.path.basename(self.model_path),
                'confidence_threshold': self.confidence_threshold,
                'iou_threshold': self.iou_threshold,
                'device': str(self.device) if self.device else 'auto',
                'class_count': len(self.class_names),
                'class_names': dict(self.class_names) if self.class_names else {}
            }
            
            # 尝试获取模型的额外信息
            try:
                if hasattr(self.model, 'model'):
                    model_info['model_type'] = str(type(self.model.model).__name__)
                if hasattr(self.model, 'task'):
                    model_info['task'] = self.model.task
            except:
                pass
            
            return model_info
            
        except Exception as e:
            logger.error(f"获取模型信息失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取模型信息失败: {str(e)}'
            }
    
    @timing_decorator('detect_dicom')
    def detect_dicom(self, dicom_files, output_dir=None, save_images=False):
        """对DICOM文件进行疾病检测
        
        Args:
            dicom_files: DICOM文件路径列表
            output_dir: 输出目录，用于保存检测结果图像
            save_images: 是否保存检测结果图像
            
        Returns:
            检测结果字典
        """
        if not self.model:
            raise RuntimeError("YOLO模型未加载")
        
        if output_dir and save_images:
            os.makedirs(output_dir, exist_ok=True)
        
        results = []
        for image_index, dicom_file in enumerate(dicom_files):
            try:
                # 读取DICOM文件
                img_array = self._read_dicom(dicom_file)
                if img_array is None:
                    logger.warning(f"无法读取DICOM文件: {dicom_file}")
                    continue
                
                # 进行检测
                detections = self._detect_image(img_array, image_index)
                
                # 保存检测结果图像
                result_image_path = None
                if output_dir and save_images:
                    base_name = os.path.splitext(os.path.basename(dicom_file))[0]
                    result_image_path = os.path.join(output_dir, f"{base_name}_result.jpg")
                    self._save_detection_image(img_array, detections, result_image_path)
                
                # 添加到结果列表
                results.append({
                    'file': dicom_file,
                    'image_index': image_index,
                    'detections': detections,
                    'result_image': result_image_path
                })
            except Exception as e:
                logger.error(f"处理DICOM文件 {dicom_file} 时出错: {str(e)}")
                results.append({
                    'file': dicom_file,
                    'image_index': image_index,
                    'error': str(e),
                    'detections': []
                })
        
        return results
    
    @timing_decorator('detect_single_dicom')
    def detect_single_dicom(self, dicom_file, output_dir=None, save_result_image=False, confidence_threshold=None):
        """对单个DICOM文件进行疾病检测
        
        Args:
            dicom_file: DICOM文件路径
            output_dir: 输出目录，用于保存检测结果图像
            save_result_image: 是否保存检测结果图像
            confidence_threshold: 检测置信度阈值（可选，覆盖默认值）
            
        Returns:
            检测结果字典
        """
        if not self.model:
            raise RuntimeError("YOLO模型未加载")
        
        # 临时设置置信度阈值
        original_threshold = self.confidence_threshold
        if confidence_threshold is not None:
            self.confidence_threshold = confidence_threshold
        
        try:
            # 读取DICOM文件
            img_array = self._read_dicom(dicom_file)
            if img_array is None:
                logger.warning(f"无法读取DICOM文件: {dicom_file}")
                return None
            
            # 进行检测
            detections = self._detect_image(img_array)
            
            # 保存检测结果图像
            result_image_path = None
            if output_dir and save_result_image:
                os.makedirs(output_dir, exist_ok=True)
                base_name = os.path.splitext(os.path.basename(dicom_file))[0]
                result_image_path = os.path.join(output_dir, f"{base_name}_result.jpg")
                self._save_detection_image(img_array, detections, result_image_path)
            
            return {
                'file': dicom_file,
                'detections': detections,
                'result_image': result_image_path
            }
        except Exception as e:
            logger.error(f"检测单个DICOM文件时出错: {str(e)}")
            return {
                'file': dicom_file,
                'error': str(e),
                'detections': []
            }
        finally:
            # 恢复原始置信度阈值
            self.confidence_threshold = original_threshold
    
    def get_model_info(self):
        """获取模型信息
        
        Returns:
            dict: 模型信息字典
        """
        if not self.model:
            return None
        
        try:
            return {
                'model_path': self.model_path,
                'model_loaded': self.model is not None,
                'class_count': len(self.class_names),
                'class_names': self.class_names,
                'confidence_threshold': self.confidence_threshold,
                'iou_threshold': self.iou_threshold,
                'device': str(self.device) if self.device else 'auto'
            }
        except Exception as e:
            logger.error(f"获取模型信息时出错: {str(e)}")
            return None
    
    def check_health(self):
        """检查检测服务健康状态
        
        Returns:
            bool: 服务健康状态
        """
        try:
            return self.model is not None and os.path.exists(self.model_path)
        except Exception as e:
            logger.error(f"检测服务健康检查失败: {str(e)}")
            return False
    
    def _read_dicom(self, dicom_path):
        """读取DICOM文件并转换为图像数组
        
        Args:
            dicom_path: DICOM文件路径
            
        Returns:
            图像数组
        """
        try:
            # 读取DICOM文件
            ds = pydicom.dcmread(dicom_path)
            
            # 获取像素数据
            pixel_array = ds.pixel_array
            
            # 处理不同的图像格式
            if hasattr(ds, 'PhotometricInterpretation'):
                # 根据光度解释进行处理
                if ds.PhotometricInterpretation == "MONOCHROME1":
                    # 反转灰度
                    pixel_array = np.max(pixel_array) - pixel_array
            
            # 归一化到0-255
            if pixel_array.dtype != np.uint8:
                pixel_array = self._normalize_to_uint8(pixel_array)
            
            # 如果是灰度图像，转换为RGB
            if len(pixel_array.shape) == 2:
                pixel_array = cv2.cvtColor(pixel_array, cv2.COLOR_GRAY2RGB)
            elif len(pixel_array.shape) == 3 and pixel_array.shape[2] == 1:
                pixel_array = cv2.cvtColor(pixel_array, cv2.COLOR_GRAY2RGB)
            
            return pixel_array
        except Exception as e:
            logger.error(f"读取DICOM文件 {dicom_path} 时出错: {str(e)}")
            return None
    
    def _normalize_to_uint8(self, img_array):
        """将图像数组归一化到uint8格式
        使用与训练数据相同的归一化方法
        
        Args:
            img_array: 输入图像数组
            
        Returns:
            归一化后的uint8图像数组
        """
        # 使用与训练时相同的归一化方法（简单的min-max归一化）
        # 移除百分位数窗口化，因为训练数据没有使用这种方法
        
        # 转换为float32进行计算
        img_float = img_array.astype(np.float32)
        
        # 简单的min-max归一化到0-255范围
        if img_float.max() > img_float.min():
            img_normalized = ((img_float - img_float.min()) / 
                            (img_float.max() - img_float.min()) * 255).astype(np.uint8)
        else:
            # 如果图像是常数，返回全零图像
            img_normalized = np.zeros_like(img_float, dtype=np.uint8)
        
        return img_normalized
    
    def _detect_image(self, img_array, image_index=None):
        """对图像数组进行疾病检测
        
        Args:
            img_array: 图像数组
            image_index: 图像索引（可选）
            
        Returns:
            检测结果列表
        """
        try:
            # 使用YOLO模型进行检测
            results = self.model(img_array, conf=self.confidence_threshold, iou=self.iou_threshold)
            
            # 解析检测结果
            detections = []
            for result in results:
                boxes = result.boxes
                for i in range(len(boxes)):
                    box = boxes[i]
                    x1, y1, x2, y2 = box.xyxy[0].tolist()
                    confidence = float(box.conf[0])
                    class_id = int(box.cls[0])
                    class_name = self.class_names.get(class_id, f"class_{class_id}")
                    
                    # 计算中心点和宽高
                    x = (x1 + x2) / 2
                    y = (y1 + y2) / 2
                    width = x2 - x1
                    height = y2 - y1
                    
                    detection = {
                        'class_id': class_id,
                        'class': class_name,
                        'confidence': confidence,
                        'x': x,
                        'y': y,
                        'width': width,
                        'height': height,
                        'x1': x1,
                        'y1': y1,
                        'x2': x2,
                        'y2': y2
                    }
                    
                    # 如果提供了图像索引，添加到检测结果中
                    if image_index is not None:
                        detection['image_index'] = image_index
                    
                    detections.append(detection)
            
            return detections
        except Exception as e:
            logger.error(f"执行YOLO检测时出错: {str(e)}")
            raise
    
    def _save_detection_image(self, img_array, detections, output_path):
        """保存带有检测框的图像
        
        Args:
            img_array: 原始图像数组
            detections: 检测结果列表
            output_path: 输出图像路径
        """
        try:
            # 复制图像以避免修改原始数据
            img_result = img_array.copy()
            
            # 绘制检测框
            for detection in detections:
                x1, y1 = int(detection['x1']), int(detection['y1'])
                x2, y2 = int(detection['x2']), int(detection['y2'])
                class_name = detection['class']
                confidence = detection['confidence']
                
                # 绘制边界框
                color = (0, 255, 0)  # 绿色
                cv2.rectangle(img_result, (x1, y1), (x2, y2), color, 2)
                
                # 绘制标签
                label = f"{class_name}: {confidence:.2f}"
                font_scale = 0.6
                font_thickness = 1
                (label_width, label_height), baseline = cv2.getTextSize(
                    label, cv2.FONT_HERSHEY_SIMPLEX, font_scale, font_thickness)
                
                # 绘制标签背景
                cv2.rectangle(
                    img_result,
                    (x1, y1 - label_height - 10),
                    (x1 + label_width, y1),
                    color,
                    -1
                )
                
                # 绘制标签文本
                cv2.putText(
                    img_result,
                    label,
                    (x1, y1 - 5),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    font_scale,
                    (0, 0, 0),  # 黑色文本
                    font_thickness
                )
            
            # 保存图像
            cv2.imwrite(output_path, cv2.cvtColor(img_result, cv2.COLOR_RGB2BGR))
        except Exception as e:
            logger.error(f"保存检测结果图像时出错: {str(e)}")
            raise
    
    def detect_directory(self, input_dir, output_dir=None, save_images=True, recursive=False):
        """对目录中的所有DICOM文件进行疾病检测
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            save_images: 是否保存检测结果图像
            recursive: 是否递归处理子目录
            
        Returns:
            检测结果字典
        """
        if not os.path.isdir(input_dir):
            raise ValueError(f"输入路径不是目录: {input_dir}")
        
        if output_dir is None and save_images:
            output_dir = os.path.join(input_dir, 'detection_results')
            os.makedirs(output_dir, exist_ok=True)
        
        # 收集所有DICOM文件
        dicom_files = []
        if recursive:
            for root, _, files in os.walk(input_dir):
                for file in files:
                    if file.lower().endswith(('.dcm', '.dicom')) or not os.path.splitext(file)[1]:
                        dicom_files.append(os.path.join(root, file))
        else:
            for file in os.listdir(input_dir):
                if file.lower().endswith(('.dcm', '.dicom')) or not os.path.splitext(file)[1]:
                    dicom_files.append(os.path.join(input_dir, file))
        
        # 执行检测
        return self.detect_dicom(dicom_files, output_dir, save_images)
    
    def export_results(self, detection_results, output_path):
        """导出检测结果为JSON文件
        
        Args:
            detection_results: 检测结果列表
            output_path: 输出JSON文件路径
            
        Returns:
            输出文件路径
        """
        try:
            # 准备可序列化的结果
            serializable_results = []
            for result in detection_results:
                serializable_result = {
                    'file': result['file'],
                    'detections': result['detections']
                }
                if 'result_image' in result and result['result_image']:
                    serializable_result['result_image'] = result['result_image']
                if 'error' in result:
                    serializable_result['error'] = result['error']
                
                serializable_results.append(serializable_result)
            
            # 写入JSON文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, ensure_ascii=False, indent=2)
            
            return output_path
        except Exception as e:
            logger.error(f"导出检测结果时出错: {str(e)}")
            raise
    
    def get_model_info(self):
        """获取模型信息
        
        Returns:
            模型信息字典
        """
        if not self.model:
            return {'status': 'error', 'message': 'Model not loaded'}
        
        try:
            info = {
                'model_path': self.model_path,
                'model_type': type(self.model).__name__,
                'confidence_threshold': self.confidence_threshold,
                'iou_threshold': self.iou_threshold,
                'device': self.model.device.type if hasattr(self.model, 'device') else 'unknown',
                'class_count': len(self.class_names),
                'classes': self.class_names
            }
            
            # 添加模型特定信息
            if hasattr(self.model, 'model') and hasattr(self.model.model, 'yaml'):
                info['model_yaml'] = self.model.model.yaml
            
            return info
        except Exception as e:
            logger.error(f"获取模型信息时出错: {str(e)}")
            return {'status': 'error', 'message': str(e)}