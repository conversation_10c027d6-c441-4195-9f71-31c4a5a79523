# YOLO-OHIF 医学图像检测系统部署指南

## 系统概述

YOLO-OHIF 是一个集成了 YOLO 深度学习模型的医学图像检测系统，支持 DICOM 文件的上传、处理和可视化。系统采用 Flask 后端架构，集成了 Orthanc DICOM 服务器和 OHIF 查看器。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (OHIF)   │    │  后端 (Flask)   │    │ DICOM (Orthanc) │
│                 │◄──►│                 │◄──►│                 │
│ - 图像查看器    │    │ - API 服务      │    │ - DICOM 存储    │
│ - 用户界面      │    │ - 用户认证      │    │ - 元数据管理    │
│ - 检测结果显示  │    │ - 文件上传      │    │ - 图像检索      │
└─────────────────┘    │ - YOLO 检测     │    └─────────────────┘
                       │ - 数据库管理    │
                       └─────────────────┘
                                │
                       ┌─────────────────┐
                       │ 数据库 (SQLite) │
                       │                 │
                       │ - 用户数据      │
                       │ - 研究记录      │
                       │ - 检测结果      │
                       │ - 审计日志      │
                       └─────────────────┘
```

## 环境要求

### 硬件要求
- **CPU**: 4核心以上，推荐8核心
- **内存**: 8GB以上，推荐16GB
- **存储**: 100GB以上可用空间
- **GPU**: 可选，支持CUDA的显卡可加速YOLO推理

### 软件要求
- **操作系统**: Windows 10/11, Ubuntu 18.04+, CentOS 7+
- **Python**: 3.8 - 3.11
- **Docker**: 20.10+ (可选，用于容器化部署)
- **Node.js**: 16+ (用于OHIF前端)

## 安装步骤

### 1. 环境准备

#### Windows 环境
```powershell
# 安装 Python
winget install Python.Python.3.11

# 安装 Git
winget install Git.Git

# 安装 Node.js
winget install OpenJS.NodeJS
```

#### Linux 环境
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3.11 python3.11-pip git nodejs npm

# CentOS/RHEL
sudo yum install python3.11 python3.11-pip git nodejs npm
```

### 2. 克隆项目
```bash
git clone <repository-url>
cd yolo_ohif
```

### 3. 创建虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

### 4. 安装依赖
```bash
# 安装 Python 依赖
pip install -r requirements.txt

# 如果使用 GPU
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

### 5. 配置环境变量

创建 `.env` 文件：
```bash
# Flask 配置
YOLO_OHIF_SECRET_KEY=your-very-long-secret-key-at-least-32-characters
YOLO_OHIF_DEBUG=False
YOLO_OHIF_HOST=0.0.0.0
YOLO_OHIF_PORT=5000

# Orthanc 配置
YOLO_OHIF_ORTHANC_URL=http://localhost:8042
YOLO_OHIF_ORTHANC_USERNAME=orthanc
YOLO_OHIF_ORTHANC_PASSWORD=orthanc

# OHIF 配置
YOLO_OHIF_OHIF_URL=http://localhost:3000

# YOLO 配置
YOLO_OHIF_YOLO_MODEL_PATH=models/yolo_model.pt
YOLO_OHIF_YOLO_CONFIDENCE_THRESHOLD=0.5
YOLO_OHIF_YOLO_DEVICE=cpu

# 数据库配置
YOLO_OHIF_DATABASE_PATH=data/app.db
YOLO_OHIF_DATABASE_BACKUP_ENABLED=True
YOLO_OHIF_DATABASE_BACKUP_INTERVAL=24

# 日志配置
YOLO_OHIF_LOG_LEVEL=INFO
YOLO_OHIF_LOG_FILE=logs/app.log
YOLO_OHIF_LOG_MAX_SIZE=10485760

# 安全配置
YOLO_OHIF_JWT_SECRET_KEY=your-jwt-secret-key-at-least-32-characters
YOLO_OHIF_JWT_EXPIRATION_HOURS=24
YOLO_OHIF_MAX_UPLOAD_SIZE=104857600
YOLO_OHIF_ALLOWED_EXTENSIONS=dcm,dicom
```

### 6. 创建必要目录
```bash
mkdir -p data logs uploads temp models docs
```

### 7. 下载 YOLO 模型
```bash
# 下载预训练模型（示例）
wget https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5s.pt -O models/yolo_model.pt
```

### 8. 初始化数据库
```bash
python -c "from services.database_service import DatabaseService; db = DatabaseService(); db.init_database()"
```

## Orthanc 配置

### 1. 安装 Orthanc

#### Docker 方式（推荐）
```bash
docker run -d \
  --name orthanc \
  -p 8042:8042 \
  -p 4242:4242 \
  -v orthanc-data:/var/lib/orthanc/db \
  -e ORTHANC__AUTHENTICATION_ENABLED=true \
  -e ORTHANC__REGISTERED_USERS='{"orthanc":"orthanc"}' \
  orthancteam/orthanc
```

#### 本地安装
```bash
# Ubuntu/Debian
sudo apt install orthanc orthanc-dicomweb

# 配置文件位置: /etc/orthanc/
```

### 2. Orthanc 配置文件

创建 `orthanc.json`：
```json
{
  "Name": "YOLO-OHIF Orthanc",
  "HttpPort": 8042,
  "DicomPort": 4242,
  "AuthenticationEnabled": true,
  "RegisteredUsers": {
    "orthanc": "orthanc"
  },
  "DicomWeb": {
    "Enable": true,
    "Root": "/dicom-web/"
  },
  "CorsEnabled": true,
  "CorsOrigins": "*"
}
```

## OHIF 配置

### 1. 克隆 OHIF
```bash
git clone https://github.com/OHIF/Viewers.git ohif
cd ohif
npm install
```

### 2. 配置 OHIF

编辑 `platform/app/public/config/default.js`：
```javascript
window.config = {
  routerBasename: '/',
  servers: {
    dicomWeb: [
      {
        name: 'Orthanc',
        wadoUriRoot: 'http://localhost:8042/wado',
        qidoRoot: 'http://localhost:8042/dicom-web',
        wadoRoot: 'http://localhost:8042/dicom-web',
        qidoSupportsIncludeField: false,
        supportsReject: false,
        imageRendering: 'wadors',
        thumbnailRendering: 'wadors',
        enableStudyLazyLoad: true,
        supportsFuzzyMatching: false
      }
    ]
  }
};
```

### 3. 启动 OHIF
```bash
npm run dev
```

## 启动系统

### 1. 启动 Orthanc
```bash
# Docker 方式
docker start orthanc

# 本地方式
sudo systemctl start orthanc
```

### 2. 启动 OHIF
```bash
cd ohif
npm run dev
```

### 3. 启动后端服务
```bash
# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 启动应用
python app.py
```

## 生产环境部署

### 1. 使用 Gunicorn (Linux)

安装 Gunicorn：
```bash
pip install gunicorn
```

创建 `gunicorn.conf.py`：
```python
bind = "0.0.0.0:5000"
workers = 4
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
```

启动服务：
```bash
gunicorn -c gunicorn.conf.py app:app
```

### 2. 使用 Nginx 反向代理

安装 Nginx：
```bash
sudo apt install nginx
```

配置文件 `/etc/nginx/sites-available/yolo-ohif`：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    client_max_body_size 100M;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    location /ohif {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /orthanc {
        proxy_pass http://127.0.0.1:8042;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

启用配置：
```bash
sudo ln -s /etc/nginx/sites-available/yolo-ohif /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 3. 使用 Systemd 服务

创建服务文件 `/etc/systemd/system/yolo-ohif.service`：
```ini
[Unit]
Description=YOLO-OHIF Medical Image Detection System
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/yolo_ohif
Environment=PATH=/path/to/yolo_ohif/venv/bin
ExecStart=/path/to/yolo_ohif/venv/bin/gunicorn -c gunicorn.conf.py app:app
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable yolo-ohif
sudo systemctl start yolo-ohif
```

## Docker 部署

### 1. 创建 Dockerfile

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p data logs uploads temp models docs

# 设置权限
RUN chmod +x app.py

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["gunicorn", "-c", "gunicorn.conf.py", "app:app"]
```

### 2. 创建 docker-compose.yml

```yaml
version: '3.8'

services:
  yolo-ohif:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./models:/app/models
    environment:
      - YOLO_OHIF_SECRET_KEY=your-secret-key
      - YOLO_OHIF_ORTHANC_URL=http://orthanc:8042
      - YOLO_OHIF_OHIF_URL=http://ohif:3000
    depends_on:
      - orthanc
      - ohif
    restart: unless-stopped

  orthanc:
    image: orthancteam/orthanc:latest
    ports:
      - "8042:8042"
      - "4242:4242"
    volumes:
      - orthanc-data:/var/lib/orthanc/db
    environment:
      - ORTHANC__AUTHENTICATION_ENABLED=true
      - ORTHANC__REGISTERED_USERS={"orthanc":"orthanc"}
    restart: unless-stopped

  ohif:
    image: ohif/app:latest
    ports:
      - "3000:80"
    volumes:
      - ./ohif-config:/usr/share/nginx/html/config
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - yolo-ohif
      - orthanc
      - ohif
    restart: unless-stopped

volumes:
  orthanc-data:
```

### 3. 启动容器

```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 监控和维护

### 1. 健康检查

系统提供多个健康检查端点：
- `/health` - 基本健康检查
- `/api/health` - 详细健康状态
- `/api/metrics` - 性能指标
- `/api/database/stats` - 数据库统计

### 2. 日志管理

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log

# 日志轮转配置
sudo logrotate -d /etc/logrotate.d/yolo-ohif
```

### 3. 数据库维护

```bash
# 数据库优化
curl -X POST http://localhost:5000/api/database/optimize

# 数据库备份
sqlite3 data/app.db ".backup backup_$(date +%Y%m%d_%H%M%S).db"

# 数据库恢复
sqlite3 data/app.db ".restore backup_file.db"
```

### 4. 性能监控

```bash
# 系统资源监控
htop
iotop
netstat -tulpn

# 应用性能监控
curl http://localhost:5000/api/metrics
```

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :5000
   # 杀死占用进程
   sudo kill -9 <PID>
   ```

2. **权限问题**
   ```bash
   # 设置正确权限
   sudo chown -R www-data:www-data /path/to/yolo_ohif
   sudo chmod -R 755 /path/to/yolo_ohif
   ```

3. **依赖问题**
   ```bash
   # 重新安装依赖
   pip install --force-reinstall -r requirements.txt
   ```

4. **YOLO 模型加载失败**
   ```bash
   # 检查模型文件
   ls -la models/
   # 验证模型格式
   python -c "import torch; print(torch.load('models/yolo_model.pt'))"
   ```

### 日志分析

```bash
# 查看启动日志
grep "Starting" logs/app.log

# 查看错误日志
grep "ERROR\|CRITICAL" logs/app.log

# 查看性能日志
grep "performance" logs/app.log
```

## 安全建议

1. **更改默认密码**
   - 修改 Orthanc 默认用户名密码
   - 使用强密码策略

2. **启用 HTTPS**
   - 配置 SSL 证书
   - 强制 HTTPS 重定向

3. **网络安全**
   - 配置防火墙规则
   - 限制访问 IP

4. **定期更新**
   - 更新系统依赖
   - 更新 Python 包
   - 更新 Docker 镜像

## 备份策略

### 1. 数据库备份
```bash
#!/bin/bash
# backup_db.sh
BACKUP_DIR="/backup/database"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR
sqlite3 data/app.db ".backup $BACKUP_DIR/app_$DATE.db"
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
```

### 2. 文件备份
```bash
#!/bin/bash
# backup_files.sh
BACKUP_DIR="/backup/files"
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz uploads/
tar -czf $BACKUP_DIR/models_$DATE.tar.gz models/
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

### 3. 自动备份
```bash
# 添加到 crontab
crontab -e

# 每天凌晨2点备份数据库
0 2 * * * /path/to/backup_db.sh

# 每周日凌晨3点备份文件
0 3 * * 0 /path/to/backup_files.sh
```

## 扩展和定制

### 1. 添加新的检测模型
1. 将模型文件放入 `models/` 目录
2. 修改 `detection_service.py` 中的模型加载逻辑
3. 更新配置文件中的模型路径

### 2. 自定义前端界面
1. 修改 OHIF 配置文件
2. 添加自定义插件
3. 修改样式和布局

### 3. 集成其他 DICOM 服务器
1. 修改 `orthanc_service.py`
2. 更新配置参数
3. 测试兼容性

## 联系支持

如果遇到问题，请：
1. 查看日志文件
2. 检查配置文件
3. 参考故障排除指南
4. 联系技术支持团队

---

**注意**: 本指南适用于 YOLO-OHIF v1.0.0，不同版本可能存在差异。部署前请确保阅读最新的发布说明。