{% extends "base.html" %}

{% block title %}检测结果 - YOLO-OHIF医学图像疾病检测系统{% endblock %}

{% block extra_css %}
<style>
    .detection-card {
        transition: transform 0.3s;
    }
    .detection-card:hover {
        transform: translateY(-5px);
    }
    .detection-image {
        max-height: 300px;
        object-fit: contain;
    }
    .confidence-bar {
        height: 5px;
    }
    .detection-badge {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .detection-details {
        max-height: 400px;
        overflow-y: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('web.dashboard') }}">仪表板</a></li>
                <li class="breadcrumb-item active">检测结果</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-0">{{ study.patient_name or '未知患者' }} 的检测结果</h2>
                        <p class="text-muted">
                            <i class="fas fa-calendar-alt me-1"></i>{{ study.study_date or '未知日期' }} | 
                            <i class="fas fa-x-ray me-1"></i>{{ study.modality or '未知模态' }} | 
                            <i class="fas fa-images me-1"></i>{{ study.images_count or 0 }} 张图像
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ url_for('web.dicom_viewer', study_id=study.id) }}" class="btn btn-primary">
                            <i class="fas fa-eye me-1"></i>在DICOM查看器中查看
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-4 mb-4">
        <div class="card bg-primary text-white shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase">检测图像总数</h6>
                        <h2 class="mb-0">{{ detection_results|length if detection_results else 0 }}</h2>
                    </div>
                    <i class="fas fa-images fa-3x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card bg-warning text-white shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase">检测到异常的图像</h6>
                        <h2 class="mb-0">{{ abnormal_count if abnormal_count else 0 }}</h2>
                    </div>
                    <i class="fas fa-exclamation-triangle fa-3x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card bg-info text-white shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase">平均置信度</h6>
                        <h2 class="mb-0">{{ avg_confidence|round(2) if avg_confidence else 0 }}%</h2>
                    </div>
                    <i class="fas fa-chart-line fa-3x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
</div>

{% if detection_summary %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-white">
                <h4 class="mb-0"><i class="fas fa-chart-pie me-2"></i>检测结果摘要</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <canvas id="detectionPieChart"></canvas>
                    </div>
                    <div class="col-md-6">
                        <h5>检测到的异常类型</h5>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>异常类型</th>
                                    <th>检测数量</th>
                                    <th>平均置信度</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in detection_summary %}
                                <tr>
                                    <td>
                                        <span class="badge" style="background-color: {{ item.color }}">&nbsp;</span>
                                        {{ item.label }}
                                    </td>
                                    <td>{{ item.count }}</td>
                                    <td>{{ item.avg_confidence|round(2) }}%</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="fas fa-search me-2"></i>检测结果过滤</h4>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('web.results', study_id=study.id) }}" class="row g-3">
                    <div class="col-md-4">
                        <label for="label" class="form-label">异常类型</label>
                        <select class="form-select" id="label" name="label">
                            <option value="" {% if not request.args.get('label') %}selected{% endif %}>全部</option>
                            {% for label in available_labels %}
                            <option value="{{ label }}" {% if request.args.get('label') == label %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="min_confidence" class="form-label">最低置信度</label>
                        <div class="input-group">
                            <input type="range" class="form-range" id="min_confidence" name="min_confidence" min="0" max="100" step="5" value="{{ request.args.get('min_confidence', 50) }}" oninput="this.nextElementSibling.value = this.value + '%'">
                            <output>{{ request.args.get('min_confidence', 50) }}%</output>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="sort" class="form-label">排序</label>
                        <select class="form-select" id="sort" name="sort">
                            <option value="confidence_desc" {% if request.args.get('sort') == 'confidence_desc' or not request.args.get('sort') %}selected{% endif %}>置信度（降序）</option>
                            <option value="confidence_asc" {% if request.args.get('sort') == 'confidence_asc' %}selected{% endif %}>置信度（升序）</option>
                            <option value="instance_desc" {% if request.args.get('sort') == 'instance_desc' %}selected{% endif %}>实例数（降序）</option>
                            <option value="instance_asc" {% if request.args.get('sort') == 'instance_asc' %}selected{% endif %}>实例数（升序）</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-1"></i>应用过滤
                        </button>
                        <a href="{{ url_for('web.results', study_id=study.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-undo me-1"></i>重置
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <h3 class="mb-3">检测结果详情</h3>
        {% if detection_results %}
        <div class="row">
            {% for result in detection_results %}
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm detection-card h-100">
                    {% if result.detections|length > 0 %}
                    <div class="detection-badge badge bg-warning text-dark">
                        <i class="fas fa-exclamation-triangle me-1"></i>检测到 {{ result.detections|length }} 个异常
                    </div>
                    {% else %}
                    <div class="detection-badge badge bg-success">
                        <i class="fas fa-check-circle me-1"></i>正常
                    </div>
                    {% endif %}
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <img src="{{ url_for('api.get_detection_image', detection_id=result.id) }}" class="img-fluid detection-image" alt="检测结果图像">
                        </div>
                        <h5 class="card-title">{{ result.instance_number or '未知实例' }}</h5>
                        <p class="card-text">
                            <small class="text-muted">
                                <i class="fas fa-calendar-alt me-1"></i>{{ result.acquisition_date or '未知日期' }}
                            </small>
                            <br>
                            <small class="text-muted">
                                <i class="fas fa-ruler me-1"></i>{{ result.image_size or '未知尺寸' }}
                            </small>
                        </p>
                        
                        {% if result.detections|length > 0 %}
                        <div class="detection-details">
                            <h6>检测到的异常：</h6>
                            <div class="list-group">
                                {% for detection in result.detections %}
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-1">{{ detection.label }}</h6>
                                        <span class="badge bg-primary">{{ detection.confidence|round(2) }}%</span>
                                    </div>
                                    <p class="mb-1">位置: [{{ detection.x }}, {{ detection.y }}, {{ detection.width }}, {{ detection.height }}]</p>
                                    <div class="progress confidence-bar">
                                        <div class="progress-bar" role="progressbar" style="width: {{ detection.confidence }}%" aria-valuenow="{{ detection.confidence }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% else %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>未检测到异常
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-footer bg-white">
                        <div class="btn-group w-100">
                            <a href="{{ url_for('web.view_image', study_id=study.id, instance_id=result.instance_id) }}" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>在OHIF中查看
                            </a>
                            <a href="{{ url_for('api.download_detection_image', detection_id=result.id) }}" class="btn btn-outline-success">
                                <i class="fas fa-download me-1"></i>下载图像
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- 分页 -->
        {% if pagination and pagination.pages > 1 %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                <li class="page-item {% if pagination.page == 1 %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('web.results', study_id=study.id, page=pagination.page-1, **request.args) if pagination.page > 1 else '#' }}">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                {% for p in range(1, pagination.pages + 1) %}
                <li class="page-item {% if p == pagination.page %}active{% endif %}">
                    <a class="page-link" href="{{ url_for('web.results', study_id=study.id, page=p, **request.args) }}">{{ p }}</a>
                </li>
                {% endfor %}
                <li class="page-item {% if pagination.page == pagination.pages %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('web.results', study_id=study.id, page=pagination.page+1, **request.args) if pagination.page < pagination.pages else '#' }}">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>没有找到检测结果。
            <a href="{{ url_for('web.detect', study_id=study.id) }}" class="alert-link">点击这里运行YOLO检测</a>。
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if detection_summary %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 准备饼图数据
        const ctx = document.getElementById('detectionPieChart').getContext('2d');
        const data = {
            labels: [{% for item in detection_summary %}'{{ item.label }}',{% endfor %}],
            datasets: [{
                data: [{% for item in detection_summary %}{{ item.count }},{% endfor %}],
                backgroundColor: [{% for item in detection_summary %}'{{ item.color }}',{% endfor %}],
                hoverOffset: 4
            }]
        };
        
        // 创建饼图
        const detectionPieChart = new Chart(ctx, {
            type: 'pie',
            data: data,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: true,
                        text: '检测到的异常类型分布'
                    }
                }
            }
        });
    });
</script>
{% endif %}

<script>
    // 自动提交表单当选择改变时
    document.querySelectorAll('#label, #sort').forEach(function(element) {
        element.addEventListener('change', function() {
            this.form.submit();
        });
    });
    
    // 延迟提交置信度滑块，避免频繁刷新
    let confidenceTimer;
    document.getElementById('min_confidence').addEventListener('change', function() {
        clearTimeout(confidenceTimer);
        confidenceTimer = setTimeout(() => {
            this.form.submit();
        }, 500);
    });
</script>
{% endblock %}