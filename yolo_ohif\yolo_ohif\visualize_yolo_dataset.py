#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO数据集可视化工具
用于验证图像和标签的匹配情况

使用方法:
    python visualize_yolo_dataset.py
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import random
from typing import List, Tuple, Optional

class YOLODatasetVisualizer:
    """YOLO数据集可视化器"""
    
    def __init__(self, dataset_path: str):
        """
        初始化可视化器
        
        Args:
            dataset_path: YOLO数据集根目录路径
        """
        self.dataset_path = Path(dataset_path)
        self.images_dir = self.dataset_path / "images"
        self.labels_dir = self.dataset_path / "labels"
        
        # 验证数据集结构
        if not self.dataset_path.exists():
            raise ValueError(f"数据集路径不存在: {dataset_path}")
        if not self.images_dir.exists():
            raise ValueError(f"图像目录不存在: {self.images_dir}")
        if not self.labels_dir.exists():
            raise ValueError(f"标签目录不存在: {self.labels_dir}")
    
    def get_splits(self) -> List[str]:
        """获取数据集分割列表"""
        splits = []
        for split_dir in self.images_dir.iterdir():
            if split_dir.is_dir():
                splits.append(split_dir.name)
        return sorted(splits)
    
    def get_image_files(self, split: str) -> List[Path]:
        """获取指定分割的所有图像文件"""
        image_dir = self.images_dir / split
        if not image_dir.exists():
            return []
        
        image_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
            image_files.extend(image_dir.glob(ext))
        return sorted(image_files)
    
    def load_yolo_annotation(self, label_path: Path) -> List[Tuple[int, float, float, float, float]]:
        """加载YOLO格式标注文件"""
        annotations = []
        if label_path.exists():
            with open(label_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        parts = line.split()
                        if len(parts) >= 5:
                            class_id = int(parts[0])
                            x_center = float(parts[1])
                            y_center = float(parts[2])
                            width = float(parts[3])
                            height = float(parts[4])
                            annotations.append((class_id, x_center, y_center, width, height))
        return annotations
    
    def yolo_to_bbox(self, yolo_coords: Tuple[float, float, float, float], 
                     img_width: int, img_height: int) -> Tuple[int, int, int, int]:
        """将YOLO坐标转换为边界框坐标"""
        x_center, y_center, width, height = yolo_coords
        
        # 转换为像素坐标
        x_center_px = x_center * img_width
        y_center_px = y_center * img_height
        width_px = width * img_width
        height_px = height * img_height
        
        # 计算边界框左上角和右下角坐标
        x1 = int(x_center_px - width_px / 2)
        y1 = int(y_center_px - height_px / 2)
        x2 = int(x_center_px + width_px / 2)
        y2 = int(y_center_px + height_px / 2)
        
        return x1, y1, x2, y2
    
    def visualize_image(self, image_path: Path, save_path: Optional[str] = None, 
                       show: bool = True) -> None:
        """可视化单张图像及其标注"""
        # 读取图像
        image = cv2.imread(str(image_path))
        if image is None:
            print(f"无法读取图像: {image_path}")
            return
        
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        img_height, img_width = image.shape[:2]
        
        # 获取对应的标签文件路径
        split = image_path.parent.name
        label_filename = image_path.stem + '.txt'
        label_path = self.labels_dir / split / label_filename
        
        # 加载标注
        annotations = self.load_yolo_annotation(label_path)
        
        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.imshow(image)
        
        # 绘制边界框
        colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange']
        for i, (class_id, x_center, y_center, width, height) in enumerate(annotations):
            x1, y1, x2, y2 = self.yolo_to_bbox((x_center, y_center, width, height), 
                                               img_width, img_height)
            
            # 绘制边界框
            color = colors[class_id % len(colors)]
            rect = patches.Rectangle((x1, y1), x2-x1, y2-y1, 
                                   linewidth=2, edgecolor=color, facecolor='none')
            ax.add_patch(rect)
            
            # 添加类别标签
            ax.text(x1, y1-5, f'Class {class_id}', 
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.7),
                   fontsize=10, color='white')
        
        # 设置标题
        title = f"图像: {image_path.name}"
        if annotations:
            title += f" | 标注数量: {len(annotations)}"
        else:
            title += " | 无标注"
        
        if not label_path.exists():
            title += " | 标签文件不存在"
        
        ax.set_title(title, fontsize=14)
        ax.axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"可视化结果已保存到: {save_path}")
        
        if show:
            plt.show()
        else:
            plt.close()
    
    def check_dataset_integrity(self) -> dict:
        """检查数据集完整性"""
        results = {}
        
        for split in self.get_splits():
            print(f"\n检查 {split} 分割...")
            
            # 获取图像文件
            image_files = self.get_image_files(split)
            
            # 统计信息
            total_images = len(image_files)
            images_with_labels = 0
            images_without_labels = 0
            total_annotations = 0
            
            missing_labels = []
            
            for image_path in image_files:
                label_filename = image_path.stem + '.txt'
                label_path = self.labels_dir / split / label_filename
                
                if label_path.exists():
                    annotations = self.load_yolo_annotation(label_path)
                    if annotations:
                        images_with_labels += 1
                        total_annotations += len(annotations)
                    else:
                        images_without_labels += 1
                else:
                    images_without_labels += 1
                    missing_labels.append(image_path.name)
            
            results[split] = {
                'total_images': total_images,
                'images_with_labels': images_with_labels,
                'images_without_labels': images_without_labels,
                'total_annotations': total_annotations,
                'missing_labels': missing_labels[:10]  # 只显示前10个
            }
            
            print(f"  总图像数: {total_images}")
            print(f"  有标注的图像: {images_with_labels}")
            print(f"  无标注的图像: {images_without_labels}")
            print(f"  总标注数: {total_annotations}")
            if missing_labels:
                print(f"  缺失标签文件的图像数: {len(missing_labels)}")
                if len(missing_labels) <= 10:
                    print(f"  缺失标签的图像: {missing_labels}")
                else:
                    print(f"  部分缺失标签的图像: {missing_labels[:10]}...")
        
        return results
    
    def visualize_random_samples(self, split: str = 'val', num_samples: int = 6, 
                               save_dir: Optional[str] = None) -> None:
        """可视化随机样本"""
        image_files = self.get_image_files(split)
        if not image_files:
            print(f"在 {split} 分割中没有找到图像文件")
            return
        
        # 随机选择样本
        num_samples = min(num_samples, len(image_files))
        selected_files = random.sample(image_files, num_samples)
        
        print(f"\n可视化 {split} 分割中的 {num_samples} 个随机样本...")
        
        for i, image_path in enumerate(selected_files):
            save_path = None
            if save_dir:
                os.makedirs(save_dir, exist_ok=True)
                save_path = os.path.join(save_dir, f"{split}_sample_{i+1}_{image_path.name}.png")
            
            print(f"\n样本 {i+1}: {image_path.name}")
            self.visualize_image(image_path, save_path=save_path, show=False)
        
        print(f"\n完成 {num_samples} 个样本的可视化")
    
    def analyze_annotations(self) -> dict:
        """分析标注统计信息"""
        print("\n分析标注统计信息...")
        
        stats = {}
        overall_stats = {
            'total_images': 0,
            'total_annotations': 0,
            'class_distribution': {},
            'bbox_sizes': []
        }
        
        for split in self.get_splits():
            image_files = self.get_image_files(split)
            split_stats = {
                'total_images': len(image_files),
                'total_annotations': 0,
                'class_distribution': {},
                'bbox_sizes': []
            }
            
            for image_path in image_files:
                label_filename = image_path.stem + '.txt'
                label_path = self.labels_dir / split / label_filename
                
                annotations = self.load_yolo_annotation(label_path)
                split_stats['total_annotations'] += len(annotations)
                
                for class_id, x_center, y_center, width, height in annotations:
                    # 类别分布
                    split_stats['class_distribution'][class_id] = \
                        split_stats['class_distribution'].get(class_id, 0) + 1
                    overall_stats['class_distribution'][class_id] = \
                        overall_stats['class_distribution'].get(class_id, 0) + 1
                    
                    # 边界框大小
                    bbox_area = width * height
                    split_stats['bbox_sizes'].append(bbox_area)
                    overall_stats['bbox_sizes'].append(bbox_area)
            
            stats[split] = split_stats
            overall_stats['total_images'] += split_stats['total_images']
            overall_stats['total_annotations'] += split_stats['total_annotations']
        
        # 打印统计信息
        print(f"\n总体统计:")
        print(f"  总图像数: {overall_stats['total_images']}")
        print(f"  总标注数: {overall_stats['total_annotations']}")
        print(f"  类别分布: {dict(sorted(overall_stats['class_distribution'].items()))}")
        
        if overall_stats['bbox_sizes']:
            bbox_sizes = np.array(overall_stats['bbox_sizes'])
            print(f"  边界框大小统计:")
            print(f"    平均: {bbox_sizes.mean():.4f}")
            print(f"    中位数: {np.median(bbox_sizes):.4f}")
            print(f"    最小: {bbox_sizes.min():.4f}")
            print(f"    最大: {bbox_sizes.max():.4f}")
        
        for split, split_stats in stats.items():
            print(f"\n{split} 分割统计:")
            print(f"  图像数: {split_stats['total_images']}")
            print(f"  标注数: {split_stats['total_annotations']}")
            print(f"  类别分布: {dict(sorted(split_stats['class_distribution'].items()))}")
        
        return stats

def main():
    """主函数"""
    # 数据集路径
    dataset_path = r"e:\Trae\yolo_ohif\yolo_training_output\yolo_dataset"
    
    try:
        # 创建可视化器
        visualizer = YOLODatasetVisualizer(dataset_path)
        
        print("YOLO数据集可视化工具")
        print("=" * 50)
        
        # 检查数据集完整性
        print("\n1. 检查数据集完整性...")
        integrity_results = visualizer.check_dataset_integrity()
        
        # 分析标注统计
        print("\n2. 分析标注统计...")
        stats = visualizer.analyze_annotations()
        
        # 可视化随机样本
        print("\n3. 可视化随机样本...")
        
        # 为每个分割可视化样本
        splits = visualizer.get_splits()
        for split in splits:
            if split in ['train', 'val', 'test']:
                print(f"\n可视化 {split} 分割的样本...")
                save_dir = f"visualization_output/{split}_samples"
                visualizer.visualize_random_samples(split, num_samples=3, save_dir=save_dir)
        
        print("\n" + "=" * 50)
        print("数据集验证完成！")
        print("\n验证要点:")
        print("1. 检查边界框是否正确标注了撕裂区域")
        print("2. 确认图像和标签文件名匹配")
        print("3. 验证标注的准确性和一致性")
        print("4. 查看可视化输出文件夹中的样本图像")
        
        # 交互式可视化
        print("\n4. 交互式可视化...")
        while True:
            print("\n选择操作:")
            print("1. 可视化特定图像")
            print("2. 可视化更多随机样本")
            print("3. 退出")
            
            choice = input("请输入选择 (1-3): ").strip()
            
            if choice == '1':
                split = input("请输入分割名称 (train/val/test): ").strip()
                if split in splits:
                    image_files = visualizer.get_image_files(split)
                    if image_files:
                        print(f"\n{split} 分割中的前10个图像:")
                        for i, img_path in enumerate(image_files[:10]):
                            print(f"  {i+1}. {img_path.name}")
                        
                        try:
                            idx = int(input("请选择图像编号: ")) - 1
                            if 0 <= idx < len(image_files):
                                visualizer.visualize_image(image_files[idx])
                            else:
                                print("无效的编号")
                        except ValueError:
                            print("请输入有效的数字")
                    else:
                        print(f"在 {split} 分割中没有找到图像")
                else:
                    print("无效的分割名称")
            
            elif choice == '2':
                split = input("请输入分割名称 (train/val/test): ").strip()
                if split in splits:
                    try:
                        num = int(input("请输入样本数量: "))
                        save_dir = f"visualization_output/{split}_additional_samples"
                        visualizer.visualize_random_samples(split, num_samples=num, save_dir=save_dir)
                    except ValueError:
                        print("请输入有效的数字")
                else:
                    print("无效的分割名称")
            
            elif choice == '3':
                break
            
            else:
                print("无效的选择")
    
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()