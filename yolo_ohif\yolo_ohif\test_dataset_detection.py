#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据集检测功能
验证智能跳过功能是否正常工作
"""

import os
import sys
from pathlib import Path
import shutil

def test_dataset_detection():
    """
    测试数据集检测功能
    """
    print("🧪 测试数据集检测功能")
    print("=" * 50)
    
    # 导入训练器
    try:
        from train_yolo11x_from_scratch import YOLO11xTrainer
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    
    # 创建测试训练器
    trainer = YOLO11xTrainer(
        dataset_root='./dataset',
        output_root='./test_output',
        img_size=640
    )
    
    print("\n📋 测试场景1: 无已存在数据集")
    # 确保测试输出目录不存在
    test_output = Path('./test_output')
    if test_output.exists():
        shutil.rmtree(test_output)
    
    # 检查数据集
    result1 = trainer.check_existing_dataset()
    if result1 is None:
        print("✅ 正确检测到无已存在数据集")
    else:
        print(f"❌ 错误: 应该返回None，但返回了 {result1}")
        return False
    
    print("\n📋 测试场景2: 创建模拟数据集")
    # 创建模拟的YOLO数据集结构
    yolo_dataset_dir = test_output / "yolo_dataset"
    train_dir = yolo_dataset_dir / "train"
    val_dir = yolo_dataset_dir / "val"
    
    # 创建目录结构
    (train_dir / "images").mkdir(parents=True, exist_ok=True)
    (train_dir / "labels").mkdir(parents=True, exist_ok=True)
    (val_dir / "images").mkdir(parents=True, exist_ok=True)
    (val_dir / "labels").mkdir(parents=True, exist_ok=True)
    
    # 创建模拟文件
    (train_dir / "images" / "test1.jpg").touch()
    (train_dir / "labels" / "test1.txt").touch()
    (val_dir / "images" / "test2.jpg").touch()
    (val_dir / "labels" / "test2.txt").touch()
    
    # 创建配置文件
    config_content = """
path: ./test_output/yolo_dataset
train: train/images
val: val/images

names:
  0: supraspinatus_tear
"""
    with open(yolo_dataset_dir / "data.yaml", 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ 模拟数据集创建完成")
    
    print("\n📋 测试场景3: 检测已存在数据集")
    # 重新创建训练器以测试检测功能
    trainer2 = YOLO11xTrainer(
        dataset_root='./dataset',
        output_root='./test_output',
        img_size=640
    )
    
    result2 = trainer2.check_existing_dataset()
    if result2 is not None:
        print(f"✅ 正确检测到已存在数据集: {result2}")
    else:
        print("❌ 错误: 应该检测到已存在数据集，但返回了None")
        return False
    
    print("\n📋 测试场景4: 测试create_dataset方法")
    # 测试create_dataset方法是否会跳过创建
    try:
        config_path = trainer2.create_dataset()
        if config_path == result2:
            print("✅ create_dataset正确跳过了数据集创建")
        else:
            print(f"❌ create_dataset返回了不同的路径: {config_path} vs {result2}")
            return False
    except Exception as e:
        print(f"⚠️ create_dataset测试跳过（可能因为缺少原始数据）: {e}")
    
    # 清理测试文件
    print("\n🧹 清理测试文件")
    if test_output.exists():
        shutil.rmtree(test_output)
    print("✅ 清理完成")
    
    print("\n🎉 所有测试通过！数据集检测功能正常工作")
    return True

def main():
    """
    主函数
    """
    success = test_dataset_detection()
    if success:
        print("\n✅ 测试成功完成")
        sys.exit(0)
    else:
        print("\n❌ 测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()