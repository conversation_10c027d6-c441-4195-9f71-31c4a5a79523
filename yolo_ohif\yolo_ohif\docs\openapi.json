{"openapi": "3.0.0", "info": {"title": "YOLO-OHIF Medical Image Detection API", "version": "1.0.0", "description": "医学图像检测系统API文档", "contact": {"name": "API Support", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:5000", "description": "开发服务器"}], "paths": {}, "components": {"schemas": {"User": {"type": "object", "properties": {"id": {"type": "integer", "description": "用户ID"}, "username": {"type": "string", "description": "用户名"}, "email": {"type": "string", "format": "email", "description": "邮箱地址"}, "role": {"type": "string", "enum": ["admin", "doctor", "technician"], "description": "用户角色"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}}, "required": ["username", "email", "role"]}, "Study": {"type": "object", "properties": {"id": {"type": "integer", "description": "研究ID"}, "orthanc_id": {"type": "string", "description": "Orthanc系统ID"}, "patient_id": {"type": "string", "description": "患者ID"}, "patient_name": {"type": "string", "description": "患者姓名"}, "study_date": {"type": "string", "format": "date", "description": "检查日期"}, "modality": {"type": "string", "description": "检查方式"}, "description": {"type": "string", "description": "研究描述"}}, "required": ["orthanc_id", "patient_id"]}, "DetectionResult": {"type": "object", "properties": {"id": {"type": "integer", "description": "检测结果ID"}, "study_id": {"type": "integer", "description": "研究ID"}, "user_id": {"type": "integer", "description": "用户ID"}, "detection_time": {"type": "string", "format": "date-time", "description": "检测时间"}, "results": {"type": "object", "description": "检测结果JSON"}, "confidence_threshold": {"type": "number", "description": "置信度阈值"}, "model_version": {"type": "string", "description": "模型版本"}}, "required": ["study_id", "user_id", "results"]}, "HealthStatus": {"type": "object", "properties": {"status": {"type": "string", "enum": ["healthy", "unhealthy"], "description": "健康状态"}, "orthanc": {"type": "object", "description": "Orthanc服务状态"}, "detection_service": {"type": "object", "description": "检测服务状态"}, "database": {"type": "object", "description": "数据库状态"}, "timestamp": {"type": "string", "format": "date-time", "description": "检查时间"}}}, "ErrorResponse": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息"}, "code": {"type": "integer", "description": "错误代码"}, "details": {"type": "object", "description": "错误详情"}}, "required": ["error"]}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "tags": [{"name": "authentication", "description": "用户认证相关接口"}, {"name": "detection", "description": "图像检测相关接口"}, {"name": "studies", "description": "研究管理相关接口"}, {"name": "system", "description": "系统监控相关接口"}]}