import os
import json
import yaml
import logging
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from pathlib import Path
from datetime import datetime
import re

logger = logging.getLogger(__name__)

@dataclass
class ConfigValidationRule:
    """配置验证规则"""
    key: str
    required: bool = True
    data_type: type = str
    default_value: Any = None
    validator: Optional[callable] = None
    description: str = ""
    sensitive: bool = False  # 是否为敏感信息

@dataclass
class ConfigSection:
    """配置段"""
    name: str
    rules: List[ConfigValidationRule] = field(default_factory=list)
    description: str = ""

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None, env_prefix: str = "YOLO_OHIF"):
        self.config_file = config_file
        self.env_prefix = env_prefix
        self.config_data: Dict[str, Any] = {}
        self.validation_rules: Dict[str, ConfigSection] = {}
        self.validation_errors: List[str] = []
        
        self._setup_default_rules()
        self._load_config()
    
    def _setup_default_rules(self):
        """设置默认验证规则"""
        
        # Flask配置规则
        flask_rules = [
            ConfigValidationRule(
                key="SECRET_KEY",
                required=True,
                validator=lambda x: len(x) >= 32,
                description="Flask密钥，至少32字符",
                sensitive=True
            ),
            ConfigValidationRule(
                key="DEBUG",
                required=False,
                data_type=bool,
                default_value=False,
                description="调试模式"
            ),
            ConfigValidationRule(
                key="HOST",
                required=False,
                default_value="0.0.0.0",
                validator=self._validate_ip_address,
                description="服务器主机地址"
            ),
            ConfigValidationRule(
                key="PORT",
                required=False,
                data_type=int,
                default_value=5000,
                validator=lambda x: 1 <= x <= 65535,
                description="服务器端口"
            )
        ]
        
        # Orthanc配置规则
        orthanc_rules = [
            ConfigValidationRule(
                key="ORTHANC_URL",
                required=True,
                validator=self._validate_url,
                description="Orthanc服务器URL"
            ),
            ConfigValidationRule(
                key="ORTHANC_USERNAME",
                required=False,
                description="Orthanc用户名"
            ),
            ConfigValidationRule(
                key="ORTHANC_PASSWORD",
                required=False,
                description="Orthanc密码",
                sensitive=True
            )
        ]
        
        # OHIF配置规则
        ohif_rules = [
            ConfigValidationRule(
                key="OHIF_URL",
                required=True,
                validator=self._validate_url,
                description="OHIF查看器URL"
            )
        ]
        
        # YOLO配置规则
        yolo_rules = [
            ConfigValidationRule(
                key="YOLO_MODEL_PATH",
                required=True,
                validator=self._validate_file_path,
                description="YOLO模型文件路径"
            ),
            ConfigValidationRule(
                key="YOLO_CONFIDENCE_THRESHOLD",
                required=False,
                data_type=float,
                default_value=0.5,
                validator=lambda x: 0.0 <= x <= 1.0,
                description="YOLO置信度阈值"
            ),
            ConfigValidationRule(
                key="YOLO_DEVICE",
                required=False,
                default_value="cpu",
                validator=lambda x: x in ["cpu", "cuda", "mps"],
                description="YOLO推理设备"
            )
        ]
        
        # 数据库配置规则
        database_rules = [
            ConfigValidationRule(
                key="DATABASE_PATH",
                required=True,
                validator=self._validate_database_path,
                description="数据库文件路径"
            ),
            ConfigValidationRule(
                key="DATABASE_BACKUP_ENABLED",
                required=False,
                data_type=bool,
                default_value=True,
                description="是否启用数据库备份"
            ),
            ConfigValidationRule(
                key="DATABASE_BACKUP_INTERVAL",
                required=False,
                data_type=int,
                default_value=24,
                validator=lambda x: x > 0,
                description="数据库备份间隔（小时）"
            )
        ]
        
        # 日志配置规则
        logging_rules = [
            ConfigValidationRule(
                key="LOG_LEVEL",
                required=False,
                default_value="INFO",
                validator=lambda x: x.upper() in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                description="日志级别"
            ),
            ConfigValidationRule(
                key="LOG_FILE",
                required=False,
                validator=self._validate_log_path,
                description="日志文件路径"
            ),
            ConfigValidationRule(
                key="LOG_MAX_SIZE",
                required=False,
                data_type=int,
                default_value=10485760,  # 10MB
                validator=lambda x: x > 0,
                description="日志文件最大大小（字节）"
            )
        ]
        
        # 安全配置规则
        security_rules = [
            ConfigValidationRule(
                key="JWT_SECRET_KEY",
                required=True,
                validator=lambda x: len(x) >= 32,
                description="JWT密钥，至少32字符",
                sensitive=True
            ),
            ConfigValidationRule(
                key="JWT_EXPIRATION_HOURS",
                required=False,
                data_type=int,
                default_value=24,
                validator=lambda x: 1 <= x <= 168,  # 1小时到1周
                description="JWT过期时间（小时）"
            ),
            ConfigValidationRule(
                key="MAX_UPLOAD_SIZE",
                required=False,
                data_type=int,
                default_value=104857600,  # 100MB
                validator=lambda x: x > 0,
                description="最大上传文件大小（字节）"
            ),
            ConfigValidationRule(
                key="ALLOWED_EXTENSIONS",
                required=False,
                default_value="dcm,dicom",
                description="允许的文件扩展名（逗号分隔）"
            )
        ]
        
        # 注册配置段
        self.validation_rules = {
            "flask": ConfigSection("Flask配置", flask_rules, "Flask应用程序配置"),
            "orthanc": ConfigSection("Orthanc配置", orthanc_rules, "Orthanc DICOM服务器配置"),
            "ohif": ConfigSection("OHIF配置", ohif_rules, "OHIF查看器配置"),
            "yolo": ConfigSection("YOLO配置", yolo_rules, "YOLO模型配置"),
            "database": ConfigSection("数据库配置", database_rules, "数据库相关配置"),
            "logging": ConfigSection("日志配置", logging_rules, "日志系统配置"),
            "security": ConfigSection("安全配置", security_rules, "安全相关配置")
        }
    
    def _load_config(self):
        """加载配置"""
        # 首先从环境变量加载
        self._load_from_env()
        
        # 然后从配置文件加载（如果存在）
        if self.config_file and os.path.exists(self.config_file):
            self._load_from_file()
        
        # 应用默认值
        self._apply_defaults()
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        for section_name, section in self.validation_rules.items():
            section_data = {}
            
            for rule in section.rules:
                env_key = f"{self.env_prefix}_{rule.key}"
                env_value = os.getenv(env_key)
                
                if env_value is not None:
                    # 类型转换
                    try:
                        if rule.data_type == bool:
                            section_data[rule.key] = env_value.lower() in ['true', '1', 'yes', 'on']
                        elif rule.data_type == int:
                            section_data[rule.key] = int(env_value)
                        elif rule.data_type == float:
                            section_data[rule.key] = float(env_value)
                        else:
                            section_data[rule.key] = env_value
                    except ValueError as e:
                        logger.warning(f"环境变量 {env_key} 类型转换失败: {str(e)}")
            
            if section_data:
                self.config_data[section_name] = section_data
    
    def _load_from_file(self):
        """从配置文件加载"""
        try:
            file_path = Path(self.config_file)
            
            if file_path.suffix.lower() in ['.yml', '.yaml']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    file_config = yaml.safe_load(f) or {}
            elif file_path.suffix.lower() == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
            else:
                logger.warning(f"不支持的配置文件格式: {file_path.suffix}")
                return
            
            # 合并配置
            for section_name, section_data in file_config.items():
                if section_name in self.config_data:
                    self.config_data[section_name].update(section_data)
                else:
                    self.config_data[section_name] = section_data
            
            logger.info(f"从配置文件加载配置: {self.config_file}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
    
    def _apply_defaults(self):
        """应用默认值"""
        for section_name, section in self.validation_rules.items():
            if section_name not in self.config_data:
                self.config_data[section_name] = {}
            
            for rule in section.rules:
                if rule.key not in self.config_data[section_name] and rule.default_value is not None:
                    self.config_data[section_name][rule.key] = rule.default_value
    
    def validate_config(self) -> bool:
        """验证配置"""
        self.validation_errors.clear()
        is_valid = True
        
        for section_name, section in self.validation_rules.items():
            section_data = self.config_data.get(section_name, {})
            
            for rule in section.rules:
                value = section_data.get(rule.key)
                
                # 检查必需字段
                if rule.required and value is None:
                    self.validation_errors.append(
                        f"[{section_name}] 缺少必需配置: {rule.key} - {rule.description}"
                    )
                    is_valid = False
                    continue
                
                # 跳过空值的非必需字段
                if value is None:
                    continue
                
                # 类型检查
                if not isinstance(value, rule.data_type):
                    self.validation_errors.append(
                        f"[{section_name}] {rule.key} 类型错误: 期望 {rule.data_type.__name__}, 实际 {type(value).__name__}"
                    )
                    is_valid = False
                    continue
                
                # 自定义验证器
                if rule.validator and not rule.validator(value):
                    self.validation_errors.append(
                        f"[{section_name}] {rule.key} 验证失败: {rule.description}"
                    )
                    is_valid = False
        
        if not is_valid:
            logger.error("配置验证失败:")
            for error in self.validation_errors:
                logger.error(f"  - {error}")
        else:
            logger.info("配置验证通过")
        
        return is_valid
    
    def get_config(self, section: str, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.config_data.get(section, {}).get(key, default)
    
    def set_config(self, section: str, key: str, value: Any):
        """设置配置值"""
        if section not in self.config_data:
            self.config_data[section] = {}
        self.config_data[section][key] = value
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取整个配置段"""
        return self.config_data.get(section, {})
    
    def save_config(self, output_file: str, format: str = "yaml", include_sensitive: bool = False):
        """保存配置到文件"""
        config_to_save = {}
        
        for section_name, section_data in self.config_data.items():
            filtered_data = {}
            section_rules = self.validation_rules.get(section_name, ConfigSection("", []))
            
            for key, value in section_data.items():
                # 查找对应的规则
                rule = next((r for r in section_rules.rules if r.key == key), None)
                
                # 如果是敏感信息且不包含敏感信息，则跳过
                if rule and rule.sensitive and not include_sensitive:
                    filtered_data[key] = "[HIDDEN]"
                else:
                    filtered_data[key] = value
            
            if filtered_data:
                config_to_save[section_name] = filtered_data
        
        try:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            if format.lower() in ['yml', 'yaml']:
                with open(output_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config_to_save, f, default_flow_style=False, allow_unicode=True)
            elif format.lower() == 'json':
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(config_to_save, f, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"不支持的格式: {format}")
            
            logger.info(f"配置已保存到: {output_file}")
            
        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}")
    
    def generate_config_template(self, output_file: str = "config_template.yaml"):
        """生成配置模板"""
        template = {}
        
        for section_name, section in self.validation_rules.items():
            section_config = {}
            section_config['_description'] = section.description
            
            for rule in section.rules:
                key_config = {
                    'description': rule.description,
                    'required': rule.required,
                    'type': rule.data_type.__name__
                }
                
                if rule.default_value is not None:
                    key_config['default'] = rule.default_value
                
                if rule.sensitive:
                    key_config['sensitive'] = True
                
                section_config[rule.key] = key_config
            
            template[section_name] = section_config
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(template, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"配置模板已生成: {output_file}")
            
        except Exception as e:
            logger.error(f"生成配置模板失败: {str(e)}")
    
    def get_validation_report(self) -> Dict[str, Any]:
        """获取验证报告"""
        return {
            'is_valid': len(self.validation_errors) == 0,
            'errors': self.validation_errors,
            'sections': {
                section_name: {
                    'description': section.description,
                    'rules_count': len(section.rules),
                    'configured_keys': list(self.config_data.get(section_name, {}).keys())
                }
                for section_name, section in self.validation_rules.items()
            },
            'timestamp': datetime.now().isoformat()
        }
    
    # 验证器方法
    def _validate_url(self, url: str) -> bool:
        """验证URL格式"""
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+'  # domain...
            r'(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|'  # host...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        return bool(url_pattern.match(url))
    
    def _validate_ip_address(self, ip: str) -> bool:
        """验证IP地址格式"""
        if ip in ['0.0.0.0', 'localhost']:
            return True
        
        parts = ip.split('.')
        if len(parts) != 4:
            return False
        
        try:
            return all(0 <= int(part) <= 255 for part in parts)
        except ValueError:
            return False
    
    def _validate_file_path(self, path: str) -> bool:
        """验证文件路径"""
        return os.path.isfile(path)
    
    def _validate_database_path(self, path: str) -> bool:
        """验证数据库路径"""
        # 检查目录是否存在
        directory = os.path.dirname(path)
        return os.path.isdir(directory) if directory else True
    
    def _validate_log_path(self, path: str) -> bool:
        """验证日志路径"""
        # 检查目录是否存在或可创建
        directory = os.path.dirname(path)
        if directory:
            try:
                os.makedirs(directory, exist_ok=True)
                return True
            except OSError:
                return False
        return True

# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None

def get_config_manager(config_file: Optional[str] = None, 
                      env_prefix: str = "YOLO_OHIF") -> ConfigManager:
    """获取全局配置管理器"""
    global _config_manager
    if not _config_manager:
        _config_manager = ConfigManager(config_file, env_prefix)
    return _config_manager

def validate_environment() -> bool:
    """验证环境配置"""
    config_manager = get_config_manager()
    return config_manager.validate_config()

def get_config(section: str, key: str, default: Any = None) -> Any:
    """获取配置值的便捷函数"""
    config_manager = get_config_manager()
    return config_manager.get_config(section, key, default)