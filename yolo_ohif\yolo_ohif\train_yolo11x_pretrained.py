#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用预训练权重训练YOLO11x模型
基于预训练权重进行微调训练

功能:
1. 使用YOLO11x预训练权重作为起点
2. 在现有数据集上进行微调训练
3. 提供完整的训练监控和结果保存
4. 支持迁移学习和领域适应
"""

import os
import sys
import logging
import argparse
from pathlib import Path
import yaml
import shutil
from datetime import datetime
import json
import time
import psutil
from contextlib import contextmanager

# 导入YOLO相关模块
try:
    from ultralytics import YOLO
except ImportError:
    print("错误: 请安装ultralytics库")
    print("运行: pip install ultralytics>=8.3.0")
    sys.exit(1)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('yolo11x_pretrained_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class YOLO11xPretrainedTrainer:
    def __init__(self, output_root, img_size=640):
        """
        初始化YOLO11x预训练训练器
        
        Args:
            output_root: 输出目录
            img_size: 图像尺寸
        """
        self.output_root = Path(output_root)
        self.img_size = img_size
        
        # 创建输出目录
        self.output_root.mkdir(parents=True, exist_ok=True)
        
        # 设置子目录
        self.training_output = self.output_root / "training_results"
        self.logs_output = self.output_root / "logs"
        
        # 创建子目录
        for dir_path in [self.training_output, self.logs_output]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"YOLO11x预训练训练器初始化完成")
        logger.info(f"输出根目录: {self.output_root}")
        logger.info(f"图像尺寸: {self.img_size}")
    
    def _detect_device(self):
        """
        智能检测可用的训练设备
        """
        try:
            import torch
            if torch.cuda.is_available():
                device_count = torch.cuda.device_count()
                if device_count > 0:
                    # 选择显存最大的GPU
                    best_device = 0
                    max_memory = 0
                    for i in range(device_count):
                        memory = torch.cuda.get_device_properties(i).total_memory
                        if memory > max_memory:
                            max_memory = memory
                            best_device = i
                    
                    device_name = torch.cuda.get_device_name(best_device)
                    memory_gb = max_memory / (1024**3)
                    logger.info(f"检测到GPU: {device_name} ({memory_gb:.1f}GB)")
                    return str(best_device)
                else:
                    logger.warning("未检测到可用GPU，使用CPU训练")
                    return 'cpu'
            else:
                logger.warning("CUDA不可用，使用CPU训练")
                return 'cpu'
        except ImportError:
            logger.warning("PyTorch未安装，使用CPU训练")
            return 'cpu'
    
    def _check_model_exists(self, model_name):
        """
        检查模型文件是否已存在
        
        Args:
            model_name: 模型名称 (如 'yolo11n.pt', 'yolo11x.pt')
            
        Returns:
            bool: 模型是否存在
        """
        # 检查当前目录
        if os.path.exists(model_name):
            return True
            
        # 检查ultralytics缓存目录
        try:
            from ultralytics.utils import WEIGHTS_DIR
            cache_path = WEIGHTS_DIR / model_name
            if cache_path.exists():
                return True
        except:
            pass
            
        # 检查用户主目录的.ultralytics缓存
        home_cache = Path.home() / '.ultralytics' / 'weights' / model_name
        if home_cache.exists():
            return True
            
        return False
    
    def _select_model_size(self):
        """
        让用户选择YOLO模型大小
        
        Returns:
            str: 选择的模型文件名
        """
        models = {
            '1': ('yolo11n.pt', 'YOLO11n - 最小模型 (~2.6M参数, 最快训练)'),
            '2': ('yolo11s.pt', 'YOLO11s - 小型模型 (~9.4M参数, 平衡速度和精度)'),
            '3': ('yolo11m.pt', 'YOLO11m - 中型模型 (~20.1M参数, 较好精度)'),
            '4': ('yolo11l.pt', 'YOLO11l - 大型模型 (~25.3M参数, 高精度)'),
            '5': ('yolo11x.pt', 'YOLO11x - 超大模型 (~56.9M参数, 最高精度)')
        }
        
        print("\n🤖 请选择YOLO模型大小:")
        print("┌─────────────────────────────────────────────────────────────┐")
        for key, (model_file, description) in models.items():
            exists_status = "✅ 已下载" if self._check_model_exists(model_file) else "📥 需下载"
            print(f"│ {key}. {description:<45} {exists_status} │")
        print("└─────────────────────────────────────────────────────────────┘")
        
        print("\n💡 建议:")
        print("   • 医学图像检测: 推荐YOLO11m或YOLO11l (精度和速度平衡)")
        print("   • 快速原型验证: 推荐YOLO11n或YOLO11s (训练速度快)")
        print("   • 生产环境: 推荐YOLO11l或YOLO11x (最高精度)")
        print("   • GPU内存有限: 推荐YOLO11n或YOLO11s (内存占用少)")
        
        while True:
            choice = input("\n请输入选择 (1-5): ").strip()
            if choice in models:
                model_file, description = models[choice]
                print(f"\n✅ 已选择: {description}")
                
                if self._check_model_exists(model_file):
                    print(f"🎉 模型文件已存在，无需重新下载!")
                else:
                    print(f"📥 模型文件不存在，将自动下载...")
                    
                return model_file
            else:
                print("❌ 无效选择，请输入1-5之间的数字")
    
    def train_with_pretrained(self, config_path, epochs=100, batch_size=16, learning_rate=0.001, freeze_layers=10, model_size=None):
        """
        使用预训练权重训练YOLO模型
        
        Args:
            config_path: 数据集配置文件路径
            epochs: 训练轮数
            batch_size: 批次大小
            learning_rate: 学习率（预训练模型通常使用较小的学习率）
            freeze_layers: 冻结的层数（前N层保持不变）
            model_size: 指定模型大小，如果为None则让用户选择
        """
        logger.info("=== 使用预训练权重训练YOLO模型 ===")
        
        # 智能设备检测
        device = self._detect_device()
        logger.info(f"🔧 使用设备: {device}")
        
        # 选择或指定模型
        if model_size is None:
            model_file = self._select_model_size()
        else:
            model_file = model_size
            logger.info(f"📋 使用指定模型: {model_file}")
        
        # 创建YOLO模型（使用预训练权重）
        logger.info(f"📥 加载{model_file}预训练权重...")
        model = YOLO(model_file)  # 使用选择的预训练权重文件
        
        # 冻结前N层（可选）
        if freeze_layers > 0:
            logger.info(f"🧊 冻结前{freeze_layers}层参数")
            for i, (name, param) in enumerate(model.model.named_parameters()):
                if i < freeze_layers:
                    param.requires_grad = False
                    logger.debug(f"冻结层: {name}")
        
        # 训练参数配置
        train_args = {
            # 基本参数
            'data': str(config_path),
            'epochs': epochs,
            'batch': batch_size,
            'imgsz': self.img_size,
            
            # 输出设置
            'project': str(self.training_output),
            'name': f'yolo11x_pretrained_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
            'save_period': 5,  # 每5个epoch保存一次
            
            # 设备和性能
            'device': device,
            'workers': 4,
            'patience': 20,  # 早停耐心值（预训练模型通常收敛更快）
            
            # 优化器设置（预训练模型使用较小的学习率）
            'optimizer': 'AdamW',
            'lr0': learning_rate,
            'lrf': 0.1,  # 最终学习率因子
            'momentum': 0.937,
            'weight_decay': 0.0005,
            'warmup_epochs': 3,  # 较短的预热期
            'warmup_momentum': 0.8,
            'warmup_bias_lr': 0.1,
            
            # 损失函数权重
            'box': 7.5,
            'cls': 0.5,
            'dfl': 1.5,
            
            # 数据增强（预训练模型可以使用更温和的增强）
            'hsv_h': 0.01,  # 较小的色调变化
            'hsv_s': 0.5,   # 较小的饱和度变化
            'hsv_v': 0.3,   # 较小的亮度变化
            'degrees': 0.0,
            'translate': 0.05,  # 较小的平移
            'scale': 0.3,       # 较小的缩放
            'shear': 0.0,
            'perspective': 0.0,
            'flipud': 0.0,
            'fliplr': 0.5,
            'mosaic': 0.8,      # 较低的mosaic概率
            'mixup': 0.0,
            'copy_paste': 0.0,
            
            # 验证设置
            'val': True,
            'plots': True,
            'save_json': True,
            
            # 其他设置
            'verbose': True,
            'seed': 42,
            'deterministic': True,
            'single_cls': True,  # 单类检测
            'pretrained': True,  # 明确使用预训练权重
        }
        
        logger.info("开始预训练模型微调...")
        logger.info(f"训练参数: {json.dumps(train_args, indent=2, ensure_ascii=False)}")
        
        # 开始训练
        try:
            results = model.train(**train_args)
            
            # 保存训练摘要
            self.save_training_summary(results, config_path, freeze_layers)
            
            logger.info("🎉 预训练模型微调完成!")
            logger.info(f"📁 结果保存在: {self.training_output}")
            
            return results
            
        except Exception as e:
            logger.error(f"训练过程中出现错误: {e}")
            raise
    
    def save_training_summary(self, results, config_path, freeze_layers):
        """
        保存训练摘要信息
        """
        summary = {
            'training_type': 'pretrained_finetuning',
            'model': 'yolo11x',
            'config_path': str(config_path),
            'freeze_layers': freeze_layers,
            'timestamp': datetime.now().isoformat(),
            'results_path': str(self.training_output),
            'image_size': self.img_size,
            'training_results': {
                'best_fitness': float(results.best_fitness) if hasattr(results, 'best_fitness') else None,
                'best_epoch': int(results.best_epoch) if hasattr(results, 'best_epoch') else None,
            }
        }
        
        summary_path = self.logs_output / f"training_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        logger.info(f"训练摘要已保存: {summary_path}")

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='YOLO11x预训练模型微调')
    parser.add_argument('--data', type=str, required=True, help='数据集配置文件路径')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=16, help='批次大小')
    parser.add_argument('--learning-rate', type=float, default=0.001, help='学习率')
    parser.add_argument('--freeze-layers', type=int, default=10, help='冻结的层数')
    parser.add_argument('--img-size', type=int, default=640, help='图像尺寸')
    parser.add_argument('--output', type=str, default='./yolo11x_pretrained_output', help='输出目录')
    
    args = parser.parse_args()
    
    # 检查数据集配置文件
    if not os.path.exists(args.data):
        logger.error(f"数据集配置文件不存在: {args.data}")
        sys.exit(1)
    
    # 创建训练器
    trainer = YOLO11xPretrainedTrainer(
        output_root=args.output,
        img_size=args.img_size
    )
    
    # 开始训练
    try:
        trainer.train_with_pretrained(
            config_path=args.data,
            epochs=args.epochs,
            batch_size=args.batch_size,
            learning_rate=args.learning_rate,
            freeze_layers=args.freeze_layers
        )
    except KeyboardInterrupt:
        logger.info("训练被用户中断")
    except Exception as e:
        logger.error(f"训练失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()