#!/usr/bin/env python3
"""
nnUNet环境安装脚本
自动安装nnUNet及其依赖
"""

import os
import sys
import subprocess
import platform
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NnUNetInstaller:
    """nnUNet安装器"""
    
    def __init__(self):
        self.python_executable = sys.executable
        self.system = platform.system()
        logger.info(f"系统: {self.system}")
        logger.info(f"Python: {sys.version}")
        logger.info(f"Python可执行文件: {self.python_executable}")
    
    def check_python_version(self):
        """检查Python版本"""
        if sys.version_info < (3, 8):
            logger.error(f"Python版本过低: {sys.version}")
            logger.error("nnUNet需要Python 3.8或更高版本")
            return False
        
        logger.info(f"✓ Python版本检查通过: {sys.version}")
        return True
    
    def install_package(self, package, upgrade=False):
        """
        安装Python包
        
        Args:
            package: 包名
            upgrade: 是否升级
        """
        cmd = [self.python_executable, "-m", "pip", "install"]
        
        if upgrade:
            cmd.append("--upgrade")
        
        cmd.append(package)
        
        try:
            logger.info(f"安装 {package}...")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            logger.info(f"✓ {package} 安装成功")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"✗ {package} 安装失败: {e}")
            if e.stderr:
                logger.error(f"错误信息: {e.stderr}")
            return False
    
    def check_cuda(self):
        """检查CUDA环境"""
        try:
            result = subprocess.run(["nvidia-smi"], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("✓ NVIDIA GPU检测到")
                logger.info("NVIDIA-SMI输出:")
                for line in result.stdout.split('\n')[:10]:  # 只显示前10行
                    if line.strip():
                        logger.info(f"  {line}")
                return True
            else:
                logger.warning("⚠ 未检测到NVIDIA GPU")
                return False
        except FileNotFoundError:
            logger.warning("⚠ nvidia-smi未找到，可能没有安装NVIDIA驱动")
            return False
    
    def install_pytorch(self, cuda_version=None):
        """
        安装PyTorch
        
        Args:
            cuda_version: CUDA版本，如果为None则自动检测
        """
        logger.info("安装PyTorch...")
        
        # 检查CUDA
        has_cuda = self.check_cuda()
        
        if has_cuda and cuda_version is None:
            # 尝试自动检测CUDA版本
            try:
                result = subprocess.run(["nvcc", "--version"], capture_output=True, text=True)
                if result.returncode == 0:
                    # 解析CUDA版本
                    for line in result.stdout.split('\n'):
                        if 'release' in line.lower():
                            import re
                            match = re.search(r'release (\d+\.\d+)', line)
                            if match:
                                cuda_version = match.group(1)
                                logger.info(f"检测到CUDA版本: {cuda_version}")
                                break
            except FileNotFoundError:
                logger.warning("nvcc未找到，使用默认CUDA版本")
        
        # 根据CUDA版本选择PyTorch安装命令
        if has_cuda:
            if cuda_version and cuda_version.startswith("12"):
                torch_package = "torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121"
            elif cuda_version and cuda_version.startswith("11"):
                torch_package = "torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
            else:
                torch_package = "torch torchvision torchaudio"
        else:
            torch_package = "torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
        
        # 安装PyTorch
        cmd = [self.python_executable, "-m", "pip", "install"] + torch_package.split()
        
        try:
            logger.info(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            logger.info("✓ PyTorch安装成功")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"✗ PyTorch安装失败: {e}")
            return False
    
    def install_nnunet_dependencies(self):
        """安装nnUNet依赖"""
        logger.info("安装nnUNet依赖...")
        
        dependencies = [
            "numpy",
            "scipy",
            "nibabel",
            "SimpleITK",
            "batchgenerators>=0.25",
            "scikit-image",
            "scikit-learn",
            "matplotlib",
            "tqdm",
            "dicom2nifti",
            "pandas"
        ]
        
        failed_packages = []
        for package in dependencies:
            if not self.install_package(package):
                failed_packages.append(package)
        
        if failed_packages:
            logger.error(f"以下包安装失败: {failed_packages}")
            return False
        
        logger.info("✓ 所有依赖安装成功")
        return True
    
    def install_nnunet(self):
        """安装nnUNet"""
        logger.info("安装nnUNet...")
        
        # 首先尝试从PyPI安装
        if self.install_package("nnunetv2"):
            return True
        
        # 如果PyPI安装失败，尝试从GitHub安装
        logger.info("从PyPI安装失败，尝试从GitHub安装...")
        cmd = [
            self.python_executable, "-m", "pip", "install",
            "git+https://github.com/MIC-DKFZ/nnUNet.git"
        ]
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            logger.info("✓ nnUNet从GitHub安装成功")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"✗ nnUNet安装失败: {e}")
            return False
    
    def verify_installation(self):
        """验证安装"""
        logger.info("验证安装...")
        
        try:
            # 检查PyTorch
            import torch
            logger.info(f"✓ PyTorch版本: {torch.__version__}")
            
            if torch.cuda.is_available():
                logger.info(f"✓ CUDA可用，设备数量: {torch.cuda.device_count()}")
                logger.info(f"✓ CUDA版本: {torch.version.cuda}")
            else:
                logger.info("ℹ CUDA不可用，将使用CPU")
            
            # 检查nnUNet
            import nnunetv2
            logger.info("✓ nnUNetv2导入成功")
            
            # 检查nnUNet命令
            commands = [
                "nnUNetv2_plan_and_preprocess",
                "nnUNetv2_train",
                "nnUNetv2_predict"
            ]
            
            for cmd in commands:
                try:
                    result = subprocess.run([cmd, "--help"], capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        logger.info(f"✓ {cmd} 命令可用")
                    else:
                        logger.warning(f"⚠ {cmd} 命令可能有问题")
                except (subprocess.TimeoutExpired, FileNotFoundError):
                    logger.warning(f"⚠ {cmd} 命令不可用")
            
            logger.info("✓ 安装验证完成")
            return True
            
        except ImportError as e:
            logger.error(f"✗ 导入失败: {e}")
            return False
    
    def install_all(self):
        """执行完整安装流程"""
        logger.info("开始nnUNet完整安装流程...")
        
        # 检查Python版本
        if not self.check_python_version():
            return False
        
        # 升级pip
        logger.info("升级pip...")
        self.install_package("pip", upgrade=True)
        
        # 安装PyTorch
        if not self.install_pytorch():
            logger.error("PyTorch安装失败，终止安装")
            return False
        
        # 安装依赖
        if not self.install_nnunet_dependencies():
            logger.error("依赖安装失败，终止安装")
            return False
        
        # 安装nnUNet
        if not self.install_nnunet():
            logger.error("nnUNet安装失败，终止安装")
            return False
        
        # 验证安装
        if not self.verify_installation():
            logger.error("安装验证失败")
            return False
        
        logger.info("🎉 nnUNet安装完成！")
        logger.info("现在可以使用nnUNet进行医学图像分割训练了")
        
        return True

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="nnUNet环境安装脚本")
    parser.add_argument("--cuda-version", help="指定CUDA版本 (例如: 11.8, 12.1)")
    parser.add_argument("--verify-only", action="store_true", help="仅验证现有安装")
    
    args = parser.parse_args()
    
    installer = NnUNetInstaller()
    
    if args.verify_only:
        success = installer.verify_installation()
    else:
        success = installer.install_all()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
