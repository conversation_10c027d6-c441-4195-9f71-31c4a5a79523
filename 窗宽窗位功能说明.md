# 窗宽窗位功能实现说明

## 功能概述

为影像处理界面和智能标注界面添加了完整的窗宽窗位调整功能，支持自定义窗宽窗位设置和医学影像标准预设窗口。

## 实现内容

### 1. 新增WindowLevelControl用户控件

**文件位置**: `src/MedicalImageAnalysis.Wpf/Controls/WindowLevelControl.xaml`

**功能特性**:
- ✅ 自定义窗宽窗位输入框
- ✅ 实时窗宽窗位调整
- ✅ 四种医学影像标准预设窗口
- ✅ 重置到原始值功能
- ✅ 当前设置显示

**预设窗口**:
- **肺窗**: 窗宽 1500, 窗位 -600 HU
- **软组织窗**: 窗宽 400, 窗位 40 HU  
- **骨窗**: 窗宽 2000, 窗位 400 HU
- **脑窗**: 窗宽 80, 窗位 40 HU

### 2. 影像处理界面集成

**修改文件**: `src/MedicalImageAnalysis.Wpf/Views/ImageProcessingView.xaml`

**集成特性**:
- ✅ 仅对DICOM文件显示窗宽窗位控制
- ✅ 自动获取DICOM文件的原始窗宽窗位值
- ✅ 实时应用窗宽窗位调整到图像显示
- ✅ 与现有图像处理功能完美集成

### 3. 智能标注界面集成

**修改文件**: `src/MedicalImageAnalysis.Wpf/Views/AnnotationView.xaml`

**集成特性**:
- ✅ 仅对DICOM文件显示窗宽窗位控制
- ✅ 标注过程中可实时调整窗宽窗位
- ✅ 窗宽窗位调整不影响已有标注
- ✅ 与AI辅助标注功能兼容

## 技术实现

### 1. 事件驱动架构

```csharp
// 窗宽窗位变化事件
public event EventHandler<WindowLevelChangedEventArgs>? WindowLevelChanged;

// 事件参数
public class WindowLevelChangedEventArgs : EventArgs
{
    public double WindowWidth { get; }
    public double WindowCenter { get; }
}
```

### 2. 异步图像处理

```csharp
private async void OnWindowLevelChanged(object? sender, WindowLevelChangedEventArgs e)
{
    // 使用GDCM图像处理器应用窗宽窗位
    var adjustedImage = await _gdcmImageProcessor.ApplyWindowLevelAsync(
        _currentDicomFilePath, e.WindowCenter, e.WindowWidth);
    
    if (adjustedImage != null)
    {
        _currentImage = adjustedImage;
        MainImage.Source = _currentImage;
    }
}
```

### 3. 智能显示控制

```csharp
// 仅对DICOM文件显示窗宽窗位控制
if (fileInfo.Extension.ToLower() == ".dcm")
{
    _isDicomImage = true;
    WindowLevelControl.Visibility = Visibility.Visible;
    WindowLevelControl.SetOriginalValues(dicomInstance.WindowWidth, dicomInstance.WindowCenter);
}
else
{
    _isDicomImage = false;
    WindowLevelControl.Visibility = Visibility.Collapsed;
}
```

## 使用方法

### 1. 影像处理界面

1. **打开DICOM文件**:
   - 点击"打开图像"按钮
   - 选择.dcm文件
   - 窗宽窗位控制面板自动显示

2. **调整窗宽窗位**:
   - **自定义调整**: 在窗宽/窗位输入框中输入数值
   - **预设窗口**: 点击肺窗/软组织窗/骨窗/脑窗按钮
   - **重置**: 点击"重置到原始值"按钮

3. **实时预览**: 窗宽窗位调整立即应用到图像显示

### 2. 智能标注界面

1. **打开DICOM文件**:
   - 点击"打开图像"按钮
   - 选择.dcm文件
   - 窗宽窗位控制面板自动显示

2. **标注过程中调整**:
   - 在标注过程中可随时调整窗宽窗位
   - 窗宽窗位调整不会影响已绘制的标注
   - 可根据不同组织类型选择合适的预设窗口

## 医学影像标准

### 窗宽窗位的医学意义

**窗宽 (Window Width)**:
- 控制图像对比度
- 数值越大，对比度越低
- 数值越小，对比度越高

**窗位 (Window Center)**:
- 控制图像亮度
- 对应感兴趣组织的CT值中心
- 调整可突出不同密度的组织

### 预设窗口的临床应用

1. **肺窗 (1500/-600)**:
   - 观察肺实质、肺结节
   - 检测肺炎、肺气肿
   - 评估肺血管结构

2. **软组织窗 (400/40)**:
   - 观察腹部器官
   - 检测软组织病变
   - 评估肌肉、脂肪组织

3. **骨窗 (2000/400)**:
   - 观察骨骼结构
   - 检测骨折、骨病变
   - 评估骨密度变化

4. **脑窗 (80/40)**:
   - 观察脑组织
   - 检测脑出血、梗塞
   - 评估脑室系统

## 技术优势

### 1. 性能优化
- ✅ 异步处理，不阻塞UI线程
- ✅ 实时响应，用户体验流畅
- ✅ 内存高效，避免重复加载

### 2. 用户体验
- ✅ 直观的预设按钮，一键切换
- ✅ 实时数值显示，精确控制
- ✅ 智能显示控制，仅对DICOM文件可用

### 3. 医学专业性
- ✅ 符合医学影像标准
- ✅ 预设值基于临床实践
- ✅ 支持精确的HU值调整

## 测试建议

### 1. 功能测试
- 测试不同类型的DICOM文件
- 验证预设窗口的效果
- 检查自定义窗宽窗位的精度

### 2. 性能测试
- 测试大尺寸DICOM文件的响应速度
- 验证连续调整时的性能表现
- 检查内存使用情况

### 3. 用户体验测试
- 测试界面的直观性
- 验证操作的便捷性
- 检查错误处理的友好性

## 后续扩展

### 1. 高级功能
- 窗宽窗位预设的自定义保存
- 基于检查部位的智能预设推荐
- 窗宽窗位调整的历史记录

### 2. AI集成
- 基于图像内容的自动窗宽窗位优化
- 病变区域的自适应窗宽窗位调整
- 多窗口同步显示功能

这个实现为医学影像处理系统提供了专业级的窗宽窗位调整功能，大大提升了医学影像的观察和分析能力。
