using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.Logging;

namespace MedicalImageAnalysis.Wpf
{
    /// <summary>
    /// HelpWindow.xaml 的交互逻辑
    /// </summary>
    public partial class HelpWindow : Window
    {
        private readonly ILogger<HelpWindow> _logger;

        public HelpWindow()
        {
            InitializeComponent();
            _logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<HelpWindow>.Instance;
            
            // 默认选择第一项
            HelpNavigationListBox.SelectedIndex = 0;
        }

        /// <summary>
        /// 帮助导航选择变化事件
        /// </summary>
        private void HelpNavigationListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (HelpNavigationListBox.SelectedItem == null) return;

            var selectedItem = HelpNavigationListBox.SelectedItem as ListBoxItem;
            var itemName = selectedItem?.Name;

            try
            {
                switch (itemName)
                {
                    case "GettingStartedItem":
                        ShowGettingStartedContent();
                        break;
                    case "DicomUploadItem":
                        ShowDicomUploadContent();
                        break;
                    case "ImageProcessingItem":
                        ShowImageProcessingContent();
                        break;
                    case "AnnotationItem":
                        ShowAnnotationContent();
                        break;
                    case "ModelTrainingItem":
                        ShowModelTrainingContent();
                        break;
                    case "TroubleshootingItem":
                        ShowTroubleshootingContent();
                        break;
                    case "AboutItem":
                        ShowAboutContent();
                        break;
                    default:
                        ShowGettingStartedContent();
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示帮助内容时发生错误: {ItemName}", itemName);
            }
        }

        /// <summary>
        /// 显示快速开始内容
        /// </summary>
        private void ShowGettingStartedContent()
        {
            // 内容已在XAML中定义，这里不需要额外操作
        }

        /// <summary>
        /// 显示DICOM上传帮助内容
        /// </summary>
        private void ShowDicomUploadContent()
        {
            HelpContentPanel.Children.Clear();
            
            var title = new TextBlock
            {
                Text = "DICOM文件上传",
                FontSize = 24,
                FontWeight = FontWeights.Medium,
                Margin = new Thickness(0, 0, 0, 16)
            };
            HelpContentPanel.Children.Add(title);

            var content = new TextBlock
            {
                Text = "DICOM上传功能允许您将医学影像文件导入系统进行处理和分析。\n\n" +
                       "支持的操作：\n" +
                       "• 拖拽文件到上传区域\n" +
                       "• 点击选择文件按钮\n" +
                       "• 批量上传多个文件\n" +
                       "• 文件格式验证\n" +
                       "• 元数据提取\n\n" +
                       "支持的文件格式：\n" +
                       "• .dcm (DICOM标准格式)\n" +
                       "• .dicom (DICOM扩展格式)\n\n" +
                       "注意事项：\n" +
                       "• 单个文件最大500MB\n" +
                       "• 确保文件完整性\n" +
                       "• 检查文件权限",
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 16)
            };
            HelpContentPanel.Children.Add(content);
        }

        /// <summary>
        /// 显示图像处理帮助内容
        /// </summary>
        private void ShowImageProcessingContent()
        {
            HelpContentPanel.Children.Clear();
            
            var title = new TextBlock
            {
                Text = "图像处理",
                FontSize = 24,
                FontWeight = FontWeights.Medium,
                Margin = new Thickness(0, 0, 0, 16)
            };
            HelpContentPanel.Children.Add(title);

            var content = new TextBlock
            {
                Text = "图像处理模块提供多种医学影像增强和预处理功能。\n\n" +
                       "主要功能：\n" +
                       "• 亮度和对比度调整\n" +
                       "• 图像滤波和降噪\n" +
                       "• 直方图均衡化\n" +
                       "• 边缘检测和增强\n" +
                       "• 图像分割预处理\n\n" +
                       "使用步骤：\n" +
                       "1. 选择要处理的图像\n" +
                       "2. 选择处理算法\n" +
                       "3. 调整参数\n" +
                       "4. 预览效果\n" +
                       "5. 应用处理并保存",
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 16)
            };
            HelpContentPanel.Children.Add(content);
        }

        /// <summary>
        /// 显示智能标注帮助内容
        /// </summary>
        private void ShowAnnotationContent()
        {
            HelpContentPanel.Children.Clear();
            
            var title = new TextBlock
            {
                Text = "智能标注",
                FontSize = 24,
                FontWeight = FontWeights.Medium,
                Margin = new Thickness(0, 0, 0, 16)
            };
            HelpContentPanel.Children.Add(title);

            var content = new TextBlock
            {
                Text = "智能标注功能结合AI技术，帮助快速准确地标注医学影像。\n\n" +
                       "标注工具：\n" +
                       "• 矩形框标注\n" +
                       "• 多边形标注\n" +
                       "• 点标注\n" +
                       "• 线段标注\n" +
                       "• 自由绘制\n\n" +
                       "AI辅助功能：\n" +
                       "• 自动检测和预标注\n" +
                       "• 智能边缘吸附\n" +
                       "• 相似区域批量标注\n" +
                       "• 标注质量检查\n\n" +
                       "标注管理：\n" +
                       "• 标注分类管理\n" +
                       "• 导出标注数据\n" +
                       "• 标注统计分析",
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 16)
            };
            HelpContentPanel.Children.Add(content);
        }

        /// <summary>
        /// 显示模型训练帮助内容
        /// </summary>
        private void ShowModelTrainingContent()
        {
            HelpContentPanel.Children.Clear();
            
            var title = new TextBlock
            {
                Text = "模型训练",
                FontSize = 24,
                FontWeight = FontWeights.Medium,
                Margin = new Thickness(0, 0, 0, 16)
            };
            HelpContentPanel.Children.Add(title);

            var content = new TextBlock
            {
                Text = "模型训练模块支持深度学习模型的训练和优化。\n\n" +
                       "支持的模型：\n" +
                       "• YOLO目标检测\n" +
                       "• U-Net图像分割\n" +
                       "• ResNet分类网络\n" +
                       "• 自定义网络架构\n\n" +
                       "训练流程：\n" +
                       "1. 准备训练数据集\n" +
                       "2. 配置训练参数\n" +
                       "3. 选择预训练模型\n" +
                       "4. 开始训练\n" +
                       "5. 监控训练进度\n" +
                       "6. 模型评估和验证\n\n" +
                       "注意事项：\n" +
                       "• 确保数据集质量\n" +
                       "• 合理设置学习率\n" +
                       "• 监控过拟合现象",
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 16)
            };
            HelpContentPanel.Children.Add(content);
        }

        /// <summary>
        /// 显示故障排除内容
        /// </summary>
        private void ShowTroubleshootingContent()
        {
            HelpContentPanel.Children.Clear();
            
            var title = new TextBlock
            {
                Text = "故障排除",
                FontSize = 24,
                FontWeight = FontWeights.Medium,
                Margin = new Thickness(0, 0, 0, 16)
            };
            HelpContentPanel.Children.Add(title);

            var content = new TextBlock
            {
                Text = "常见问题及解决方案：\n\n" +
                       "1. 无法连接API服务器\n" +
                       "   • 检查网络连接\n" +
                       "   • 确认服务器地址正确\n" +
                       "   • 检查防火墙设置\n\n" +
                       "2. DICOM文件上传失败\n" +
                       "   • 检查文件格式\n" +
                       "   • 确认文件完整性\n" +
                       "   • 检查文件大小限制\n\n" +
                       "3. 处理速度慢\n" +
                       "   • 检查硬件配置\n" +
                       "   • 调整并发处理数\n" +
                       "   • 清理临时文件\n\n" +
                       "4. 内存不足\n" +
                       "   • 减少批处理大小\n" +
                       "   • 关闭其他应用程序\n" +
                       "   • 增加虚拟内存\n\n" +
                       "如果问题仍然存在，请查看日志文件或联系技术支持。",
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 16)
            };
            HelpContentPanel.Children.Add(content);
        }

        /// <summary>
        /// 显示关于内容
        /// </summary>
        private void ShowAboutContent()
        {
            HelpContentPanel.Children.Clear();
            
            var title = new TextBlock
            {
                Text = "关于系统",
                FontSize = 24,
                FontWeight = FontWeights.Medium,
                Margin = new Thickness(0, 0, 0, 16)
            };
            HelpContentPanel.Children.Add(title);

            var content = new TextBlock
            {
                Text = "医学影像解析系统 v1.0.0\n\n" +
                       "系统信息：\n" +
                       "• 基于 .NET 8.0 开发\n" +
                       "• 使用 WPF 技术栈\n" +
                       "• 集成 Material Design 界面\n" +
                       "• 支持 CUDA 加速\n\n" +
                       "主要技术：\n" +
                       "• YOLO 目标检测\n" +
                       "• PyTorch 深度学习框架\n" +
                       "• DICOM 医学影像标准\n" +
                       "• RESTful API 架构\n\n" +
                       "开发团队：\n" +
                       "• 医学影像AI研发团队\n\n" +
                       "版权信息：\n" +
                       "• © 2025 医学影像解析系统\n" +
                       "• 保留所有权利\n\n" +
                       "技术支持：\n" +
                       "• 邮箱：<EMAIL>\n" +
                       "• 电话：400-123-4567",
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 16)
            };
            HelpContentPanel.Children.Add(content);
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
