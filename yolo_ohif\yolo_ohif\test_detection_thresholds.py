#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测阈值测试脚本
用于测试不同置信度阈值下的检测效果
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.detection_service import DetectionService
from config import Config

def test_different_thresholds():
    """测试不同置信度阈值的检测效果"""
    print("="*60)
    print("检测阈值测试")
    print("="*60)
    
    # 测试数据路径
    test_data_dir = "E:/Trae/yolo_ohif/results/abnormal"
    
    if not os.path.exists(test_data_dir):
        print(f"❌ 测试数据目录不存在: {test_data_dir}")
        return
    
    # 获取第一个病人的第一个DICOM文件进行测试
    test_file = None
    for patient_dir in os.listdir(test_data_dir):
        patient_path = os.path.join(test_data_dir, patient_dir)
        if os.path.isdir(patient_path):
            for sequence_dir in os.listdir(patient_path):
                sequence_path = os.path.join(patient_path, sequence_dir)
                if os.path.isdir(sequence_path):
                    for file in os.listdir(sequence_path):
                        if file.endswith('.dcm'):
                            test_file = os.path.join(sequence_path, file)
                            break
                    if test_file:
                        break
            if test_file:
                break
    
    if not test_file:
        print("❌ 未找到测试DICOM文件")
        return
    
    print(f"测试文件: {test_file}")
    
    # 测试不同的置信度阈值
    thresholds = [0.01, 0.05, 0.1, 0.2, 0.3, 0.5]
    
    for threshold in thresholds:
        print(f"\n{'='*40}")
        print(f"测试置信度阈值: {threshold}")
        print(f"{'='*40}")
        
        try:
            # 创建检测服务实例
            model_path = os.path.join(Config.YOLO.MODEL_PATH, Config.YOLO.DEFAULT_MODEL)
            detection_service = DetectionService(
                model_path=model_path,
                confidence_threshold=threshold,
                iou_threshold=Config.YOLO.IOU_THRESHOLD,
                device=Config.YOLO.DEVICE
            )
            
            # 执行检测
            results = detection_service.detect_dicom([test_file])
            
            if results and len(results) > 0:
                result = results[0]
                if 'detections' in result and result['detections']:
                    detections = result['detections']
                    print(f"✅ 检测到 {len(detections)} 个目标:")
                    
                    for i, detection in enumerate(detections):
                        confidence = detection.get('confidence', 0)
                        class_name = detection.get('class', 'unknown')
                        x = detection.get('x', 0)
                        y = detection.get('y', 0)
                        width = detection.get('width', 0)
                        height = detection.get('height', 0)
                        
                        print(f"  目标 {i+1}:")
                        print(f"    类别: {class_name}")
                        print(f"    置信度: {confidence:.4f}")
                        print(f"    位置: ({x:.1f}, {y:.1f})")
                        print(f"    大小: {width:.1f} x {height:.1f}")
                else:
                    print(f"❌ 未检测到目标")
            else:
                print(f"❌ 检测失败或无结果")
                
        except Exception as e:
            print(f"❌ 检测过程出错: {str(e)}")
    
    print(f"\n{'='*60}")
    print("测试完成")
    print(f"{'='*60}")
    
    print("\n建议:")
    print("1. 如果所有阈值都无法检测到目标，可能是模型训练问题")
    print("2. 如果只有极低阈值才能检测到，说明模型置信度普遍较低")
    print("3. 如果检测到的类别显示为'unknown'，说明类别映射有问题")
    print("4. 建议检查模型是否使用正确的训练数据")

def test_with_pretrained_model():
    """使用预训练模型进行对比测试"""
    print(f"\n{'='*60}")
    print("预训练模型对比测试")
    print(f"{'='*60}")
    
    try:
        from ultralytics import YOLO
        
        # 使用YOLOv8预训练模型
        print("\n正在下载并测试YOLOv8预训练模型...")
        pretrained_model = YOLO('yolov8n.pt')  # 下载预训练模型
        
        # 测试数据路径
        test_data_dir = "E:/Trae/yolo_ohif/results/abnormal"
        
        # 获取测试文件
        test_file = None
        for patient_dir in os.listdir(test_data_dir):
            patient_path = os.path.join(test_data_dir, patient_dir)
            if os.path.isdir(patient_path):
                for sequence_dir in os.listdir(patient_path):
                    sequence_path = os.path.join(patient_path, sequence_dir)
                    if os.path.isdir(sequence_path):
                        for file in os.listdir(sequence_path):
                            if file.endswith('.dcm'):
                                test_file = os.path.join(sequence_path, file)
                                break
                        if test_file:
                            break
                if test_file:
                    break
        
        if test_file:
            # 读取DICOM文件并转换为图像
            import pydicom
            import numpy as np
            from PIL import Image
            
            dicom_data = pydicom.dcmread(test_file)
            img_array = dicom_data.pixel_array
            
            # 归一化到0-255
            img_array = ((img_array - img_array.min()) / (img_array.max() - img_array.min()) * 255).astype(np.uint8)
            
            # 转换为RGB
            if len(img_array.shape) == 2:
                img_array = np.stack([img_array] * 3, axis=-1)
            
            # 使用预训练模型检测
            results = pretrained_model(img_array, conf=0.1)
            
            if results and len(results) > 0:
                result = results[0]
                if result.boxes is not None and len(result.boxes) > 0:
                    print(f"✅ 预训练模型检测到 {len(result.boxes)} 个目标")
                    for i, box in enumerate(result.boxes):
                        conf = box.conf.item()
                        cls = int(box.cls.item())
                        class_name = pretrained_model.names.get(cls, f'class_{cls}')
                        print(f"  目标 {i+1}: {class_name} (置信度: {conf:.3f})")
                else:
                    print("❌ 预训练模型也未检测到目标")
            else:
                print("❌ 预训练模型检测失败")
        
        print("\n说明: 预训练模型主要用于验证检测流程是否正常")
        print("如果预训练模型能检测到通用目标，说明检测流程正常")
        print("如果预训练模型也无法检测，可能是图像预处理问题")
        
    except Exception as e:
        print(f"❌ 预训练模型测试失败: {str(e)}")

if __name__ == "__main__":
    test_different_thresholds()
    test_with_pretrained_model()