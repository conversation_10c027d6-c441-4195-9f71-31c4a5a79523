#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型性能基准测试脚本
用于测试模型的推理速度、内存使用和准确性基准
"""

import os
import sys
import time
import psutil
import numpy as np
import cv2
from pathlib import Path
from typing import List, Dict, Tuple
import json
from datetime import datetime
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from services.detection_service import DetectionService
from config import Config

# 配置参数
MODEL_PATH = r"E:\Trae\yolo_ohif\models\weights\best.pt"
TEST_IMAGE_DIR = r"E:\Trae\yolo_ohif\yolo11x_training_output\yolo_dataset\yolo_dataset\images\test"
OUTPUT_DIR = "benchmark_results"  # 基准测试结果输出目录

class ModelBenchmark:
    """
    模型基准测试器
    """
    
    def __init__(self, model_path: str = None, test_image_dir: str = None):
        """
        初始化基准测试器
        
        Args:
            model_path: YOLO模型路径
            test_image_dir: 测试图像目录
        """
        self.model_path = model_path or MODEL_PATH
        self.test_image_dir = test_image_dir or TEST_IMAGE_DIR
        self.results = {
            'model_path': self.model_path,
            'test_time': datetime.now().isoformat(),
            'system_info': self._get_system_info(),
            'benchmarks': {}
        }
        
        print(f"模型基准测试器初始化")
        print(f"模型路径: {self.model_path}")
        print(f"系统信息: {self.results['system_info']}")
    
    def _get_system_info(self) -> Dict:
        """
        获取系统信息
        """
        return {
            'cpu_count': psutil.cpu_count(),
            'memory_total_gb': round(psutil.virtual_memory().total / (1024**3), 2),
            'python_version': sys.version.split()[0],
            'platform': sys.platform
        }
    
    def _get_memory_usage(self) -> float:
        """
        获取当前进程内存使用量 (MB)
        """
        process = psutil.Process(os.getpid())
        return process.memory_info().rss / (1024 * 1024)
    
    def benchmark_model_loading(self, num_trials: int = 3) -> Dict:
        """
        基准测试模型加载时间
        
        Args:
            num_trials: 测试次数
            
        Returns:
            加载时间统计
        """
        print("\n=== 模型加载速度测试 ===")
        
        loading_times = []
        
        for i in range(num_trials):
            print(f"第 {i+1}/{num_trials} 次加载测试...")
            
            start_time = time.time()
            start_memory = self._get_memory_usage()
            
            # 创建检测服务（会加载模型）
            detection_service = DetectionService(
                model_path=self.model_path,
                confidence_threshold=Config.YOLO.CONFIDENCE_THRESHOLD,
                iou_threshold=Config.YOLO.IOU_THRESHOLD
            )
            
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            loading_time = end_time - start_time
            memory_increase = end_memory - start_memory
            
            loading_times.append(loading_time)
            
            print(f"  加载时间: {loading_time:.3f}s, 内存增加: {memory_increase:.1f}MB")
            
            # 清理
            del detection_service
        
        stats = {
            'mean_loading_time': np.mean(loading_times),
            'std_loading_time': np.std(loading_times),
            'min_loading_time': np.min(loading_times),
            'max_loading_time': np.max(loading_times),
            'all_times': loading_times
        }
        
        print(f"\n加载时间统计:")
        print(f"  平均: {stats['mean_loading_time']:.3f}s")
        print(f"  标准差: {stats['std_loading_time']:.3f}s")
        print(f"  最小: {stats['min_loading_time']:.3f}s")
        print(f"  最大: {stats['max_loading_time']:.3f}s")
        
        return stats
    
    def benchmark_inference_speed(self, image_sizes: List[Tuple[int, int]] = None, 
                                 num_trials: int = 10) -> Dict:
        """
        基准测试推理速度
        
        Args:
            image_sizes: 测试图像尺寸列表 [(width, height), ...]
            num_trials: 每个尺寸的测试次数
            
        Returns:
            推理速度统计
        """
        if image_sizes is None:
            image_sizes = [(640, 640), (512, 512), (416, 416), (320, 320)]
        
        print("\n=== 推理速度测试 ===")
        
        # 初始化检测服务
        detection_service = DetectionService(
            model_path=self.model_path,
            confidence_threshold=Config.YOLO.CONFIDENCE_THRESHOLD,
            iou_threshold=Config.YOLO.IOU_THRESHOLD
        )
        
        results = {}
        
        for width, height in image_sizes:
            print(f"\n测试图像尺寸: {width}x{height}")
            
            # 创建随机测试图像
            test_image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
            
            inference_times = []
            
            # 预热
            for _ in range(3):
                _ = detection_service._detect_image(test_image)
            
            # 正式测试
            for i in range(num_trials):
                start_time = time.time()
                detections = detection_service._detect_image(test_image)
                end_time = time.time()
                
                inference_time = end_time - start_time
                inference_times.append(inference_time)
                
                if (i + 1) % 5 == 0:
                    print(f"  完成 {i+1}/{num_trials} 次测试")
            
            # 计算统计信息
            stats = {
                'mean_time': np.mean(inference_times),
                'std_time': np.std(inference_times),
                'min_time': np.min(inference_times),
                'max_time': np.max(inference_times),
                'fps': 1.0 / np.mean(inference_times),
                'all_times': inference_times
            }
            
            results[f"{width}x{height}"] = stats
            
            print(f"  平均推理时间: {stats['mean_time']:.3f}s")
            print(f"  FPS: {stats['fps']:.1f}")
            print(f"  时间范围: {stats['min_time']:.3f}s - {stats['max_time']:.3f}s")
        
        return results
    
    def benchmark_memory_usage(self, image_sizes: List[Tuple[int, int]] = None) -> Dict:
        """
        基准测试内存使用
        
        Args:
            image_sizes: 测试图像尺寸列表
            
        Returns:
            内存使用统计
        """
        if image_sizes is None:
            image_sizes = [(640, 640), (512, 512), (416, 416), (320, 320)]
        
        print("\n=== 内存使用测试 ===")
        
        # 基线内存使用
        baseline_memory = self._get_memory_usage()
        print(f"基线内存使用: {baseline_memory:.1f}MB")
        
        # 初始化检测服务
        detection_service = DetectionService(
            model_path=self.model_path,
            confidence_threshold=Config.YOLO.CONFIDENCE_THRESHOLD,
            iou_threshold=Config.YOLO.IOU_THRESHOLD
        )
        
        model_loaded_memory = self._get_memory_usage()
        model_memory = model_loaded_memory - baseline_memory
        print(f"模型加载后内存: {model_loaded_memory:.1f}MB (增加 {model_memory:.1f}MB)")
        
        results = {
            'baseline_memory': baseline_memory,
            'model_memory': model_memory,
            'inference_memory': {}
        }
        
        for width, height in image_sizes:
            print(f"\n测试图像尺寸: {width}x{height}")
            
            # 创建测试图像
            test_image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
            
            # 测试推理内存使用
            pre_inference_memory = self._get_memory_usage()
            
            # 进行多次推理以观察内存变化
            for _ in range(5):
                _ = detection_service._detect_image(test_image)
            
            post_inference_memory = self._get_memory_usage()
            inference_memory_increase = post_inference_memory - pre_inference_memory
            
            results['inference_memory'][f"{width}x{height}"] = {
                'pre_inference': pre_inference_memory,
                'post_inference': post_inference_memory,
                'increase': inference_memory_increase
            }
            
            print(f"  推理前内存: {pre_inference_memory:.1f}MB")
            print(f"  推理后内存: {post_inference_memory:.1f}MB")
            print(f"  内存增加: {inference_memory_increase:.1f}MB")
        
        return results
    
    def benchmark_accuracy_vs_speed(self, test_images_dir: str = None,
                                   confidence_thresholds: List[float] = None) -> Dict:
        """
        基准测试准确性与速度的权衡
        
        Args:
            test_images_dir: 测试图像目录
            confidence_thresholds: 置信度阈值列表
            
        Returns:
            准确性与速度统计
        """
        if confidence_thresholds is None:
            confidence_thresholds = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        print("\n=== 准确性与速度权衡测试 ===")
        
        # 使用默认测试图像目录如果未指定
        if test_images_dir is None:
            test_images_dir = self.test_image_dir
        
        # 获取测试图像
        if not os.path.exists(test_images_dir):
            print(f"警告: 测试图像目录不存在: {test_images_dir}")
            return {}
        
        image_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
            image_files.extend(Path(test_images_dir).glob(ext))
        
        if not image_files:
            print(f"错误: 在目录 {test_images_dir} 中没有找到图像文件")
            return {}
        
        # 限制测试图像数量
        image_files = image_files[:10]
        print(f"使用 {len(image_files)} 张图像进行测试")
        
        results = {}
        
        for conf_thresh in confidence_thresholds:
            print(f"\n测试置信度阈值: {conf_thresh}")
            
            # 初始化检测服务
            detection_service = DetectionService(
                model_path=self.model_path,
                confidence_threshold=conf_thresh,
                iou_threshold=Config.YOLO.IOU_THRESHOLD
            )
            
            total_detections = 0
            total_time = 0
            detection_details = []
            
            for img_path in image_files:
                # 读取图像
                image = cv2.imread(str(img_path))
                if image is None:
                    continue
                
                # 测试推理时间
                start_time = time.time()
                detections = detection_service._detect_image(image)
                end_time = time.time()
                
                inference_time = end_time - start_time
                total_time += inference_time
                total_detections += len(detections)
                
                detection_details.append({
                    'image': img_path.name,
                    'detections': len(detections),
                    'time': inference_time
                })
            
            avg_time = total_time / len(image_files)
            avg_detections = total_detections / len(image_files)
            
            results[conf_thresh] = {
                'avg_inference_time': avg_time,
                'avg_detections_per_image': avg_detections,
                'total_detections': total_detections,
                'fps': 1.0 / avg_time,
                'detection_details': detection_details
            }
            
            print(f"  平均推理时间: {avg_time:.3f}s")
            print(f"  平均检测数量: {avg_detections:.1f}")
            print(f"  FPS: {1.0/avg_time:.1f}")
        
        return results
    
    def plot_benchmark_results(self):
        """
        绘制基准测试结果图表
        """
        if 'inference_speed' not in self.results['benchmarks']:
            print("错误: 没有推理速度测试结果")
            return
        
        # 推理速度图表
        speed_results = self.results['benchmarks']['inference_speed']
        
        sizes = list(speed_results.keys())
        mean_times = [speed_results[size]['mean_time'] for size in sizes]
        fps_values = [speed_results[size]['fps'] for size in sizes]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 推理时间图
        ax1.bar(sizes, mean_times, color='skyblue')
        ax1.set_title('推理时间 vs 图像尺寸')
        ax1.set_xlabel('图像尺寸')
        ax1.set_ylabel('推理时间 (秒)')
        ax1.tick_params(axis='x', rotation=45)
        
        # 在柱状图上添加数值标签
        for i, v in enumerate(mean_times):
            ax1.text(i, v + 0.001, f'{v:.3f}s', ha='center', va='bottom')
        
        # FPS图
        ax2.bar(sizes, fps_values, color='lightcoral')
        ax2.set_title('FPS vs 图像尺寸')
        ax2.set_xlabel('图像尺寸')
        ax2.set_ylabel('FPS')
        ax2.tick_params(axis='x', rotation=45)
        
        # 在柱状图上添加数值标签
        for i, v in enumerate(fps_values):
            ax2.text(i, v + 1, f'{v:.1f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('benchmark_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 准确性与速度权衡图表
        if 'accuracy_vs_speed' in self.results['benchmarks']:
            self._plot_accuracy_speed_tradeoff()
    
    def _plot_accuracy_speed_tradeoff(self):
        """
        绘制准确性与速度权衡图表
        """
        acc_speed_results = self.results['benchmarks']['accuracy_vs_speed']
        
        thresholds = list(acc_speed_results.keys())
        avg_times = [acc_speed_results[t]['avg_inference_time'] for t in thresholds]
        avg_detections = [acc_speed_results[t]['avg_detections_per_image'] for t in thresholds]
        fps_values = [acc_speed_results[t]['fps'] for t in thresholds]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 检测数量 vs 置信度阈值
        ax1.plot(thresholds, avg_detections, 'bo-', linewidth=2, markersize=8)
        ax1.set_title('平均检测数量 vs 置信度阈值')
        ax1.set_xlabel('置信度阈值')
        ax1.set_ylabel('平均检测数量')
        ax1.grid(True, alpha=0.3)
        
        # FPS vs 置信度阈值
        ax2.plot(thresholds, fps_values, 'ro-', linewidth=2, markersize=8)
        ax2.set_title('FPS vs 置信度阈值')
        ax2.set_xlabel('置信度阈值')
        ax2.set_ylabel('FPS')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('accuracy_speed_tradeoff.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def run_full_benchmark(self, save_results: bool = True) -> Dict:
        """
        运行完整的基准测试
        
        Args:
            save_results: 是否保存结果到文件
            
        Returns:
            完整的基准测试结果
        """
        print("开始完整基准测试...")
        
        # 模型加载测试
        self.results['benchmarks']['model_loading'] = self.benchmark_model_loading()
        
        # 推理速度测试
        self.results['benchmarks']['inference_speed'] = self.benchmark_inference_speed()
        
        # 内存使用测试
        self.results['benchmarks']['memory_usage'] = self.benchmark_memory_usage()
        
        # 准确性与速度权衡测试
        self.results['benchmarks']['accuracy_vs_speed'] = self.benchmark_accuracy_vs_speed()
        
        # 生成图表
        self.plot_benchmark_results()
        
        # 保存结果
        if save_results:
            self.save_results()
        
        # 打印总结
        self.print_summary()
        
        return self.results
    
    def save_results(self, filename: str = None):
        """
        保存基准测试结果
        
        Args:
            filename: 保存文件名
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"benchmark_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n基准测试结果已保存到: {filename}")
    
    def print_summary(self):
        """
        打印基准测试总结
        """
        print("\n" + "=" * 60)
        print("基准测试总结")
        print("=" * 60)
        
        # 模型加载总结
        if 'model_loading' in self.results['benchmarks']:
            loading = self.results['benchmarks']['model_loading']
            print(f"模型加载时间: {loading['mean_loading_time']:.3f}s (±{loading['std_loading_time']:.3f}s)")
        
        # 推理速度总结
        if 'inference_speed' in self.results['benchmarks']:
            speed = self.results['benchmarks']['inference_speed']
            print("\n推理速度 (不同图像尺寸):")
            for size, stats in speed.items():
                print(f"  {size}: {stats['mean_time']:.3f}s ({stats['fps']:.1f} FPS)")
        
        # 内存使用总结
        if 'memory_usage' in self.results['benchmarks']:
            memory = self.results['benchmarks']['memory_usage']
            print(f"\n内存使用:")
            print(f"  模型内存: {memory['model_memory']:.1f}MB")
        
        # 最佳配置推荐
        if 'accuracy_vs_speed' in self.results['benchmarks']:
            acc_speed = self.results['benchmarks']['accuracy_vs_speed']
            
            # 找到平衡点（检测数量和速度的权衡）
            best_balance = None
            best_score = 0
            
            for thresh, stats in acc_speed.items():
                # 简单的平衡评分：检测数量 * FPS
                score = stats['avg_detections_per_image'] * stats['fps']
                if score > best_score:
                    best_score = score
                    best_balance = (thresh, stats)
            
            if best_balance:
                thresh, stats = best_balance
                print(f"\n推荐配置:")
                print(f"  置信度阈值: {thresh}")
                print(f"  平均检测数量: {stats['avg_detections_per_image']:.1f}")
                print(f"  FPS: {stats['fps']:.1f}")

def main():
    """
    主函数
    """
    print("YOLO模型基准测试工具")
    print("=" * 50)
    print(f"模型路径: {MODEL_PATH}")
    print(f"测试图像目录: {TEST_IMAGE_DIR}")
    
    # 创建基准测试器
    benchmark = ModelBenchmark(model_path=MODEL_PATH, test_image_dir=TEST_IMAGE_DIR)
    
    # 运行完整基准测试
    results = benchmark.run_full_benchmark()
    
    print("\n基准测试完成!")

if __name__ == "__main__":
    main()