@echo off
echo 正在启动YOLO-OHIF医学影像检测系统...

:: 设置环境变量
set FLASK_APP=app.py
set FLASK_ENV=development

:: 创建必要的目录
if not exist uploads mkdir uploads
if not exist models mkdir models
if not exist results mkdir results
if not exist logs mkdir logs
if not exist instance mkdir instance

:: 检查Python环境
python --version > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到Python环境，请安装Python 3.9或更高版本。
    exit /b 1
)

:: 检查依赖包
echo 检查依赖包...
python -c "import flask, ultralytics, pydicom, requests" > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if %ERRORLEVEL% neq 0 (
        echo 错误: 依赖包安装失败，请检查网络连接或手动安装。
        exit /b 1
    )
)

:: 检查YOLO模型
if not exist models\yolov8n.pt (
    echo 正在下载YOLO模型...
    python -c "from ultralytics import YOLO; YOLO('yolov8n.pt')"
    if %ERRORLEVEL% neq 0 (
        echo 错误: YOLO模型下载失败，请检查网络连接或手动下载。
        exit /b 1
    )
)

:: 启动Orthanc服务器（如果已安装）
where orthanc > nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo 正在启动Orthanc DICOM服务器...
    start "Orthanc DICOM服务器" orthanc
) else (
    echo 警告: 未找到Orthanc可执行文件，请确保Orthanc服务器已在外部启动。
)

:: 检查Orthanc服务状态
echo 检查Orthanc服务状态...
curl -s http://localhost:8042/system > nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo Orthanc服务已运行，OHIF查看器可通过 http://localhost:8042/ohif/viewer 访问
) else (
    echo 警告: Orthanc服务未运行。OHIF查看器需要Orthanc服务支持。
    echo 请确保Orthanc服务已启动，或使用Docker: docker-compose up -d
)

:: 启动Flask应用
echo 正在启动Flask应用...
start "YOLO-OHIF Flask应用" python run.py --debug

:: 等待服务启动
echo 等待服务启动...
timeout /t 5 /nobreak > nul

:: 打开浏览器
echo 正在打开浏览器...
start http://localhost:5000

echo.
echo YOLO-OHIF医学影像检测系统已启动！
echo 访问 http://localhost:5000 使用系统
echo 按Ctrl+C关闭窗口可停止服务
echo.

:: 保持窗口打开
cmd /k