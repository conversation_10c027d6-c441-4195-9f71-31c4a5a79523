#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试bbox生成，检查几个具体的图像和对应的标注
"""

import cv2
import numpy as np
from pathlib import Path

def debug_specific_images():
    """
    调试几个具体的图像，查看它们的像素分布和生成的bbox
    """
    label_dir = Path("e:/Trae/yolo_ohif/sliced_output/sliced_images/label_T2")
    bbox_dir = Path("e:/Trae/yolo_ohif/sliced_output/bbox_labels")
    
    # 检查几个具体的图像
    test_images = ["130_slice_000.jpg", "150_slice_010.jpg", "170_slice_005.jpg"]
    
    for img_name in test_images:
        img_path = label_dir / img_name
        txt_name = img_name.replace(".jpg", ".txt")
        txt_path = bbox_dir / txt_name
        
        print(f"\n=== 分析图像: {img_name} ===")
        
        # 读取图像
        img = cv2.imread(str(img_path), cv2.IMREAD_GRAYSCALE)
        if img is None:
            print(f"无法读取图像: {img_path}")
            continue
            
        print(f"图像尺寸: {img.shape}")
        print(f"像素值范围: {img.min()} - {img.max()}")
        
        # 统计像素值
        unique_values, counts = np.unique(img, return_counts=True)
        print(f"唯一像素值: {unique_values}")
        print(f"对应数量: {counts}")
        
        # 检查白色像素
        white_pixels = np.sum(img == 255)
        print(f"白色像素数量: {white_pixels}")
        
        # 如果有白色像素，分析其分布
        if white_pixels > 0:
            coords = np.where(img == 255)
            y_min, y_max = coords[0].min(), coords[0].max()
            x_min, x_max = coords[1].min(), coords[1].max()
            
            print(f"白色像素边界框:")
            print(f"  y范围: {y_min} - {y_max}")
            print(f"  x范围: {x_min} - {x_max}")
            
            # 计算YOLO格式坐标
            height, width = img.shape
            bbox_width = x_max - x_min + 1
            bbox_height = y_max - y_min + 1
            x_center = (x_min + x_max) / 2 / width
            y_center = (y_min + y_max) / 2 / height
            norm_width = bbox_width / width
            norm_height = bbox_height / height
            
            print(f"计算的YOLO坐标: {x_center:.8f} {y_center:.8f} {norm_width:.8f} {norm_height:.8f}")
        
        # 读取生成的标注文件
        if txt_path.exists():
            with open(txt_path, 'r') as f:
                content = f.read().strip()
            print(f"生成的标注: {content}")
        else:
            print("未生成标注文件")

if __name__ == "__main__":
    debug_specific_images()