{% extends "base.html" %}

{% block title %}YOLO检测 - YOLO-OHIF医学图像疾病检测系统{% endblock %}

{% block extra_css %}
<style>
    .detection-card {
        border: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
    }
    
    .detection-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px 10px 0 0;
        padding: 1.5rem;
    }
    
    .model-info {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .confidence-slider {
        margin: 1rem 0;
    }
    
    .confidence-value {
        font-weight: bold;
        color: #667eea;
        font-size: 1.1em;
    }
    
    .detection-options {
        background-color: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .btn-detect {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 12px 30px;
        font-size: 1.1em;
        border-radius: 25px;
        transition: all 0.3s ease;
    }
    
    .btn-detect:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(102, 126, 234, 0.3);
    }
    
    .study-info {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 1rem;
        border-radius: 0 8px 8px 0;
        margin-bottom: 1.5rem;
    }
    
    .feature-icon {
        color: #667eea;
        margin-right: 0.5rem;
    }
    
    .detection-process {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="detection-card card">
                <div class="detection-header">
                    <h2 class="mb-0">
                        <i class="fas fa-brain me-2"></i>
                        YOLO疾病检测
                    </h2>
                    <p class="mb-0 mt-2">使用人工智能模型对医学图像进行疾病检测和病灶定位</p>
                </div>
                
                <div class="card-body">
                    <!-- 研究信息 -->
                    {% if study %}
                    <div class="study-info">
                        <h5><i class="fas fa-folder-open me-2"></i>研究信息</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>患者姓名:</strong> {{ study.patient_name or '未知' }}</p>
                                <p><strong>患者ID:</strong> {{ study.patient_id or '未知' }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>研究日期:</strong> {{ study.study_date | safe_strftime }}</p>
                                <p><strong>研究描述:</strong> {{ study.study_description or '无描述' }}</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- 模型信息 -->
                    {% if model_info %}
                    <div class="model-info">
                        <h5><i class="fas fa-cog me-2"></i>模型信息</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <p><strong>模型类型:</strong> {{ model_info.get('model_type', 'YOLO') }}</p>
                            </div>
                            <div class="col-md-4">
                                <p><strong>类别数量:</strong> {{ model_info.get('num_classes', '未知') }}</p>
                            </div>
                            <div class="col-md-4">
                                <p><strong>设备:</strong> {{ model_info.get('device', '未知') }}</p>
                            </div>
                        </div>
                        {% if model_info.get('class_names') %}
                        <p><strong>检测类别:</strong> 
                            {% for class_name in model_info.get('class_names', []) %}
                                <span class="badge bg-secondary me-1">{{ class_name }}</span>
                            {% endfor %}
                        </p>
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <!-- 检测配置 -->
                    <form method="POST" id="detectionForm">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        
                        <div class="detection-options">
                            <h5><i class="fas fa-sliders-h me-2"></i>检测参数</h5>
                            
                            <!-- 置信度阈值 -->
                            <div class="confidence-slider">
                                <label for="confidence" class="form-label">
                                    <i class="feature-icon fas fa-percentage"></i>
                                    置信度阈值: <span class="confidence-value" id="confidenceValue">0.25</span>
                                </label>
                                <input type="range" class="form-range" id="confidence" name="confidence" 
                                       min="0.1" max="0.9" step="0.05" value="0.25">
                                <div class="form-text">
                                    较高的置信度阈值会减少误检，但可能遗漏一些病灶；较低的阈值会检测更多目标，但可能增加误检。
                                </div>
                            </div>
                            
                            <!-- 保存选项 -->
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="save_images" name="save_images" value="true" checked>
                                <label class="form-check-label" for="save_images">
                                    <i class="feature-icon fas fa-save"></i>
                                    保存检测结果图像
                                </label>
                                <div class="form-text">
                                    保存带有检测标注的图像，便于后续查看和分析。
                                </div>
                            </div>
                        </div>
                        
                        <!-- 检测流程说明 -->
                        <div class="detection-process">
                            <h6><i class="fas fa-info-circle me-2"></i>检测流程说明</h6>
                            <ol class="mb-0">
                                <li><strong>图像预处理:</strong> 系统会自动将DICOM图像转换为YOLO模型可处理的格式</li>
                                <li><strong>疾病检测:</strong> 使用训练好的YOLO模型对图像进行病灶检测和定位</li>
                                <li><strong>结果后处理:</strong> 对检测结果进行筛选和优化，去除重复检测</li>
                                <li><strong>结果存储:</strong> 将检测结果保存到数据库，并可选择保存标注图像</li>
                                <li><strong>结果展示:</strong> 在OHIF查看器中叠加显示检测结果</li>
                            </ol>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-detect me-3">
                                <i class="fas fa-play me-2"></i>
                                开始检测
                            </button>
                            {% if study %}
                            <a href="{{ url_for('web.dicom_viewer', study_id=study.id) }}" class="btn btn-info me-3">
                                <i class="fas fa-eye me-2"></i>
                                DICOM查看器
                            </a>
                            {% endif %}
                            <a href="{{ url_for('web.dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                返回仪表板
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 检测进度模态框 -->
<div class="modal fade" id="detectionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-brain me-2"></i>
                    正在进行YOLO检测
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">检测中...</span>
                </div>
                <p>系统正在对医学图像进行疾病检测，请稍候...</p>
                <p class="text-muted">检测时间取决于图像数量和复杂度，通常需要几分钟时间。</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 置信度滑块事件
    $('#confidence').on('input', function() {
        $('#confidenceValue').text($(this).val());
    });
    
    // 表单提交事件
    $('#detectionForm').on('submit', function(e) {
        // 显示进度模态框
        $('#detectionModal').modal('show');
        
        // 禁用表单提交按钮
        $(this).find('button[type="submit"]').prop('disabled', true);
    });
    
    // 防止模态框被手动关闭
    $('#detectionModal').modal({
        backdrop: 'static',
        keyboard: false
    });
});
</script>
{% endblock %}