import os
from dotenv import load_dotenv

# 加载.env文件中的环境变量
load_dotenv()

# 基本配置
BASE_DIR = os.path.abspath(os.path.dirname(__file__))
DEBUG = os.getenv('DEBUG', 'True').lower() in ('true', '1', 't')
SECRET_KEY = os.getenv('SECRET_KEY', 'dev_key_please_change_in_production')

# Flask应用配置
class FlaskConfig:
    SECRET_KEY = SECRET_KEY
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL', f'sqlite:///{os.path.join(BASE_DIR, "app.db")}')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    UPLOAD_FOLDER = os.path.join(BASE_DIR, 'uploads')
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 限制上传文件大小为100MB

# Orthanc DICOM服务器配置
class OrthancConfig:
    ORTHANC_URL = os.getenv('ORTHANC_URL', 'http://localhost:8042')
    ORTHANC_USERNAME = os.getenv('ORTHANC_USERNAME', 'admin')
    ORTHANC_PASSWORD = os.getenv('ORTHANC_PASSWORD', 'password')
    ORTHANC_DICOMWEB_URL = os.getenv('ORTHANC_DICOMWEB_URL', 'http://localhost:8042/dicom-web')

# OHIF查看器配置
class OHIFConfig:
    # 使用Orthanc集成的Web查看器
    OHIF_VIEWER_URL = os.getenv('OHIF_VIEWER_URL', 'http://localhost:8042/ui/app/#/')
    OHIF_STATIC_PATH = os.path.join(BASE_DIR, 'static', 'ohif')
    # Orthanc Web界面URL
    ORTHANC_WEB_URL = os.getenv('ORTHANC_WEB_URL', 'http://localhost:8042/ui/app')

# YOLO模型配置
class YOLOConfig:
    MODEL_PATH = "E:/Trae/yolo_ohif/models/weights"
    DEFAULT_MODEL = os.getenv('DEFAULT_YOLO_MODEL', 'best.pt')  # 默认使用训练好的最佳模型
    CONFIDENCE_THRESHOLD = float(os.getenv('YOLO_CONFIDENCE_THRESHOLD', '0.1'))
    IOU_THRESHOLD = float(os.getenv('YOLO_IOU_THRESHOLD', '0.45'))
    DEVICE = os.getenv('YOLO_DEVICE', 'cuda:0' if os.path.exists('/dev/nvidia0') else 'cpu')

# 日志配置
class LogConfig:
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_DIR = os.path.join(BASE_DIR, 'logs')

# 整合所有配置
class Config:
    DEBUG = DEBUG  # 添加全局DEBUG配置
    FLASK = FlaskConfig
    ORTHANC = OrthancConfig
    OHIF = OHIFConfig
    YOLO = YOLOConfig
    LOG = LogConfig
    
    # 数据库路径
    DATABASE_PATH = os.path.join(BASE_DIR, 'data', 'app.db')
    
    # OHIF查看器URL（向后兼容）
    OHIF_VIEWER_URL = OHIFConfig.OHIF_VIEWER_URL
    
    # Orthanc URL（向后兼容）
    ORTHANC_URL = OrthancConfig.ORTHANC_URL

# 创建必要的目录
os.makedirs(FlaskConfig.UPLOAD_FOLDER, exist_ok=True)
os.makedirs(YOLOConfig.MODEL_PATH, exist_ok=True)
os.makedirs(LogConfig.LOG_DIR, exist_ok=True)
os.makedirs(OHIFConfig.OHIF_STATIC_PATH, exist_ok=True)
os.makedirs(os.path.join(BASE_DIR, 'data'), exist_ok=True)