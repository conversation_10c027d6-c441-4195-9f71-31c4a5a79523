import os
import cv2
import numpy as np
from collections import defaultdict

def validate_bbox_labels():
    """验证生成的bbox标注文件的质量"""
    
    label_dir = r"e:\Trae\yolo_ohif\sliced_output\sliced_images\label_T2"
    bbox_dir = r"e:\Trae\yolo_ohif\sliced_output\bbox_labels"
    
    # 统计信息
    stats = {
        'total_images': 0,
        'images_with_labels': 0,
        'images_without_labels': 0,
        'bbox_count_distribution': defaultdict(int),
        'sample_files': []
    }
    
    # 获取所有图像文件
    image_files = [f for f in os.listdir(label_dir) if f.endswith('.jpg')]
    stats['total_images'] = len(image_files)
    
    print(f"总图像数量: {stats['total_images']}")
    
    # 检查前100个文件作为样本
    sample_files = image_files[:100]
    
    for img_file in sample_files:
        img_path = os.path.join(label_dir, img_file)
        txt_file = img_file.replace('.jpg', '.txt')
        txt_path = os.path.join(bbox_dir, txt_file)
        
        # 读取图像
        img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
        white_pixels = np.sum(img == 255)
        
        # 检查是否有对应的标注文件
        if os.path.exists(txt_path):
            stats['images_with_labels'] += 1
            
            # 读取标注文件
            with open(txt_path, 'r') as f:
                lines = f.readlines()
                bbox_count = len(lines)
                stats['bbox_count_distribution'][bbox_count] += 1
                
                # 记录样本信息
                stats['sample_files'].append({
                    'file': img_file,
                    'white_pixels': white_pixels,
                    'bbox_count': bbox_count,
                    'bboxes': [line.strip() for line in lines]
                })
        else:
            stats['images_without_labels'] += 1
            stats['sample_files'].append({
                'file': img_file,
                'white_pixels': white_pixels,
                'bbox_count': 0,
                'bboxes': []
            })
    
    # 打印统计结果
    print(f"\n样本统计 (前100个文件):")
    print(f"有标注的图像: {stats['images_with_labels']}")
    print(f"无标注的图像: {stats['images_without_labels']}")
    
    print(f"\n边界框数量分布:")
    for bbox_count, count in sorted(stats['bbox_count_distribution'].items()):
        print(f"  {bbox_count}个边界框: {count}个文件")
    
    # 显示一些样本
    print(f"\n样本文件详情:")
    for i, sample in enumerate(stats['sample_files'][:10]):
        print(f"  {sample['file']}: 白色像素={sample['white_pixels']}, 边界框数量={sample['bbox_count']}")
        if sample['bboxes']:
            for bbox in sample['bboxes']:
                print(f"    {bbox}")
    
    return stats

if __name__ == "__main__":
    validate_bbox_labels()