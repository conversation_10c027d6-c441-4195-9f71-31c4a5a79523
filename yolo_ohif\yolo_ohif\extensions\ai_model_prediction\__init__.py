# AI Model Prediction Extension for DICOM Viewer
# Based on OHIF extension architecture (Refactored)

from .refactored_extension import RefactoredAIPredictionExtension
from .factory import ExtensionFactory
from .integration import init_ai_extension, get_ai_extension, cleanup_ai_extension

# 向后兼容性别名
AIPredictionExtension = RefactoredAIPredictionExtension

__version__ = '2.0.0'
__author__ = 'YOLO-OHIF Team'

__all__ = [
    'RefactoredAIPredictionExtension',
    'AIPredictionExtension',  # 向后兼容
    'ExtensionFactory',
    'init_ai_extension',
    'get_ai_extension',
    'cleanup_ai_extension'
]