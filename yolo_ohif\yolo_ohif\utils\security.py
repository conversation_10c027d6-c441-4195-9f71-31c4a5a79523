import os
import re
import hashlib
import secrets
import logging
from typing import List, Optional, Dict, Any
from pathlib import Path
import mimetypes
from werkzeug.utils import secure_filename

logger = logging.getLogger(__name__)

class SecurityValidator:
    """安全验证器"""
    
    # 允许的DICOM文件扩展名
    ALLOWED_DICOM_EXTENSIONS = {'.dcm', '.dicom', '.DCM', '.DICOM'}
    
    # 允许的压缩文件扩展名
    ALLOWED_ARCHIVE_EXTENSIONS = {'.zip', '.tar', '.gz'}
    
    # 危险文件扩展名黑名单
    DANGEROUS_EXTENSIONS = {
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
        '.jar', '.sh', '.ps1', '.php', '.asp', '.aspx', '.jsp'
    }
    
    # 最大文件大小 (500MB)
    MAX_FILE_SIZE = 500 * 1024 * 1024
    
    @classmethod
    def validate_filename(cls, filename: str) -> Dict[str, Any]:
        """验证文件名安全性
        
        Args:
            filename: 文件名
            
        Returns:
            验证结果字典
        """
        result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'secure_filename': secure_filename(filename)
        }
        
        if not filename:
            result['is_valid'] = False
            result['errors'].append('文件名不能为空')
            return result
        
        # 检查文件名长度
        if len(filename) > 255:
            result['is_valid'] = False
            result['errors'].append('文件名过长')
        
        # 检查危险字符
        dangerous_chars = ['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for char in dangerous_chars:
            if char in filename:
                result['is_valid'] = False
                result['errors'].append(f'文件名包含危险字符: {char}')
        
        # 检查文件扩展名
        file_ext = Path(filename).suffix.lower()
        
        if file_ext in cls.DANGEROUS_EXTENSIONS:
            result['is_valid'] = False
            result['errors'].append(f'不允许的文件类型: {file_ext}')
        
        if file_ext not in cls.ALLOWED_DICOM_EXTENSIONS and file_ext not in cls.ALLOWED_ARCHIVE_EXTENSIONS:
            result['warnings'].append(f'未知文件类型: {file_ext}')
        
        return result
    
    @classmethod
    def validate_file_content(cls, file_path: str) -> Dict[str, Any]:
        """验证文件内容安全性
        
        Args:
            file_path: 文件路径
            
        Returns:
            验证结果字典
        """
        result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'file_info': {}
        }
        
        try:
            if not os.path.exists(file_path):
                result['is_valid'] = False
                result['errors'].append('文件不存在')
                return result
            
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            result['file_info']['size'] = file_size
            
            if file_size > cls.MAX_FILE_SIZE:
                result['is_valid'] = False
                result['errors'].append(f'文件过大: {file_size / (1024*1024):.1f}MB')
            
            if file_size == 0:
                result['is_valid'] = False
                result['errors'].append('文件为空')
            
            # 检查MIME类型
            mime_type, _ = mimetypes.guess_type(file_path)
            result['file_info']['mime_type'] = mime_type
            
            # 计算文件哈希
            result['file_info']['sha256'] = cls._calculate_file_hash(file_path)
            
            # 检查文件头部
            result.update(cls._check_file_header(file_path))
            
        except Exception as e:
            logger.error(f"文件验证错误: {str(e)}")
            result['is_valid'] = False
            result['errors'].append(f'文件验证失败: {str(e)}')
        
        return result
    
    @classmethod
    def _calculate_file_hash(cls, file_path: str) -> str:
        """计算文件SHA256哈希"""
        hash_sha256 = hashlib.sha256()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    @classmethod
    def _check_file_header(cls, file_path: str) -> Dict[str, Any]:
        """检查文件头部信息"""
        result = {'header_check': {'is_valid': True, 'info': []}}
        
        try:
            with open(file_path, 'rb') as f:
                header = f.read(1024)  # 读取前1KB
            
            # 检查DICOM文件头
            if file_path.lower().endswith(('.dcm', '.dicom')):
                # DICOM文件应该在128字节后有"DICM"标识
                if len(header) > 132 and header[128:132] != b'DICM':
                    result['header_check']['info'].append('可能不是有效的DICOM文件')
            
            # 检查ZIP文件头
            elif file_path.lower().endswith('.zip'):
                if not header.startswith(b'PK'):
                    result['header_check']['is_valid'] = False
                    result['header_check']['info'].append('无效的ZIP文件头')
            
            # 检查可执行文件头
            executable_headers = [b'MZ', b'\x7fELF', b'\xca\xfe\xba\xbe']
            for exe_header in executable_headers:
                if header.startswith(exe_header):
                    result['header_check']['is_valid'] = False
                    result['header_check']['info'].append('检测到可执行文件头')
                    break
            
        except Exception as e:
            logger.error(f"文件头检查错误: {str(e)}")
            result['header_check']['info'].append(f'头部检查失败: {str(e)}')
        
        return result

class InputSanitizer:
    """输入清理器"""
    
    @staticmethod
    def sanitize_string(input_str: str, max_length: int = 255) -> str:
        """清理字符串输入
        
        Args:
            input_str: 输入字符串
            max_length: 最大长度
            
        Returns:
            清理后的字符串
        """
        if not input_str:
            return ''
        
        # 移除控制字符
        sanitized = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', str(input_str))
        
        # 限制长度
        sanitized = sanitized[:max_length]
        
        # 移除前后空白
        sanitized = sanitized.strip()
        
        return sanitized
    
    @staticmethod
    def sanitize_path(path_str: str) -> str:
        """清理路径字符串
        
        Args:
            path_str: 路径字符串
            
        Returns:
            清理后的路径
        """
        if not path_str:
            return ''
        
        # 移除危险字符
        sanitized = re.sub(r'[<>:"|?*]', '', str(path_str))
        
        # 移除路径遍历尝试
        sanitized = re.sub(r'\.\./+', '', sanitized)
        sanitized = re.sub(r'\\\\+', '\\', sanitized)
        
        return sanitized
    
    @staticmethod
    def validate_study_id(study_id: str) -> bool:
        """验证研究ID格式
        
        Args:
            study_id: 研究ID
            
        Returns:
            是否有效
        """
        if not study_id:
            return False
        
        # 研究ID应该是字母数字和连字符的组合
        pattern = r'^[a-zA-Z0-9\-_]{1,64}$'
        return bool(re.match(pattern, study_id))
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """验证邮箱格式
        
        Args:
            email: 邮箱地址
            
        Returns:
            是否有效
        """
        if not email:
            return False
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email)) and len(email) <= 254

class SecureTokenGenerator:
    """安全令牌生成器"""
    
    @staticmethod
    def generate_secure_token(length: int = 32) -> str:
        """生成安全令牌
        
        Args:
            length: 令牌长度
            
        Returns:
            安全令牌
        """
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def generate_api_key(prefix: str = 'yolo') -> str:
        """生成API密钥
        
        Args:
            prefix: 密钥前缀
            
        Returns:
            API密钥
        """
        token = secrets.token_urlsafe(32)
        return f"{prefix}_{token}"
    
    @staticmethod
    def hash_password(password: str, salt: Optional[str] = None) -> tuple:
        """哈希密码
        
        Args:
            password: 明文密码
            salt: 盐值（可选）
            
        Returns:
            (哈希值, 盐值)
        """
        if salt is None:
            salt = secrets.token_hex(16)
        
        # 使用PBKDF2进行哈希
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # 迭代次数
        )
        
        return password_hash.hex(), salt
    
    @staticmethod
    def verify_password(password: str, password_hash: str, salt: str) -> bool:
        """验证密码
        
        Args:
            password: 明文密码
            password_hash: 存储的哈希值
            salt: 盐值
            
        Returns:
            是否匹配
        """
        computed_hash, _ = SecureTokenGenerator.hash_password(password, salt)
        return secrets.compare_digest(computed_hash, password_hash)

def validate_upload_file(file_path: str, filename: str) -> Dict[str, Any]:
    """验证上传文件的安全性
    
    Args:
        file_path: 文件路径
        filename: 原始文件名
        
    Returns:
        验证结果
    """
    validator = SecurityValidator()
    
    # 验证文件名
    filename_result = validator.validate_filename(filename)
    
    # 验证文件内容
    content_result = validator.validate_file_content(file_path)
    
    # 合并结果
    combined_result = {
        'is_valid': filename_result['is_valid'] and content_result['is_valid'],
        'errors': filename_result['errors'] + content_result['errors'],
        'warnings': filename_result['warnings'] + content_result['warnings'],
        'secure_filename': filename_result['secure_filename'],
        'file_info': content_result.get('file_info', {})
    }
    
    return combined_result