using MedicalImageAnalysis.Core.Entities;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace MedicalImageAnalysis.Infrastructure.Services;

/// <summary>
/// 训练监控服务
/// </summary>
public class TrainingMonitoringService
{
    private readonly ILogger<TrainingMonitoringService> _logger;
    private readonly Dictionary<string, TrainingSession> _activeSessions = new();

    public TrainingMonitoringService(ILogger<TrainingMonitoringService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 开始训练会话
    /// </summary>
    public async Task<string> StartTrainingSessionAsync(TrainingSessionConfig config)
    {
        var sessionId = Guid.NewGuid().ToString();
        
        var session = new TrainingSession
        {
            SessionId = sessionId,
            Config = config,
            StartTime = DateTime.UtcNow,
            Status = TrainingStatus.Starting,
            Metrics = new TrainingMetrics(),
            Logs = new List<TrainingLogEntry>()
        };

        _activeSessions[sessionId] = session;

        await LogTrainingEventAsync(sessionId, "INFO", "训练会话开始", new
        {
            config.ModelType,
            config.DatasetPath,
            config.Epochs,
            config.BatchSize,
            config.LearningRate
        });

        _logger.LogInformation("训练会话开始: {SessionId}", sessionId);
        return sessionId;
    }

    /// <summary>
    /// 更新训练进度
    /// </summary>
    public async Task UpdateTrainingProgressAsync(string sessionId, TrainingProgressUpdate update)
    {
        if (!_activeSessions.TryGetValue(sessionId, out var session))
        {
            _logger.LogWarning("训练会话不存在: {SessionId}", sessionId);
            return;
        }

        session.CurrentEpoch = update.CurrentEpoch;
        session.TotalEpochs = update.TotalEpochs;
        session.Progress = (double)update.CurrentEpoch / update.TotalEpochs * 100;
        session.Status = TrainingStatus.Training;
        session.LastUpdateTime = DateTime.UtcNow;

        // 更新指标
        if (update.Metrics != null)
        {
            await UpdateMetricsAsync(session, update.Metrics);
        }

        // 记录日志
        if (!string.IsNullOrEmpty(update.Message))
        {
            await LogTrainingEventAsync(sessionId, "INFO", update.Message, update.Metrics);
        }

        // 检查早停条件
        if (session.Config.EnableEarlyStopping)
        {
            await CheckEarlyStoppingAsync(session);
        }

        // 保存检查点
        if (update.CurrentEpoch % session.Config.CheckpointInterval == 0)
        {
            await SaveCheckpointAsync(session, update.CurrentEpoch);
        }
    }

    /// <summary>
    /// 完成训练会话
    /// </summary>
    public async Task CompleteTrainingSessionAsync(string sessionId, TrainingCompletionResult result)
    {
        if (!_activeSessions.TryGetValue(sessionId, out var session))
        {
            _logger.LogWarning("训练会话不存在: {SessionId}", sessionId);
            return;
        }

        session.EndTime = DateTime.UtcNow;
        session.Status = result.Success ? TrainingStatus.Completed : TrainingStatus.Failed;
        session.FinalModelPath = result.ModelPath;
        session.ErrorMessage = result.ErrorMessage;

        var trainingTime = session.EndTime.Value - session.StartTime;
        session.TotalTrainingTimeMs = (long)trainingTime.TotalMilliseconds;

        await LogTrainingEventAsync(sessionId, result.Success ? "INFO" : "ERROR", 
            result.Success ? "训练完成" : $"训练失败: {result.ErrorMessage}", 
            new { 
                TrainingTimeMs = session.TotalTrainingTimeMs,
                FinalAccuracy = session.Metrics.BestValidationAccuracy,
                FinalLoss = session.Metrics.CurrentValidationLoss
            });

        // 生成训练报告
        await GenerateTrainingReportAsync(session);

        _logger.LogInformation("训练会话完成: {SessionId}, 状态: {Status}", sessionId, session.Status);
    }

    /// <summary>
    /// 获取训练会话状态
    /// </summary>
    public TrainingSession? GetTrainingSession(string sessionId)
    {
        return _activeSessions.TryGetValue(sessionId, out var session) ? session : null;
    }

    /// <summary>
    /// 获取所有活跃的训练会话
    /// </summary>
    public List<TrainingSession> GetActiveTrainingSessions()
    {
        return _activeSessions.Values.Where(s => s.Status == TrainingStatus.Training || s.Status == TrainingStatus.Starting).ToList();
    }

    /// <summary>
    /// 停止训练会话
    /// </summary>
    public async Task StopTrainingSessionAsync(string sessionId, string reason = "用户停止")
    {
        if (!_activeSessions.TryGetValue(sessionId, out var session))
        {
            _logger.LogWarning("训练会话不存在: {SessionId}", sessionId);
            return;
        }

        session.Status = TrainingStatus.Stopped;
        session.EndTime = DateTime.UtcNow;
        session.ErrorMessage = reason;

        await LogTrainingEventAsync(sessionId, "WARN", $"训练被停止: {reason}");

        _logger.LogInformation("训练会话被停止: {SessionId}, 原因: {Reason}", sessionId, reason);
    }

    private async Task UpdateMetricsAsync(TrainingSession session, Dictionary<string, double> metrics)
    {
        await Task.CompletedTask;

        var sessionMetrics = session.Metrics;

        foreach (var (key, value) in metrics)
        {
            switch (key.ToLowerInvariant())
            {
                case "train_loss":
                    sessionMetrics.CurrentTrainLoss = value;
                    sessionMetrics.TrainLossHistory.Add(value);
                    if (value < sessionMetrics.BestTrainLoss)
                        sessionMetrics.BestTrainLoss = value;
                    break;

                case "val_loss":
                case "validation_loss":
                    sessionMetrics.CurrentValidationLoss = value;
                    sessionMetrics.ValidationLossHistory.Add(value);
                    if (value < sessionMetrics.BestValidationLoss)
                        sessionMetrics.BestValidationLoss = value;
                    break;

                case "train_acc":
                case "train_accuracy":
                    sessionMetrics.CurrentTrainAccuracy = value;
                    sessionMetrics.TrainAccuracyHistory.Add(value);
                    if (value > sessionMetrics.BestTrainAccuracy)
                        sessionMetrics.BestTrainAccuracy = value;
                    break;

                case "val_acc":
                case "validation_accuracy":
                    sessionMetrics.CurrentValidationAccuracy = value;
                    sessionMetrics.ValidationAccuracyHistory.Add(value);
                    if (value > sessionMetrics.BestValidationAccuracy)
                        sessionMetrics.BestValidationAccuracy = value;
                    break;

                case "learning_rate":
                    sessionMetrics.CurrentLearningRate = value;
                    sessionMetrics.LearningRateHistory.Add(value);
                    break;
            }
        }
    }

    private async Task CheckEarlyStoppingAsync(TrainingSession session)
    {
        await Task.CompletedTask;

        var config = session.Config;
        var metrics = session.Metrics;

        if (!config.EnableEarlyStopping || metrics.ValidationLossHistory.Count < config.EarlyStoppingPatience)
            return;

        // 检查验证损失是否在指定轮数内没有改善
        var recentLosses = metrics.ValidationLossHistory.TakeLast(config.EarlyStoppingPatience).ToList();
        var minRecentLoss = recentLosses.Min();
        var bestOverallLoss = metrics.BestValidationLoss;

        if (minRecentLoss >= bestOverallLoss + config.EarlyStoppingMinDelta)
        {
            session.Status = TrainingStatus.EarlyStopped;
            session.EndTime = DateTime.UtcNow;
            
            await LogTrainingEventAsync(session.SessionId, "INFO", 
                $"早停触发 - 验证损失在 {config.EarlyStoppingPatience} 轮内未改善");

            _logger.LogInformation("训练早停: {SessionId}", session.SessionId);
        }
    }

    private async Task SaveCheckpointAsync(TrainingSession session, int epoch)
    {
        await Task.CompletedTask;

        var checkpointPath = Path.Combine(session.Config.OutputPath, "checkpoints", $"epoch_{epoch}.pt");
        Directory.CreateDirectory(Path.GetDirectoryName(checkpointPath)!);

        // 这里应该保存实际的模型检查点
        // 目前只记录检查点信息
        session.Checkpoints.Add(new TrainingCheckpoint
        {
            Epoch = epoch,
            Path = checkpointPath,
            SaveTime = DateTime.UtcNow,
            ValidationLoss = session.Metrics.CurrentValidationLoss,
            ValidationAccuracy = session.Metrics.CurrentValidationAccuracy
        });

        await LogTrainingEventAsync(session.SessionId, "INFO", $"保存检查点: epoch_{epoch}.pt");
    }

    private async Task LogTrainingEventAsync(string sessionId, string level, string message, object? data = null)
    {
        if (!_activeSessions.TryGetValue(sessionId, out var session))
            return;

        var logEntry = new TrainingLogEntry
        {
            Timestamp = DateTime.UtcNow,
            Level = level,
            Message = message,
            Data = data != null ? JsonSerializer.Serialize(data) : null
        };

        session.Logs.Add(logEntry);

        // 限制日志数量，避免内存溢出
        if (session.Logs.Count > 10000)
        {
            session.Logs.RemoveRange(0, 1000);
        }

        await Task.CompletedTask;
    }

    private async Task GenerateTrainingReportAsync(TrainingSession session)
    {
        await Task.CompletedTask;

        var report = new TrainingReport
        {
            SessionId = session.SessionId,
            StartTime = session.StartTime,
            EndTime = session.EndTime,
            Status = session.Status,
            TotalTrainingTimeMs = session.TotalTrainingTimeMs,
            Config = session.Config,
            FinalMetrics = session.Metrics,
            Checkpoints = session.Checkpoints,
            LogSummary = GenerateLogSummary(session.Logs)
        };

        var reportPath = Path.Combine(session.Config.OutputPath, "training_report.json");
        var reportJson = JsonSerializer.Serialize(report, new JsonSerializerOptions { WriteIndented = true });
        await File.WriteAllTextAsync(reportPath, reportJson);

        _logger.LogInformation("训练报告已生成: {ReportPath}", reportPath);
    }

    private object GenerateLogSummary(List<TrainingLogEntry> logs)
    {
        return new
        {
            TotalLogs = logs.Count,
            ErrorCount = logs.Count(l => l.Level == "ERROR"),
            WarningCount = logs.Count(l => l.Level == "WARN"),
            InfoCount = logs.Count(l => l.Level == "INFO"),
            LastError = logs.LastOrDefault(l => l.Level == "ERROR")?.Message,
            LastWarning = logs.LastOrDefault(l => l.Level == "WARN")?.Message
        };
    }
}

/// <summary>
/// 训练会话配置
/// </summary>
public class TrainingSessionConfig
{
    public string ModelType { get; set; } = string.Empty;
    public string DatasetPath { get; set; } = string.Empty;
    public string OutputPath { get; set; } = string.Empty;
    public int Epochs { get; set; } = 100;
    public int BatchSize { get; set; } = 16;
    public double LearningRate { get; set; } = 0.001;
    public bool EnableEarlyStopping { get; set; } = true;
    public int EarlyStoppingPatience { get; set; } = 10;
    public double EarlyStoppingMinDelta { get; set; } = 0.001;
    public int CheckpointInterval { get; set; } = 10;
    public bool SaveBestModel { get; set; } = true;
    public Dictionary<string, object> AdditionalParams { get; set; } = new();
}

/// <summary>
/// 训练会话
/// </summary>
public class TrainingSession
{
    public string SessionId { get; set; } = string.Empty;
    public TrainingSessionConfig Config { get; set; } = new();
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public DateTime LastUpdateTime { get; set; }
    public TrainingStatus Status { get; set; }
    public int CurrentEpoch { get; set; }
    public int TotalEpochs { get; set; }
    public double Progress { get; set; }
    public string? FinalModelPath { get; set; }
    public string? ErrorMessage { get; set; }
    public long TotalTrainingTimeMs { get; set; }
    public TrainingMetrics Metrics { get; set; } = new();
    public List<TrainingLogEntry> Logs { get; set; } = new();
    public List<TrainingCheckpoint> Checkpoints { get; set; } = new();
}

/// <summary>
/// 训练状态
/// </summary>
public enum TrainingStatus
{
    Starting,
    Training,
    Completed,
    Failed,
    Stopped,
    EarlyStopped
}

/// <summary>
/// 训练指标
/// </summary>
public class TrainingMetrics
{
    public double CurrentTrainLoss { get; set; }
    public double CurrentValidationLoss { get; set; }
    public double CurrentTrainAccuracy { get; set; }
    public double CurrentValidationAccuracy { get; set; }
    public double CurrentLearningRate { get; set; }

    public double BestTrainLoss { get; set; } = double.MaxValue;
    public double BestValidationLoss { get; set; } = double.MaxValue;
    public double BestTrainAccuracy { get; set; }
    public double BestValidationAccuracy { get; set; }

    public List<double> TrainLossHistory { get; set; } = new();
    public List<double> ValidationLossHistory { get; set; } = new();
    public List<double> TrainAccuracyHistory { get; set; } = new();
    public List<double> ValidationAccuracyHistory { get; set; } = new();
    public List<double> LearningRateHistory { get; set; } = new();
}

/// <summary>
/// 训练进度更新
/// </summary>
public class TrainingProgressUpdate
{
    public int CurrentEpoch { get; set; }
    public int TotalEpochs { get; set; }
    public string? Message { get; set; }
    public Dictionary<string, double>? Metrics { get; set; }
}

/// <summary>
/// 训练完成结果
/// </summary>
public class TrainingCompletionResult
{
    public bool Success { get; set; }
    public string? ModelPath { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 训练日志条目
/// </summary>
public class TrainingLogEntry
{
    public DateTime Timestamp { get; set; }
    public string Level { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? Data { get; set; }
}

/// <summary>
/// 训练检查点
/// </summary>
public class TrainingCheckpoint
{
    public int Epoch { get; set; }
    public string Path { get; set; } = string.Empty;
    public DateTime SaveTime { get; set; }
    public double ValidationLoss { get; set; }
    public double ValidationAccuracy { get; set; }
}

/// <summary>
/// 训练报告
/// </summary>
public class TrainingReport
{
    public string SessionId { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TrainingStatus Status { get; set; }
    public long TotalTrainingTimeMs { get; set; }
    public TrainingSessionConfig Config { get; set; } = new();
    public TrainingMetrics FinalMetrics { get; set; } = new();
    public List<TrainingCheckpoint> Checkpoints { get; set; } = new();
    public object? LogSummary { get; set; }
}
