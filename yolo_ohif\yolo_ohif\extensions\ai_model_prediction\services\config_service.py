"""配置服务

提供配置管理和验证功能
"""

import logging
import json
import os
from typing import Any, Dict, List, Optional, Union, Type
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime

from ..core.interfaces import ConfigValidatorInterface
from ..core.exceptions import ConfigurationError, ValidationError
from ..core.types import ExtensionConfig, ModelConfig

logger = logging.getLogger(__name__)


@dataclass
class ConfigMetadata:
    """配置元数据"""
    version: str
    created_at: datetime
    updated_at: datetime
    source: str
    checksum: Optional[str] = None


class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate_model_config(config: Dict[str, Any]) -> ModelConfig:
        """验证模型配置"""
        try:
            # 必需字段检查
            required_fields = ['id', 'name', 'api_endpoint', 'supported_modalities']
            for field in required_fields:
                if field not in config:
                    raise ValidationError(f"缺少必需字段: {field}")
            
            # 类型检查
            if not isinstance(config['id'], str) or not config['id'].strip():
                raise ValidationError("模型ID必须是非空字符串")
            
            if not isinstance(config['name'], str) or not config['name'].strip():
                raise ValidationError("模型名称必须是非空字符串")
            
            if not isinstance(config['api_endpoint'], str) or not config['api_endpoint'].strip():
                raise ValidationError("API端点必须是非空字符串")
            
            if not isinstance(config['supported_modalities'], list) or not config['supported_modalities']:
                raise ValidationError("支持的模态必须是非空列表")
            
            # URL格式检查
            import re
            url_pattern = r'^https?://[\w\.-]+(?:\:[0-9]+)?(?:/.*)?$'
            if not re.match(url_pattern, config['api_endpoint']):
                raise ValidationError("API端点必须是有效的HTTP/HTTPS URL")
            
            # 阈值检查
            confidence_threshold = config.get('confidence_threshold', 0.5)
            if not isinstance(confidence_threshold, (int, float)) or not 0 <= confidence_threshold <= 1:
                raise ValidationError("置信度阈值必须是0-1之间的数值")
            
            iou_threshold = config.get('iou_threshold', 0.5)
            if not isinstance(iou_threshold, (int, float)) or not 0 <= iou_threshold <= 1:
                raise ValidationError("IoU阈值必须是0-1之间的数值")
            
            # 创建ModelConfig对象
            return ModelConfig(
                id=config['id'],
                name=config['name'],
                api_endpoint=config['api_endpoint'],
                supported_modalities=config['supported_modalities'],
                model_type=config.get('model_type', 'detection'),
                confidence_threshold=confidence_threshold,
                iou_threshold=iou_threshold,
                class_names=config.get('class_names', []),
                input_size=config.get('input_size', [640, 640]),
                preprocessing=config.get('preprocessing', {}),
                postprocessing=config.get('postprocessing', {}),
                metadata=config.get('metadata', {})
            )
            
        except Exception as e:
            if isinstance(e, ValidationError):
                raise
            raise ValidationError(f"模型配置验证失败: {str(e)}")
    
    @staticmethod
    def validate_extension_config(config: Dict[str, Any]) -> ExtensionConfig:
        """验证扩展配置"""
        try:
            # 验证模型配置
            models = []
            if 'models' in config:
                for model_config in config['models']:
                    models.append(ConfigValidator.validate_model_config(model_config))
            
            # 验证选项
            options = config.get('options', {})
            if not isinstance(options, dict):
                raise ValidationError("选项必须是字典类型")
            
            # 创建ExtensionConfig对象
            return ExtensionConfig(
                models=models,
                options=options
            )
            
        except Exception as e:
            if isinstance(e, ValidationError):
                raise
            raise ValidationError(f"扩展配置验证失败: {str(e)}")


class ConfigService(ConfigValidatorInterface):
    """配置服务
    
    提供配置加载、保存、验证和管理功能
    """
    
    def __init__(self, 
                 config_dir: Optional[str] = None,
                 auto_backup: bool = True,
                 max_backups: int = 5):
        """初始化配置服务
        
        Args:
            config_dir: 配置目录
            auto_backup: 是否自动备份
            max_backups: 最大备份数量
        """
        self._config_dir = Path(config_dir) if config_dir else Path.cwd() / 'config'
        self._auto_backup = auto_backup
        self._max_backups = max_backups
        self._configs: Dict[str, Any] = {}
        self._metadata: Dict[str, ConfigMetadata] = {}
        self._validator = ConfigValidator()
        
        # 确保配置目录存在
        self._config_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"配置服务初始化完成，配置目录: {self._config_dir}")
    
    def load_config(self, 
                    config_name: str, 
                    config_file: Optional[str] = None,
                    validate: bool = True) -> Dict[str, Any]:
        """加载配置
        
        Args:
            config_name: 配置名称
            config_file: 配置文件路径
            validate: 是否验证配置
            
        Returns:
            配置字典
        """
        try:
            if config_file is None:
                config_file = self._config_dir / f"{config_name}.json"
            else:
                config_file = Path(config_file)
            
            if not config_file.exists():
                raise ConfigurationError(f"配置文件不存在: {config_file}")
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 验证配置
            if validate:
                if config_name == 'extension':
                    self._validator.validate_extension_config(config_data)
                elif config_name.startswith('model_'):
                    self._validator.validate_model_config(config_data)
            
            # 保存到内存
            self._configs[config_name] = config_data
            
            # 保存元数据
            self._metadata[config_name] = ConfigMetadata(
                version="1.0",
                created_at=datetime.fromtimestamp(config_file.stat().st_ctime),
                updated_at=datetime.fromtimestamp(config_file.stat().st_mtime),
                source=str(config_file),
                checksum=self._calculate_checksum(config_data)
            )
            
            logger.info(f"配置加载成功: {config_name}")
            return config_data
            
        except Exception as e:
            if isinstance(e, (ConfigurationError, ValidationError)):
                raise
            raise ConfigurationError(f"加载配置失败: {str(e)}")
    
    def save_config(self, 
                    config_name: str, 
                    config_data: Dict[str, Any],
                    config_file: Optional[str] = None,
                    validate: bool = True) -> None:
        """保存配置
        
        Args:
            config_name: 配置名称
            config_data: 配置数据
            config_file: 配置文件路径
            validate: 是否验证配置
        """
        try:
            # 验证配置
            if validate:
                if config_name == 'extension':
                    self._validator.validate_extension_config(config_data)
                elif config_name.startswith('model_'):
                    self._validator.validate_model_config(config_data)
            
            if config_file is None:
                config_file = self._config_dir / f"{config_name}.json"
            else:
                config_file = Path(config_file)
            
            # 自动备份
            if self._auto_backup and config_file.exists():
                self._create_backup(config_file)
            
            # 确保目录存在
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            # 更新内存和元数据
            self._configs[config_name] = config_data
            self._metadata[config_name] = ConfigMetadata(
                version="1.0",
                created_at=self._metadata.get(config_name, ConfigMetadata(
                    version="1.0",
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                    source=str(config_file)
                )).created_at,
                updated_at=datetime.now(),
                source=str(config_file),
                checksum=self._calculate_checksum(config_data)
            )
            
            logger.info(f"配置保存成功: {config_name}")
            
        except Exception as e:
            if isinstance(e, (ConfigurationError, ValidationError)):
                raise
            raise ConfigurationError(f"保存配置失败: {str(e)}")
    
    def get_config(self, config_name: str) -> Optional[Dict[str, Any]]:
        """获取配置"""
        return self._configs.get(config_name)
    
    def update_config(self, 
                      config_name: str, 
                      updates: Dict[str, Any],
                      validate: bool = True) -> None:
        """更新配置
        
        Args:
            config_name: 配置名称
            updates: 更新数据
            validate: 是否验证配置
        """
        if config_name not in self._configs:
            raise ConfigurationError(f"配置不存在: {config_name}")
        
        # 深度合并配置
        updated_config = self._deep_merge(self._configs[config_name], updates)
        
        # 保存更新后的配置
        self.save_config(config_name, updated_config, validate=validate)
    
    def delete_config(self, config_name: str) -> None:
        """删除配置"""
        if config_name in self._configs:
            del self._configs[config_name]
        
        if config_name in self._metadata:
            del self._metadata[config_name]
        
        # 删除配置文件
        config_file = self._config_dir / f"{config_name}.json"
        if config_file.exists():
            config_file.unlink()
        
        logger.info(f"配置删除成功: {config_name}")
    
    def list_configs(self) -> List[str]:
        """列出所有配置"""
        return list(self._configs.keys())
    
    def get_config_metadata(self, config_name: str) -> Optional[ConfigMetadata]:
        """获取配置元数据"""
        return self._metadata.get(config_name)
    
    def validate_config(self, config_name: str, config_data: Dict[str, Any]) -> bool:
        """验证配置"""
        try:
            if config_name == 'extension':
                self._validator.validate_extension_config(config_data)
            elif config_name.startswith('model_'):
                self._validator.validate_model_config(config_data)
            return True
        except ValidationError:
            return False
    
    def export_config(self, config_name: str, export_file: str) -> None:
        """导出配置"""
        if config_name not in self._configs:
            raise ConfigurationError(f"配置不存在: {config_name}")
        
        export_path = Path(export_file)
        export_path.parent.mkdir(parents=True, exist_ok=True)
        
        export_data = {
            'config': self._configs[config_name],
            'metadata': asdict(self._metadata[config_name]) if config_name in self._metadata else None,
            'exported_at': datetime.now().isoformat()
        }
        
        with open(export_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"配置导出成功: {config_name} -> {export_file}")
    
    def import_config(self, config_name: str, import_file: str, validate: bool = True) -> None:
        """导入配置"""
        import_path = Path(import_file)
        if not import_path.exists():
            raise ConfigurationError(f"导入文件不存在: {import_file}")
        
        with open(import_path, 'r', encoding='utf-8') as f:
            import_data = json.load(f)
        
        if 'config' not in import_data:
            raise ConfigurationError("导入文件格式错误，缺少config字段")
        
        self.save_config(config_name, import_data['config'], validate=validate)
        logger.info(f"配置导入成功: {import_file} -> {config_name}")
    
    def get_default_extension_config(self) -> Dict[str, Any]:
        """获取默认扩展配置"""
        return {
            "models": [
                {
                    "id": "yolo_chest_xray",
                    "name": "YOLO胸部X光检测",
                    "api_endpoint": "http://localhost:8000/predict",
                    "supported_modalities": ["CR", "DX"],
                    "model_type": "detection",
                    "confidence_threshold": 0.5,
                    "iou_threshold": 0.5,
                    "class_names": ["肺炎", "肺结节", "气胸"],
                    "input_size": [640, 640],
                    "preprocessing": {
                        "normalize": True,
                        "resize_mode": "letterbox"
                    },
                    "postprocessing": {
                        "nms_threshold": 0.5,
                        "max_detections": 100
                    },
                    "metadata": {
                        "version": "1.0",
                        "description": "基于YOLO的胸部X光异常检测模型",
                        "author": "AI团队"
                    }
                }
            ],
            "options": {
                "auto_detection": True,
                "show_confidence": True,
                "enable_caching": True,
                "cache_ttl": 3600,
                "max_concurrent_requests": 5,
                "request_timeout": 30,
                "retry_attempts": 3,
                "ui_theme": "default",
                "language": "zh-CN"
            }
        }
    
    def _create_backup(self, config_file: Path) -> None:
        """创建配置备份"""
        try:
            backup_dir = config_file.parent / 'backups'
            backup_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = backup_dir / f"{config_file.stem}_{timestamp}.json"
            
            import shutil
            shutil.copy2(config_file, backup_file)
            
            # 清理旧备份
            self._cleanup_old_backups(backup_dir, config_file.stem)
            
            logger.debug(f"配置备份创建: {backup_file}")
            
        except Exception as e:
            logger.warning(f"创建配置备份失败: {e}")
    
    def _cleanup_old_backups(self, backup_dir: Path, config_name: str) -> None:
        """清理旧备份"""
        try:
            backup_files = list(backup_dir.glob(f"{config_name}_*.json"))
            backup_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
            
            # 删除超出限制的备份
            for backup_file in backup_files[self._max_backups:]:
                backup_file.unlink()
                logger.debug(f"删除旧备份: {backup_file}")
                
        except Exception as e:
            logger.warning(f"清理旧备份失败: {e}")
    
    def _deep_merge(self, base: Dict[str, Any], updates: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并字典"""
        result = base.copy()
        
        for key, value in updates.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _calculate_checksum(self, data: Any) -> str:
        """计算数据校验和"""
        import hashlib
        json_str = json.dumps(data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(json_str.encode('utf-8')).hexdigest()
    
    def cleanup(self) -> None:
        """清理配置服务"""
        self._configs.clear()
        self._metadata.clear()
        logger.info("配置服务已清理")