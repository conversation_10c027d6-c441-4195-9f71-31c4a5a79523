# 标注类别管理功能说明

## 功能概述

为智能标注系统添加了完整的标注类别管理功能，允许用户添加自定义类别和删除已存在的标注类型，同时保护系统预设类别不被误删。

## 新增功能

### 1. 标注类别删除功能

#### 核心特性
- ✅ **智能保护**：系统预设类别无法删除，只能删除用户自定义类别
- ✅ **关联检查**：删除类别前检查是否有标注使用该类别
- ✅ **批量清理**：删除类别时可选择同时删除相关标注
- ✅ **安全确认**：删除操作需要用户确认

#### 系统预设类别（受保护）
- 病灶区域 🟢
- 正常组织 🔵
- 骨折 🟠
- 肿瘤 🔴
- 血管 🔵
- 其他 🟡

### 2. 可视化类别管理界面

#### 类别管理面板
- ✅ **类别列表显示**：显示所有可用类别
- ✅ **颜色指示器**：每个类别显示对应的颜色
- ✅ **类型标识**：区分"系统"和"自定义"类别
- ✅ **交互选择**：点击列表项自动选择对应类别

#### 增强的操作界面
- ✅ **并排按钮**：添加类别和删除类别按钮并排显示
- ✅ **操作提示**：清晰的使用说明和限制提示
- ✅ **实时更新**：操作后自动刷新类别列表

### 3. 智能类别验证

#### 添加类别验证
- ✅ **空值检查**：防止添加空白类别
- ✅ **重复检查**：防止添加已存在的类别
- ✅ **自动选择**：添加后自动选择新类别

#### 删除类别验证
- ✅ **系统类别保护**：无法删除系统预设类别
- ✅ **使用情况检查**：检查类别是否被标注使用
- ✅ **关联标注处理**：提供删除相关标注的选项

## 技术实现

### 1. 系统类别保护机制

```csharp
// 初始化系统预设类别
_systemCategories = new HashSet<string>
{
    "病灶区域", "正常组织", "骨折", "肿瘤", "血管", "其他"
};

// 删除时检查保护
if (!string.IsNullOrEmpty(categoryName) && _systemCategories.Contains(categoryName))
{
    MessageBox.Show($"无法删除系统预设类别 '{categoryName}'", "提示",
                  MessageBoxButton.OK, MessageBoxImage.Warning);
    return;
}
```

### 2. 关联标注检查机制

```csharp
// 检查是否有使用该类别的标注
var annotationsWithCategory = _annotations.Where(a => a.Category == categoryName).ToList();
if (annotationsWithCategory.Any())
{
    var result = MessageBox.Show(
        $"类别 '{categoryName}' 正在被 {annotationsWithCategory.Count} 个标注使用。\n删除类别将同时删除这些标注，是否继续？",
        "确认删除",
        MessageBoxButton.YesNo,
        MessageBoxImage.Question);
}
```

### 3. 可视化类别信息模型

```csharp
public class CategoryInfo : INotifyPropertyChanged
{
    public string Name { get; set; }           // 类别名称
    public bool IsSystemCategory { get; set; } // 是否为系统类别
    public string TypeLabel { get; set; }      // 类型标签（"系统"/"自定义"）
    public Color ColorIndicator { get; set; }  // 颜色指示器
    public Color BorderColor { get; set; }     // 边框颜色
}
```

### 4. 动态颜色映射

```csharp
private Color GetCategoryColor(string category)
{
    return category.ToLowerInvariant() switch
    {
        "病灶区域" or "病灶" or "lesion" => Colors.Green,
        "肿瘤" or "tumor" => Colors.Red,
        "骨折" or "fracture" => Colors.Orange,
        "血管" or "vessel" => Colors.Blue,
        "正常组织" or "normal" => Colors.LightBlue,
        _ => Colors.Yellow
    };
}
```

## 界面设计

### 1. 标注类别区域

```xml
<!-- 标注类别选择 -->
<ComboBox x:Name="AnnotationCategoryComboBox">
    <ComboBoxItem Content="病灶区域"/>
    <ComboBoxItem Content="正常组织"/>
    <!-- ... 其他预设类别 -->
</ComboBox>

<!-- 自定义类别输入 -->
<TextBox x:Name="CustomCategoryTextBox" 
         materialDesign:HintAssist.Hint="自定义类别"/>

<!-- 操作按钮 -->
<Grid>
    <Button Content="添加类别" Click="AddCategory_Click"/>
    <Button Content="删除类别" Click="RemoveCategory_Click"/>
</Grid>
```

### 2. 类别管理面板

```xml
<!-- 类别管理展开面板 -->
<Expander Header="类别管理">
    <ListBox x:Name="CategoryListBox">
        <ListBox.ItemTemplate>
            <DataTemplate>
                <Grid>
                    <!-- 颜色指示器 -->
                    <Rectangle Fill="{Binding ColorIndicator}" 
                             Stroke="{Binding BorderColor}"/>
                    <!-- 类别名称 -->
                    <TextBlock Text="{Binding Name}"/>
                    <!-- 类型标识 -->
                    <TextBlock Text="{Binding TypeLabel}"/>
                </Grid>
            </DataTemplate>
        </ListBox.ItemTemplate>
    </ListBox>
</Expander>
```

## 使用方法

### 1. 添加自定义类别

1. **输入类别名称**：
   - 在"自定义类别"文本框中输入新类别名称
   - 例如：输入"血管瘤"

2. **添加类别**：
   - 点击"添加类别"按钮
   - 系统验证类别名称（非空、不重复）
   - 成功后自动选择新类别

3. **使用新类别**：
   - 新类别自动出现在下拉列表中
   - 可以立即用于标注
   - 根据名称自动分配颜色

### 2. 删除自定义类别

1. **选择要删除的类别**：
   - 在下拉框中选择要删除的类别
   - 或在类别管理面板中点击选择

2. **执行删除操作**：
   - 点击"删除类别"按钮
   - 系统检查是否为系统预设类别

3. **处理关联标注**：
   - 如果有标注使用该类别，系统会提示
   - 选择是否同时删除相关标注
   - 确认后执行删除操作

### 3. 查看类别信息

1. **打开类别管理面板**：
   - 展开"类别管理"区域
   - 查看所有可用类别列表

2. **查看类别详情**：
   - 每个类别显示颜色指示器
   - 显示类型标识（系统/自定义）
   - 点击可快速选择类别

3. **刷新类别列表**：
   - 点击"刷新类别列表"按钮
   - 更新显示最新的类别信息

## 安全机制

### 1. 系统类别保护

- **无法删除**：系统预设的6个类别无法删除
- **明确提示**：尝试删除时显示保护提示
- **标识区分**：在类别管理中明确标识"系统"类别

### 2. 数据完整性保护

- **关联检查**：删除前检查标注使用情况
- **批量处理**：可选择同时删除相关标注
- **操作确认**：重要操作需要用户确认

### 3. 输入验证

- **空值防护**：防止添加空白类别名称
- **重复检查**：防止添加已存在的类别
- **格式验证**：确保类别名称格式正确

## 错误处理

### 1. 添加类别错误

```csharp
// 空值检查
if (string.IsNullOrEmpty(customCategory))
{
    MessageBox.Show("请输入类别名称", "提示");
    return;
}

// 重复检查
if (categoryExists)
{
    MessageBox.Show($"类别 '{customCategory}' 已存在", "提示");
    return;
}
```

### 2. 删除类别错误

```csharp
// 系统类别保护
if (_systemCategories.Contains(categoryName))
{
    MessageBox.Show($"无法删除系统预设类别 '{categoryName}'", "提示");
    return;
}

// 未选择类别
if (selectedItem == null)
{
    MessageBox.Show("请先选择要删除的类别", "提示");
    return;
}
```

## 用户体验优化

### 1. 直观的视觉反馈

- **颜色指示器**：每个类别都有对应的颜色显示
- **类型标识**：清楚区分系统和自定义类别
- **实时更新**：操作后立即更新界面

### 2. 友好的操作提示

- **操作说明**：清晰的使用提示和限制说明
- **确认对话框**：重要操作前的确认提示
- **状态反馈**：操作结果的及时反馈

### 3. 便捷的交互设计

- **并排按钮**：添加和删除按钮便于操作
- **快速选择**：点击列表项快速选择类别
- **自动刷新**：操作后自动更新显示

## 扩展性设计

### 1. 易于扩展的类别系统

- **动态颜色映射**：支持新类别的颜色自动分配
- **多语言支持**：支持中英文类别名称识别
- **插件化设计**：便于添加新的类别管理功能

### 2. 数据持久化准备

- **结构化数据**：类别信息使用结构化模型
- **序列化支持**：支持类别配置的保存和加载
- **版本兼容**：设计考虑了向后兼容性

这个类别管理功能大大提升了智能标注系统的灵活性和专业性，用户可以根据具体需求自定义标注类别，同时系统确保了核心功能的稳定性。
