# 医学影像AI模型训练教程

## 📋 目录
1. [训练系统概述](#训练系统概述)
2. [环境准备](#环境准备)
3. [数据准备](#数据准备)
4. [YOLO模型训练](#yolo模型训练)
5. [WPF界面训练](#wpf界面训练)
6. [训练监控](#训练监控)
7. [模型评估](#模型评估)
8. [模型部署](#模型部署)
9. [故障排除](#故障排除)

## 🎯 训练系统概述

医学影像AI模型训练系统支持多种训练方式：

### 训练方式
1. **WPF界面训练**: 通过图形界面进行模型训练
2. **Python脚本训练**: 使用专业的Python训练脚本
3. **命令行训练**: 通过命令行参数进行批量训练

### 支持的模型
- **YOLO11**: 最新的目标检测模型
- **自定义模型**: 支持自定义网络架构
- **预训练模型**: 基于预训练权重的微调

### 训练特性
- **多GPU支持**: 支持单GPU和多GPU训练
- **混合精度**: 支持FP16混合精度训练
- **数据增强**: 丰富的数据增强策略
- **实时监控**: 训练过程实时监控和可视化

## 🛠️ 环境准备

### 系统要求
- **操作系统**: Windows 10/11 (x64)
- **.NET Runtime**: .NET 8.0 或更高版本
- **Python**: Python 3.8+ (用于YOLO训练)
- **内存**: 至少 8GB RAM (推荐 16GB+)
- **存储**: 至少 10GB 可用空间
- **显卡**: NVIDIA GPU (推荐，支持CUDA)

### 安装Python环境

#### 1. 安装Python
从官网下载并安装Python 3.8+：
```
https://www.python.org/downloads/
```

#### 2. 安装YOLO依赖
打开命令提示符，运行：
```bash
pip install ultralytics
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install opencv-python
pip install pillow
pip install pyyaml
pip install tqdm
```

#### 3. 验证安装
```python
import torch
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"GPU数量: {torch.cuda.device_count()}")
    print(f"GPU名称: {torch.cuda.get_device_name(0)}")
```

### 验证.NET环境
```cmd
dotnet --version
```
应显示 8.0.x 或更高版本。

## 📊 数据准备

### 1. 数据集结构

#### 标准YOLO数据集结构
```
dataset/
├── images/
│   ├── train/          # 训练图像
│   ├── val/            # 验证图像
│   └── test/           # 测试图像
├── labels/
│   ├── train/          # 训练标签
│   ├── val/            # 验证标签
│   └── test/           # 测试标签
└── dataset.yaml        # 数据集配置文件
```

#### dataset.yaml 配置示例
```yaml
# 数据集路径
path: ./dataset
train: images/train
val: images/val
test: images/test

# 类别数量
nc: 1

# 类别名称
names:
  0: lesion
```

### 2. 数据标注格式

#### YOLO格式标注
每个图像对应一个.txt文件，格式为：
```
class_id center_x center_y width height
```

示例：
```
0 0.5 0.5 0.3 0.4
```

#### 坐标说明
- 所有坐标都是归一化的（0-1之间）
- center_x, center_y: 边界框中心点坐标
- width, height: 边界框宽度和高度

### 3. 数据集创建

#### 使用智能标注系统创建
1. 启动智能标注模块
2. 加载医学影像
3. 进行手动或AI辅助标注
4. 导出为YOLO格式

#### 使用Python脚本创建
项目提供了数据集创建脚本：
```bash
cd yolo_ohif/yolo_ohif
python create_yolo_dataset.py
```

## 🚀 YOLO模型训练

### 1. 使用Python脚本训练

#### 从头训练YOLO11x
```bash
cd yolo_ohif/yolo_ohif
python start_yolo11x_training.py
```

#### 使用预训练权重训练
```bash
cd yolo_ohif/yolo_ohif
python start_yolo11x_pretrained_training.py
```

#### 512×512医学图像专用训练
```bash
cd yolo_ohif/yolo_ohif
python start_yolo11x_training_512.py
```

### 2. 训练参数配置

#### 基础参数
```python
# 训练轮数
epochs = 200

# 批次大小
batch_size = 16

# 学习率
learning_rate = 0.01

# 图像尺寸
img_size = 640
```

#### 高级参数
```python
# 优化器设置
optimizer = 'AdamW'
weight_decay = 0.0005
momentum = 0.937

# 学习率调度
warmup_epochs = 5
warmup_momentum = 0.8

# 数据增强
hsv_h = 0.015
hsv_s = 0.7
hsv_v = 0.4
degrees = 0.0
translate = 0.1
scale = 0.5
```

### 3. 自定义训练脚本

#### 创建训练配置
```python
from ultralytics import YOLO

# 加载模型
model = YOLO('yolo11x.pt')  # 或 'yolo11x.yaml' 从头训练

# 训练参数
train_args = {
    'data': 'dataset.yaml',
    'epochs': 100,
    'batch': 16,
    'imgsz': 640,
    'device': 'auto',
    'workers': 4,
    'project': 'runs/train',
    'name': 'medical_detection',
    'patience': 20,
    'save_period': 10,
    'optimizer': 'AdamW',
    'lr0': 0.01,
    'weight_decay': 0.0005,
}

# 开始训练
results = model.train(**train_args)
```

## 🖥️ WPF界面训练

### 1. 启动训练界面

#### 进入模型训练模块
1. 启动应用程序
2. 点击左侧导航栏的 **"模型训练"** 按钮
3. 系统将打开模型训练界面

### 2. 配置训练参数

#### 基础设置
1. **模型类型**: 选择YOLO11或自定义模型
2. **数据集路径**: 选择准备好的数据集目录
3. **输出目录**: 设置训练结果保存位置

#### 训练参数
1. **训练轮数**: 设置epoch数量（推荐100-200）
2. **批次大小**: 根据GPU内存设置（推荐16-32）
3. **学习率**: 设置初始学习率（推荐0.01）
4. **图像尺寸**: 设置输入图像尺寸（推荐640或512）

#### 高级选项
1. **使用预训练权重**: 勾选以使用预训练模型
2. **数据增强**: 启用数据增强策略
3. **混合精度**: 启用FP16混合精度训练
4. **早停**: 设置早停耐心值

### 3. 开始训练

#### 启动训练
1. 配置完所有参数后，点击 **"开始训练"** 按钮
2. 系统会验证配置并开始训练
3. 训练过程中可以实时查看进度

#### 监控训练
1. **进度条**: 显示当前epoch进度和总体进度
2. **损失曲线**: 实时显示训练和验证损失
3. **指标显示**: 显示精度、召回率等指标
4. **日志输出**: 显示详细的训练日志

#### 停止训练
1. 点击 **"停止训练"** 按钮可以随时停止
2. 系统会保存当前的训练状态
3. 可以选择从断点继续训练

## 📈 训练监控

### 1. 实时监控

#### 训练指标
- **损失函数**: 训练损失和验证损失
- **精度指标**: mAP@0.5, mAP@0.5:0.95
- **学习率**: 当前学习率变化
- **GPU使用率**: GPU内存和计算使用率

#### 可视化图表
- **损失曲线**: 训练过程中的损失变化
- **精度曲线**: 模型精度随时间的变化
- **学习率曲线**: 学习率调度可视化

### 2. 日志记录

#### 训练日志
训练日志保存在：
```
runs/train/medical_detection/
├── weights/
│   ├── best.pt         # 最佳模型权重
│   ├── last.pt         # 最后一次训练权重
│   └── epoch_*.pt      # 定期保存的权重
├── results.png         # 训练结果图表
├── confusion_matrix.png # 混淆矩阵
├── labels.jpg          # 标签分布
├── train_batch*.jpg    # 训练批次示例
├── val_batch*.jpg      # 验证批次示例
└── args.yaml           # 训练参数
```

#### 应用程序日志
WPF应用程序日志位置：
```
src/MedicalImageAnalysis.Wpf/bin/Debug/net8.0-windows/logs/
└── medical-image-analysis-YYYYMMDD.txt
```

### 3. TensorBoard监控

#### 启动TensorBoard
```bash
tensorboard --logdir runs/train
```

#### 查看训练过程
1. 打开浏览器访问 http://localhost:6006
2. 查看详细的训练指标和图表
3. 分析模型性能和收敛情况

## 🎯 模型评估

### 1. 自动评估

#### 训练完成后评估
训练完成后，系统会自动进行模型评估：
- **验证集评估**: 在验证集上计算各项指标
- **测试集评估**: 在测试集上进行最终评估
- **混淆矩阵**: 生成分类混淆矩阵
- **PR曲线**: 精确率-召回率曲线

#### 评估指标
- **mAP@0.5**: IoU阈值0.5时的平均精度
- **mAP@0.5:0.95**: IoU阈值0.5-0.95的平均精度
- **Precision**: 精确率
- **Recall**: 召回率
- **F1-Score**: F1分数

### 2. 手动评估

#### 使用Python脚本评估
```python
from ultralytics import YOLO

# 加载训练好的模型
model = YOLO('runs/train/medical_detection/weights/best.pt')

# 在验证集上评估
results = model.val(data='dataset.yaml')

# 打印评估结果
print(f"mAP@0.5: {results.box.map50}")
print(f"mAP@0.5:0.95: {results.box.map}")
```

#### 单张图像推理测试
```python
# 对单张图像进行推理
results = model('test_image.jpg')

# 显示结果
results[0].show()

# 保存结果
results[0].save('result.jpg')
```

### 3. 性能分析

#### 推理速度测试
```python
import time
import torch

model = YOLO('best.pt')
model.to('cuda' if torch.cuda.is_available() else 'cpu')

# 预热
for _ in range(10):
    model('test_image.jpg')

# 测试推理速度
start_time = time.time()
for _ in range(100):
    results = model('test_image.jpg')
end_time = time.time()

avg_time = (end_time - start_time) / 100
fps = 1 / avg_time
print(f"平均推理时间: {avg_time:.3f}s")
print(f"FPS: {fps:.1f}")
```

## 🚀 模型部署

### 1. 模型导出

#### 导出为ONNX格式
```python
from ultralytics import YOLO

# 加载模型
model = YOLO('best.pt')

# 导出为ONNX
model.export(format='onnx', dynamic=True)
```

#### 导出为TensorRT
```python
# 导出为TensorRT（需要TensorRT环境）
model.export(format='engine', device=0)
```

### 2. 集成到WPF应用

#### 模型加载
训练完成的模型会自动添加到WPF应用的模型列表中：
1. 在智能标注模块中可以选择使用训练好的模型
2. 系统会自动加载模型权重
3. 可以调整推理参数（置信度阈值等）

#### 实时推理
1. 加载医学影像
2. 选择训练好的模型
3. 系统会自动进行推理并显示结果
4. 可以手动调整和确认AI生成的标注

### 3. 批量推理

#### 批量处理脚本
```python
import os
from ultralytics import YOLO

# 加载模型
model = YOLO('best.pt')

# 批量推理
input_dir = 'input_images/'
output_dir = 'output_results/'

for image_file in os.listdir(input_dir):
    if image_file.endswith(('.jpg', '.png', '.dcm')):
        image_path = os.path.join(input_dir, image_file)
        results = model(image_path)
        
        # 保存结果
        output_path = os.path.join(output_dir, f'result_{image_file}')
        results[0].save(output_path)
```

## 🔧 故障排除

### 常见问题

#### 1. CUDA内存不足
**错误**: RuntimeError: CUDA out of memory
**解决方案**:
1. 减小批次大小（batch_size）
2. 减小图像尺寸（imgsz）
3. 启用梯度累积
4. 使用混合精度训练

#### 2. 数据集格式错误
**错误**: Dataset not found or format incorrect
**解决方案**:
1. 检查dataset.yaml文件路径
2. 确认图像和标签文件对应
3. 验证标注格式是否正确
4. 检查类别数量和名称

#### 3. 训练不收敛
**问题**: 损失不下降或精度不提升
**解决方案**:
1. 调整学习率（尝试更小的值）
2. 增加训练轮数
3. 检查数据质量和标注准确性
4. 尝试不同的数据增强策略

#### 4. 模型过拟合
**问题**: 训练精度高但验证精度低
**解决方案**:
1. 增加数据增强
2. 使用正则化技术
3. 减小模型复杂度
4. 增加训练数据

### 性能优化

#### 训练速度优化
1. **使用SSD存储**: 将数据集存储在SSD上
2. **增加workers**: 提高数据加载并行度
3. **混合精度**: 启用FP16训练
4. **批次大小**: 根据GPU内存调整到最大

#### 内存优化
1. **数据缓存**: 启用数据缓存功能
2. **梯度累积**: 使用梯度累积模拟大批次
3. **模型剪枝**: 训练后进行模型剪枝
4. **量化**: 使用模型量化减少内存占用

## 📞 技术支持

### 获取帮助
1. **查看日志**: 首先查看训练日志和错误信息
2. **检查环境**: 验证Python和CUDA环境
3. **重新训练**: 尝试使用不同的参数重新训练
4. **联系支持**: 创建GitHub Issue或联系开发团队

### 最佳实践
1. **数据质量**: 确保标注数据的质量和一致性
2. **参数调优**: 根据具体任务调整训练参数
3. **定期保存**: 设置定期保存检查点
4. **监控训练**: 实时监控训练过程和指标

## 🎓 实战案例

### 案例1：肺部结节检测模型训练

#### 数据准备
1. 收集肺部CT图像（DICOM格式）
2. 标注肺部结节区域
3. 数据集划分：训练70%，验证20%，测试10%

#### 训练配置
```python
train_args = {
    'data': 'lung_nodule_dataset.yaml',
    'epochs': 150,
    'batch': 24,
    'imgsz': 512,  # 肺部图像推荐512×512
    'device': 'auto',
    'optimizer': 'AdamW',
    'lr0': 0.005,  # 医学图像推荐较小学习率
    'weight_decay': 0.0005,
    'patience': 25,
    'save_period': 10,
    # 医学图像专用数据增强
    'hsv_h': 0.01,
    'hsv_s': 0.3,
    'hsv_v': 0.2,
    'degrees': 5.0,
    'translate': 0.05,
    'scale': 0.3,
    'fliplr': 0.5,
    'mosaic': 0.8,
}
```

#### 预期结果
- mAP@0.5: 85%+
- 推理速度: 50+ FPS
- 模型大小: <100MB

### 案例2：骨折检测模型训练

#### 数据特点
- X光图像，通常为灰度图
- 骨折区域较小，需要高分辨率
- 类别不平衡问题

#### 训练策略
```python
# 针对类别不平衡的配置
train_args = {
    'data': 'fracture_dataset.yaml',
    'epochs': 200,
    'batch': 16,
    'imgsz': 640,
    'cls': 1.0,  # 增加分类损失权重
    'box': 7.5,
    'dfl': 1.5,
    'label_smoothing': 0.1,  # 标签平滑
    'copy_paste': 0.3,  # 复制粘贴增强
}
```

## 🔬 高级训练技巧

### 1. 多尺度训练
```python
# 多尺度训练配置
train_args = {
    'multiscale': True,
    'imgsz': 640,
    'scale': 0.5,  # 尺度变化范围
}
```

### 2. 知识蒸馏
```python
# 使用大模型指导小模型训练
teacher_model = YOLO('yolo11x.pt')
student_model = YOLO('yolo11n.pt')

# 实现知识蒸馏训练逻辑
```

### 3. 渐进式训练
```python
# 第一阶段：低分辨率训练
model.train(data='dataset.yaml', epochs=50, imgsz=320)

# 第二阶段：高分辨率微调
model.train(data='dataset.yaml', epochs=50, imgsz=640, resume=True)
```

## 📊 训练结果分析

### 1. 损失函数分析
- **Box Loss**: 边界框回归损失，应该稳定下降
- **Class Loss**: 分类损失，反映分类准确性
- **DFL Loss**: 分布焦点损失，提高定位精度

### 2. 指标解读
- **Precision**: 预测为正例中实际为正例的比例
- **Recall**: 实际正例中被正确预测的比例
- **mAP**: 平均精度，综合评估模型性能

### 3. 结果可视化
```python
# 绘制训练曲线
import matplotlib.pyplot as plt
import pandas as pd

# 读取训练结果
results = pd.read_csv('runs/train/exp/results.csv')

# 绘制损失曲线
plt.figure(figsize=(12, 4))
plt.subplot(1, 3, 1)
plt.plot(results['epoch'], results['train/box_loss'], label='Train')
plt.plot(results['epoch'], results['val/box_loss'], label='Val')
plt.title('Box Loss')
plt.legend()

plt.subplot(1, 3, 2)
plt.plot(results['epoch'], results['train/cls_loss'], label='Train')
plt.plot(results['epoch'], results['val/cls_loss'], label='Val')
plt.title('Class Loss')
plt.legend()

plt.subplot(1, 3, 3)
plt.plot(results['epoch'], results['metrics/mAP50'])
plt.title('mAP@0.5')

plt.tight_layout()
plt.show()
```

## 🛠️ 自定义训练脚本

### 完整训练脚本示例
```python
#!/usr/bin/env python3
"""
医学影像YOLO模型训练脚本
"""

import os
import yaml
import torch
from ultralytics import YOLO
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MedicalYOLOTrainer:
    def __init__(self, config_path):
        """初始化训练器"""
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)

        self.model = None
        self.results = None

    def setup_model(self):
        """设置模型"""
        model_path = self.config.get('model', 'yolo11x.pt')
        self.model = YOLO(model_path)
        logger.info(f"模型加载完成: {model_path}")

    def train(self):
        """开始训练"""
        if not self.model:
            self.setup_model()

        # 训练参数
        train_args = self.config.get('train_args', {})

        logger.info("开始训练...")
        logger.info(f"训练参数: {train_args}")

        # 开始训练
        self.results = self.model.train(**train_args)

        logger.info("训练完成!")
        return self.results

    def evaluate(self):
        """评估模型"""
        if not self.model:
            logger.error("模型未训练")
            return None

        # 在验证集上评估
        val_results = self.model.val()

        logger.info(f"验证结果:")
        logger.info(f"mAP@0.5: {val_results.box.map50:.3f}")
        logger.info(f"mAP@0.5:0.95: {val_results.box.map:.3f}")

        return val_results

    def export_model(self, format='onnx'):
        """导出模型"""
        if not self.model:
            logger.error("模型未训练")
            return None

        export_path = self.model.export(format=format)
        logger.info(f"模型已导出: {export_path}")
        return export_path

# 使用示例
if __name__ == "__main__":
    # 创建配置文件
    config = {
        'model': 'yolo11x.pt',
        'train_args': {
            'data': 'dataset.yaml',
            'epochs': 100,
            'batch': 16,
            'imgsz': 640,
            'device': 'auto',
            'project': 'runs/train',
            'name': 'medical_detection',
            'patience': 20,
            'save_period': 10,
            'optimizer': 'AdamW',
            'lr0': 0.01,
            'weight_decay': 0.0005,
        }
    }

    # 保存配置
    with open('train_config.yaml', 'w') as f:
        yaml.dump(config, f)

    # 开始训练
    trainer = MedicalYOLOTrainer('train_config.yaml')
    results = trainer.train()
    val_results = trainer.evaluate()
    export_path = trainer.export_model()
```

---

**注意事项**:
- 训练需要大量计算资源，建议使用GPU
- 确保数据集质量，这是模型性能的关键
- 定期备份训练结果和模型权重
- 本系统仅用于研究和教育目的
- 医学影像训练需要专业知识，建议与医学专家合作
