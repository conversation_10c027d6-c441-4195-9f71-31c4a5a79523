# 智能标注系统颜色分类功能说明

## 功能概述

为智能标注系统添加了基于标注类别的颜色分类功能，特别是为病灶区域使用绿色矩形框进行标记，提高了医学影像标注的专业性和可视化效果。

## 新增功能

### 1. 智能颜色分类系统

根据不同的标注类别，系统会自动使用相应的颜色进行标注：

| 标注类别 | 颜色 | 医学意义 | 适用场景 |
|---------|------|----------|----------|
| **病灶区域** | 🟢 绿色 | 病理性改变区域 | 肿瘤、炎症、感染等病变 |
| **肿瘤** | 🔴 红色 | 恶性或良性肿瘤 | 各种肿瘤性病变 |
| **骨折** | 🟠 橙色 | 骨骼断裂 | 外伤性骨折、病理性骨折 |
| **血管** | 🔵 蓝色 | 血管结构 | 动脉、静脉、血管异常 |
| **正常组织** | 🔵 浅蓝色 | 正常解剖结构 | 对照区域、正常组织 |
| **其他** | 🟡 黄色 | 其他类型标注 | 自定义类别 |

### 2. 可视化颜色说明面板

在右侧控制面板中添加了"标注颜色说明"区域，用户可以直观地了解每种颜色对应的标注类别。

### 3. 智能状态提示

完成标注时，系统会显示包含颜色信息的状态提示，例如：
- "已添加 绿色矩形 标注 - 病灶区域"
- "已添加 红色圆形 标注 - 肿瘤"

## 技术实现

### 1. 颜色映射系统

```csharp
/// <summary>
/// 根据标注类别获取颜色
/// </summary>
private Brush GetAnnotationColor()
{
    var category = AnnotationCategoryComboBox.Text?.Trim() ?? "";
    
    return category.ToLowerInvariant() switch
    {
        "病灶区域" or "病灶" or "lesion" => Brushes.Green,
        "肿瘤" or "tumor" => Brushes.Red,
        "骨折" or "fracture" => Brushes.Orange,
        "血管" or "vessel" => Brushes.Blue,
        "正常组织" or "normal" => Brushes.LightBlue,
        _ => Brushes.Yellow // 默认颜色
    };
}
```

### 2. 多语言支持

系统支持中英文标注类别识别：
- 中文：病灶区域、肿瘤、骨折、血管、正常组织
- 英文：lesion、tumor、fracture、vessel、normal

### 3. 统一的标注工具颜色

所有标注工具（矩形、圆形、点标注）都使用统一的颜色系统：

#### 矩形标注
```csharp
var rectangle = new Rectangle
{
    Stroke = GetAnnotationColor(),
    StrokeThickness = 2,
    Fill = Brushes.Transparent
};
```

#### 圆形标注
```csharp
var ellipse = new Ellipse
{
    Stroke = GetAnnotationColor(),
    StrokeThickness = 2,
    Fill = Brushes.Transparent
};
```

#### 点标注
```csharp
var ellipse = new Ellipse
{
    Fill = GetAnnotationColor(),
    Stroke = GetDarkerBrush(GetAnnotationColor()),
    StrokeThickness = 1
};
```

## 使用方法

### 1. 病灶标注（绿色）

1. **选择标注类别**：
   - 在"标注类别"下拉框中选择"病灶区域"
   - 或者在自定义类别中输入"病灶"

2. **选择标注工具**：
   - 点击矩形工具按钮
   - 或选择圆形、点标注工具

3. **绘制标注**：
   - 在图像上拖拽绘制矩形框
   - 系统自动使用绿色边框
   - 完成后显示"已添加 绿色矩形 标注 - 病灶区域"

### 2. 其他类别标注

按照相同的步骤，选择不同的标注类别即可获得相应的颜色：
- 选择"肿瘤" → 红色标注
- 选择"骨折" → 橙色标注
- 选择"血管" → 蓝色标注
- 选择"正常组织" → 浅蓝色标注

### 3. 窗宽窗位配合使用

结合窗宽窗位调整功能，可以更好地观察和标注不同类型的病变：
- **病灶观察**：使用软组织窗观察软组织病变
- **骨折标注**：使用骨窗观察骨折线
- **血管标注**：调整窗宽窗位突出血管结构

## 医学专业性

### 1. 符合医学影像标准

颜色选择基于医学影像学的常见约定：
- **绿色**：通常用于标记病理性改变，与正常组织形成对比
- **红色**：用于标记高风险或恶性病变
- **蓝色**：用于标记血管或正常结构
- **橙色**：用于标记外伤性改变

### 2. 提高诊断效率

- **快速识别**：不同颜色帮助医生快速识别不同类型的标注
- **减少错误**：颜色编码减少了标注类别的混淆
- **标准化**：统一的颜色系统提高了标注的标准化程度

### 3. 支持多学科应用

- **放射科**：CT、MRI影像的病变标注
- **病理科**：组织切片的区域标记
- **外科**：手术规划中的结构标注
- **肿瘤科**：肿瘤边界和转移灶标注

## 界面改进

### 1. 颜色说明面板

在右侧控制面板中添加了折叠式的"标注颜色说明"区域：
- 直观显示每种颜色对应的标注类别
- 使用小矩形图标展示实际的标注颜色
- 支持折叠/展开，节省界面空间

### 2. 增强的状态提示

状态栏显示更详细的信息：
- 包含颜色名称
- 包含标注类型（矩形、圆形、点）
- 包含标注类别

### 3. 保持界面一致性

- 与现有的Material Design风格保持一致
- 颜色选择考虑了色盲用户的可访问性
- 所有标注工具使用统一的颜色系统

## 扩展性设计

### 1. 易于添加新类别

可以轻松添加新的标注类别和对应颜色：
```csharp
"新类别" => Brushes.Purple,
```

### 2. 支持自定义颜色

未来可以扩展为支持用户自定义颜色：
- 颜色选择器
- 用户偏好设置
- 颜色主题切换

### 3. 国际化支持

已经为多语言支持做好准备：
- 支持中英文类别名称
- 可以轻松扩展到其他语言

## 测试建议

### 1. 功能测试

1. **基本颜色测试**：
   - 测试每种标注类别是否显示正确的颜色
   - 验证矩形、圆形、点标注的颜色一致性

2. **窗宽窗位配合测试**：
   - 在不同窗宽窗位设置下测试标注颜色的可见性
   - 验证绿色病灶标注在各种背景下的对比度

3. **多类别混合测试**：
   - 在同一图像上标注多种类别
   - 验证颜色区分的清晰度

### 2. 用户体验测试

1. **直观性测试**：
   - 验证颜色选择是否直观
   - 测试颜色说明面板的有效性

2. **工作流程测试**：
   - 测试实际医学影像标注工作流程
   - 验证颜色系统是否提高了工作效率

## 总结

这次更新为智能标注系统添加了专业的颜色分类功能，特别突出了病灶区域的绿色标注。这个功能不仅提高了系统的专业性，还大大改善了用户体验和标注效率。通过颜色编码，医生可以更快速、准确地进行医学影像标注工作。
