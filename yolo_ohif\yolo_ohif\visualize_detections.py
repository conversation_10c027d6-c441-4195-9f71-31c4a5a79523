#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测结果可视化脚本
显示原图和带有检测框的图像，确认检测效果
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib import rcParams
import random
from pathlib import Path

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

# 导入检测服务
from services.detection_service import DetectionService

# 配置路径
MODEL_PATH = "E:/Trae/yolo_ohif/yolo11x_training_output/training_results/yolo11x_from_scratch_20250719_135134/weights/best.pt"
TEST_IMAGE_DIR = "E:/Trae/yolo_ohif/yolo11x_training_output/yolo_dataset/yolo_dataset/images/test"
OUTPUT_DIR = "E:/Trae/yolo_ohif/detection_visualizations"

def create_output_dir():
    """创建输出目录"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    print(f"输出目录: {OUTPUT_DIR}")

def get_test_images(num_images=10):
    """获取测试图像列表"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(Path(TEST_IMAGE_DIR).glob(f'*{ext}'))
        image_files.extend(Path(TEST_IMAGE_DIR).glob(f'*{ext.upper()}'))
    
    # 随机选择图像
    if len(image_files) > num_images:
        image_files = random.sample(image_files, num_images)
    
    return sorted(image_files)

def draw_detection_boxes(image, detections, confidence_threshold=0.1):
    """在图像上绘制检测框"""
    # 复制图像
    result_image = image.copy()
    
    # 定义颜色
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255)]
    
    detection_count = 0
    for detection in detections:
        confidence = detection.get('confidence', 0)
        if confidence >= confidence_threshold:
            # 获取边界框坐标
            x1 = int(detection['x1'])
            y1 = int(detection['y1'])
            x2 = int(detection['x2'])
            y2 = int(detection['y2'])
            
            # 选择颜色
            color = colors[detection_count % len(colors)]
            
            # 绘制矩形框
            cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 2)
            
            # 添加标签
            class_name = detection.get('class', 'unknown')
            label = f"{class_name}: {confidence:.2f}"
            
            # 计算文本大小
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.6
            thickness = 1
            (text_width, text_height), _ = cv2.getTextSize(label, font, font_scale, thickness)
            
            # 绘制文本背景
            cv2.rectangle(result_image, (x1, y1 - text_height - 10), 
                         (x1 + text_width, y1), color, -1)
            
            # 绘制文本
            cv2.putText(result_image, label, (x1, y1 - 5), 
                       font, font_scale, (255, 255, 255), thickness)
            
            detection_count += 1
    
    return result_image, detection_count

def visualize_single_image(image_path, detection_service, confidence_threshold=0.1):
    """可视化单张图像的检测结果"""
    print(f"\n处理图像: {os.path.basename(image_path)}")
    
    # 读取图像
    image = cv2.imread(str(image_path))
    if image is None:
        print(f"无法读取图像: {image_path}")
        return None
    
    # 转换为RGB格式用于matplotlib显示
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 进行检测
    try:
        detections = detection_service._detect_image(image)
        print(f"检测到 {len(detections)} 个目标")
        
        # 过滤低置信度检测
        filtered_detections = [d for d in detections if d.get('confidence', 0) >= confidence_threshold]
        print(f"置信度 >= {confidence_threshold} 的检测: {len(filtered_detections)} 个")
        
        # 绘制检测框
        result_image, detection_count = draw_detection_boxes(image_rgb, detections, confidence_threshold)
        
        # 创建可视化
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # 显示原图
        axes[0].imshow(image_rgb)
        axes[0].set_title(f'原图\n{os.path.basename(image_path)}', fontsize=12)
        axes[0].axis('off')
        
        # 显示检测结果
        axes[1].imshow(result_image)
        axes[1].set_title(f'检测结果 (置信度 >= {confidence_threshold})\n检测到 {detection_count} 个目标', fontsize=12)
        axes[1].axis('off')
        
        # 保存图像
        output_filename = f"detection_{os.path.splitext(os.path.basename(image_path))[0]}.png"
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        plt.tight_layout()
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.show()
        
        print(f"可视化结果已保存: {output_path}")
        
        # 打印检测详情
        if filtered_detections:
            print("检测详情:")
            for i, det in enumerate(filtered_detections):
                print(f"  {i+1}. 类别: {det.get('class', 'unknown')}, "
                      f"置信度: {det.get('confidence', 0):.3f}, "
                      f"位置: ({det['x1']:.0f}, {det['y1']:.0f}, {det['x2']:.0f}, {det['y2']:.0f})")
        
        return len(filtered_detections)
        
    except Exception as e:
        print(f"检测过程中出错: {e}")
        return None

def main():
    """主函数"""
    print("=== 检测结果可视化 ===")
    print(f"模型路径: {MODEL_PATH}")
    print(f"测试图像目录: {TEST_IMAGE_DIR}")
    
    # 创建输出目录
    create_output_dir()
    
    # 初始化检测服务
    try:
        detection_service = DetectionService(MODEL_PATH)
        print("检测服务初始化成功")
    except Exception as e:
        print(f"检测服务初始化失败: {e}")
        return
    
    # 获取测试图像
    test_images = get_test_images(num_images=5)  # 可视化5张图像
    if not test_images:
        print("未找到测试图像")
        return
    
    print(f"\n找到 {len(test_images)} 张测试图像")
    
    # 统计信息
    total_detections = 0
    images_with_detections = 0
    
    # 处理每张图像
    for i, image_path in enumerate(test_images):
        print(f"\n{'='*50}")
        print(f"处理第 {i+1}/{len(test_images)} 张图像")
        
        detection_count = visualize_single_image(image_path, detection_service, confidence_threshold=0.1)
        
        if detection_count is not None:
            total_detections += detection_count
            if detection_count > 0:
                images_with_detections += 1
    
    # 打印总结
    print(f"\n{'='*50}")
    print("=== 检测总结 ===")
    print(f"处理图像数量: {len(test_images)}")
    print(f"有检测结果的图像: {images_with_detections}")
    print(f"总检测数量: {total_detections}")
    print(f"平均每张图像检测数: {total_detections/len(test_images):.2f}")
    print(f"可视化结果保存在: {OUTPUT_DIR}")

if __name__ == "__main__":
    main()