# GDCM查看器脑窗功能添加说明

## 更新概述

为GDCM查看器界面添加了脑窗预设功能，现在用户可以快速切换到适合观察脑组织的窗宽窗位设置。

## 修改内容

### 1. 界面修改 (XAML)

**文件**: `src/MedicalImageAnalysis.Wpf/Views/GdcmDicomViewer.xaml`

**修改前**:
```xml
<Button Name="BoneWindowButton" Content="骨窗" Padding="6,5" Margin="0,0,10,0"
        Background="#007ACC" Foreground="White" BorderBrush="#007ACC"
        Click="BoneWindowButton_Click"/>

<Separator Margin="10,0" Background="#555"/>
```

**修改后**:
```xml
<Button Name="BoneWindowButton" Content="骨窗" Padding="6,5" Margin="0,0,5,0"
        Background="#007ACC" Foreground="White" BorderBrush="#007ACC"
        Click="BoneWindowButton_Click"/>
<Button Name="BrainWindowButton" Content="脑窗" Padding="6,5" Margin="0,0,10,0"
        Background="#007ACC" Foreground="White" BorderBrush="#007ACC"
        Click="BrainWindowButton_Click"/>

<Separator Margin="10,0" Background="#555"/>
```

**变更说明**:
- 调整了骨窗按钮的右边距从`10,0`改为`5,0`
- 添加了脑窗按钮，设置了相同的样式和布局
- 保持了整体工具栏的布局一致性

### 2. 功能实现 (C#)

**文件**: `src/MedicalImageAnalysis.Wpf/Views/GdcmDicomViewer.xaml.cs`

**新增方法**:
```csharp
/// <summary>
/// 脑窗预设按钮点击事件
/// </summary>
private async void BrainWindowButton_Click(object sender, RoutedEventArgs e)
{
    // 脑窗：窗宽80，窗位40
    WindowCenterTextBox.Text = "40";
    WindowWidthTextBox.Text = "80";
    await ApplyWindowLevelAsync(40, 80);
}
```

## 脑窗参数说明

### 窗宽窗位设置
- **窗宽 (Window Width)**: 80 HU
- **窗位 (Window Center)**: 40 HU

### 医学意义
脑窗是专门用于观察脑组织的窗宽窗位设置：

1. **适用场景**:
   - 脑CT扫描的常规观察
   - 脑出血的检测
   - 脑梗塞的诊断
   - 脑室系统的评估
   - 脑肿瘤的观察

2. **技术特点**:
   - **窄窗宽(80 HU)**: 提供高对比度，能够清晰区分脑组织的细微密度差异
   - **中等窗位(40 HU)**: 适合脑组织的平均密度，能够同时显示灰质和白质
   - **优化显示**: 突出脑组织结构，抑制骨骼和空气的干扰

3. **临床价值**:
   - 提高脑出血的检出率
   - 更好地显示脑水肿
   - 清晰显示脑室边界
   - 有助于评估脑组织的对称性

## 完整的预设窗口列表

GDCM查看器现在支持四种标准的医学影像预设窗口：

| 预设窗口 | 窗宽 (HU) | 窗位 (HU) | 主要用途 |
|---------|-----------|-----------|----------|
| **肺窗** | 1500 | -600 | 观察肺实质、肺结节、肺血管 |
| **软组织窗** | 400 | 40 | 观察腹部器官、软组织病变 |
| **骨窗** | 2000 | 400 | 观察骨骼结构、骨折、骨病变 |
| **脑窗** | 80 | 40 | 观察脑组织、脑出血、脑梗塞 |

## 使用方法

### 1. 打开DICOM文件
- 点击"打开DICOM文件"按钮
- 选择脑部CT扫描的DICOM文件

### 2. 使用脑窗预设
- 在工具栏的预设区域找到"脑窗"按钮
- 点击"脑窗"按钮
- 窗宽自动设置为80，窗位自动设置为40
- 图像显示立即更新为适合观察脑组织的对比度

### 3. 结合其他功能
- **亮度调整**: 可以在脑窗基础上微调亮度
- **手动调整**: 可以在脑窗基础上手动微调窗宽窗位
- **重置功能**: 可以重置到DICOM文件的原始窗宽窗位

## 界面布局

工具栏中预设按钮的排列顺序（从左到右）：
```
[肺窗] [软组织窗] [骨窗] [脑窗]
```

每个按钮都采用统一的样式：
- 背景色: #007ACC (蓝色)
- 前景色: 白色
- 边框色: #007ACC
- 内边距: 6,5
- 外边距: 适当间距

## 技术实现细节

### 1. 事件处理
- 使用异步方法`ApplyWindowLevelAsync`应用窗宽窗位
- 自动更新窗宽窗位输入框的显示值
- 保持与其他预设按钮一致的行为

### 2. 参数设置
```csharp
// 设置窗位为40 HU
WindowCenterTextBox.Text = "40";

// 设置窗宽为80 HU  
WindowWidthTextBox.Text = "80";

// 异步应用窗宽窗位调整
await ApplyWindowLevelAsync(40, 80);
```

### 3. 用户体验
- 点击按钮后立即生效
- 视觉反馈清晰
- 与现有功能无缝集成

## 测试建议

### 1. 功能测试
- 加载脑部CT DICOM文件
- 点击脑窗按钮验证效果
- 检查窗宽窗位数值是否正确设置
- 验证图像对比度是否适合观察脑组织

### 2. 兼容性测试
- 测试与其他预设窗口的切换
- 验证与亮度调整功能的兼容性
- 检查重置功能是否正常工作

### 3. 用户体验测试
- 验证按钮布局是否合理
- 检查响应速度是否满意
- 确认操作流程是否直观

## 后续扩展建议

### 1. 更多预设窗口
- 血管窗 (窗宽600, 窗位100)
- 纵隔窗 (窗宽350, 窗位50)
- 肝脏窗 (窗宽150, 窗位60)

### 2. 智能预设
- 根据DICOM文件的检查部位自动推荐合适的预设窗口
- 基于图像内容的自适应窗宽窗位调整

### 3. 自定义预设
- 允许用户保存自定义的窗宽窗位设置
- 提供预设管理功能

## 总结

通过添加脑窗功能，GDCM查看器现在提供了更完整的医学影像观察工具集。脑窗的加入特别有助于神经科医生和放射科医生更好地观察和诊断脑部疾病，提高了系统的专业性和实用性。

这个更新保持了界面的一致性和用户体验的连贯性，同时扩展了系统的医学专业功能。
