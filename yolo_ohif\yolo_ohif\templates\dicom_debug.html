<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DICOM调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-container { max-width: 1200px; margin: 0 auto; }
        .debug-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .viewer-container { width: 512px; height: 512px; border: 2px solid #000; margin: 20px 0; }
        .log-container { height: 300px; overflow-y: auto; background: #f5f5f5; padding: 10px; font-family: monospace; font-size: 12px; }
        .btn { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .status { padding: 5px 10px; border-radius: 4px; margin: 5px 0; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>DICOM查看器调试页面</h1>
        
        <div class="debug-section">
            <h3>库加载状态</h3>
            <div id="libraryStatus"></div>
        </div>
        
        <div class="debug-section">
            <h3>API连接测试</h3>
            <button class="btn" onclick="testAPI()">测试API连接</button>
            <div id="apiStatus"></div>
        </div>
        
        <div class="debug-section">
            <h3>DICOM图像加载测试</h3>
            <button class="btn" onclick="loadTestImage()">加载测试图像</button>
            <div class="viewer-container" id="dicomViewer"></div>
            <div id="imageStatus"></div>
        </div>
        
        <div class="debug-section">
            <h3>调试日志</h3>
            <button class="btn" onclick="clearLog()">清除日志</button>
            <div class="log-container" id="debugLog"></div>
        </div>
    </div>

    <!-- Cornerstone库 -->
    <script src="https://unpkg.com/cornerstone-core@2.6.1/dist/cornerstone.min.js"></script>
    <script src="https://unpkg.com/dicom-parser@1.8.21/dist/dicomParser.min.js"></script>
    <script src="https://unpkg.com/cornerstone-wado-image-loader@4.1.3/dist/cornerstoneWADOImageLoader.bundle.min.js"></script>

    <script>
        let viewer = null;
        
        function log(message, type = 'info') {
            const logContainer = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logEntry.style.color = type === 'error' ? 'red' : type === 'warning' ? 'orange' : 'black';
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
        }
        
        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }
        
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function checkLibraries() {
            const libraries = [
                { name: 'Cornerstone', check: () => typeof cornerstone !== 'undefined' },
                { name: 'DICOM Parser', check: () => typeof dicomParser !== 'undefined' },
                { name: 'WADO Image Loader', check: () => typeof cornerstoneWADOImageLoader !== 'undefined' }
            ];
            
            let statusHTML = '';
            let allLoaded = true;
            
            libraries.forEach(lib => {
                const loaded = lib.check();
                if (!loaded) allLoaded = false;
                statusHTML += `<div class="status ${loaded ? 'success' : 'error'}">${lib.name}: ${loaded ? '已加载' : '未加载'}</div>`;
                log(`${lib.name}: ${loaded ? '已加载' : '未加载'}`, loaded ? 'info' : 'error');
            });
            
            document.getElementById('libraryStatus').innerHTML = statusHTML;
            return allLoaded;
        }
        
        function initializeCornerstone() {
            try {
                const element = document.getElementById('dicomViewer');
                cornerstone.enable(element);
                
                // 配置WADO图像加载器
                cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
                cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
                
                cornerstoneWADOImageLoader.configure({
                    useWebWorkers: true,
                    decodeConfig: {
                        convertFloatPixelDataToInt: false
                    },
                    beforeSend: function(xhr) {
                        xhr.timeout = 30000;
                        xhr.onerror = function() {
                            log('DICOM图像请求失败', 'error');
                        };
                        xhr.ontimeout = function() {
                            log('DICOM图像请求超时', 'error');
                        };
                    }
                });
                
                cornerstone.registerImageLoader('wadouri', cornerstoneWADOImageLoader.wadouri.loadImage);
                
                log('Cornerstone初始化成功', 'info');
                return element;
            } catch (error) {
                log(`Cornerstone初始化失败: ${error.message}`, 'error');
                return null;
            }
        }
        
        async function testAPI() {
            try {
                updateStatus('apiStatus', '正在测试API连接...', 'warning');
                log('开始测试API连接', 'info');
                
                // 测试健康检查
                const healthResponse = await fetch('/api/v1/health');
                if (!healthResponse.ok) {
                    throw new Error(`健康检查失败: ${healthResponse.status}`);
                }
                log('健康检查通过', 'info');
                
                // 测试研究列表
                const studiesResponse = await fetch('/api/v1/studies');
                if (!studiesResponse.ok) {
                    throw new Error(`获取研究列表失败: ${studiesResponse.status}`);
                }
                const studies = await studiesResponse.json();
                log(`获取到 ${studies.length} 个研究`, 'info');
                
                if (studies.length > 0) {
                    const studyId = studies[0].id;
                    
                    // 测试研究详情
                    const studyResponse = await fetch(`/api/v1/studies/${studyId}`);
                    if (!studyResponse.ok) {
                        throw new Error(`获取研究详情失败: ${studyResponse.status}`);
                    }
                    const studyData = await studyResponse.json();
                    log(`研究详情获取成功，包含 ${studyData.series.length} 个序列`, 'info');
                    
                    if (studyData.series.length > 0) {
                        const seriesId = studyData.series[0].id;
                        
                        // 测试序列图像
                        const imagesResponse = await fetch(`/api/v1/series/${seriesId}/images`);
                        if (!imagesResponse.ok) {
                            throw new Error(`获取序列图像失败: ${imagesResponse.status}`);
                        }
                        const imagesData = await imagesResponse.json();
                        log(`序列图像获取成功，包含 ${imagesData.images.length} 张图像`, 'info');
                        
                        if (imagesData.images.length > 0) {
                            const imageId = imagesData.images[0].id;
                            
                            // 测试DICOM文件
                            const dicomResponse = await fetch(`/api/v1/images/${imageId}/dicom`, { method: 'HEAD' });
                            if (!dicomResponse.ok) {
                                throw new Error(`DICOM文件访问失败: ${dicomResponse.status}`);
                            }
                            log('DICOM文件访问成功', 'info');
                            
                            // 保存测试数据供图像加载使用
                            window.testData = {
                                studyId: studyId,
                                seriesId: seriesId,
                                imageId: imageId
                            };
                        }
                    }
                }
                
                updateStatus('apiStatus', 'API连接测试通过', 'success');
                log('API连接测试完成', 'info');
                
            } catch (error) {
                updateStatus('apiStatus', `API连接测试失败: ${error.message}`, 'error');
                log(`API连接测试失败: ${error.message}`, 'error');
            }
        }
        
        async function loadTestImage() {
            try {
                if (!window.testData) {
                    updateStatus('imageStatus', '请先运行API连接测试', 'warning');
                    return;
                }
                
                updateStatus('imageStatus', '正在加载DICOM图像...', 'warning');
                log('开始加载DICOM图像', 'info');
                
                const imageId = window.testData.imageId;
                const dicomEndpoint = `/api/v1/images/${imageId}/dicom`;
                const cornerstoneImageId = `wadouri:${dicomEndpoint}`;
                
                log(`图像ID: ${imageId}`, 'info');
                log(`DICOM端点: ${dicomEndpoint}`, 'info');
                log(`Cornerstone图像ID: ${cornerstoneImageId}`, 'info');
                
                // 加载图像
                const image = await cornerstone.loadImage(cornerstoneImageId);
                log('图像加载成功', 'info');
                log(`图像尺寸: ${image.width}x${image.height}`, 'info');
                
                // 显示图像
                const element = document.getElementById('dicomViewer');
                cornerstone.displayImage(element, image);
                log('图像显示成功', 'info');
                
                updateStatus('imageStatus', 'DICOM图像加载成功', 'success');
                
            } catch (error) {
                updateStatus('imageStatus', `图像加载失败: ${error.message}`, 'error');
                log(`图像加载失败: ${error.message}`, 'error');
                console.error('详细错误信息:', error);
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            log('页面加载完成，开始初始化', 'info');
            
            // 检查库加载状态
            const librariesLoaded = checkLibraries();
            
            if (librariesLoaded) {
                // 初始化Cornerstone
                viewer = initializeCornerstone();
                if (viewer) {
                    log('所有组件初始化完成', 'info');
                } else {
                    log('Cornerstone初始化失败', 'error');
                }
            } else {
                log('部分库未加载，无法初始化Cornerstone', 'error');
            }
        });
    </script>
</body>
</html>