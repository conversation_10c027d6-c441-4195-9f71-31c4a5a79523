"""UI服务

提供用户界面组件管理功能
"""

import logging
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

from ..core.interfaces import UIComponentsInterface
from ..core.exceptions import UIError
from ..core.types import BoundingBox, DetectionResult, SeverityLevel

logger = logging.getLogger(__name__)


class UITheme(Enum):
    """UI主题"""
    DEFAULT = "default"
    DARK = "dark"
    LIGHT = "light"
    HIGH_CONTRAST = "high_contrast"


class ComponentState(Enum):
    """组件状态"""
    HIDDEN = "hidden"
    VISIBLE = "visible"
    DISABLED = "disabled"
    LOADING = "loading"
    ERROR = "error"


@dataclass
class UIComponent:
    """UI组件"""
    id: str
    type: str
    state: ComponentState
    properties: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    parent_id: Optional[str] = None
    children: List[str] = None
    
    def __post_init__(self):
        if self.children is None:
            self.children = []


@dataclass
class BoundingBoxStyle:
    """边界框样式"""
    color: str = "#ff0000"
    width: int = 2
    opacity: float = 0.8
    fill_color: Optional[str] = None
    fill_opacity: float = 0.2
    dash_pattern: Optional[List[int]] = None


@dataclass
class LabelStyle:
    """标签样式"""
    font_size: int = 12
    font_family: str = "Arial, sans-serif"
    color: str = "#ffffff"
    background_color: str = "#000000"
    padding: int = 4
    border_radius: int = 2
    opacity: float = 0.9


class UIService(UIComponentsInterface):
    """UI服务
    
    提供用户界面组件的创建、管理和更新功能
    """
    
    def __init__(self, theme: UITheme = UITheme.DEFAULT):
        """初始化UI服务
        
        Args:
            theme: UI主题
        """
        self._components: Dict[str, UIComponent] = {}
        self._theme = theme
        self._event_handlers: Dict[str, List[Callable]] = {}
        self._styles = self._load_default_styles()
        self._active_overlays: List[str] = []
        
        logger.info(f"UI服务初始化完成，主题: {theme.value}")
    
    def create_toolbar_button(self, 
                              button_id: str,
                              label: str,
                              icon: Optional[str] = None,
                              tooltip: Optional[str] = None,
                              enabled: bool = True) -> str:
        """创建工具栏按钮
        
        Args:
            button_id: 按钮ID
            label: 按钮标签
            icon: 图标
            tooltip: 提示文本
            enabled: 是否启用
            
        Returns:
            组件ID
        """
        component = UIComponent(
            id=button_id,
            type="toolbar_button",
            state=ComponentState.VISIBLE if enabled else ComponentState.DISABLED,
            properties={
                "label": label,
                "icon": icon,
                "tooltip": tooltip or label,
                "enabled": enabled
            },
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self._components[button_id] = component
        logger.debug(f"创建工具栏按钮: {button_id}")
        return button_id
    
    def create_panel(self, 
                     panel_id: str,
                     title: str,
                     position: str = "right",
                     width: int = 300,
                     collapsible: bool = True) -> str:
        """创建面板
        
        Args:
            panel_id: 面板ID
            title: 面板标题
            position: 位置（left, right, top, bottom）
            width: 宽度
            collapsible: 是否可折叠
            
        Returns:
            组件ID
        """
        component = UIComponent(
            id=panel_id,
            type="panel",
            state=ComponentState.VISIBLE,
            properties={
                "title": title,
                "position": position,
                "width": width,
                "height": "auto",
                "collapsible": collapsible,
                "collapsed": False
            },
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self._components[panel_id] = component
        logger.debug(f"创建面板: {panel_id}")
        return panel_id
    
    def create_model_selector(self, 
                              selector_id: str,
                              models: List[Dict[str, Any]],
                              selected_model: Optional[str] = None) -> str:
        """创建模型选择器
        
        Args:
            selector_id: 选择器ID
            models: 模型列表
            selected_model: 选中的模型ID
            
        Returns:
            组件ID
        """
        component = UIComponent(
            id=selector_id,
            type="model_selector",
            state=ComponentState.VISIBLE,
            properties={
                "models": models,
                "selected_model": selected_model,
                "placeholder": "选择AI模型..."
            },
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self._components[selector_id] = component
        logger.debug(f"创建模型选择器: {selector_id}")
        return selector_id
    
    def create_progress_indicator(self, 
                                  indicator_id: str,
                                  message: str = "处理中...",
                                  progress: float = 0.0) -> str:
        """创建进度指示器
        
        Args:
            indicator_id: 指示器ID
            message: 消息
            progress: 进度（0-1）
            
        Returns:
            组件ID
        """
        component = UIComponent(
            id=indicator_id,
            type="progress_indicator",
            state=ComponentState.LOADING,
            properties={
                "message": message,
                "progress": max(0.0, min(1.0, progress)),
                "show_percentage": True,
                "animated": True
            },
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self._components[indicator_id] = component
        logger.debug(f"创建进度指示器: {indicator_id}")
        return indicator_id
    
    def create_results_display(self, 
                               display_id: str,
                               results: List[DetectionResult],
                               show_confidence: bool = True) -> str:
        """创建结果显示组件
        
        Args:
            display_id: 显示器ID
            results: 检测结果
            show_confidence: 是否显示置信度
            
        Returns:
            组件ID
        """
        # 处理结果数据
        processed_results = []
        for result in results:
            processed_result = {
                "bbox": asdict(result.bbox),
                "class_name": result.class_name,
                "confidence": result.confidence,
                "severity": result.severity.value if result.severity else None,
                "description": result.description
            }
            processed_results.append(processed_result)
        
        component = UIComponent(
            id=display_id,
            type="results_display",
            state=ComponentState.VISIBLE,
            properties={
                "results": processed_results,
                "show_confidence": show_confidence,
                "show_severity": True,
                "sortable": True,
                "filterable": True
            },
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self._components[display_id] = component
        logger.debug(f"创建结果显示: {display_id}")
        return display_id
    
    def show_bounding_boxes(self, 
                            overlay_id: str,
                            bounding_boxes: List[BoundingBox],
                            class_names: Optional[List[str]] = None,
                            confidences: Optional[List[float]] = None) -> None:
        """显示边界框
        
        Args:
            overlay_id: 覆盖层ID
            bounding_boxes: 边界框列表
            class_names: 类别名称列表
            confidences: 置信度列表
        """
        try:
            # 处理边界框数据
            processed_boxes = []
            for i, bbox in enumerate(bounding_boxes):
                box_data = {
                    "id": f"{overlay_id}_box_{i}",
                    "bbox": asdict(bbox),
                    "class_name": class_names[i] if class_names and i < len(class_names) else f"类别{i}",
                    "confidence": confidences[i] if confidences and i < len(confidences) else 1.0,
                    "style": self._get_box_style(i, len(bounding_boxes))
                }
                processed_boxes.append(box_data)
            
            # 创建或更新覆盖层组件
            component = UIComponent(
                id=overlay_id,
                type="bounding_box_overlay",
                state=ComponentState.VISIBLE,
                properties={
                    "boxes": processed_boxes,
                    "show_labels": True,
                    "show_confidence": True,
                    "interactive": True
                },
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            self._components[overlay_id] = component
            
            # 添加到活动覆盖层列表
            if overlay_id not in self._active_overlays:
                self._active_overlays.append(overlay_id)
            
            logger.debug(f"显示边界框: {overlay_id} ({len(bounding_boxes)}个)")
            
        except Exception as e:
            logger.error(f"显示边界框失败: {e}")
            raise UIError(f"显示边界框失败: {str(e)}")
    
    def hide_bounding_boxes(self, overlay_id: Optional[str] = None) -> None:
        """隐藏边界框
        
        Args:
            overlay_id: 覆盖层ID，如果为None则隐藏所有
        """
        try:
            if overlay_id:
                # 隐藏指定覆盖层
                if overlay_id in self._components:
                    self._components[overlay_id].state = ComponentState.HIDDEN
                    self._components[overlay_id].updated_at = datetime.now()
                
                if overlay_id in self._active_overlays:
                    self._active_overlays.remove(overlay_id)
                
                logger.debug(f"隐藏边界框: {overlay_id}")
            else:
                # 隐藏所有覆盖层
                for overlay_id in self._active_overlays[:]:
                    if overlay_id in self._components:
                        self._components[overlay_id].state = ComponentState.HIDDEN
                        self._components[overlay_id].updated_at = datetime.now()
                
                self._active_overlays.clear()
                logger.debug("隐藏所有边界框")
                
        except Exception as e:
            logger.error(f"隐藏边界框失败: {e}")
            raise UIError(f"隐藏边界框失败: {str(e)}")
    
    def update_component(self, 
                         component_id: str, 
                         properties: Dict[str, Any]) -> None:
        """更新组件属性
        
        Args:
            component_id: 组件ID
            properties: 新属性
        """
        if component_id not in self._components:
            raise UIError(f"组件不存在: {component_id}")
        
        component = self._components[component_id]
        component.properties.update(properties)
        component.updated_at = datetime.now()
        
        logger.debug(f"更新组件: {component_id}")
    
    def set_component_state(self, 
                            component_id: str, 
                            state: ComponentState) -> None:
        """设置组件状态
        
        Args:
            component_id: 组件ID
            state: 新状态
        """
        if component_id not in self._components:
            raise UIError(f"组件不存在: {component_id}")
        
        component = self._components[component_id]
        component.state = state
        component.updated_at = datetime.now()
        
        logger.debug(f"设置组件状态: {component_id} -> {state.value}")
    
    def get_component(self, component_id: str) -> Optional[UIComponent]:
        """获取组件
        
        Args:
            component_id: 组件ID
            
        Returns:
            组件对象
        """
        return self._components.get(component_id)
    
    def remove_component(self, component_id: str) -> bool:
        """移除组件
        
        Args:
            component_id: 组件ID
            
        Returns:
            是否成功移除
        """
        if component_id in self._components:
            # 移除子组件
            component = self._components[component_id]
            for child_id in component.children[:]:
                self.remove_component(child_id)
            
            # 从父组件中移除
            if component.parent_id and component.parent_id in self._components:
                parent = self._components[component.parent_id]
                if component_id in parent.children:
                    parent.children.remove(component_id)
            
            # 移除组件
            del self._components[component_id]
            
            # 从活动覆盖层列表中移除
            if component_id in self._active_overlays:
                self._active_overlays.remove(component_id)
            
            logger.debug(f"移除组件: {component_id}")
            return True
        
        return False
    
    def show_notification(self, 
                          message: str,
                          type: str = "info",
                          duration: int = 5000,
                          actions: Optional[List[Dict[str, Any]]] = None) -> str:
        """显示通知
        
        Args:
            message: 消息内容
            type: 通知类型（info, success, warning, error）
            duration: 显示时长（毫秒）
            actions: 操作按钮
            
        Returns:
            通知ID
        """
        notification_id = f"notification_{int(datetime.now().timestamp() * 1000)}"
        
        component = UIComponent(
            id=notification_id,
            type="notification",
            state=ComponentState.VISIBLE,
            properties={
                "message": message,
                "type": type,
                "duration": duration,
                "actions": actions or [],
                "dismissible": True
            },
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self._components[notification_id] = component
        logger.debug(f"显示通知: {type} - {message}")
        return notification_id
    
    def show_modal(self, 
                   modal_id: str,
                   title: str,
                   content: str,
                   buttons: Optional[List[Dict[str, Any]]] = None,
                   size: str = "medium") -> str:
        """显示模态对话框
        
        Args:
            modal_id: 模态框ID
            title: 标题
            content: 内容
            buttons: 按钮列表
            size: 大小（small, medium, large）
            
        Returns:
            组件ID
        """
        component = UIComponent(
            id=modal_id,
            type="modal",
            state=ComponentState.VISIBLE,
            properties={
                "title": title,
                "content": content,
                "buttons": buttons or [{"label": "确定", "action": "close"}],
                "size": size,
                "closable": True,
                "backdrop": True
            },
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self._components[modal_id] = component
        logger.debug(f"显示模态框: {modal_id}")
        return modal_id
    
    def set_theme(self, theme: UITheme) -> None:
        """设置UI主题
        
        Args:
            theme: 新主题
        """
        self._theme = theme
        self._styles = self._load_default_styles()
        
        # 更新所有组件的样式
        for component in self._components.values():
            component.updated_at = datetime.now()
        
        logger.info(f"UI主题已更改: {theme.value}")
    
    def get_theme(self) -> UITheme:
        """获取当前主题"""
        return self._theme
    
    def register_event_handler(self, 
                               event_type: str, 
                               handler: Callable) -> None:
        """注册事件处理器
        
        Args:
            event_type: 事件类型
            handler: 处理函数
        """
        if event_type not in self._event_handlers:
            self._event_handlers[event_type] = []
        
        self._event_handlers[event_type].append(handler)
        logger.debug(f"注册事件处理器: {event_type}")
    
    def unregister_event_handler(self, 
                                 event_type: str, 
                                 handler: Callable) -> bool:
        """取消注册事件处理器
        
        Args:
            event_type: 事件类型
            handler: 处理函数
            
        Returns:
            是否成功取消注册
        """
        if event_type in self._event_handlers:
            try:
                self._event_handlers[event_type].remove(handler)
                logger.debug(f"取消注册事件处理器: {event_type}")
                return True
            except ValueError:
                pass
        
        return False
    
    def emit_event(self, event_type: str, data: Any = None) -> None:
        """触发事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        if event_type in self._event_handlers:
            for handler in self._event_handlers[event_type]:
                try:
                    handler(data)
                except Exception as e:
                    logger.error(f"事件处理器执行失败: {e}")
        
        logger.debug(f"触发事件: {event_type}")
    
    def get_component_tree(self) -> Dict[str, Any]:
        """获取组件树
        
        Returns:
            组件树结构
        """
        tree = {}
        
        # 找到根组件（没有父组件的组件）
        root_components = [
            comp for comp in self._components.values()
            if comp.parent_id is None
        ]
        
        def build_tree(component: UIComponent) -> Dict[str, Any]:
            node = {
                "id": component.id,
                "type": component.type,
                "state": component.state.value,
                "properties": component.properties,
                "created_at": component.created_at.isoformat(),
                "updated_at": component.updated_at.isoformat(),
                "children": []
            }
            
            for child_id in component.children:
                if child_id in self._components:
                    child_component = self._components[child_id]
                    node["children"].append(build_tree(child_component))
            
            return node
        
        for root_component in root_components:
            tree[root_component.id] = build_tree(root_component)
        
        return tree
    
    def get_stats(self) -> Dict[str, Any]:
        """获取UI统计信息
        
        Returns:
            统计信息
        """
        component_types = {}
        component_states = {}
        
        for component in self._components.values():
            # 统计组件类型
            comp_type = component.type
            component_types[comp_type] = component_types.get(comp_type, 0) + 1
            
            # 统计组件状态
            comp_state = component.state.value
            component_states[comp_state] = component_states.get(comp_state, 0) + 1
        
        return {
            "total_components": len(self._components),
            "active_overlays": len(self._active_overlays),
            "theme": self._theme.value,
            "component_types": component_types,
            "component_states": component_states,
            "event_handlers": {k: len(v) for k, v in self._event_handlers.items()}
        }
    
    def _get_box_style(self, index: int, total: int) -> Dict[str, Any]:
        """获取边界框样式"""
        # 根据索引生成不同颜色
        colors = [
            "#ff0000", "#00ff00", "#0000ff", "#ffff00", "#ff00ff",
            "#00ffff", "#ffa500", "#800080", "#008000", "#000080"
        ]
        
        color = colors[index % len(colors)]
        
        return {
            "color": color,
            "width": 2,
            "opacity": 0.8,
            "fill_opacity": 0.2
        }
    
    def _load_default_styles(self) -> Dict[str, Any]:
        """加载默认样式"""
        base_styles = {
            "bounding_box": BoundingBoxStyle(),
            "label": LabelStyle()
        }
        
        # 根据主题调整样式
        if self._theme == UITheme.DARK:
            base_styles["label"].color = "#ffffff"
            base_styles["label"].background_color = "#333333"
        elif self._theme == UITheme.LIGHT:
            base_styles["label"].color = "#000000"
            base_styles["label"].background_color = "#ffffff"
        elif self._theme == UITheme.HIGH_CONTRAST:
            base_styles["bounding_box"].width = 3
            base_styles["label"].font_size = 14
        
        return base_styles
    
    def cleanup(self) -> None:
        """清理UI服务"""
        self._components.clear()
        self._event_handlers.clear()
        self._active_overlays.clear()
        logger.info("UI服务已清理")