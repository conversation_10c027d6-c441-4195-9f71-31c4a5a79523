#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速数据集检查工具
用于快速验证YOLO数据集的基本情况

使用方法:
    python quick_dataset_check.py
"""

import os
from pathlib import Path
import random

def quick_check_dataset(dataset_path: str):
    """快速检查数据集"""
    dataset_path = Path(dataset_path)
    images_dir = dataset_path / "images"
    labels_dir = dataset_path / "labels"
    
    print("YOLO数据集快速检查")
    print("=" * 40)
    print(f"数据集路径: {dataset_path}")
    
    # 检查基本结构
    if not dataset_path.exists():
        print("❌ 数据集路径不存在")
        return
    
    if not images_dir.exists():
        print("❌ images目录不存在")
        return
    
    if not labels_dir.exists():
        print("❌ labels目录不存在")
        return
    
    print("✅ 基本目录结构正确")
    
    # 检查分割
    splits = []
    for split_dir in images_dir.iterdir():
        if split_dir.is_dir():
            splits.append(split_dir.name)
    
    print(f"\n📁 发现的数据分割: {sorted(splits)}")
    
    # 检查每个分割
    total_images = 0
    total_labels = 0
    total_annotations = 0
    
    for split in sorted(splits):
        print(f"\n📊 {split} 分割:")
        
        # 图像文件
        image_split_dir = images_dir / split
        image_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
            image_files.extend(image_split_dir.glob(ext))
        
        # 标签文件
        label_split_dir = labels_dir / split
        label_files = list(label_split_dir.glob('*.txt')) if label_split_dir.exists() else []
        
        print(f"  📷 图像文件: {len(image_files)}")
        print(f"  🏷️  标签文件: {len(label_files)}")
        
        # 检查匹配情况
        matched_pairs = 0
        split_annotations = 0
        
        for image_file in image_files:
            label_file = label_split_dir / (image_file.stem + '.txt')
            if label_file.exists():
                matched_pairs += 1
                # 计算标注数量
                try:
                    with open(label_file, 'r') as f:
                        lines = [line.strip() for line in f if line.strip()]
                        split_annotations += len(lines)
                except:
                    pass
        
        print(f"  🔗 匹配的图像-标签对: {matched_pairs}")
        print(f"  📝 总标注数: {split_annotations}")
        
        if len(image_files) > 0:
            match_rate = (matched_pairs / len(image_files)) * 100
            print(f"  📈 匹配率: {match_rate:.1f}%")
        
        total_images += len(image_files)
        total_labels += len(label_files)
        total_annotations += split_annotations
        
        # 显示一些示例文件
        if image_files:
            sample_files = random.sample(image_files, min(3, len(image_files)))
            print(f"  📋 示例图像文件:")
            for sample in sample_files:
                label_exists = (label_split_dir / (sample.stem + '.txt')).exists()
                status = "✅" if label_exists else "❌"
                print(f"    {status} {sample.name}")
    
    # 总结
    print(f"\n📈 总体统计:")
    print(f"  📷 总图像数: {total_images}")
    print(f"  🏷️  总标签文件数: {total_labels}")
    print(f"  📝 总标注数: {total_annotations}")
    
    if total_images > 0:
        overall_match_rate = (total_labels / total_images) * 100
        print(f"  🎯 整体匹配率: {overall_match_rate:.1f}%")
        
        if total_annotations > 0:
            avg_annotations = total_annotations / total_images
            print(f"  📊 平均每张图像标注数: {avg_annotations:.2f}")
    
    # 检查dataset.yaml
    yaml_file = dataset_path / "dataset.yaml"
    if yaml_file.exists():
        print(f"\n✅ 发现 dataset.yaml 配置文件")
        try:
            with open(yaml_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print("📄 配置文件内容预览:")
                lines = content.split('\n')[:10]
                for line in lines:
                    if line.strip():
                        print(f"    {line}")
        except:
            print("⚠️  无法读取配置文件")
    else:
        print(f"\n⚠️  未发现 dataset.yaml 配置文件")
    
    # 给出建议
    print(f"\n💡 建议:")
    if total_images == 0:
        print("  ❌ 没有找到图像文件，请检查数据集")
    elif total_labels == 0:
        print("  ⚠️  没有找到标签文件，这可能是只有图像的数据集")
    elif total_labels < total_images:
        missing_rate = ((total_images - total_labels) / total_images) * 100
        print(f"  ⚠️  有 {missing_rate:.1f}% 的图像缺少标签文件")
        print("  💭 这符合预期：只有包含撕裂的层面才有标签文件")
    else:
        print("  ✅ 数据集结构看起来正常")
    
    print("\n🔍 要进行详细可视化验证，请运行:")
    print("    python visualize_yolo_dataset.py")

def main():
    """主函数"""
    # 数据集路径
    dataset_path = r"e:\Trae\yolo_ohif\yolo_training_output\yolo_dataset"
    
    try:
        quick_check_dataset(dataset_path)
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()