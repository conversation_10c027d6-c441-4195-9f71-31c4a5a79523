#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单张图像
"""

import os
import sys
import cv2
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from services.detection_service import DetectionService
from config import Config

# 测试特定图像
TEST_IMAGE = r"E:\Trae\yolo_ohif\yolo11x_training_output\yolo_dataset\yolo_dataset\images\test\137_slice_005.jpg"
TEST_LABEL = r"E:\Trae\yolo_ohif\yolo11x_training_output\yolo_dataset\yolo_dataset\labels\test\137_slice_005.txt"
MODEL_PATH = r"E:\Trae\yolo_ohif\yolo11x_training_output\training_results\yolo11x_from_scratch_20250719_135134\weights\best.pt"

def test_single_image():
    """
    测试单张图像
    """
    print("=" * 60)
    print("测试单张图像")
    print("=" * 60)
    
    # 检查文件是否存在
    if not os.path.exists(TEST_IMAGE):
        print(f"错误: 图像文件不存在: {TEST_IMAGE}")
        return
    
    if not os.path.exists(TEST_LABEL):
        print(f"错误: 标签文件不存在: {TEST_LABEL}")
        return
    
    # 读取标签
    with open(TEST_LABEL, 'r') as f:
        label_content = f.read().strip()
    print(f"标签内容: {label_content}")
    
    # 解析标签
    parts = label_content.split()
    if len(parts) == 5:
        class_id, center_x, center_y, width, height = map(float, parts)
        print(f"标签解析: 类别={int(class_id)}, 中心=({center_x:.3f}, {center_y:.3f}), 尺寸=({width:.3f}, {height:.3f})")
    
    # 读取图像
    image = cv2.imread(TEST_IMAGE)
    if image is None:
        print(f"错误: 无法读取图像: {TEST_IMAGE}")
        return
    
    h, w = image.shape[:2]
    print(f"图像尺寸: {w}x{h}")
    
    # 将归一化坐标转换为像素坐标
    if len(parts) == 5:
        pixel_center_x = center_x * w
        pixel_center_y = center_y * h
        pixel_width = width * w
        pixel_height = height * h
        
        x1 = int(pixel_center_x - pixel_width/2)
        y1 = int(pixel_center_y - pixel_height/2)
        x2 = int(pixel_center_x + pixel_width/2)
        y2 = int(pixel_center_y + pixel_height/2)
        
        print(f"标签边界框 (像素): ({x1}, {y1}) -> ({x2}, {y2})")
    
    # 初始化检测服务
    print(f"\n初始化检测服务...")
    print(f"模型路径: {MODEL_PATH}")
    print(f"置信度阈值: 0.1")
    
    try:
        detection_service = DetectionService(
            model_path=MODEL_PATH,
            confidence_threshold=0.1,
            iou_threshold=Config.YOLO.IOU_THRESHOLD
        )
        print("检测服务初始化成功")
    except Exception as e:
        print(f"检测服务初始化失败: {e}")
        return
    
    # 进行检测
    print("\n开始检测...")
    try:
        detections = detection_service._detect_image(image)
        print(f"检测完成，结果数量: {len(detections)}")
        
        if detections:
            print("\n检测结果:")
            for i, det in enumerate(detections):
                print(f"  目标 {i+1}:")
                print(f"    类别: {det['class_name']}")
                print(f"    置信度: {det['confidence']:.3f}")
                print(f"    位置: ({det['x']:.0f}, {det['y']:.0f})")
                print(f"    尺寸: {det['width']:.0f} x {det['height']:.0f}")
                
                # 计算边界框
                x1 = int(det['x'] - det['width']/2)
                y1 = int(det['y'] - det['height']/2)
                x2 = int(det['x'] + det['width']/2)
                y2 = int(det['y'] + det['height']/2)
                print(f"    边界框: ({x1}, {y1}) -> ({x2}, {y2})")
        else:
            print("\n未检测到任何目标")
            print("\n可能的原因:")
            print("1. 置信度阈值仍然太高")
            print("2. 模型训练不充分")
            print("3. 图像预处理问题")
            print("4. 模型与数据不匹配")
            
            # 尝试更低的置信度
            print("\n尝试更低的置信度阈值 (0.01)...")
            detection_service_low = DetectionService(
                model_path=MODEL_PATH,
                confidence_threshold=0.01,
                iou_threshold=Config.YOLO.IOU_THRESHOLD
            )
            detections_low = detection_service_low._detect_image(image)
            print(f"低置信度检测结果数量: {len(detections_low)}")
            
            if detections_low:
                print("低置信度检测结果:")
                for i, det in enumerate(detections_low):
                    print(f"  目标 {i+1}: 类别={det['class_name']}, 置信度={det['confidence']:.3f}")
            
    except Exception as e:
        print(f"检测失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_image()