# 医学影像智能打标教程

## 📋 目录
1. [系统概述](#系统概述)
2. [环境准备](#环境准备)
3. [启动应用程序](#启动应用程序)
4. [智能标注功能](#智能标注功能)
5. [手动标注操作](#手动标注操作)
6. [AI辅助标注](#ai辅助标注)
7. [标注数据管理](#标注数据管理)
8. [高级功能](#高级功能)
9. [故障排除](#故障排除)

## 🎯 系统概述

医学影像智能打标系统是一个基于 .NET 8 和 WPF 的现代化桌面应用程序，专为医学影像标注而设计。系统支持：

- **多格式支持**: DICOM、PNG、JPEG、BMP 等格式
- **智能标注**: AI 辅助的自动标注生成
- **手动标注**: 完整的手动标注工具集
- **质量控制**: 标注质量评估和验证
- **数据管理**: 标注数据的导入、导出和管理

## 🛠️ 环境准备

### 系统要求
- **操作系统**: Windows 10/11 (x64)
- **.NET Runtime**: .NET 8.0 或更高版本
- **内存**: 至少 4GB RAM (推荐 8GB)
- **存储**: 至少 2GB 可用空间
- **显卡**: 支持 DirectX 11 (AI 功能推荐独立显卡)

### 安装 .NET 8 Runtime
如果系统未安装 .NET 8，请从官网下载安装：
```
https://dotnet.microsoft.com/download/dotnet/8.0
```

### 验证环境
打开命令提示符，运行以下命令验证：
```cmd
dotnet --version
```
应显示 8.0.x 或更高版本。

## 🚀 启动应用程序

### 方法一：使用批处理文件（推荐）
1. 双击项目根目录下的 `启动桌面端应用.bat` 文件
2. 等待应用程序启动

### 方法二：使用 PowerShell
1. 右键点击项目根目录，选择"在此处打开 PowerShell"
2. 运行命令：
```powershell
.\启动桌面端应用.ps1
```

### 方法三：手动构建运行
1. 打开命令提示符或 PowerShell
2. 导航到项目根目录
3. 运行以下命令：
```bash
# 构建项目
dotnet build src/MedicalImageAnalysis.Wpf

# 运行应用程序
dotnet run --project src/MedicalImageAnalysis.Wpf
```

### 启动验证
应用程序启动后，您将看到主界面，包含以下模块：
- 首页
- DICOM上传
- GDCM查看
- 影像处理
- 智能标注
- 模型训练
- 统计分析

## 🎨 智能标注功能

### 进入智能标注模块
1. 启动应用程序后，点击左侧导航栏的 **"智能标注"** 按钮
2. 系统将打开智能标注界面

### 界面布局
智能标注界面分为三个主要区域：
- **左侧**: 工具栏和控制面板
- **中央**: 图像显示和标注区域
- **右侧**: 标注列表和属性面板

## 📝 手动标注操作

### 1. 加载图像

#### 加载 DICOM 文件
1. 点击 **"打开图像"** 按钮
2. 在文件对话框中选择 DICOM 文件（.dcm 格式）
3. 系统将自动加载并显示图像
4. 对于 DICOM 文件，右侧会显示窗宽窗位控制面板

#### 加载标准图像
1. 点击 **"打开图像"** 按钮
2. 选择支持的图像格式：PNG、JPEG、BMP
3. 图像将在中央区域显示

#### 使用示例数据
项目包含示例 DICOM 文件，位于 `Brain/` 目录：
- DJ01.dcm ~ DJ10.dcm（脑部 CT 示例）

### 2. 选择标注工具

系统提供多种标注工具：

#### 矩形标注
1. 点击 **"矩形"** 工具按钮
2. 在图像上按住鼠标左键并拖拽
3. 释放鼠标完成矩形标注

#### 椭圆标注
1. 点击 **"椭圆"** 工具按钮
2. 在图像上按住鼠标左键并拖拽
3. 释放鼠标完成椭圆标注

#### 多边形标注
1. 点击 **"多边形"** 工具按钮
2. 在图像上依次点击多个点
3. 双击或右键完成多边形标注

#### 自由绘制
1. 点击 **"自由绘制"** 工具按钮
2. 按住鼠标左键在图像上绘制
3. 释放鼠标完成绘制

### 3. 设置标注属性

#### 选择标注类别
1. 在右侧面板的 **"标注类别"** 下拉框中选择类别
2. 系统预设类别包括：
   - 病灶区域
   - 正常组织
   - 骨折
   - 肿瘤
   - 血管
   - 其他

#### 添加自定义类别
1. 在类别下拉框中输入新的类别名称
2. 按回车键添加自定义类别

#### 设置标注颜色
1. 点击颜色选择器
2. 选择合适的标注颜色
3. 新的标注将使用选定的颜色

### 4. 编辑标注

#### 选择标注
1. 点击图像上的标注图形
2. 选中的标注会高亮显示
3. 右侧面板会显示标注详细信息

#### 移动标注
1. 选中标注后，按住鼠标左键
2. 拖拽到新位置
3. 释放鼠标完成移动

#### 调整大小
1. 选中标注后，拖拽边角控制点
2. 调整到合适的大小

#### 删除标注
1. 选中要删除的标注
2. 按 Delete 键或点击删除按钮

## 🤖 AI辅助标注

### 1. 启用AI功能

#### 配置AI设置
1. 在右侧面板找到 **"AI 辅助设置"** 区域
2. 勾选以下选项：
   - ✅ 启用AI预标注
   - ✅ 智能边缘吸附
   - ✅ 相似区域建议

#### 设置置信度阈值
1. 使用 **"检测置信度阈值"** 滑块
2. 调整范围：0.1 - 1.0
3. 推荐设置：0.7（平衡精度和召回率）
4. 可使用快捷按钮：0.5、0.7、0.9

### 2. 自动标注生成

#### 触发自动标注
1. 加载图像后，AI 会自动分析
2. 系统会在后台生成候选标注
3. 高置信度的标注会自动显示

#### 查看AI建议
1. AI 生成的标注会以不同颜色显示
2. 标注列表中会显示置信度分数
3. 可以通过置信度阈值过滤显示

#### 确认或拒绝AI标注
1. 右键点击AI生成的标注
2. 选择 **"确认标注"** 或 **"删除标注"**
3. 确认的标注会转为手动标注

### 3. 智能辅助功能

#### 边缘吸附
1. 启用 **"智能边缘吸附"** 功能
2. 手动绘制时，标注会自动吸附到图像边缘
3. 提高标注精度

#### 相似区域建议
1. 启用 **"相似区域建议"** 功能
2. 系统会分析已标注区域
3. 自动建议相似的未标注区域

## 💾 标注数据管理

### 1. 保存标注

#### 保存到文件
1. 点击 **"保存标注"** 按钮
2. 选择保存位置和文件名
3. 支持的格式：
   - .ann（系统原生格式）
   - .json（JSON 格式）
   - .xml（XML 格式）

#### 自动保存
系统支持自动保存功能：
1. 在设置中启用自动保存
2. 设置自动保存间隔（默认5分钟）
3. 标注会自动保存到临时文件

### 2. 加载标注

#### 从文件加载
1. 点击 **"加载标注"** 按钮
2. 选择之前保存的标注文件
3. 系统会自动加载并显示标注

#### 合并标注
1. 可以加载多个标注文件
2. 系统会自动合并标注
3. 重复的标注会提示处理方式

### 3. 导出标注

#### 导出为训练数据
1. 点击 **"导出"** 按钮
2. 选择导出格式：
   - YOLO 格式（.txt）
   - COCO 格式（.json）
   - Pascal VOC 格式（.xml）
3. 选择导出目录
4. 系统会生成训练数据集

#### 批量导出
1. 选择多个已标注的图像
2. 使用批量导出功能
3. 系统会自动处理所有选中的图像

## 🔧 高级功能

### 1. 窗宽窗位调整（DICOM专用）

#### 手动调整
1. 在右侧面板找到 **"窗宽窗位调整"** 区域
2. 输入窗宽和窗位数值
3. 实时预览调整效果

#### 预设窗口
系统提供常用预设：
- **肺窗**: 窗宽 1500, 窗位 -600
- **软组织窗**: 窗宽 400, 窗位 40
- **骨窗**: 窗宽 2000, 窗位 400
- **脑窗**: 窗宽 80, 窗位 40

### 2. 标注质量控制

#### 质量评估
1. 系统会自动评估标注质量
2. 显示质量分数和建议
3. 标识可能的问题标注

#### 一致性检查
1. 检查标注的一致性
2. 发现重复或冲突的标注
3. 提供修正建议

### 3. 快捷键操作

常用快捷键：
- **Ctrl + O**: 打开图像
- **Ctrl + S**: 保存标注
- **Ctrl + Z**: 撤销操作
- **Ctrl + Y**: 重做操作
- **Delete**: 删除选中标注
- **Esc**: 取消当前操作
- **Space**: 切换工具

## 🔍 故障排除

### 常见问题

#### 1. 应用程序无法启动
**问题**: 双击启动文件后没有反应
**解决方案**:
1. 检查是否安装了 .NET 8 Runtime
2. 右键以管理员身份运行
3. 查看系统事件日志中的错误信息

#### 2. 图像无法加载
**问题**: 选择图像文件后无法显示
**解决方案**:
1. 确认文件格式是否支持
2. 检查文件是否损坏
3. 查看应用程序日志文件

#### 3. AI功能不工作
**问题**: AI辅助标注没有生成结果
**解决方案**:
1. 确认AI功能已启用
2. 检查置信度阈值设置
3. 确保图像质量足够好

#### 4. 标注无法保存
**问题**: 点击保存按钮后没有反应
**解决方案**:
1. 检查目标目录的写入权限
2. 确保磁盘空间充足
3. 尝试保存到不同位置

### 日志查看

应用程序日志位置：
```
src/MedicalImageAnalysis.Wpf/bin/Debug/net8.0-windows/logs/
└── medical-image-analysis-YYYYMMDD.txt
```

查看日志可以帮助诊断问题：
1. 打开日志文件
2. 查找错误信息（ERROR 级别）
3. 根据错误信息进行相应处理

### 性能优化

#### 内存使用优化
1. 处理大图像时，关闭不必要的应用程序
2. 定期清理临时文件
3. 重启应用程序释放内存

#### 响应速度优化
1. 使用SSD硬盘存储图像文件
2. 确保系统有足够的可用内存
3. 关闭实时杀毒软件的实时扫描

## 📞 技术支持

如遇到问题，可以通过以下方式获取帮助：

1. **查看日志**: 首先查看应用程序日志文件
2. **重启应用**: 尝试重启应用程序
3. **重新安装**: 重新安装 .NET 8 Runtime
4. **联系支持**: 创建 GitHub Issue 或联系开发团队

## 🎓 实战案例

### 案例1：肺部结节标注

#### 准备工作
1. 加载胸部CT的DICOM文件
2. 调整窗宽窗位到肺窗（窗宽1500，窗位-600）
3. 选择合适的标注工具（推荐椭圆工具）

#### 标注步骤
1. **识别结节**：寻找肺部的圆形或椭圆形高密度影
2. **精确标注**：使用椭圆工具框选结节区域
3. **设置类别**：选择"结节"或"肿瘤"类别
4. **质量检查**：确保标注边界准确，无遗漏

#### 注意事项
- 结节通常较小，需要放大图像进行精确标注
- 注意区分结节和血管横断面
- 对于边界模糊的结节，标注应包含完整病灶

### 案例2：骨折标注

#### 准备工作
1. 加载X光图像
2. 调整窗宽窗位到骨窗（窗宽2000，窗位400）
3. 选择矩形或多边形工具

#### 标注步骤
1. **定位骨折线**：寻找骨皮质的连续性中断
2. **标注范围**：框选包含骨折线的区域
3. **分类标注**：根据骨折类型设置相应类别
4. **多角度验证**：如有多个投照角度，进行对比确认

#### 标注技巧
- 骨折线可能很细，需要仔细观察
- 注意区分骨折和正常的骨缝
- 对于粉碎性骨折，可能需要多个标注框

### 案例3：脑部病灶标注

#### 准备工作
1. 加载头颅CT或MRI的DICOM文件
2. 调整到脑窗（窗宽80，窗位40）
3. 根据病灶形状选择合适工具

#### 标注步骤
1. **病灶识别**：识别异常密度区域
2. **边界确定**：精确勾画病灶边界
3. **类别分类**：区分出血、梗死、肿瘤等
4. **周围结构**：注意避开正常脑组织

## 🔧 高级标注技巧

### 1. 多层面标注

#### 3D数据集标注
对于多层面的DICOM数据：
1. **逐层标注**：在每个层面进行标注
2. **一致性检查**：确保相邻层面标注的连续性
3. **3D重建验证**：使用3D重建功能验证标注准确性

#### 标注传播
1. 在关键层面进行精确标注
2. 使用插值功能传播到相邻层面
3. 手动调整传播结果

### 2. 精细化标注

#### 边界优化
1. **放大操作**：放大图像进行精细标注
2. **边缘吸附**：启用智能边缘吸附功能
3. **多次调整**：反复调整标注边界直到满意

#### 子区域标注
对于复杂病灶：
1. 先标注整体区域
2. 再标注内部子结构
3. 使用不同颜色区分不同区域

### 3. 批量标注

#### 相似病例处理
1. **模板标注**：为相似病例创建标注模板
2. **批量应用**：将模板应用到多个相似图像
3. **个性化调整**：根据具体情况调整标注

#### 自动化流程
1. 使用AI预标注生成初始标注
2. 批量审核和修正AI标注
3. 导出最终标注结果

## 📊 标注质量控制

### 1. 质量评估指标

#### 标注一致性
- **重复标注测试**：同一图像多次标注的一致性
- **标注者间一致性**：不同标注者的一致性评估
- **时间一致性**：不同时间标注的一致性

#### 准确性评估
- **专家审核**：由医学专家审核标注质量
- **金标准对比**：与已知的金标准进行对比
- **交叉验证**：使用交叉验证评估标注质量

### 2. 质量改进方法

#### 标注规范化
1. **制定标注指南**：明确标注标准和规范
2. **培训标注人员**：确保标注人员理解规范
3. **定期质量检查**：定期抽查标注质量

#### 工具优化
1. **界面优化**：改进标注界面的易用性
2. **功能增强**：添加更多辅助标注功能
3. **自动化辅助**：使用AI辅助提高标注效率

### 3. 错误处理

#### 常见错误类型
1. **边界不准确**：标注边界偏离实际病灶
2. **分类错误**：病灶类别标注错误
3. **遗漏标注**：漏标重要病灶
4. **重复标注**：同一病灶被重复标注

#### 错误修正流程
1. **错误检测**：使用自动化工具检测潜在错误
2. **人工审核**：人工审核可疑标注
3. **批量修正**：对发现的错误进行批量修正
4. **质量验证**：修正后重新验证质量

## 🔄 标注工作流程

### 1. 标准工作流程

#### 准备阶段
1. **数据收集**：收集需要标注的医学影像
2. **数据预处理**：格式转换、质量检查
3. **标注计划**：制定标注计划和时间安排

#### 标注阶段
1. **初始标注**：进行初始的粗略标注
2. **精细化标注**：对标注进行精细化调整
3. **质量检查**：检查标注质量和完整性
4. **专家审核**：由专家进行最终审核

#### 完成阶段
1. **数据导出**：导出标注数据
2. **格式转换**：转换为所需的训练格式
3. **数据验证**：验证导出数据的正确性
4. **归档保存**：保存原始标注和导出数据

### 2. 团队协作流程

#### 任务分配
1. **角色定义**：定义标注员、审核员、专家等角色
2. **任务分工**：根据专业领域分配标注任务
3. **进度跟踪**：跟踪各成员的标注进度

#### 协作机制
1. **标注共享**：共享标注数据和经验
2. **讨论机制**：建立疑难病例讨论机制
3. **知识传递**：经验丰富者指导新手

### 3. 自动化集成

#### AI辅助集成
1. **预标注生成**：使用AI生成初始标注
2. **质量评估**：AI辅助质量评估
3. **异常检测**：自动检测异常标注

#### 工具集成
1. **数据管理**：集成数据管理系统
2. **版本控制**：标注版本控制和历史追踪
3. **统计分析**：标注统计和分析工具

## 📈 标注效率优化

### 1. 快捷操作

#### 键盘快捷键
- **Ctrl + Z**: 撤销操作
- **Ctrl + Y**: 重做操作
- **Delete**: 删除选中标注
- **Ctrl + S**: 快速保存
- **Space**: 切换工具
- **Ctrl + A**: 全选标注
- **Ctrl + C/V**: 复制粘贴标注

#### 鼠标操作
- **左键拖拽**: 创建标注
- **右键菜单**: 快速操作菜单
- **滚轮缩放**: 快速缩放图像
- **中键拖拽**: 平移图像

### 2. 批量操作

#### 批量编辑
1. **多选标注**：同时选择多个标注
2. **批量修改**：同时修改多个标注的属性
3. **批量删除**：同时删除多个标注

#### 模板应用
1. **创建模板**：为常见病例创建标注模板
2. **模板库管理**：管理和组织标注模板
3. **快速应用**：一键应用模板到新图像

### 3. 自动化辅助

#### 智能建议
1. **相似区域检测**：自动检测相似的未标注区域
2. **标注补全**：根据已有标注智能补全
3. **异常提醒**：提醒可能遗漏的重要区域

#### 预处理优化
1. **图像增强**：自动优化图像显示效果
2. **ROI提取**：自动提取感兴趣区域
3. **预筛选**：自动筛选需要标注的图像

---

**注意事项**:
- 本系统仅用于研究和教育目的
- 不应用于临床诊断
- 使用前请充分测试功能
- 定期备份重要的标注数据
- 标注质量直接影响AI模型性能
- 建议与医学专家合作进行标注工作
