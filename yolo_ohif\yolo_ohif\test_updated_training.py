#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的train_supraspinatus_yolo.py文件
验证新的mask_to_bbox函数是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
import cv2
from train_supraspinatus_yolo import SupraspinatusYOLOTrainer
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_mask_to_bbox():
    """
    测试新的mask_to_bbox函数
    """
    print("=== 测试新的mask_to_bbox函数 ===")
    
    # 创建训练器实例
    trainer = SupraspinatusYOLOTrainer(
        dataset_root="./dataset",
        output_root="./test_output"
    )
    
    # 测试用例1：单个区域
    print("\n测试用例1：单个区域")
    mask1 = np.zeros((100, 100), dtype=np.uint8)
    mask1[30:50, 40:60] = 1  # 创建一个20x20的区域
    
    bbox1 = trainer.mask_to_bbox(mask1)
    print(f"单个区域边界框: {bbox1}")
    
    # 测试用例2：多个分离区域
    print("\n测试用例2：多个分离区域")
    mask2 = np.zeros((100, 100), dtype=np.uint8)
    mask2[20:30, 20:30] = 1  # 第一个区域
    mask2[60:70, 60:70] = 1  # 第二个区域
    
    bbox2 = trainer.mask_to_bbox(mask2)
    print(f"多个区域合并后的边界框: {bbox2}")
    
    # 测试用例3：过小的区域（应该被过滤）
    print("\n测试用例3：过小的区域")
    mask3 = np.zeros((100, 100), dtype=np.uint8)
    mask3[50:52, 50:52] = 1  # 只有2x2的小区域
    
    bbox3 = trainer.mask_to_bbox(mask3)
    print(f"过小区域的边界框: {bbox3}")
    
    # 测试用例4：覆盖整张图像的区域（应该被过滤）
    print("\n测试用例4：覆盖整张图像的区域")
    mask4 = np.ones((100, 100), dtype=np.uint8)  # 整张图像都是1
    
    bbox4 = trainer.mask_to_bbox(mask4)
    print(f"覆盖整张图像的边界框: {bbox4}")
    
    # 测试用例5：空mask
    print("\n测试用例5：空mask")
    mask5 = np.zeros((100, 100), dtype=np.uint8)
    
    bbox5 = trainer.mask_to_bbox(mask5)
    print(f"空mask的边界框: {bbox5}")
    
    print("\n=== 测试完成 ===")

def test_imports():
    """
    测试所有必要的导入是否正常
    """
    print("=== 测试导入模块 ===")
    
    try:
        from scipy import ndimage
        print("✓ scipy.ndimage 导入成功")
    except ImportError as e:
        print(f"✗ scipy.ndimage 导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print("✓ numpy 导入成功")
    except ImportError as e:
        print(f"✗ numpy 导入失败: {e}")
        return False
    
    try:
        import cv2
        print("✓ opencv 导入成功")
    except ImportError as e:
        print(f"✗ opencv 导入失败: {e}")
        return False
    
    try:
        from train_supraspinatus_yolo import SupraspinatusYOLOTrainer
        print("✓ SupraspinatusYOLOTrainer 导入成功")
    except ImportError as e:
        print(f"✗ SupraspinatusYOLOTrainer 导入失败: {e}")
        return False
    
    print("=== 所有模块导入成功 ===")
    return True

def main():
    """
    主测试函数
    """
    print("开始测试更新后的训练代码...")
    
    # 测试导入
    if not test_imports():
        print("导入测试失败，请检查依赖包是否安装")
        return
    
    # 测试mask_to_bbox函数
    test_mask_to_bbox()
    
    print("\n所有测试完成！")

if __name__ == "__main__":
    main()