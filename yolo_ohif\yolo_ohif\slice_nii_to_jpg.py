#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将NII.gz文件切片转换为JPG格式
分别处理image_T2、image_T2_normal和label_T2目录
"""

import os
import cv2
import numpy as np
import nibabel as nib
from pathlib import Path
import logging
from tqdm import tqdm
import argparse

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NIISliceConverter:
    def __init__(self, dataset_root, output_root, img_size=640):
        self.dataset_root = Path(dataset_root)
        self.output_root = Path(output_root)
        self.img_size = img_size
        
        # 输入目录
        self.image_t2_dir = self.dataset_root / "image_T2"
        self.image_t2_normal_dir = self.dataset_root / "image_T2_normal"
        self.label_t2_dir = self.dataset_root / "label_T2"
        
        # 输出目录
        self.output_image_t2 = self.output_root / "sliced_images" / "image_T2"
        self.output_image_t2_normal = self.output_root / "sliced_images" / "image_T2_normal"
        self.output_label_t2 = self.output_root / "sliced_images" / "label_T2"
        
        # 创建输出目录
        for output_dir in [self.output_image_t2, self.output_image_t2_normal, self.output_label_t2]:
            output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"初始化完成，数据集根目录: {self.dataset_root}")
        logger.info(f"输出根目录: {self.output_root}")
    
    def load_nii_image(self, nii_path):
        """加载NII图像"""
        try:
            nii_img = nib.load(str(nii_path))
            img_data = nii_img.get_fdata()
            logger.debug(f"成功加载 {nii_path}，形状: {img_data.shape}")
            return img_data
        except Exception as e:
            logger.error(f"加载NII文件失败 {nii_path}: {e}")
            return None
    
    def process_image_slices(self, volume, output_dir, base_name, is_label=False):
        """处理图像切片"""
        if volume is None:
            return 0
        
        num_slices = volume.shape[2] if len(volume.shape) == 3 else 1
        saved_count = 0
        
        for slice_idx in range(num_slices):
            if len(volume.shape) == 3:
                slice_data = volume[:, :, slice_idx]
            else:
                slice_data = volume
            
            # 检查切片是否为空（全零）
            if np.all(slice_data == 0):
                logger.debug(f"跳过空切片: {base_name}_slice_{slice_idx:03d}")
                continue
            
            # 调整尺寸
            slice_resized = cv2.resize(slice_data.astype(np.float32), (self.img_size, self.img_size))
            
            if is_label:
                # 标签图像：保持二值化
                slice_processed = (slice_resized > 0.5).astype(np.uint8) * 255
            else:
                # 普通图像：归一化到0-255
                if slice_resized.max() > slice_resized.min():
                    slice_normalized = ((slice_resized - slice_resized.min()) / 
                                      (slice_resized.max() - slice_resized.min()) * 255).astype(np.uint8)
                else:
                    slice_normalized = np.zeros_like(slice_resized, dtype=np.uint8)
                
                # 转换为3通道（如果需要）
                if len(slice_normalized.shape) == 2:
                    slice_processed = cv2.cvtColor(slice_normalized, cv2.COLOR_GRAY2RGB)
                else:
                    slice_processed = slice_normalized
            
            # 生成文件名
            output_filename = f"{base_name}_slice_{slice_idx:03d}.jpg"
            output_path = output_dir / output_filename
            
            # 保存图像
            try:
                cv2.imwrite(str(output_path), slice_processed)
                saved_count += 1
                logger.debug(f"保存切片: {output_path}")
            except Exception as e:
                logger.error(f"保存切片失败 {output_path}: {e}")
        
        return saved_count
    
    def process_directory(self, input_dir, output_dir, dir_name, is_label=False):
        """处理指定目录下的所有NII.gz文件"""
        if not input_dir.exists():
            logger.warning(f"输入目录不存在: {input_dir}")
            return
        
        nii_files = list(input_dir.glob("*.nii.gz"))
        if not nii_files:
            logger.warning(f"在 {input_dir} 中未找到NII.gz文件")
            return
        
        logger.info(f"开始处理 {dir_name} 目录，共 {len(nii_files)} 个文件")
        
        total_slices = 0
        for nii_file in tqdm(nii_files, desc=f"处理{dir_name}"):
            # 加载NII图像
            volume = self.load_nii_image(nii_file)
            if volume is None:
                continue
            
            # 获取基础文件名（去掉.nii.gz后缀）
            base_name = nii_file.stem.replace('.nii', '')
            
            # 处理切片
            slice_count = self.process_image_slices(volume, output_dir, base_name, is_label)
            total_slices += slice_count
            
            logger.info(f"文件 {nii_file.name} 处理完成，生成 {slice_count} 个切片")
        
        logger.info(f"{dir_name} 目录处理完成，总共生成 {total_slices} 个切片")
    
    def run_conversion(self):
        """运行完整的转换流程"""
        logger.info("开始NII.gz到JPG的切片转换...")
        
        # 处理image_T2目录
        self.process_directory(self.image_t2_dir, self.output_image_t2, "image_T2", is_label=False)
        
        # 处理image_T2_normal目录
        self.process_directory(self.image_t2_normal_dir, self.output_image_t2_normal, "image_T2_normal", is_label=False)
        
        # 处理label_T2目录
        self.process_directory(self.label_t2_dir, self.output_label_t2, "label_T2", is_label=True)
        
        logger.info("所有转换完成！")
        logger.info(f"输出目录结构:")
        logger.info(f"  - 撕裂图像: {self.output_image_t2}")
        logger.info(f"  - 正常图像: {self.output_image_t2_normal}")
        logger.info(f"  - 标签图像: {self.output_label_t2}")

def main():
    parser = argparse.ArgumentParser(description='将NII.gz文件切片转换为JPG格式')
    parser.add_argument('--dataset_root', type=str, default='./dataset',
                       help='数据集根目录路径')
    parser.add_argument('--output_root', type=str, default='./sliced_output',
                       help='输出根目录路径')
    parser.add_argument('--img_size', type=int, default=640,
                       help='输出图像尺寸')
    
    args = parser.parse_args()
    
    # 创建转换器并运行
    converter = NIISliceConverter(
        dataset_root=args.dataset_root,
        output_root=args.output_root,
        img_size=args.img_size
    )
    
    converter.run_conversion()

if __name__ == "__main__":
    main()