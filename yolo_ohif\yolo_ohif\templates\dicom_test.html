<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DICOM 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .dicom-viewport {
            width: 512px;
            height: 512px;
            background: #000;
            border: 2px solid #ccc;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DICOM 图像加载诊断工具</h1>
        
        <!-- 库加载状态检查 -->
        <div class="test-section">
            <h3>1. JavaScript 库加载状态</h3>
            <div id="library-status"></div>
        </div>
        
        <!-- Orthanc 连接测试 -->
        <div class="test-section">
            <h3>2. Orthanc 服务器连接测试</h3>
            <button onclick="testOrthancConnection()">测试 Orthanc 连接</button>
            <div id="orthanc-status"></div>
        </div>
        
        <!-- API 端点测试 -->
        <div class="test-section">
            <h3>3. API 端点测试</h3>
            <button onclick="testAPIEndpoints()">测试 API 端点</button>
            <div id="api-status"></div>
        </div>
        
        <!-- DICOM 图像加载测试 -->
        <div class="test-section">
            <h3>4. DICOM 图像加载测试</h3>
            <button onclick="testDICOMLoading()" id="dicom-test-btn">测试 DICOM 加载</button>
            <div id="dicom-status"></div>
            <div id="dicomViewport" class="dicom-viewport"></div>
        </div>
        
        <!-- 详细日志 -->
        <div class="test-section">
            <h3>5. 详细日志</h3>
            <button onclick="clearLog()">清除日志</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <!-- JavaScript 库 -->
    <script src="https://unpkg.com/cornerstone-core@2.6.1/dist/cornerstone.min.js"></script>
    <script src="https://unpkg.com/cornerstone-web-image-loader@2.1.1/dist/cornerstoneWebImageLoader.min.js"></script>
    <script src="https://unpkg.com/cornerstone-wado-image-loader@4.1.3/dist/cornerstoneWADOImageLoader.bundle.min.js"></script>
    <script src="https://unpkg.com/cornerstone-tools@4.21.1/dist/cornerstoneTools.min.js"></script>
    <script src="https://unpkg.com/dicom-parser@1.8.21/dist/dicomParser.min.js"></script>

    <script>
        let logElement;
        let currentStudyId = '927e4af3-ffe70e59-686ea538-61a3431d-398b89f1'; // 默认研究ID
        
        function log(message, type = 'info') {
            if (!logElement) {
                logElement = document.getElementById('log');
            }
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logMessage);
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        function setStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        // 检查库加载状态
        function checkLibraries() {
            const libraries = [
                { name: 'cornerstone', obj: window.cornerstone },
                { name: 'cornerstoneWADOImageLoader', obj: window.cornerstoneWADOImageLoader },
                { name: 'cornerstoneWebImageLoader', obj: window.cornerstoneWebImageLoader },
                { name: 'cornerstoneTools', obj: window.cornerstoneTools },
                { name: 'dicomParser', obj: window.dicomParser }
            ];
            
            let allLoaded = true;
            let statusHtml = '';
            
            libraries.forEach(lib => {
                if (lib.obj) {
                    statusHtml += `<div class="status success">✓ ${lib.name} 已加载</div>`;
                    log(`${lib.name} 库加载成功`);
                } else {
                    statusHtml += `<div class="status error">✗ ${lib.name} 未加载</div>`;
                    log(`${lib.name} 库加载失败`, 'error');
                    allLoaded = false;
                }
            });
            
            document.getElementById('library-status').innerHTML = statusHtml;
            return allLoaded;
        }
        
        // 测试 Orthanc 连接
        async function testOrthancConnection() {
            try {
                log('开始测试 Orthanc 连接...');
                const response = await fetch('/api/health');
                const data = await response.json();
                
                if (data.success) {
                    setStatus('orthanc-status', `Orthanc 连接正常 - ${data.orthanc_status.status}`, 'success');
                    log('Orthanc 连接测试成功');
                } else {
                    setStatus('orthanc-status', `Orthanc 连接失败: ${data.message}`, 'error');
                    log(`Orthanc 连接测试失败: ${data.message}`, 'error');
                }
            } catch (error) {
                setStatus('orthanc-status', `连接错误: ${error.message}`, 'error');
                log(`Orthanc 连接错误: ${error.message}`, 'error');
            }
        }
        
        // 测试 API 端点
        async function testAPIEndpoints() {
            const endpoints = [
                { name: '研究信息', url: `/api/studies/${currentStudyId}` },
                { name: '健康检查', url: '/api/health' }
            ];
            
            let statusHtml = '';
            
            for (const endpoint of endpoints) {
                try {
                    log(`测试 API 端点: ${endpoint.url}`);
                    const response = await fetch(endpoint.url);
                    
                    if (response.ok) {
                        const data = await response.json();
                        statusHtml += `<div class="status success">✓ ${endpoint.name}: ${response.status}</div>`;
                        log(`${endpoint.name} API 测试成功: ${response.status}`);
                        
                        if (endpoint.name === '研究信息' && data.success && data.series) {
                            log(`找到 ${data.series.length} 个序列`);
                            // 测试第一个序列的图像
                            if (data.series.length > 0) {
                                await testSeriesImages(data.series[0].id);
                            }
                        }
                    } else {
                        statusHtml += `<div class="status error">✗ ${endpoint.name}: ${response.status} ${response.statusText}</div>`;
                        log(`${endpoint.name} API 测试失败: ${response.status}`, 'error');
                    }
                } catch (error) {
                    statusHtml += `<div class="status error">✗ ${endpoint.name}: ${error.message}</div>`;
                    log(`${endpoint.name} API 错误: ${error.message}`, 'error');
                }
            }
            
            document.getElementById('api-status').innerHTML = statusHtml;
        }
        
        // 测试序列图像
        async function testSeriesImages(seriesId) {
            try {
                log(`测试序列图像: ${seriesId}`);
                const response = await fetch(`/api/series/${seriesId}/images`);
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.images && data.images.length > 0) {
                        log(`序列包含 ${data.images.length} 张图像`);
                        
                        // 测试第一张图像的 DICOM 端点
                        const firstImage = data.images[0];
                        await testDICOMEndpoint(firstImage.id);
                    } else {
                        log('序列中没有图像', 'warning');
                    }
                } else {
                    log(`获取序列图像失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`测试序列图像错误: ${error.message}`, 'error');
            }
        }
        
        // 测试 DICOM 端点
        async function testDICOMEndpoint(imageId) {
            try {
                log(`测试 DICOM 端点: /api/images/${imageId}/dicom`);
                const response = await fetch(`/api/images/${imageId}/dicom`, { method: 'HEAD' });
                
                if (response.ok) {
                    log(`DICOM 端点可访问: ${response.status}`);
                    return imageId;
                } else {
                    log(`DICOM 端点不可访问: ${response.status}`, 'error');
                    return null;
                }
            } catch (error) {
                log(`DICOM 端点测试错误: ${error.message}`, 'error');
                return null;
            }
        }
        
        // 测试 DICOM 图像加载
        async function testDICOMLoading() {
            if (!checkLibraries()) {
                setStatus('dicom-status', '无法测试 DICOM 加载：库未完全加载', 'error');
                return;
            }
            
            try {
                log('开始 DICOM 图像加载测试...');
                const btn = document.getElementById('dicom-test-btn');
                btn.disabled = true;
                
                // 获取研究信息
                const studyResponse = await fetch(`/api/studies/${currentStudyId}`);
                if (!studyResponse.ok) {
                    throw new Error(`无法获取研究信息: ${studyResponse.status}`);
                }
                
                const studyData = await studyResponse.json();
                if (!studyData.success || !studyData.series || studyData.series.length === 0) {
                    throw new Error('研究中没有序列数据');
                }
                
                // 获取第一个序列的图像
                const firstSeries = studyData.series[0];
                log(`使用序列: ${firstSeries.id}`);
                
                const seriesResponse = await fetch(`/api/series/${firstSeries.id}/images`);
                if (!seriesResponse.ok) {
                    throw new Error(`无法获取序列图像: ${seriesResponse.status}`);
                }
                
                const seriesData = await seriesResponse.json();
                if (!seriesData.success || !seriesData.images || seriesData.images.length === 0) {
                    throw new Error('序列中没有图像');
                }
                
                // 初始化 Cornerstone
                const element = document.getElementById('dicomViewport');
                cornerstone.enable(element);
                log('Cornerstone 已启用');
                
                // 配置 WADO 图像加载器
                cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
                cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
                
                cornerstoneWADOImageLoader.configure({
                    useWebWorkers: true,
                    decodeConfig: {
                        convertFloatPixelDataToInt: false
                    }
                });
                
                cornerstone.registerImageLoader('wadouri', cornerstoneWADOImageLoader.wadouri.loadImage);
                log('WADO 图像加载器已配置');
                
                // 加载第一张图像
                const firstImage = seriesData.images[0];
                const imageId = `wadouri:/api/images/${firstImage.id}/dicom`;
                log(`尝试加载图像: ${imageId}`);
                
                const loadedImage = await cornerstone.loadImage(imageId);
                log('图像加载成功');
                
                cornerstone.displayImage(element, loadedImage);
                log('图像显示成功');
                
                setStatus('dicom-status', '✓ DICOM 图像加载和显示成功！', 'success');
                
            } catch (error) {
                setStatus('dicom-status', `✗ DICOM 加载失败: ${error.message}`, 'error');
                log(`DICOM 加载失败: ${error.message}`, 'error');
                log(`错误堆栈: ${error.stack}`, 'error');
            } finally {
                document.getElementById('dicom-test-btn').disabled = false;
            }
        }
        
        // 页面加载完成后自动检查库状态
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('页面加载完成，开始检查库状态...');
                checkLibraries();
            }, 1000);
        });
    </script>
</body>
</html>