import os
import json
import sqlite3
import logging
import datetime
from pathlib import Path

logger = logging.getLogger(__name__)

class DatabaseService:
    """数据库服务，用于存储检测结果和用户数据"""
    
    def __init__(self, db_path):
        """初始化数据库服务
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self._init_db()
    
    def _init_db(self):
        """初始化数据库表结构"""
        # 确保目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建用户表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                email TEXT,
                role TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
            ''')
            
            # 创建研究表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS studies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                orthanc_id TEXT UNIQUE NOT NULL,
                patient_name TEXT,
                patient_id TEXT,
                study_date TEXT,
                study_description TEXT,
                modality TEXT,
                series_count INTEGER,
                instance_count INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # 创建检测结果表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS detection_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                study_id INTEGER NOT NULL,
                user_id INTEGER,
                model_name TEXT,
                confidence_threshold REAL,
                detection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                result_data TEXT,
                FOREIGN KEY (study_id) REFERENCES studies (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            ''')
            
            # 创建标注表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS annotations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                study_id INTEGER NOT NULL,
                user_id INTEGER,
                instance_id TEXT,
                annotation_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP,
                FOREIGN KEY (study_id) REFERENCES studies (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            ''')
            
            # 创建审计日志表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS audit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                entity_type TEXT NOT NULL,
                entity_id TEXT,
                details TEXT,
                ip_address TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            ''')
            
            conn.commit()
            logger.info("数据库初始化成功")
        except Exception as e:
            logger.error(f"初始化数据库时出错: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def add_user(self, username, password_hash, email=None, role='user'):
        """添加用户
        
        Args:
            username: 用户名
            password_hash: 密码哈希
            email: 电子邮件
            role: 用户角色
            
        Returns:
            用户ID
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                'INSERT INTO users (username, password_hash, email, role) VALUES (?, ?, ?, ?)',
                (username, password_hash, email, role)
            )
            
            user_id = cursor.lastrowid
            conn.commit()
            return user_id
        except sqlite3.IntegrityError:
            logger.warning(f"用户名已存在: {username}")
            raise ValueError(f"用户名已存在: {username}")
        except Exception as e:
            logger.error(f"添加用户时出错: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def check_connection(self):
        """检查数据库连接状态
        
        Returns:
            bool: 连接状态
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('SELECT 1')
            conn.close()
            return True
        except Exception as e:
            logger.error(f"数据库连接检查失败: {str(e)}")
            return False
    
    def get_user(self, username):
        """获取用户信息
        
        Args:
            username: 用户名
            
        Returns:
            用户信息字典
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
            row = cursor.fetchone()
            
            if row:
                return dict(row)
            return None
        except Exception as e:
            logger.error(f"获取用户信息时出错: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def update_last_login(self, user_id):
        """更新用户最后登录时间
        
        Args:
            user_id: 用户ID
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
                (user_id,)
            )
            
            conn.commit()
        except Exception as e:
            logger.error(f"更新用户登录时间时出错: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def add_study(self, orthanc_id, patient_name, patient_id, study_date, 
                  study_description, modality, series_count, instance_count):
        """添加研究记录
        
        Args:
            orthanc_id: Orthanc研究ID
            patient_name: 患者姓名
            patient_id: 患者ID
            study_date: 研究日期
            study_description: 研究描述
            modality: 模态
            series_count: 序列数量
            instance_count: 实例数量
            
        Returns:
            研究ID
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查研究是否已存在
            cursor.execute('SELECT id FROM studies WHERE orthanc_id = ?', (orthanc_id,))
            existing = cursor.fetchone()
            
            if existing:
                # 更新现有研究
                cursor.execute('''
                UPDATE studies SET 
                    patient_name = ?, 
                    patient_id = ?, 
                    study_date = ?, 
                    study_description = ?, 
                    modality = ?, 
                    series_count = ?, 
                    instance_count = ? 
                WHERE orthanc_id = ?
                ''', (patient_name, patient_id, study_date, study_description, 
                      modality, series_count, instance_count, orthanc_id))
                study_id = existing[0]
            else:
                # 添加新研究
                cursor.execute('''
                INSERT INTO studies (
                    orthanc_id, patient_name, patient_id, study_date, 
                    study_description, modality, series_count, instance_count
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (orthanc_id, patient_name, patient_id, study_date, 
                      study_description, modality, series_count, instance_count))
                study_id = cursor.lastrowid
            
            conn.commit()
            return study_id
        except Exception as e:
            logger.error(f"添加研究记录时出错: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def get_study(self, orthanc_id):
        """获取研究信息
        
        Args:
            orthanc_id: Orthanc研究ID
            
        Returns:
            研究信息字典
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM studies WHERE orthanc_id = ?', (orthanc_id,))
            row = cursor.fetchone()
            
            if row:
                return dict(row)
            return None
        except Exception as e:
            logger.error(f"获取研究信息时出错: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def get_all_studies(self, limit=100, offset=0):
        """获取所有研究列表
        
        Args:
            limit: 限制返回数量
            offset: 偏移量
            
        Returns:
            研究信息列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute(
                'SELECT * FROM studies ORDER BY created_at DESC LIMIT ? OFFSET ?',
                (limit, offset)
            )
            rows = cursor.fetchall()
            
            return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"获取研究列表时出错: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def add_detection_result(self, study_id, user_id, model_name, confidence_threshold, result_data):
        """添加检测结果
        
        Args:
            study_id: 研究ID
            user_id: 用户ID
            model_name: 模型名称
            confidence_threshold: 置信度阈值
            result_data: 结果数据（JSON字符串）
            
        Returns:
            结果ID
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 如果result_data是字典或列表，转换为JSON字符串
            if isinstance(result_data, (dict, list)):
                result_data = json.dumps(result_data, ensure_ascii=False)
            
            cursor.execute('''
            INSERT INTO detection_results (
                study_id, user_id, model_name, confidence_threshold, result_data
            ) VALUES (?, ?, ?, ?, ?)
            ''', (study_id, user_id, model_name, confidence_threshold, result_data))
            
            result_id = cursor.lastrowid
            conn.commit()
            return result_id
        except Exception as e:
            logger.error(f"添加检测结果时出错: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def get_detection_results(self, study_id):
        """获取研究的检测结果
        
        Args:
            study_id: 研究ID
            
        Returns:
            检测结果列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT * FROM detection_results 
            WHERE study_id = ? 
            ORDER BY detection_time DESC
            ''', (study_id,))
            rows = cursor.fetchall()
            
            results = []
            for row in rows:
                result = dict(row)
                # 解析JSON结果数据
                if result['result_data']:
                    try:
                        result['result_data'] = json.loads(result['result_data'])
                    except json.JSONDecodeError:
                        pass
                results.append(result)
            
            return results
        except Exception as e:
            logger.error(f"获取检测结果时出错: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def add_annotation(self, study_id, user_id, instance_id, annotation_data):
        """添加标注
        
        Args:
            study_id: 研究ID
            user_id: 用户ID
            instance_id: 实例ID
            annotation_data: 标注数据（JSON字符串）
            
        Returns:
            标注ID
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 如果annotation_data是字典，转换为JSON字符串
            if isinstance(annotation_data, dict):
                annotation_data = json.dumps(annotation_data)
            
            # 检查是否已存在该实例的标注
            cursor.execute('''
            SELECT id FROM annotations 
            WHERE study_id = ? AND instance_id = ? AND user_id = ?
            ''', (study_id, instance_id, user_id))
            existing = cursor.fetchone()
            
            if existing:
                # 更新现有标注
                cursor.execute('''
                UPDATE annotations SET 
                    annotation_data = ?, 
                    updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
                ''', (annotation_data, existing[0]))
                annotation_id = existing[0]
            else:
                # 添加新标注
                cursor.execute('''
                INSERT INTO annotations (
                    study_id, user_id, instance_id, annotation_data
                ) VALUES (?, ?, ?, ?)
                ''', (study_id, user_id, instance_id, annotation_data))
                annotation_id = cursor.lastrowid
            
            conn.commit()
            return annotation_id
        except Exception as e:
            logger.error(f"添加标注时出错: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def get_annotations(self, study_id, user_id=None):
        """获取研究的标注
        
        Args:
            study_id: 研究ID
            user_id: 用户ID（可选）
            
        Returns:
            标注列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            if user_id:
                cursor.execute('''
                SELECT * FROM annotations 
                WHERE study_id = ? AND user_id = ? 
                ORDER BY created_at DESC
                ''', (study_id, user_id))
            else:
                cursor.execute('''
                SELECT * FROM annotations 
                WHERE study_id = ? 
                ORDER BY created_at DESC
                ''', (study_id,))
            
            rows = cursor.fetchall()
            
            annotations = []
            for row in rows:
                annotation = dict(row)
                # 解析JSON标注数据
                if annotation['annotation_data']:
                    try:
                        annotation['annotation_data'] = json.loads(annotation['annotation_data'])
                    except json.JSONDecodeError:
                        pass
                annotations.append(annotation)
            
            return annotations
        except Exception as e:
            logger.error(f"获取标注时出错: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def add_audit_log(self, user_id, action, entity_type, entity_id=None, details=None, ip_address=None):
        """添加审计日志
        
        Args:
            user_id: 用户ID
            action: 操作类型
            entity_type: 实体类型
            entity_id: 实体ID
            details: 详细信息
            ip_address: IP地址
            
        Returns:
            日志ID
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 如果details是字典，转换为JSON字符串
            if isinstance(details, dict):
                details = json.dumps(details)
            
            cursor.execute('''
            INSERT INTO audit_logs (
                user_id, action, entity_type, entity_id, details, ip_address
            ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, action, entity_type, entity_id, details, ip_address))
            
            log_id = cursor.lastrowid
            conn.commit()
            return log_id
        except Exception as e:
            logger.error(f"添加审计日志时出错: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def get_audit_logs(self, user_id=None, action=None, entity_type=None, limit=100, offset=0):
        """获取审计日志
        
        Args:
            user_id: 用户ID（可选）
            action: 操作类型（可选）
            entity_type: 实体类型（可选）
            limit: 限制返回数量
            offset: 偏移量
            
        Returns:
            审计日志列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            query = 'SELECT * FROM audit_logs WHERE 1=1'
            params = []
            
            if user_id is not None:
                query += ' AND user_id = ?'
                params.append(user_id)
            
            if action is not None:
                query += ' AND action = ?'
                params.append(action)
            
            if entity_type is not None:
                query += ' AND entity_type = ?'
                params.append(entity_type)
            
            query += ' ORDER BY timestamp DESC LIMIT ? OFFSET ?'
            params.extend([limit, offset])
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            logs = []
            for row in rows:
                log = dict(row)
                # 解析JSON详细信息
                if log['details']:
                    try:
                        log['details'] = json.loads(log['details'])
                    except json.JSONDecodeError:
                        pass
                logs.append(log)
            
            return logs
        except Exception as e:
            logger.error(f"获取审计日志时出错: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def delete_detection_results(self, study_id):
        """删除指定研究的检测结果
        
        Args:
            study_id: 研究ID（数据库ID）
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 删除检测结果
            cursor.execute('DELETE FROM detection_results WHERE study_id = ?', (study_id,))
            deleted_count = cursor.rowcount
            
            conn.commit()
            
            logger.info(f"成功删除研究 {study_id} 的 {deleted_count} 个检测结果")
            return deleted_count
            
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"删除检测结果时出错: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def delete_study(self, study_id):
        """删除研究及其相关数据
        
        Args:
            study_id: 研究ID（可以是数据库ID或Orthanc ID）
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 如果传入的是Orthanc ID，先获取数据库ID
            if isinstance(study_id, str) and len(study_id) > 10:  # Orthanc ID通常较长
                cursor.execute('SELECT id FROM studies WHERE orthanc_id = ?', (study_id,))
                result = cursor.fetchone()
                if result:
                    db_study_id = result[0]
                else:
                    logger.warning(f"未找到研究: {study_id}")
                    return
            else:
                db_study_id = study_id
            
            # 删除检测结果
            cursor.execute('DELETE FROM detection_results WHERE study_id = ?', (db_study_id,))
            deleted_results = cursor.rowcount
            
            # 删除研究记录
            cursor.execute('DELETE FROM studies WHERE id = ?', (db_study_id,))
            deleted_studies = cursor.rowcount
            
            conn.commit()
            
            if deleted_studies > 0:
                logger.info(f"成功删除研究 {study_id}，同时删除了 {deleted_results} 个检测结果")
            else:
                logger.warning(f"未找到要删除的研究: {study_id}")
                
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"删除研究时出错: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()